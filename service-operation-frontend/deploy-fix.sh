#!/bin/bash

# DTTrip 前端反向代理问题修复部署脚本

set -e

echo "================================"
echo "DTTrip 前端反向代理问题修复"
echo "================================"

echo "🔧 问题说明:"
echo "   - nginx反向代理配置已修复"
echo "   - 修复了API路径代理问题"
echo "   - 后端服务 https://soa-api.qa.dttrip.cn 工作正常"
echo ""

echo "📋 修复内容:"
echo "   1. nginx.simple.conf 中的 proxy_pass 配置已修正"
echo "   2. 增加了请求头大小限制配置"
echo "   3. 优化了代理缓冲设置"
echo ""

echo "🚀 开始重新构建和部署..."

# 停止现有容器（如果存在）
echo "🛑 停止现有前端容器..."
docker stop dttrip-frontend 2>/dev/null || echo "   没有运行中的前端容器"
docker rm dttrip-frontend 2>/dev/null || echo "   没有需要删除的前端容器"

# 构建新镜像
echo "🔨 构建前端镜像..."
docker build -t dttrip-frontend:latest .

if [ $? -eq 0 ]; then
    echo "✅ 镜像构建成功"
    
    # 启动新容器
    echo "🚀 启动前端容器..."
    docker run -d \
        --name dttrip-frontend \
        -p 80:80 \
        --restart unless-stopped \
        dttrip-frontend:latest
    
    if [ $? -eq 0 ]; then
        echo "✅ 前端容器启动成功"
        echo ""
        echo "🎉 部署完成！"
        echo ""
        echo "📋 验证步骤:"
        echo "   1. 访问 https://soa.qa.dttrip.cn"
        echo "   2. 点击SSO登录按钮"
        echo "   3. 检查是否能正常获取登录URL"
        echo ""
        echo "🔍 如果仍有问题，请检查:"
        echo "   - 域名解析是否正确"
        echo "   - SSL证书是否有效"
        echo "   - 后端服务是否正常运行"
    else
        echo "❌ 前端容器启动失败"
        exit 1
    fi
else
    echo "❌ 镜像构建失败"
    echo ""
    echo "💡 可能的原因:"
    echo "   - 网络连接问题，无法拉取基础镜像"
    echo "   - Docker Hub访问受限"
    echo ""
    echo "🔧 解决方案:"
    echo "   1. 检查网络连接"
    echo "   2. 稍后重试构建"
    echo "   3. 或使用国内Docker镜像源"
    exit 1
fi

echo "================================" 