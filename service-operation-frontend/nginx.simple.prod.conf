server {
    listen 80;
    server_name soa.dttrip.cn;
    root /usr/share/nginx/html;
    index index.html index.htm;

    # # 增加请求头和Cookie大小限制 - 进一步增大
    # large_client_header_buffers 8 128k;
    # client_header_buffer_size 128k;
    # client_max_body_size 100m;
    # client_body_buffer_size 128k;

    # 启用gzip压缩
    gzip on; 
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri =404;
    }

    # API代理到后端服务（解决跨域）
    # location /api/ {
    #     proxy_pass https://soa-api.dttrip.cn;  # 生产环境后端服务地址
    #     proxy_set_header Host $host;
    #     proxy_set_header X-Real-IP $remote_addr;
    #     proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    #     proxy_set_header X-Forwarded-Proto $scheme;

    #     # 支持WebSocket连接
    #     proxy_http_version 1.1;
    #     proxy_set_header Upgrade $http_upgrade;
    #     proxy_set_header Connection "upgrade";

    #     # 超时设置
    #     proxy_connect_timeout 60s;
    #     proxy_send_timeout 60s;
    #     proxy_read_timeout 60s;

    #     # 代理缓冲设置 - 增大缓冲区
    #     proxy_buffering on;
    #     proxy_buffer_size 16k;
    #     proxy_buffers 16 16k;
    #     proxy_busy_buffers_size 32k;
    #     proxy_temp_file_write_size 32k;

    #     # 处理大请求头
    #     proxy_max_temp_file_size 1024m;
    # }

    # 静态文件代理到后端服务（护照图片等）
    # location /uploads/ {
    #     proxy_pass https://soa-api.dttrip.cn;  # 生产环境后端服务地址
    #     proxy_set_header Host $host;
    #     proxy_set_header X-Real-IP $remote_addr;
    #     proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    #     proxy_set_header X-Forwarded-Proto $scheme;

    #     # 静态文件缓存设置
    #     proxy_cache_valid 200 1h;
    #     add_header Cache-Control "public, max-age=3600";
    # }

    # SPA路由支持 - 所有路由都返回index.html
    location / {
        try_files $uri $uri/ /index.html;
        add_header Cache-Control "no-cache";
    }

    # 健康检查端点
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }

    # 安全头部
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";

    # 错误页面
    error_page 404 /index.html;
} 
