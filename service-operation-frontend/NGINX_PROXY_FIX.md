# Nginx 反向代理问题修复说明

## 问题描述

在使用反向代理后，前端SSO登录功能出现问题：
- 前端请求 `GET https://soa.qa.dttrip.cn/api/auth/sso/login-url` 返回 400 Bad Request
- 错误信息：`Request Header Or Cookie Too Large`

## 问题原因分析

### 1. 请求头大小限制问题
- nginx默认的请求头缓冲区太小
- SSO相关的Cookie和请求头数据量较大
- 导致nginx返回400错误

### 2. 代理配置问题
- nginx反向代理配置需要优化
- 需要增加请求头和Cookie大小限制
- 需要优化代理缓冲设置

## 解决方案

### 1. nginx配置优化

在 `nginx.simple.conf` 中添加/修改以下配置：

```nginx
# 增加请求头和Cookie大小限制
large_client_header_buffers 8 64k;
client_header_buffer_size 64k;
client_max_body_size 100m;
client_body_buffer_size 128k;

# 代理缓冲设置优化
location /api/ {
    proxy_pass https://soa-api.qa.dttrip.cn;
    
    # 代理缓冲设置 - 增大缓冲区
    proxy_buffering on;
    proxy_buffer_size 16k;
    proxy_buffers 16 16k;
    proxy_busy_buffers_size 32k;
    proxy_temp_file_write_size 32k;
    proxy_max_temp_file_size 1024m;
    
    # 其他代理设置...
}
```

### 2. 后端服务验证

验证后端服务正常工作：
```bash
curl -X GET "https://soa-api.qa.dttrip.cn/api/auth/sso/login-url" -H "Content-Type: application/json"
```

预期响应：
```json
{
  "data": {
    "login_url": "http://tccommon.qas.17usoft.com/oauth/authorize?response_type=code&scope=read&client_id=OpsFlow.local&redirect_uri=http%3A%2F%2Fsoa.qa.dttrip.cn%2Fauth%2Fcallback&state=random_state"
  }
}
```

## 部署步骤

1. **修改nginx配置**（已完成）
   - 更新 `nginx.simple.conf` 文件
   - 增加请求头大小限制
   - 优化代理缓冲设置

2. **重新构建Docker镜像**
   ```bash
   cd service-operation-frontend
   ./deploy-fix.sh
   ```

3. **验证修复效果**
   - 访问 https://soa.qa.dttrip.cn
   - 点击SSO登录按钮
   - 检查是否能正常获取登录URL

## 技术细节

### 路由结构
- 前端请求：`/api/auth/sso/login-url`
- nginx代理：`location /api/` → `https://soa-api.qa.dttrip.cn`
- 后端实际路径：`/api/auth/sso/login-url`

### 配置要点
1. `proxy_pass` 不能以 `/` 结尾，否则会截断路径
2. 请求头缓冲区大小要足够处理SSO相关数据
3. 代理缓冲设置要适配大请求响应

## 故障排除

如果问题仍然存在，请检查：

1. **域名解析**
   ```bash
   nslookup soa.qa.dttrip.cn
   nslookup soa-api.qa.dttrip.cn
   ```

2. **SSL证书**
   ```bash
   curl -I https://soa.qa.dttrip.cn
   curl -I https://soa-api.qa.dttrip.cn
   ```

3. **后端服务状态**
   ```bash
   curl -v https://soa-api.qa.dttrip.cn/health
   ```

4. **Docker容器状态**
   ```bash
   docker ps
   docker logs dttrip-frontend
   ```

## 预防措施

1. **监控请求头大小**
   - 定期检查SSO相关Cookie大小
   - 清理不必要的localStorage数据

2. **nginx配置管理**
   - 版本控制nginx配置文件
   - 测试环境先验证配置更改

3. **自动化部署**
   - 使用CI/CD流程部署配置更改
   - 自动化测试反向代理功能

---

**修复时间**: 2025-06-14  
**修复人员**: AI Assistant  
**验证状态**: 配置已修复，等待Docker重新构建部署 