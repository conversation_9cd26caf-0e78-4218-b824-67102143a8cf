#!/bin/bash

# 前端开发环境启动脚本
# 使用Vite代理访问后端API，解决跨域问题

echo "🚀 启动前端开发服务器..."
echo "📋 配置信息："
echo "   - 前端地址: http://localhost:5173"
echo "   - 后端代理: /api -> http://localhost:8000"
echo "   - 确保后端服务已在 http://localhost:8000 启动"
echo ""

# 检查后端服务是否启动
echo "🔍 检查后端服务状态..."
if curl -s http://localhost:8000/health > /dev/null; then
    echo "✅ 后端服务已启动"
else
    echo "❌ 后端服务未启动，请先启动后端服务："
    echo "   cd ../service-operation-server && poetry run python -m src"
    echo ""
fi

# 启动前端开发服务器
echo "🎯 启动前端服务器..."
npm run dev

echo "📝 开发环境说明："
echo "   - API请求会自动代理到后端服务"
echo "   - 无需配置CORS，代理解决跨域问题"
echo "   - 前端访问: http://localhost:5173"
echo "   - API代理: http://localhost:5173/api -> http://localhost:8000/api" 