# 服务运营前端管理系统

基于Vite+React+TypeScript开发的服务运营前端管理系统，采用shadcn/ui组件库实现美观的UI界面。

## 项目概述

本项目是一个服务运营管理系统的前端部分，提供了用户登录和仪表板功能。项目采用了现代化的前端技术栈和最佳实践，包括：

- **Vite** - 快速的前端构建工具
- **React** - 用户界面库
- **TypeScript** - 类型安全的JavaScript超集
- **Tailwind CSS** - 实用优先的CSS框架
- **shadcn/ui** - 基于Radix UI和Tailwind CSS的组件库
- **React Router** - 路由管理
- **Context API** - 状态管理

## 项目结构

项目采用特性优先的目录结构，而非类型优先，遵循React最佳实践：

```
src/
├── assets/          # 静态资源文件
├── components/      # 共享/通用组件
│   ├── ui/          # 基础UI组件（button, card, input等）
│   └── layout/      # 布局组件
├── features/        # 按功能模块组织的代码
│   └── auth/        # 认证模块
│       ├── components/  # 模块专用组件
│       ├── hooks/       # 模块专用钩子
│       ├── services/    # 模块相关API调用
│       └── utils/       # 模块工具函数
├── hooks/           # 全局共享钩子
├── pages/           # 页面级组件
├── services/        # API服务和数据获取
├── store/           # 全局状态管理
├── types/           # TypeScript类型定义
├── utils/           # 工具函数
└── App.tsx          # 应用入口组件
```

## 开发指南

### 环境要求

- Node.js 16.x 或更高版本
- npm 7.x 或更高版本

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

开发服务器将在 http://localhost:5173 启动。

### 构建生产版本

```bash
npm run build
```

构建后的文件将输出到 `dist` 目录。

### 预览生产版本

```bash
npm run preview
```

## 项目扩展指南

### 添加新页面

1. 在 `src/pages` 目录下创建新的页面组件
2. 在 `src/App.tsx` 中添加新的路由配置
3. 如果需要受保护的路由，请使用 `ProtectedRoute` 组件包装

示例：

```tsx
// 在App.tsx中添加新路由
<Route path="/new-page" element={
  <ProtectedRoute>
    <NewPage />
  </ProtectedRoute>
} />
```

### 添加新功能模块

1. 在 `src/features` 目录下创建新的功能模块目录
2. 按照以下结构组织代码：
   - `components/` - 模块专用组件
   - `hooks/` - 模块专用钩子
   - `services/` - 模块相关API调用
   - `utils/` - 模块工具函数

### 添加新UI组件

1. 在 `src/components/ui` 目录下创建新的UI组件
2. 使用 shadcn/ui 的设计原则，确保组件风格一致

### 添加新API服务

1. 在 `src/services` 目录下创建新的服务文件
2. 使用 `api.ts` 中提供的通用请求函数

示例：

```tsx
// src/services/newService.ts
import { api } from '@/services/api';

export const fetchData = () => {
  return api.get('/endpoint');
};
```

### 添加新状态管理

1. 在 `src/store` 目录下创建新的Context文件
2. 使用React的Context API实现状态管理

## 测试账号

系统内置了一个测试账号，可用于登录和测试系统功能：

- 邮箱: <EMAIL>
- 密码: password

## 代码规范

- 组件文件使用 PascalCase 命名（如 `Button.tsx`）
- 非组件文件使用 camelCase 命名（如 `useAuth.ts`）
- 每个文件只导出一个主要功能，便于自动导入
- 组件代码超过100行时考虑拆分
- 使用TypeScript定义props类型，提高代码可靠性
- props数量超过3个时使用对象解构
