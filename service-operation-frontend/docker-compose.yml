version: '3.8'

services:
  dttrip-frontend:
    image: dttrip-frontend:latest
    container_name: dttrip-frontend
    ports:
      - "80:80"
    environment:
      - BACKEND_URL=http://backend:8000  # 可根据实际后端服务调整
    networks:
      - dttrip-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 3s
      retries: 3
      start_period: 5s

networks:
  dttrip-network:
    external: true 