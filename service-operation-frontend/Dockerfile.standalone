# 多阶段构建 - 构建阶段
FROM node:18-alpine AS builder

# 设置工作目录
WORKDIR /app

# 设置npm镜像源
RUN npm config set registry https://registry.npmmirror.com/

# 复制package.json和package-lock.json
COPY package*.json ./
COPY yarn.lock* ./

# 安装依赖
RUN npm install --legacy-peer-deps

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 生产阶段
FROM nginx:alpine

# 安装额外的nginx模块以支持环境变量替换
RUN apk add --no-cache gettext

# 复制构建产物
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制nginx配置
COPY nginx.standalone.conf /etc/nginx/conf.d/default.conf

# 移除默认的nginx配置
RUN rm -f /etc/nginx/conf.d/default.conf.dpkg-old

# 创建启动脚本
COPY <<EOF /docker-entrypoint.sh
#!/bin/sh
set -e

# 如果提供了BACKEND_URL环境变量，替换nginx配置中的占位符
if [ -n "\$BACKEND_URL" ]; then
    echo "Setting backend URL to: \$BACKEND_URL"
    envsubst '\$BACKEND_URL' < /etc/nginx/conf.d/default.conf > /tmp/default.conf
    mv /tmp/default.conf /etc/nginx/conf.d/default.conf
else
    echo "No BACKEND_URL provided, using default configuration"
fi

# 启动nginx
exec nginx -g 'daemon off;'
EOF

# 设置启动脚本权限
RUN chmod +x /docker-entrypoint.sh

# 创建nginx运行用户
RUN addgroup -g 101 -S nginx && \
    adduser -S -D -H -u 101 -h /var/cache/nginx -s /sbin/nologin -G nginx -g nginx nginx

# 设置目录权限
RUN chown -R nginx:nginx /usr/share/nginx/html && \
    chown -R nginx:nginx /var/cache/nginx && \
    chown -R nginx:nginx /var/log/nginx && \
    chown -R nginx:nginx /etc/nginx/conf.d

# 创建nginx PID目录
RUN mkdir -p /var/run/nginx && \
    chown nginx:nginx /var/run/nginx

# 修改nginx配置以非root用户运行
RUN sed -i 's/user  nginx;/user nginx;/' /etc/nginx/nginx.conf && \
    sed -i 's|/var/run/nginx.pid|/var/run/nginx/nginx.pid|' /etc/nginx/nginx.conf

# 暴露端口
EXPOSE 80

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost/health || exit 1

# 运行时用户
USER nginx

# 启动命令
ENTRYPOINT ["/docker-entrypoint.sh"] 