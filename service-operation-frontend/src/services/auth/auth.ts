/**
 * 自动认证服务
 * 使用开发者密钥自动获取access_token
 */

import { API_BASE_URL, DEVELOPER_KEY, DEVELOPER_SECRET } from '@/utils/constants';

// Token响应接口
interface TokenResponse {
  data: {
    access_token: string;
    token_type: string;
  };
  message: string;
}

// Token对响应接口
interface TokenPairResponse {
  data: {
    access_token: string;
    refresh_token: string;
    token_type: string;
    expires_in: number;
  };
  message: string;
}

// 认证错误类
class AuthError extends Error {
  constructor(message: string, public status?: number) {
    super(message);
    this.name = 'AuthError';
  }
}

// JWT Token解析
function parseJWT(token: string): any {
  try {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map(function (c) {
          return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
        })
        .join('')
    );
    return JSON.parse(jsonPayload);
  } catch (error) {
    console.error('JWT解析失败:', error);
    return null;
  }
}

// 检查Token是否过期（提前5分钟过期）
function isTokenExpired(token: string): boolean {
  const tokenData = parseJWT(token);
  if (!tokenData || !tokenData.exp) {
    return true;
  }
  
  const currentTime = Math.floor(Date.now() / 1000);
  const bufferTime = 5 * 60; // 5分钟缓冲时间
  return (tokenData.exp - bufferTime) < currentTime;
}

/**
 * 自动认证服务类
 */
class AutoAuthService {
  private refreshPromise: Promise<string> | null = null;
  private static readonly ACCESS_TOKEN_KEY = 'access_token';
  private static readonly REFRESH_TOKEN_KEY = 'refresh_token';

  /**
   * 获取有效的access_token
   * 如果token不存在或即将过期，会自动刷新
   */
  async getValidToken(): Promise<string> {
    const currentToken = localStorage.getItem(AutoAuthService.ACCESS_TOKEN_KEY);
    
    // 检查当前token是否有效
    if (currentToken && !isTokenExpired(currentToken)) {
      return currentToken;
    }

    // 如果正在刷新，等待刷新完成
    if (this.refreshPromise) {
      return await this.refreshPromise;
    }

    // 开始刷新token
    this.refreshPromise = this.refreshTokens();
    
    try {
      const newToken = await this.refreshPromise;
      return newToken;
    } finally {
      this.refreshPromise = null;
    }
  }

  /**
   * 刷新token（优先使用refresh token，否则使用开发者密钥）
   */
  private async refreshTokens(): Promise<string> {
    const refreshToken = localStorage.getItem(AutoAuthService.REFRESH_TOKEN_KEY);
    
    // 优先尝试使用refresh token
    if (refreshToken) {
      try {
        return await this.refreshWithRefreshToken(refreshToken);
      } catch (error) {
        console.warn('🔄 Refresh token刷新失败，尝试使用开发者密钥:', error);
        // refresh token失效，清除它
        this.clearRefreshToken();
      }
    }

    // 使用开发者密钥获取新的token对
    return await this.loginWithDeveloperKey();
  }

  /**
   * 使用refresh token刷新access token
   */
  private async refreshWithRefreshToken(refreshToken: string): Promise<string> {
    console.log('🔄 使用refresh token刷新access_token...');

    try {
      const response = await fetch(`${API_BASE_URL}/auth/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          refresh_token: refreshToken,
        }),
      });

      if (!response.ok) {
        const errorData = await response.text();
        console.error('Refresh token刷新失败:', response.status, errorData);
        throw new AuthError(`Refresh token刷新失败 (${response.status}): ${errorData}`, response.status);
      }

      const result: TokenPairResponse = await response.json();
      
      // 保存新的token对
      localStorage.setItem(AutoAuthService.ACCESS_TOKEN_KEY, result.data.access_token);
      localStorage.setItem(AutoAuthService.REFRESH_TOKEN_KEY, result.data.refresh_token);
      
      console.log('✅ 成功使用refresh token刷新access_token');

      return result.data.access_token;
    } catch (error) {
      console.error('❌ Refresh token刷新失败:', error);
      
      if (error instanceof AuthError) {
        throw error;
      }
      
      throw new AuthError('网络错误，无法刷新access_token');
    }
  }

  /**
   * 使用开发者密钥登录获取token对
   */
  private async loginWithDeveloperKey(): Promise<string> {
    if (!DEVELOPER_KEY || !DEVELOPER_SECRET) {
      throw new AuthError('开发者密钥未配置，请检查环境变量 VITE_DEVELOPER_KEY 和 VITE_DEVELOPER_SECRET');
    }

    console.log('🔄 使用开发者密钥获取token对...');

    try {
      const response = await fetch(`${API_BASE_URL}/auth/token/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          api_key: DEVELOPER_KEY,
          api_secret: DEVELOPER_SECRET,
        }),
      });

      if (!response.ok) {
        const errorData = await response.text();
        console.error('认证失败:', response.status, errorData);
        throw new AuthError(`认证失败 (${response.status}): ${errorData}`, response.status);
      }

      const result: TokenPairResponse = await response.json();
      
      // 保存token对
      localStorage.setItem(AutoAuthService.ACCESS_TOKEN_KEY, result.data.access_token);
      localStorage.setItem(AutoAuthService.REFRESH_TOKEN_KEY, result.data.refresh_token);
      
      console.log('✅ 成功获取新的token对');

      return result.data.access_token;
    } catch (error) {
      console.error('❌ 获取token对失败:', error);
      
      if (error instanceof AuthError) {
        throw error;
      }
      
      throw new AuthError('网络错误，无法获取token对');
    }
  }

  /**
   * 清除本地token
   */
  clearToken(): void {
    localStorage.removeItem(AutoAuthService.ACCESS_TOKEN_KEY);
    localStorage.removeItem(AutoAuthService.REFRESH_TOKEN_KEY);
    console.log('🗑️ 已清除本地token');
  }

  /**
   * 清除refresh token
   */
  clearRefreshToken(): void {
    localStorage.removeItem(AutoAuthService.REFRESH_TOKEN_KEY);
    console.log('🗑️ 已清除refresh token');
  }

  /**
   * 检查开发者密钥是否已配置
   */
  isDeveloperKeyConfigured(): boolean {
    return !!(DEVELOPER_KEY && DEVELOPER_SECRET);
  }

  /**
   * 获取当前token信息
   */
  getTokenInfo(): {
    hasToken: boolean;
    hasRefreshToken: boolean;
    isValid: boolean;
    userInfo: any;
    expiresIn: number;
  } {
    const token = localStorage.getItem(AutoAuthService.ACCESS_TOKEN_KEY);
    const refreshToken = localStorage.getItem(AutoAuthService.REFRESH_TOKEN_KEY);
    
    if (!token) {
      return {
        hasToken: false,
        hasRefreshToken: !!refreshToken,
        isValid: false,
        userInfo: null,
        expiresIn: 0,
      };
    }

    const tokenData = parseJWT(token);
    const expired = isTokenExpired(token);
    const currentTime = Math.floor(Date.now() / 1000);
    const expiresIn = tokenData?.exp ? Math.max(0, tokenData.exp - currentTime) : 0;

    return {
      hasToken: true,
      hasRefreshToken: !!refreshToken,
      isValid: !expired,
      userInfo: tokenData,
      expiresIn,
    };
  }

  /**
   * 登出（撤销refresh token）
   */
  async logout(): Promise<void> {
    const refreshToken = localStorage.getItem(AutoAuthService.REFRESH_TOKEN_KEY);
    
    // 清除本地token
    this.clearToken();
    
    // 如果有refresh token，通知服务器撤销
    if (refreshToken) {
      try {
        await fetch(`${API_BASE_URL}/auth/logout`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            refresh_token: refreshToken,
          }),
        });
        console.log('✅ 成功撤销refresh token');
      } catch (error) {
        console.warn('⚠️ 撤销refresh token失败，但本地token已清除:', error);
      }
    }
  }
}

// 导出单例实例
export const autoAuthService = new AutoAuthService();

// 导出类型和错误
export { AuthError };
export type { TokenResponse, TokenPairResponse }; 