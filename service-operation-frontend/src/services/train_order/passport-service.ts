import { api } from '@/services/system';
import { API_BASE_URL } from '@/utils/constants';

// 护照识别结果类型定义
export interface PassportRecognitionResult {
  id: number;
  user_id: string;
  task_id: string;
  uploaded_image_url: string;
  dify_image_url?: string;
  dify_image_uuid?: string;
  dify_image_filename?: string;
  dify_filename?: string;
  document_type?: string;
  country_of_issue?: string;
  passport_number?: string;
  surname?: string;
  given_names?: string;
  nationality?: string;
  date_of_birth?: string;
  sex?: string;
  place_of_birth?: string;
  date_of_issue?: string;
  date_of_expiry?: string;
  authority?: string;
  mrz_line1?: string;
  mrz_line2?: string;
  signature_present?: boolean;
  additional_info?: Record<string, any>;
  processing_status: 'pending' | 'processing' | 'completed' | 'failed';
  created_at: string;
  updated_at: string;
}

// 文件上传响应
export interface FileUploadResponse {
  filename: string;
  file_path: string;
  file_url: string;
  file_size: number;
  content_type: string;
}

// 护照上传响应
export interface PassportUploadResponse {
  task_id: string;
  uploaded_files: FileUploadResponse[];
  total_files: number;
  message: string;
}

// 护照列表响应
export interface PassportListResponse {
  total: number;
  items: PassportRecognitionResult[];
  page: number;
  size: number;
}

// 护照API服务
export const passportService = {
  // 上传护照文件
  async uploadFiles(files: FileList, taskId?: string): Promise<PassportUploadResponse> {
    const formData = new FormData();
    
    // 添加文件
    for (let i = 0; i < files.length; i++) {
      formData.append('files', files[i]);
    }
    
    // 添加任务ID（可选）
    if (taskId) {
      formData.append('task_id', taskId);
    }

    // 获取token - 使用正确的localStorage键名
    const token = localStorage.getItem('access_token') || localStorage.getItem('app_token');
    if (!token) {
      throw new Error('未找到认证令牌，请重新登录');
    }

    try {
      // 使用constants.ts中的API基础URL
      console.log('上传文件到:', `${API_BASE_URL}/passport/upload`);
      
              const response = await fetch(`${API_BASE_URL}/passport/upload`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || `上传失败: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('文件上传失败:', error);
      throw error;
    }
  },

  // 获取护照列表
  async getPassportList(params?: {
    page?: number;
    size?: number;
    task_id?: string;
    status?: string;
  }): Promise<PassportListResponse> {
    const queryParams: Record<string, string> = {};
    
    if (params?.page) queryParams.page = params.page.toString();
    if (params?.size) queryParams.size = params.size.toString();
    if (params?.task_id) queryParams.task_id = params.task_id;
    if (params?.status) queryParams.status = params.status;

    console.log('获取护照列表，参数:', queryParams);
    const response = await api.get<PassportListResponse>('/passport/list/', { params: queryParams });
    console.log('护照列表API响应:', response);
    
    if (!response.success) {
      console.error('获取护照列表失败:', response.error);
      throw new Error(response.error || '获取护照列表失败');
    }
    
    console.log('护照列表数据:', response.data);
    return response.data!;
  },

  // 根据任务ID获取护照列表
  async getPassportsByTask(taskId: string): Promise<PassportListResponse> {
    const response = await api.get<PassportListResponse>(`/passport/task/${taskId}/`);
    
    if (!response.success) {
      throw new Error(response.error || '获取任务护照列表失败');
    }
    
    return response.data!;
  },

  // 获取单个护照详情
  async getPassportDetail(passportId: number): Promise<PassportRecognitionResult> {
    const response = await api.get<PassportRecognitionResult>(`/passport/${passportId}/`);
    
    if (!response.success) {
      throw new Error(response.error || '获取护照详情失败');
    }
    
    return response.data!;
  },

  // 删除护照记录
  async deletePassport(passportId: number): Promise<void> {
    const response = await api.delete(`/passport/${passportId}/`);
    
    if (!response.success) {
      throw new Error(response.error || '删除护照记录失败');
    }
  },
}; 