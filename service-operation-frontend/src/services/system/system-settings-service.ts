/**
 * 系统设置API服务
 */
import { api } from './api';

// 同程管家凭证类型
export interface TongchengCredentials {
  username: string | null;
  password: string | null;
}

// API响应类型
export interface SystemSettingsApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

/**
 * 获取同程管家登录凭证
 */
export async function getSystemSettingsCredentials(): Promise<SystemSettingsApiResponse<TongchengCredentials>> {
  try {
    console.log('🔍 获取系统设置凭证...');
    
    const response = await api.get<TongchengCredentials>('/system-settings/tongcheng/credentials');
    
    if (response.success && response.data) {
      console.log('✅ 成功获取系统设置凭证');
      return {
        success: true,
        data: response.data
      };
    } else {
      console.warn('⚠️ 获取系统设置凭证失败:', response.error);
      return {
        success: false,
        error: response.error || '获取系统设置凭证失败'
      };
    }
  } catch (error) {
    console.error('❌ 获取系统设置凭证异常:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : '获取系统设置凭证失败'
    };
  }
}

/**
 * 保存同程管家登录凭证
 */
export async function saveSystemSettingsCredentials(
  credentials: TongchengCredentials
): Promise<SystemSettingsApiResponse<TongchengCredentials>> {
  try {
    console.log('💾 保存系统设置凭证...', {
      username: credentials.username ? '***' : null,
      password: credentials.password ? '***' : null
    });
    
    const response = await api.put<TongchengCredentials>(
      '/system-settings/tongcheng/credentials',
      credentials
    );
    
    if (response.success && response.data) {
      console.log('✅ 成功保存系统设置凭证');
      return {
        success: true,
        data: response.data
      };
    } else {
      console.warn('⚠️ 保存系统设置凭证失败:', response.error);
      return {
        success: false,
        error: response.error || '保存系统设置凭证失败'
      };
    }
  } catch (error) {
    console.error('❌ 保存系统设置凭证异常:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : '保存系统设置凭证失败'
    };
  }
}

/**
 * 系统设置服务对象
 */
export const SystemSettingsService = {
  getCredentials: getSystemSettingsCredentials,
  saveCredentials: saveSystemSettingsCredentials,
};

export default SystemSettingsService; 