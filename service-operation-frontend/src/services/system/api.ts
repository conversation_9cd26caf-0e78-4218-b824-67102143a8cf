// 基础API服务配置
import { API_BASE_URL } from '@/utils/constants';
import { checkAndHandleAuthError } from '@/utils/global-auth-handler';

// API响应类型
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

// 通用请求配置选项
export interface RequestOptions {
  headers?: Record<string, string>;
  params?: Record<string, string>;
}

// 请求工具函数
async function request<T>(
  endpoint: string,
  method: string = 'GET',
  data?: any,
  options: RequestOptions = {}
): Promise<ApiResponse<T>> {
  // 构建完整的URL路径
  let fullUrl = `${API_BASE_URL}${endpoint}`;
  
  // 添加查询参数
  if (options.params) {
    const searchParams = new URLSearchParams();
    Object.entries(options.params).forEach(([key, value]) => {
      searchParams.append(key, value);
    });
    const queryString = searchParams.toString();
    if (queryString) {
      fullUrl += `?${queryString}`;
    }
  }

  // 默认请求头
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    ...options.headers,
  };

  // 添加认证token（如果存在）
  const token = localStorage.getItem('access_token') || localStorage.getItem('app_token');
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }

  console.log(`🌐 [API Service] 请求: ${method} ${fullUrl}`);
  console.log('📋 [API Service] 请求头:', headers);
  if (data) console.log('📦 [API Service] 请求数据:', data);

  try {
    const response = await fetch(fullUrl, {
      method,
      headers,
      body: data ? JSON.stringify(data) : undefined,
    });

    console.log(`🔄 [API Service] 响应状态: ${response.status} ${response.statusText}`);
    
    // 解析响应
    const responseData = await response.json();
    console.log('📥 [API Service] 响应数据:', responseData);

    if (!response.ok) {
      console.error(`❌ [API Service] 请求失败: ${response.status}`, responseData);
      
      // 构建错误对象
      const error = {
        status: response.status,
        response: {
          status: response.status,
          data: responseData
        },
        message: responseData.detail || responseData.message || `请求失败: ${response.status}`,
        detail: responseData.detail,
        error: responseData.error
      };
      
      // 使用全局认证错误处理器检查并处理认证错误
      const isAuthError = checkAndHandleAuthError(error);
      
      if (isAuthError) {
        console.log('✅ [API Service] 认证错误已由全局处理器处理');
      } else {
        console.log('ℹ️ [API Service] 非认证错误，继续正常处理');
      }
      
      return {
        success: false,
        error: responseData.detail || responseData.message || `请求失败: ${response.status}`,
      };
    }

    console.log('✅ [API Service] 请求成功');
    return {
      success: true,
      data: responseData,
    };
  } catch (error) {
    console.error('🔥 [API Service] 请求异常:', error);
    
    // 检查网络错误是否可能是认证相关的
    const errorObj = {
      message: error instanceof Error ? error.message : '网络请求失败',
      error: error
    };
    
    const isAuthError = checkAndHandleAuthError(errorObj);
    
    if (isAuthError) {
      console.log('✅ [API Service] 网络错误中的认证错误已由全局处理器处理');
    }
    
    return {
      success: false,
      error: error instanceof Error ? error.message : '网络请求失败',
    };
  }
}

// 导出API方法
export const api = {
  get: <T>(endpoint: string, options?: RequestOptions) => 
    request<T>(endpoint, 'GET', undefined, options),
  
  post: <T>(endpoint: string, data: any, options?: RequestOptions) => 
    request<T>(endpoint, 'POST', data, options),
  
  put: <T>(endpoint: string, data: any, options?: RequestOptions) => 
    request<T>(endpoint, 'PUT', data, options),
  
  delete: <T>(endpoint: string, options?: RequestOptions) => 
    request<T>(endpoint, 'DELETE', undefined, options),
};
