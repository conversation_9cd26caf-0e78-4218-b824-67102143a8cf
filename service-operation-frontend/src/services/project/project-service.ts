import { api } from '@/services/system';
import type {
  Project,
  ProjectListResponse,
  CreateProjectRequest,
  UpdateProjectRequest,
  ProjectQueryParams,
  ProjectStats
} from '@/types/project';

/**
 * 项目管理API服务
 */
export class ProjectService {
  private static readonly BASE_PATH = '/project/';

  /**
   * 获取项目列表
   */
  static async getProjects(params: ProjectQueryParams = {}) {
    const queryParams: Record<string, string> = {};
    
    if (params.page) queryParams.page = params.page.toString();
    if (params.page_size) queryParams.page_size = params.page_size.toString();
    if (params.project_name) queryParams.project_name = params.project_name;
    if (params.client_name) queryParams.client_name = params.client_name;
    if (params.creator_name) queryParams.creator_name = params.creator_name;

    return api.get<ProjectListResponse>(this.BASE_PATH, { params: queryParams });
  }

  /**
   * 根据ID获取项目详情
   */
  static async getProject(id: number) {
    return api.get<Project>(`${this.BASE_PATH}${id}`);
  }

  /**
   * 根据项目编号获取项目详情
   */
  static async getProjectByNumber(projectNumber: number) {
    return api.get<Project>(`${this.BASE_PATH}by-number/${projectNumber}`);
  }

  /**
   * 创建新项目
   */
  static async createProject(data: CreateProjectRequest) {
    // 确保 creator_user_id 是数字类型
    const projectData = {
      ...data,
    };

    // 发送请求
    try {
      const response = await api.post<Project>(this.BASE_PATH, projectData);
      console.log('创建项目响应:', response);
      return response;
    } catch (error) {
      console.error('创建项目异常:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '创建项目失败'
      };
    }
  }

  /**
   * 更新项目
   */
  static async updateProject(id: number, data: UpdateProjectRequest) {
    return api.put<Project>(`${this.BASE_PATH}${id}`, data);
  }

  /**
   * 删除项目
   */
  static async deleteProject(id: number) {
    return api.delete(`${this.BASE_PATH}${id}`);
  }

  /**
   * 获取项目统计信息
   */
  static async getProjectStats() {
    return api.get<ProjectStats>(`${this.BASE_PATH}stats/summary`);
  }
} 