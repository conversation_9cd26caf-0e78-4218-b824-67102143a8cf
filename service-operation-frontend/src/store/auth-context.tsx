import React, { createContext, useContext, useState, useEffect } from 'react';
import { SSOService } from '@/services/auth';
import { Permission } from '@/hooks/usePermissions';

type User = {
  id: string;
  username: string;
  email: string;
  role: string;
  department?: string; // 添加部门字段，可选
  work_id?: string;  // 添加工号字段，可选
  avatar_url?: string;
  permissions?: Permission[]; // 添加权限字段，可选
};

type AuthContextType = {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  loginWithSSO: () => Promise<void>;
  logout: () => Promise<void>;
  error: string | null;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    // 检查本地存储中是否有用户信息
    const storedUser = localStorage.getItem('userInfo');  // 改为userInfo
    const accessToken = localStorage.getItem('access_token');
    console.log('AuthProvider - 从本地存储中读取的用户信息:', storedUser);
    
    if (storedUser) {
      const parsedUser = JSON.parse(storedUser);
      console.log('AuthProvider - 解析后的用户信息:', parsedUser);
      
      // 创建用户对象
      const user: User = {
        id: parsedUser.id || parsedUser.userId,
        username: parsedUser.username,
        email: parsedUser.email || '',
        role: 'user',
        department: parsedUser.department,
        work_id: parsedUser.work_id,
        avatar_url: parsedUser.avatar_url || ''
      };
      
      setUser(user);
      setIsAuthenticated(true);
    }
    
    // 检查是否有SSO登录的访问令牌
    const checkSSOLogin = async () => {
      try {
        // 如果有访问令牌，先设置认证状态为true
        if (accessToken) {
          setIsAuthenticated(true);
          
          // 如果没有本地存储的用户信息，则获取SSO用户信息
          if (!storedUser) {
            const userInfo = await SSOService.getUserInfo();
            console.log('从SSO获取的用户信息:', userInfo);
            
            if (userInfo) {
              // 创建用户对象
              const user: User = {
                id: userInfo.id || userInfo.userId,
                username: userInfo.username,
                email: userInfo.email || '',
                role: 'user',
                department: userInfo.department,
                work_id: userInfo.work_id,
                avatar_url: userInfo.avatar_url || ''
              };
              
              console.log('创建的用户对象:', user);
              
              // 保存用户信息
              setUser(user);
              localStorage.setItem('userInfo', JSON.stringify(userInfo));  // 存储原始用户信息
            }
          }
        }
      } catch (error) {
        console.error('检查SSO登录状态时出错:', error);
        // 清除无效的访问令牌
        localStorage.removeItem('access_token');
        setIsAuthenticated(false);
      } finally {
        setIsLoading(false);
      }
    };
    
    checkSSOLogin();
  }, []);

  // 监听全局认证失败事件
  useEffect(() => {
    const handleAuthFailure = (event: CustomEvent) => {
      console.warn('🔒 [AuthContext] 收到全局认证失败事件:', event.detail);

      // 更新认证状态
      console.log('🔄 [AuthContext] 更新认证状态...');
      setIsAuthenticated(false);
      setUser(null);
      setError(event.detail?.message || '认证已过期，请重新登录');

      // 清理本地存储（防止重复清理）
      console.log('🧹 [AuthContext] 清理本地存储...');
      const authKeys = ['user', 'userInfo', 'access_token', 'app_token', 'token', 'sso_state'];
      authKeys.forEach(key => {
        const oldValue = localStorage.getItem(key);
        localStorage.removeItem(key);
        if (oldValue) {
          console.log(`  - [AuthContext] 已清理 ${key}`);
        }
      });

      // 如果事件要求强制重定向，执行跳转
      if (event.detail?.forceRedirect && window.location.pathname !== '/login') {
        console.log('🔄 [AuthContext] 执行强制重定向到登录页面...');

        // 延迟执行，确保状态更新完成
        setTimeout(() => {
          try {
            console.log('🔄 [AuthContext] 开始跳转...');
            window.location.replace('/login');
            console.log('✅ [AuthContext] 跳转命令已执行');
          } catch (error) {
            console.error('❌ [AuthContext] 跳转失败:', error);
            // 备用方法
            try {
              window.location.href = '/login';
            } catch (error2) {
              console.error('❌ [AuthContext] 备用跳转也失败:', error2);
            }
          }
        }, 100);
      }

      console.log('✅ [AuthContext] 认证失败处理完成');
    };

    console.log('🎧 [AuthContext] 添加全局认证失败事件监听器');
    // 添加事件监听器
    window.addEventListener('authenticationFailure', handleAuthFailure as EventListener);

    // 清理事件监听器
    return () => {
      console.log('🧹 [AuthContext] 移除全局认证失败事件监听器');
      window.removeEventListener('authenticationFailure', handleAuthFailure as EventListener);
    };
  }, []);

  const login = async (email: string, password: string) => {
    try {
      setIsLoading(true);
      setError(null);
      
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 模拟验证
      if (email === '<EMAIL>' && password === 'password') {
        const user = {
          id: '1',
          username: 'Admin User',
          email: '<EMAIL>',
          role: 'admin',
          work_id: '',
          avatar_url: ''
        };
        
        setUser(user);
        setIsAuthenticated(true);
        localStorage.setItem('user', JSON.stringify(user));
        localStorage.setItem('access_token', 'mock-jwt-token');
        localStorage.setItem('token', 'mock-jwt-token'); // 保持兼容性
      } else {
        throw new Error('邮箱或密码不正确');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '登录失败');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const loginWithSSO = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      // 获取当前页面的 URL，作为登录成功后的返回地址
      const returnUri = window.location.href;
      
      // 获取SSO登录URL，并指定返回地址
      const loginUrl = await SSOService.getLoginUrl(returnUri);
      
      // 重定向到SSO登录页面
      window.location.href = loginUrl;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'SSO登录失败');
      setIsLoading(false);
      throw err;
    }
  };

  const logout = async () => {
    try {
      // 调用SSO登出
      await SSOService.logout();
    } catch (error) {
      console.error('SSO登出失败:', error);
      // 即使SSO登出失败，也要清除本地用户信息
    } finally {
      // 确保本地状态被清除
      setUser(null);
      setIsAuthenticated(false);
      localStorage.removeItem('user');
      localStorage.removeItem('access_token');
      localStorage.removeItem('app_token');
      localStorage.removeItem('userInfo');
      localStorage.removeItem('sso_state');
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        isAuthenticated,
        isLoading,
        login,
        loginWithSSO,
        logout,
        error
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
