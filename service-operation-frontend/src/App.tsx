import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider, useAuth } from '@/store/auth-context';
import { 
  LoginPage, 
  AuthCallback, 
  AuthTestPage 
} from '@/pages/auth';
import { 
  DashboardPage, 
  SystemSettingsPage, 
  SimpleHealthPage 
} from '@/pages/system';
import { 
  ProjectManagementPage, 
  ProjectDetailPage, 
  ProjectTaskDetailPage 
} from '@/pages/project';
import { 
  PassportRecognitionPage, 
  TrainBookingPage, 
  TaskDetailPage, 
  TaskOrderDetailPage 
} from '@/pages/train_order';
import HotelBookingPage from '@/pages/hotel_order/HotelBookingPage';
import HotelTaskDetailPage from '@/pages/hotel_order/HotelTaskDetailPage';
import FlightBookingPage from '@/pages/flight_order/FlightBookingPage';
import FlightTaskDetailPage from '@/pages/flight_order/FlightTaskDetailPage';
import UserManagementPage from '@/pages/user-management/UserManagementPage';
import DataAnalysisPage from '@/pages/data-analysis/DataAnalysisPage';
import PermissionTestPage from '@/pages/PermissionTestPage';
import AuthDebugPage from '@/pages/AuthDebugPage';
import AuthFailureTestPage from '@/pages/AuthFailureTestPage';
import WelcomePage from '@/pages/WelcomePage';
import { Toaster } from '@/components/ui/toaster';
import { initGlobalAuthHandler } from '@/utils/global-auth-handler';
import ProtectedRoute from '@/components/common/ProtectedRoute';
import AuthGuard from '@/components/auth/AuthGuard';

// 简单的认证保护组件（不检查权限）
const AuthProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return <div className="flex items-center justify-center min-h-screen"></div>;
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  return <>{children}</>;
};

// 公共路由组件（已登录用户将被重定向到仪表盘）
const PublicRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth();
  
  if (isLoading) {
    return <div className="flex items-center justify-center min-h-screen"></div>;
  }
  
  if (isAuthenticated) {
    // 如果用户已认证，重定向到应用中心
    return <Navigate to="/dashboard" replace />;
  }
  
  return <>{children}</>;
};

const AppRoutes: React.FC = () => {
  return (
    <Routes>
      <Route path="/login" element={
        <PublicRoute>
          <LoginPage />
        </PublicRoute>
      } />
      {/* 欢迎页面 - 不需要特定权限，所有已认证用户都可以访问 */}
      <Route path="/welcome" element={
        <AuthProtectedRoute>
          <WelcomePage />
        </AuthProtectedRoute>
      } />
      <Route path="/dashboard" element={
        <ProtectedRoute requiredPermission="dttrip:dashboard">
          <DashboardPage />
        </ProtectedRoute>
      } />
      <Route path="/passport-recognition" element={
        <ProtectedRoute requiredPermission="dttrip:passport">
          <PassportRecognitionPage />
        </ProtectedRoute>
      } />
      <Route path="/projects" element={
        <ProtectedRoute requiredPermission="dttrip:projects">
          <ProjectManagementPage />
        </ProtectedRoute>
      } />
      {/* 项目详情页面 - 不在permissions表中，只需要认证 */}
      <Route path="/project-detail/:projectId" element={
        <AuthProtectedRoute>
          <ProjectDetailPage />
        </AuthProtectedRoute>
      } />
      <Route path="/project-task-detail/:projectId" element={
        <AuthProtectedRoute>
          <ProjectTaskDetailPage />
        </AuthProtectedRoute>
      } />
      <Route path="/task-detail/:taskId" element={
        <AuthProtectedRoute>
          <TaskDetailPage />
        </AuthProtectedRoute>
      } />
      <Route path="/task-orders/:taskId" element={
        <AuthProtectedRoute>
          <TaskOrderDetailPage />
        </AuthProtectedRoute>
      } />
      <Route path="/train-booking/:projectId" element={
        <AuthProtectedRoute>
          <TrainBookingPage />
        </AuthProtectedRoute>
      } />
      <Route path="/hotel-booking/:projectId" element={
        <AuthProtectedRoute>
          <HotelBookingPage />
        </AuthProtectedRoute>
      } />
      <Route path="/hotel-task-detail/:taskId" element={
        <AuthProtectedRoute>
          <HotelTaskDetailPage />
        </AuthProtectedRoute>
      } />
      <Route path="/flight-booking/:projectId" element={
        <AuthProtectedRoute>
          <FlightBookingPage />
        </AuthProtectedRoute>
      } />
      <Route path="/flight-task-detail/:taskId" element={
        <AuthProtectedRoute>
          <FlightTaskDetailPage />
        </AuthProtectedRoute>
      } />
      <Route path="/settings" element={
        <ProtectedRoute requiredPermission="dttrip:settings">
          <SystemSettingsPage />
        </ProtectedRoute>
      } />
      <Route path="/user-management" element={
        <ProtectedRoute requiredPermission="user_mgmt:access">
          <UserManagementPage />
        </ProtectedRoute>
      } />
      <Route path="/data-analysis" element={
        <ProtectedRoute requiredPermission="dttrip:data_analysis">
          <DataAnalysisPage />
        </ProtectedRoute>
      } />
      {/* 权限测试页面 - 需要认证但不需要特定权限 */}
      <Route path="/permission-test" element={
        <AuthProtectedRoute>
          <PermissionTestPage />
        </AuthProtectedRoute>
      } />
      {/* 认证调试页面 - 需要认证但不需要特定权限 */}
      <Route path="/auth-debug" element={
        <AuthProtectedRoute>
          <AuthDebugPage />
        </AuthProtectedRoute>
      } />
      {/* 认证失败测试页面 - 需要认证但不需要特定权限 */}
      <Route path="/auth-failure-test" element={
        <AuthProtectedRoute>
          <AuthFailureTestPage />
        </AuthProtectedRoute>
      } />
      {/* 健康检查页面 - 公开访问，无需登录 */}
      <Route path="/healthCheck" element={<SimpleHealthPage />} />
      {/* 自动认证功能测试页面 */}
      <Route path="/auth-test" element={<AuthTestPage />} />
      {/* 添加授权回调路由 */}
      <Route path="/auth/callback" element={<AuthCallback />} />
      <Route path="/" element={<Navigate to="/dashboard" replace />} />
      <Route path="*" element={<Navigate to="/dashboard" replace />} />
    </Routes>
  );
};

const App: React.FC = () => {
  // 初始化全局认证错误处理器
  useEffect(() => {
    console.log('🚀 [App] 初始化全局认证错误处理器...');
    initGlobalAuthHandler();
    console.log('✅ [App] 全局认证错误处理器初始化完成');
  }, []);

  return (
    <Router>
      <AuthProvider>
        <AuthGuard>
          <AppRoutes />
          <Toaster />
        </AuthGuard>
      </AuthProvider>
    </Router>
  );
};

export default App;
