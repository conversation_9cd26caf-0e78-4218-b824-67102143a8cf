import api from './request';

// 飞机票订单相关接口

export interface FlightOrder {
  id: number;
  project_id: number;
  sequence_number: number;
  
  // 出行人基础信息
  traveler_full_name: string;
  traveler_surname?: string;
  traveler_given_name?: string;
  nationality?: string;
  gender?: string;
  birth_date?: string;
  id_type?: string;
  id_number?: string;
  id_expiry_date?: string;
  mobile_country_code?: string;
  mobile_phone?: string;
  
  // 出行信息
  travel_date?: string;
  departure_airport?: string;
  arrival_airport?: string;
  flight_number?: string;
  departure_time?: string;
  arrival_time?: string;
  trip_submission_item?: string;
  
  // 联系人信息
  contact_person?: string;
  contact_mobile_phone?: string;
  contact_email?: string;
  approver?: string;
  
  // 对账单信息
  company_name?: string;
  booking_agent?: string;
  ticket_sms?: string;
  amount?: string;
  order_number?: string;
  bill_number?: string;

  // 保险信息
  insurance_name?: string;
  
  // 订单状态和元数据
  order_status: string;
  fail_reason?: string;
  created_at: string;
  updated_at: string;
  is_deleted: boolean;
}

export interface FlightOrderListResponse {
  total: number;
  page: number;
  page_size: number;
  items: FlightOrder[];
}

export interface ExcelUploadResponse {
  project_id: number;
  total_orders: number;
  failed_orders: number;
  skipped_duplicate_orders: number;
  file_path?: string;
  message: string;
}

export interface ValidationError {
  row: number;
  field: string;
  message: string;
  value?: string;
}

export interface ExcelValidationResponse {
  has_errors: boolean;
  errors: ValidationError[];
  total_rows: number;
  valid_rows: number;
  message: string;
}

export interface BookingRequest {
  booking_type: string;
  orders: number[];
  sms_notify: boolean;
  has_agent: boolean;
  agent_phone?: string;
  agent_name?: string;
}

export interface BookingResponse {
  processed_count: number;
  success_count: number;
  failed_count: number;
  message: string;
}

export interface CreateBookingTaskRequest {
  booking_type: 'book' | 'book_and_issue';
  task_title: string;
  task_description: string;
  sms_notify: boolean;
  has_agent: boolean;
  agent_phone?: string;
  agent_name?: string;
  order_ids?: number[];
}

export interface CreateBookingTaskResponse {
  task_id: string;
  processed_count: number;
  message: string;
}

export interface ClearOrdersResponse {
  deleted_count: number;
  message: string;
}

export interface ProjectOrderStatsResponse {
  total_orders: number;
  initial_orders: number;
  submitted_orders: number;
  processing_orders: number;
  completed_orders: number;
  failed_orders: number;
  check_failed_orders: number;
}

export interface ProjectOrderDetailStatsResponse extends ProjectOrderStatsResponse {
  total_amount: number;
}

export interface TaskOrderStatsResponse {
  task_id: string;
  total_orders: number;
  initial_orders: number;
  submitted_orders: number;
  processing_orders: number;
  completed_orders: number;
  failed_orders: number;
}

// 暂停已提交订单请求接口
export interface PauseSubmittedOrdersRequest {
  order_ids?: number[]; // 可选，如果为空则暂停所有已提交订单
}

// 暂停已提交订单响应接口
export interface PauseSubmittedOrdersResponse {
  paused_count: number;
  message: string;
}

// 重置已暂停订单请求接口
export interface ResetPausedOrdersRequest {
  order_ids: number[];
}

// 重置已暂停订单响应接口
export interface ResetPausedOrdersResponse {
  reset_count: number;
  message: string;
}

// 重置预定失败订单请求接口
export interface ResetFailedOrdersRequest {
  order_ids: number[];
}

// 重置预定失败订单响应接口
export interface ResetFailedOrdersResponse {
  reset_count: number;
  message: string;
}

// API 方法

// 获取项目的飞机票订单
export const getFlightOrdersByProject = async (
  projectId: number,
  params: {
    page?: number;
    page_size?: number;
    order_status?: string;
    traveler_name?: string;
    mobile_phone?: string;
    contact_phone?: string;
    sort_by_failed_first?: boolean;
  } = {}
): Promise<FlightOrderListResponse> => {
  const response = await api.get<FlightOrderListResponse>(`/flight-order/project/${projectId}`, {
    params: {
      ...params,
      sort_by_failed_first: params.sort_by_failed_first?.toString()
    }
  });
  return response.data!;
};

// 获取飞机票订单详情
export const getFlightOrder = async (orderId: number): Promise<FlightOrder> => {
  const response = await api.get<FlightOrder>(`/flight-order/${orderId}`);
  return response.data!;
};

// 创建飞机票订单
export const createFlightOrder = async (orderData: Partial<FlightOrder>): Promise<FlightOrder> => {
  const response = await api.post<FlightOrder>('/flight-order/', orderData);
  return response.data!;
};

// 更新飞机票订单
export const updateFlightOrder = async (orderId: number, orderData: Partial<FlightOrder>): Promise<FlightOrder> => {
  const response = await api.put<FlightOrder>(`/flight-order/${orderId}`, orderData);
  return response.data!;
};

// 更新飞机票订单对账单信息（绕过状态检查）
export const updateFlightOrderReconciliation = async (orderId: number, reconciliationData: {
  company_name?: string;
  booking_agent?: string;
  order_number?: string;
  bill_number?: string;
  amount?: string;
}): Promise<FlightOrder> => {
  const response = await api.put<FlightOrder>(`/flight-order/${orderId}/reconciliation`, reconciliationData);
  return response.data!;
};

// 删除飞机票订单
export const deleteFlightOrder = async (orderId: number): Promise<void> => {
  await api.delete(`/flight-order/${orderId}`);
};

// 验证Excel文件
export const validateFlightOrderExcel = async (
  projectId: number,
  file: File,
  smsNotify: boolean = false
): Promise<ExcelValidationResponse> => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('sms_notify', smsNotify.toString());

  const response = await api.post<ExcelValidationResponse>(`/flight-order/project/${projectId}/validate-excel`, formData, {
    isFormData: true,
  });
  return response.data!;
};

// 上传Excel文件
export const uploadFlightOrderExcel = async (
  projectId: number,
  file: File,
  smsNotify: boolean = false
): Promise<ExcelUploadResponse> => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('sms_notify', smsNotify.toString());

  const response = await api.post<ExcelUploadResponse>(`/flight-order/project/${projectId}/upload-excel`, formData, {
    isFormData: true,
  });
  return response.data!;
};

// 获取项目飞机票订单统计
export const getProjectFlightOrderStats = async (projectId: number): Promise<ProjectOrderStatsResponse> => {
  const response = await api.get<ProjectOrderStatsResponse>(`/flight-order/project/${projectId}/stats`);
  return response.data!;
};

// 获取项目飞机票订单详细统计
export const getProjectFlightOrderDetailStats = async (projectId: number): Promise<ProjectOrderDetailStatsResponse> => {
  const response = await api.get<ProjectOrderDetailStatsResponse>(`/flight-order/project/${projectId}/detail-stats`);
  return response.data!;
};

// 创建飞机票预订任务
export const createFlightBookingTask = async (
  projectId: number,
  taskData: CreateBookingTaskRequest
): Promise<CreateBookingTaskResponse> => {
  const response = await api.post<CreateBookingTaskResponse>(`/flight-order/project/${projectId}/create-booking-task`, taskData);
  return response.data!;
};

// 获取任务的飞机票订单
export const getFlightOrdersByTask = async (
  taskId: string,
  params: {
    page?: number;
    page_size?: number;
    status?: string;
    traveler_name?: string;
    mobile_phone?: string;
    contact_phone?: string;
  } = {}
): Promise<FlightOrderListResponse> => {
  const response = await api.get<FlightOrderListResponse>(`/flight-order/task/${taskId}`, { params });
  return response.data!;
};

// 获取任务飞机票订单统计
export const getTaskFlightOrderStats = async (taskId: string): Promise<TaskOrderStatsResponse> => {
  const response = await api.get<TaskOrderStatsResponse>(`/flight-order/task/${taskId}/stats`);
  return response.data!;
};

// 清空项目飞机票订单
export const clearFlightOrders = async (projectId: number): Promise<ClearOrdersResponse> => {
  const response = await api.post<ClearOrdersResponse>(`/flight-order/project/${projectId}/clear`);
  return response.data!;
};

// 清空项目中特定状态的飞机票订单
export const clearSpecificFlightOrders = async (projectId: number): Promise<ClearOrdersResponse> => {
  const response = await api.post<ClearOrdersResponse>(`/flight-order/project/${projectId}/clear-specific`);
  return response.data!;
};

// 暂停已提交的飞机票订单
export const pauseSubmittedFlightOrders = async (projectId: number, data: PauseSubmittedOrdersRequest): Promise<PauseSubmittedOrdersResponse> => {
  const response = await api.post<PauseSubmittedOrdersResponse>(`/flight-order/project/${projectId}/pause-submitted`, data);
  return response.data!;
};

// 重置已暂停的飞机票订单
export const resetPausedFlightOrders = async (projectId: number, data: ResetPausedOrdersRequest): Promise<ResetPausedOrdersResponse> => {
  const response = await api.post<ResetPausedOrdersResponse>(`/flight-order/project/${projectId}/reset-paused`, data);
  return response.data!;
};

// 重置预定失败的飞机票订单
export const resetFailedFlightOrders = async (projectId: number, data: ResetFailedOrdersRequest): Promise<ResetFailedOrdersResponse> => {
  const response = await api.post<ResetFailedOrdersResponse>(`/flight-order/project/${projectId}/reset-failed`, data);
  return response.data!;
};

// 下载飞机票模板
export const downloadFlightTemplate = (): void => {
  const link = document.createElement('a');
  link.href = '/templates/flight_template.xlsx';
  link.download = 'flight_template.xlsx';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

// 飞机票订单API对象
export const flightOrderApi = {
  // 获取项目统计
  getProjectStats: getProjectFlightOrderStats,

  // 获取项目订单列表
  getOrdersByProject: getFlightOrdersByProject,

  // Excel相关
  validateExcel: validateFlightOrderExcel,
  uploadExcel: uploadFlightOrderExcel,

  // 订单操作
  createOrder: createFlightOrder,
  updateOrder: updateFlightOrder,
  updateReconciliation: updateFlightOrderReconciliation,
  deleteOrder: deleteFlightOrder,
  getOrder: getFlightOrder,

  // 任务相关
  createBookingTask: createFlightBookingTask,

  // 清空订单
  clearOrders: clearFlightOrders,
  clearSpecificOrders: clearSpecificFlightOrders,

  // 暂停和重置订单状态
  pauseSubmittedOrders: pauseSubmittedFlightOrders,
  resetPausedOrders: resetPausedFlightOrders,
  resetFailedOrders: resetFailedFlightOrders,

  // 任务订单
  getOrdersByTask: getFlightOrdersByTask,
  getTaskStats: getTaskFlightOrderStats
};
