import api from './request';

// RPA错误日志类型定义
export interface RpaErrorLog {
  id: number;
  error_id: string;
  task_id: string;
  order_id: string;
  error_timestamp: string;
  module: string;
  function_name: string;
  error_type: string;
  error_message?: string;
  severity: string;
  page_url?: string;
  page_title: string;
  action_attempted?: string;
  user_agent: string;
  browser_info?: string;
  stack_trace?: string;
  additional_context?: string;
  created_at: string;
  updated_at: string;
}

export interface RpaErrorLogListResponse {
  items: RpaErrorLog[];
  total: number;
  page: number;
  page_size: number;
  total_pages: number;
}

// RPA错误日志API
export const rpaErrorLogApi = {
  // 根据订单ID获取RPA错误日志
  getByOrderId: (orderId: string, page = 1, pageSize = 10): Promise<RpaErrorLogListResponse> => {
    const params = new URLSearchParams({
      page: page.toString(),
      page_size: pageSize.toString(),
    });

    return api.get(`/rpa-error-log/by-order/${orderId}?${params.toString()}`);
  },

  // 根据任务ID获取RPA错误日志
  getByTaskId: (taskId: string, page = 1, pageSize = 10): Promise<RpaErrorLogListResponse> => {
    const params = new URLSearchParams({
      page: page.toString(),
      page_size: pageSize.toString(),
    });

    return api.get(`/rpa-error-log/by-task/${taskId}?${params.toString()}`);
  },

  // 获取RPA错误日志详情
  getById: (logId: number): Promise<RpaErrorLog> => {
    return api.get(`/rpa-error-log/${logId}`);
  },

  // 根据错误ID获取RPA错误日志详情
  getByErrorId: (errorId: string): Promise<RpaErrorLog> => {
    return api.get(`/rpa-error-log/by-error-id/${errorId}`);
  },
};
