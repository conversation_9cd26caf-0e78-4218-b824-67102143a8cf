import { API_BASE_URL } from '@/utils/constants';

// 请求配置接口
interface RequestConfig {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: any;
  params?: Record<string, string | number>; // 查询参数
  isFormData?: boolean; // 是否为FormData，影响Content-Type设置
  showErrorToast?: boolean; // 是否显示错误提示，默认true
  skipAuth?: boolean; // 是否跳过自动认证，默认false
  responseType?: 'json' | 'blob'; // 响应类型，默认为json
}

// 响应接口
export interface ApiResponse<T = any> {
  data: T;
  status: number;
  message?: string;
}

// 导入自动认证服务
import { autoAuthService, AuthError } from '@/services/auth';

// 获取认证头
const getAuthHeaders = async (isFormData: boolean = false, skipAuth: boolean = false): Promise<Record<string, string>> => {
  const headers: Record<string, string> = {};
  
  // 如果不跳过认证，获取有效token
  if (!skipAuth) {
    try {
      const token = await autoAuthService.getValidToken();
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }
    } catch (error) {
      console.warn('⚠️ 获取认证token失败:', error);
      // 如果是开发者密钥未配置的错误，尝试使用本地token
      if (error instanceof AuthError && error.message.includes('开发者密钥未配置')) {
        const localToken = localStorage.getItem('access_token');
        if (localToken) {
          headers['Authorization'] = `Bearer ${localToken}`;
        }
      }
    }
  }
  
  // FormData请求不能设置Content-Type，让浏览器自动设置
  if (!isFormData) {
    headers['Content-Type'] = 'application/json';
  }
  
  return headers;
};

// 处理请求体
const processRequestBody = (body: any, isFormData: boolean): string | FormData | undefined => {
  if (!body) return undefined;
  
  if (isFormData) {
    return body; // FormData直接返回
  }
  
  return JSON.stringify(body); // JSON数据需要序列化
};

// Toast 错误显示函数
const showError = (title: string, message: string) => {
  console.error(`${title}: ${message}`);
  
  // 尝试使用toast，如果不可用则fallback到console
  try {
    // 动态导入toast hook
    const toastEvent = new CustomEvent('showToast', {
      detail: {
        title,
        description: message,
        variant: 'destructive'
      }
    });
    window.dispatchEvent(toastEvent);
  } catch (error) {
    console.error('Toast显示失败，使用console输出:', error);
  }
};

// 处理认证失败的函数
const handleAuthenticationFailure = async () => {
  console.warn('🔒 [API Request] 认证失败，开始处理登出逻辑');

  // 使用autoAuthService进行安全登出
  try {
    await autoAuthService.logout();
    console.log('✅ [API Request] 安全登出完成');
  } catch (error) {
    console.warn('⚠️ [API Request] 登出过程中有警告:', error);
    // 即使登出失败，也要清理本地数据
    autoAuthService.clearToken();
  }

  // 清理其他可能的认证相关数据
  const authKeys = ['app_token', 'token', 'user', 'userInfo'];
  authKeys.forEach(key => {
    const oldValue = localStorage.getItem(key);
    localStorage.removeItem(key);
    if (oldValue) {
      console.log(`  - [API Request] 已清理 ${key}`);
    }
  });

  // 触发全局认证失败事件，通知AuthContext
  const authFailureEvent = new CustomEvent('authenticationFailure', {
    detail: {
      reason: 'token_expired_or_invalid',
      message: '认证已过期，请重新登录',
      source: 'api_request',
      forceRedirect: true
    }
  });

  console.log('📢 [API Request] 触发全局认证失败事件:', authFailureEvent.detail);
  window.dispatchEvent(authFailureEvent);

  // 强制跳转到登录页面（多种方法确保成功）
  const currentPath = window.location.pathname;
  console.log(`🔍 [API Request] 当前路径: ${currentPath}`);

  if (currentPath !== '/login') {
    console.log('🔄 [API Request] 开始强制跳转到登录页面...');

    // 方法1: 使用 window.location.replace (不会在历史记录中留下记录)
    try {
      console.log('🔄 尝试方法1: window.location.replace');
      window.location.replace('/login');
      console.log('✅ 方法1执行完成');
    } catch (error) {
      console.error('❌ 方法1失败:', error);

      // 方法2: 使用 window.location.href
      try {
        console.log('🔄 尝试方法2: window.location.href');
        window.location.href = '/login';
        console.log('✅ 方法2执行完成');
      } catch (error2) {
        console.error('❌ 方法2失败:', error2);

        // 方法3: 强制刷新到登录页
        try {
          console.log('🔄 尝试方法3: 强制刷新');
          window.location = '/login' as any;
          console.log('✅ 方法3执行完成');
        } catch (error3) {
          console.error('❌ 所有跳转方法都失败:', error3);
        }
      }
    }
  } else {
    console.log('ℹ️ [API Request] 已经在登录页面，无需跳转');
  }
};

// 统一请求函数
export const request = async <T = any>(
  url: string,
  config: RequestConfig = {},
  retryCount: number = 0  // 添加重试计数参数
): Promise<ApiResponse<T>> => {
  const {
    method = 'GET',
    headers = {},
    body,
    params,
    isFormData = false,
    showErrorToast = true,
    skipAuth = false,
    responseType = 'json'
  } = config;

  const MAX_AUTH_RETRIES = 1; // 最大认证重试次数

  // 检查是否为公开接口（不需要认证）
  const isPublicEndpoint = url.includes('/auth/token') || url.includes('/auth/sso/');
  const shouldSkipAuth = skipAuth || isPublicEndpoint;

  try {
    // 获取认证头
    const authHeaders = await getAuthHeaders(isFormData, shouldSkipAuth);

    // 构建完整URL，包含查询参数
    let fullUrl = `${API_BASE_URL}${url}`;
    if (params) {
      const searchParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        searchParams.append(key, String(value));
      });
      fullUrl += `?${searchParams.toString()}`;
    }

    // 构建请求配置
    const requestConfig: RequestInit = {
      method,
      headers: {
        ...authHeaders,
        ...headers,
      },
      body: processRequestBody(body, isFormData),
    };

    console.log(`🌐 API请求: ${method} ${fullUrl}`);

    // 发送请求
    const response = await fetch(fullUrl, requestConfig);
    
    // 处理HTTP错误状态
    if (!response.ok) {
      let errorMessage = `请求失败 (${response.status})`;
      let errorDetail = '';
      
      try {
        const errorData = await response.json();

        // 安全地处理错误消息
        if (typeof errorData.detail === 'string') {
          errorMessage = errorData.detail;
          errorDetail = errorData.detail;
        } else if (Array.isArray(errorData.detail)) {
          // 处理FastAPI验证错误格式
          errorMessage = errorData.detail.map((err: any) =>
            `${err.loc?.join('.')}: ${err.msg}`
          ).join('; ');
          errorDetail = errorMessage;
        } else if (typeof errorData.message === 'string') {
          errorMessage = errorData.message;
          errorDetail = errorData.message;
        }
      } catch {
        // 如果响应不是JSON格式，使用默认错误消息
        errorMessage = `HTTP ${response.status}: ${response.statusText}`;
      }

      const error = {
        message: errorMessage,
        status: response.status,
        detail: errorDetail
      };

      // 特殊处理401认证错误
      if (response.status === 401 && !shouldSkipAuth) {
        console.warn('🔒 检测到401认证错误，开始处理认证失败...');

        // 清除可能已过期的token
        autoAuthService.clearToken();

        // 检查是否已经重试过，避免无限循环
        if (retryCount < MAX_AUTH_RETRIES && autoAuthService.isDeveloperKeyConfigured()) {
          console.log(`🔄 401错误，尝试重新获取token... (重试 ${retryCount + 1}/${MAX_AUTH_RETRIES})`);
          try {
            await autoAuthService.getValidToken();
            // 重新发起请求，增加重试计数
            return await request(url, config, retryCount + 1);
          } catch (authError) {
            console.error('❌ 重新认证失败:', authError);
            error.message = '认证失败，请检查开发者密钥配置';

            // 重新认证失败，也要跳转到登录页面
            await handleAuthenticationFailure();
          }
        } else {
          if (retryCount >= MAX_AUTH_RETRIES) {
            console.warn(`⚠️ 已达到最大重试次数 (${MAX_AUTH_RETRIES})，停止重试`);
            error.message = '认证失败，已达到最大重试次数';
          } else {
            error.message = '认证失败，请重新登录';
          }
          // 没有开发者密钥或已达到重试上限，直接跳转到登录页面
          await handleAuthenticationFailure();
        }
        
        if (showErrorToast) {
          showError("认证错误", error.message);
        }
      } else if (showErrorToast) {
        // 统一处理所有HTTP错误，包括400错误（Excel格式错误）
        if (response.status === 400) {
          showError("操作失败", error.message);
        } else {
          showError("请求失败", error.message);
        }
      }

      throw error;
    }

    // 处理成功响应
    let data;
    if (responseType === 'blob') {
      data = await response.blob();
    } else {
      data = await response.json();
    }
    
    console.log('✅ API请求成功');
    
    return {
      data,
      status: response.status,
      message: 'success'
    };

  } catch (error) {
    // 检查是否是已经处理过的HTTP错误
    if (error && typeof error === 'object' && 'status' in error && 'message' in error) {
      // 这是已经处理过的HTTP错误，直接重新抛出
      throw error;
    }
    
    // 网络错误或其他异常
    if (error instanceof Error && error.name !== 'AuthError') {
      const networkError = {
        message: '网络错误，请检查网络连接后重试',
        status: 0
      };
      
      if (showErrorToast) {
        showError("网络错误", networkError.message);
      }
      throw networkError;
    }
    
    // 重新抛出已处理的错误
    throw error;
  }
};

// 便捷方法
export const api = {
  get: <T = any>(url: string, config?: Omit<RequestConfig, 'method'>) => 
    request<T>(url, { ...config, method: 'GET' }),
    
  post: <T = any>(url: string, body?: any, config?: Omit<RequestConfig, 'method' | 'body'>) => 
    request<T>(url, { ...config, method: 'POST', body }),
    
  put: <T = any>(url: string, body?: any, config?: Omit<RequestConfig, 'method' | 'body'>) => 
    request<T>(url, { ...config, method: 'PUT', body }),
    
  delete: <T = any>(url: string, config?: Omit<RequestConfig, 'method'>) => 
    request<T>(url, { ...config, method: 'DELETE' }),
    
  // 文件上传专用方法
  upload: <T = any>(url: string, formData: FormData, config?: Omit<RequestConfig, 'method' | 'body' | 'isFormData'>) => 
    request<T>(url, { ...config, method: 'POST', body: formData, isFormData: true }),
};

export default api; 