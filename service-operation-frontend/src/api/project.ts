import api from './request';

// 项目相关类型定义
export interface Project {
  id: number;
  project_number: number;
  project_name: string;
  project_description?: string;
  client_name: string;
  cost_center?: string;
  booking_agent_phone?: string;
  project_date: string;
  creator_user_id: number;
  creator_name: string;
  created_at: string;
  updated_at: string;
}

export interface CreateProjectRequest {
  project_name: string;
  project_description?: string;
  client_name: string;
  booking_agent_phone?: string;
  project_date: string;
}

export interface UpdateProjectRequest extends CreateProjectRequest {
  // 继承创建接口的所有字段
}

export interface ProjectListResponse {
  total: number;
  items: Project[];
}

// 项目API接口
export const projectApi = {
  // 获取项目列表
  getProjects: (page: number = 1, pageSize: number = 10) => 
    api.get<ProjectListResponse>(`/project/?page=${page}&page_size=${pageSize}`),

  // 获取单个项目详情
  getProject: (projectId: string | number) => 
    api.get<Project>(`/project/${projectId}`),

  // 创建新项目
  createProject: (data: CreateProjectRequest) => 
    api.post<Project>('/project/', data),

  // 更新项目
  updateProject: (projectId: string | number, data: UpdateProjectRequest) => 
    api.put<Project>(`/project/${projectId}`, data),

  // 删除项目
  deleteProject: (projectId: string | number) => 
    api.delete(`/project/${projectId}`),
};

export default projectApi; 