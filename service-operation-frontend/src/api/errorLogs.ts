import api from './request';

// 错误日志接口定义
export interface ErrorLog {
  id: number;
  error_id: string;
  task_id: string;
  order_id: string;
  error_timestamp: string;
  module: string;
  function_name: string;
  error_type: string;
  error_message?: string;
  severity: string;
  page_url?: string;
  page_title: string;
  action_attempted?: string;
  user_agent: string;
  browser_info?: string;
  stack_trace?: string;
  additional_context?: string;
  created_at: string;
  updated_at: string;
  // 关联信息
  project_id?: number;
  project_name?: string;
  creator_name?: string;
  task_type?: string;
}

export interface ErrorLogListResponse {
  items: ErrorLog[];
  total: number;
  page: number;
  page_size: number;
  total_pages: number;
}

export interface ProjectOption {
  project_id: number;
  project_name: string;
}

export interface CreatorOption {
  creator_name: string;
}

export interface TaskTypeOption {
  task_type: string;
}

export interface ErrorLogOptionsResponse {
  projects: ProjectOption[];
  creators: CreatorOption[];
  task_types: TaskTypeOption[];
}

export interface ErrorLogQueryParams {
  page?: number;
  page_size?: number;
  creator_name?: string;
  project_name?: string;
  task_type?: string;
  order_id?: string;
}

// 错误日志API
export const errorLogsApi = {
  // 获取错误日志列表
  getErrorLogs: (params: ErrorLogQueryParams = {}): Promise<ErrorLogListResponse> => {
    return api.get('/rpa-error-log/analysis', { params });
  },

  // 获取筛选选项
  getOptions: (): Promise<ErrorLogOptionsResponse> => {
    return api.get('/rpa-error-log/analysis/options');
  }
};
