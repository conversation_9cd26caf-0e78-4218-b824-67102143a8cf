import { api } from './request';

// 酒店订单相关的类型定义
export interface HotelOrder {
  id: number;
  project_id: number;
  sequence_number: number;
  
  // 入住人基础信息
  guest_full_name: string;
  guest_surname?: string;
  guest_given_name?: string;
  guest_nationality?: string;
  guest_gender?: string;
  guest_birth_date?: string;
  guest_id_type?: string;
  guest_id_number?: string;
  guest_id_expiry_date?: string;
  guest_mobile_country_code?: string;
  guest_mobile_phone?: string;
  guest_email?: string;
  
  // 酒店预订信息
  destination?: string;
  hotel_id?: string;
  room_type?: string;
  room_count?: number;
  policy_name?: string;
  include_breakfast?: string;
  is_half_day_room?: string;
  
  check_in_time?: string;
  check_out_time?: string;
  is_group_booking?: string;
  group_booking_name?: string;
  room_number?: string;
  
  // 支付和财务信息
  payment_method?: string;
  invoice_type?: string;
  tax_rate?: number;
  agreement_type?: string;
  supplier_name?: string;
  payment_channel?: string;
  payment_transaction_id?: string;
  cost_per_room?: string;
  hidden_service_fee?: string;
  cancellation_policy?: string;
  is_violation?: string;
  
  // 联系人和管理信息
  contact_person?: string;
  contact_mobile_country_code?: string;
  contact_mobile_phone?: string;
  order_remarks?: string;
  cost_center?: string;
  trip_submission_item?: string;
  approver?: string;
  
  // 同住人信息
  roommate_name?: string;
  roommate_surname?: string;
  roommate_given_name?: string;
  roommate_nationality?: string;
  roommate_gender?: string;
  roommate_birth_date?: string;
  roommate_id_type?: string;
  roommate_id_number?: string;
  roommate_id_expiry_date?: string;
  roommate_mobile_country_code?: string;
  roommate_mobile_phone?: string;
  roommate_email?: string;
  
  // 对账单信息
  company_name?: string;
  hotel_name?: string;
  amount?: number | null;
  booking_agent?: string;
  order_number?: string;
  bill_number?: string;
  
  // 订单状态和元数据
  order_status: string;
  fail_reason?: string;
  order_type?: string;
  created_at: string;
  updated_at: string;
  is_deleted: boolean;
}

export interface HotelOrderCreate {
  project_id: number;
  guest_full_name: string;
  guest_surname?: string;
  guest_given_name?: string;
  guest_nationality?: string;
  guest_gender?: string;
  guest_birth_date?: string;
  guest_id_type?: string;
  guest_id_number?: string;
  guest_id_expiry_date?: string;
  guest_mobile_country_code?: string;
  guest_mobile_phone?: string;
  guest_email?: string;
  destination?: string;
  hotel_id?: string;
  room_type?: string;
  room_count?: number;
  policy_name?: string;
  include_breakfast?: string;
  is_half_day_room?: string;
  check_in_time?: string;
  check_out_time?: string;
  is_group_booking?: string;
  group_booking_name?: string;
  room_number?: string;
  payment_method?: string;
  invoice_type?: string;
  tax_rate?: number;
  agreement_type?: string;
  supplier_name?: string;
  payment_channel?: string;
  payment_transaction_id?: string;
  cost_per_room?: string;
  hidden_service_fee?: string;
  cancellation_policy?: string;
  is_violation?: string;
  contact_person?: string;
  contact_mobile_country_code?: string;
  contact_mobile_phone?: string;
  order_remarks?: string;
  cost_center?: string;
  trip_submission_item?: string;
  approver?: string;
  roommate_name?: string;
  roommate_surname?: string;
  roommate_given_name?: string;
  roommate_nationality?: string;
  roommate_gender?: string;
  roommate_birth_date?: string;
  roommate_id_type?: string;
  roommate_id_number?: string;
  roommate_id_expiry_date?: string;
  roommate_mobile_country_code?: string;
  roommate_mobile_phone?: string;
  roommate_email?: string;
  company_name?: string;
  hotel_name?: string;
  amount?: number;
  booking_agent?: string;
  order_number?: string;
  bill_number?: string;
}

export interface HotelOrderUpdate {
  guest_full_name?: string;
  guest_surname?: string;
  guest_given_name?: string;
  guest_nationality?: string;
  guest_gender?: string;
  guest_birth_date?: string;
  guest_id_type?: string;
  guest_id_number?: string;
  guest_id_expiry_date?: string;
  guest_mobile_country_code?: string;
  guest_mobile_phone?: string;
  guest_email?: string;
  destination?: string;
  hotel_id?: string;
  room_type?: string;
  room_count?: number;
  policy_name?: string;
  include_breakfast?: string;
  is_half_day_room?: string;
  check_in_time?: string;
  check_out_time?: string;
  is_group_booking?: string;
  group_booking_name?: string;
  room_number?: string;
  payment_method?: string;
  invoice_type?: string;
  tax_rate?: number;
  agreement_type?: string;
  supplier_name?: string;
  payment_channel?: string;
  payment_transaction_id?: string;
  cost_per_room?: string;
  hidden_service_fee?: string;
  cancellation_policy?: string;
  is_violation?: string;
  contact_person?: string;
  contact_mobile_country_code?: string;
  contact_mobile_phone?: string;
  order_remarks?: string;
  cost_center?: string;
  trip_submission_item?: string;
  approver?: string;
  roommate_name?: string;
  roommate_surname?: string;
  roommate_given_name?: string;
  roommate_nationality?: string;
  roommate_gender?: string;
  roommate_birth_date?: string;
  roommate_id_type?: string;
  roommate_id_number?: string;
  roommate_id_expiry_date?: string;
  roommate_mobile_country_code?: string;
  roommate_mobile_phone?: string;
  roommate_email?: string;
  company_name?: string;
  hotel_name?: string;
  amount?: number;
  booking_agent?: string;
  order_number?: string;
  bill_number?: string;
}

export interface HotelOrderListResponse {
  total: number;
  page: number;
  page_size: number;
  items: HotelOrder[];
}

export interface ValidationError {
  row: number;
  field: string;
  error_type: string;
  message: string;
  value?: string;
}

export interface ExcelValidationResponse {
  total_rows: number;
  valid_rows: number;
  error_rows: number;
  errors: ValidationError[];
  should_continue: boolean;
  duplicate_rows: number[];
}

export interface ExcelUploadResponse {
  uploaded_count: number;
  skipped_count: number;
  total_rows: number;
  message: string;
}

export interface BookingRequest {
  booking_type: 'book' | 'book_and_issue';
  orders: number[];
  sms_notify: boolean;
  has_agent: boolean;
  agent_phone?: string;
  order_ids?: number[];
}

export interface BookingResponse {
  task_id: string;
  booking_count: number;
  message: string;
}

export interface ClearOrdersResponse {
  message: string;
  deleted_count: number;
}

export interface CreateBookingTaskRequest {
  booking_type: 'book_only' | 'book_and_ticket';
  task_title: string;
  task_description?: string;
  sms_notify: boolean;
  has_agent: boolean;
  agent_phone?: string;
  agent_name: string;
  order_ids?: number[];
}

export interface CreateBookingTaskResponse {
  task_id: string;
  booking_count: number;
  message: string;
}

export interface ProjectOrderStatsResponse {
  total_orders: number;
  completed_orders: number;
  completed_amount: string;
}

export interface ProjectOrderDetailStatsResponse {
  total_orders: number;
  check_failed_orders: number;
  initial_orders: number;
  submitted_orders: number;
  processing_orders: number;
  completed_orders: number;
  failed_orders: number;
  total_amount: string;
}

export interface TaskOrderStatsResponse {
  total_orders: number;
  completed_orders: number;
  failed_orders: number;
  completed_amount: string;
}

// 酒店订单API函数
export const hotelOrderApi = {
  // 获取项目的酒店订单列表
  getProjectOrders: async (
    projectId: number,
    page: number = 1,
    pageSize: number = 20,
    orderStatus?: string,
    guestName?: string,
    mobilePhone?: string,
    contactPhone?: string,
    sortByFailedFirst?: boolean
  ): Promise<HotelOrderListResponse> => {
    const params = new URLSearchParams({
      page: page.toString(),
      page_size: pageSize.toString(),
    });

    if (orderStatus) params.append('order_status', orderStatus);
    if (guestName) params.append('guest_name', guestName);
    if (mobilePhone) params.append('mobile_phone', mobilePhone);
    if (contactPhone) params.append('contact_phone', contactPhone);
    if (sortByFailedFirst) params.append('sort_by_failed_first', 'true');

    const response = await api.get(`/hotel-order/project/${projectId}?${params}`);
    return response.data;
  },

  // 获取任务的酒店订单列表
  getTaskOrders: async (
    taskId: string,
    page: number = 1,
    pageSize: number = 20,
    status?: string,
    guestName?: string,
    mobilePhone?: string,
    contactPhone?: string
  ): Promise<HotelOrderListResponse> => {
    const params = new URLSearchParams({
      page: page.toString(),
      page_size: pageSize.toString(),
    });

    if (status) params.append('status', status);
    if (guestName) params.append('guest_name', guestName);
    if (mobilePhone) params.append('mobile_phone', mobilePhone);
    if (contactPhone) params.append('contact_phone', contactPhone);

    const response = await api.get(`/hotel-order/task/${taskId}?${params}`);
    return response.data;
  },

  // 获取单个酒店订单详情
  getOrder: async (orderId: number): Promise<HotelOrder> => {
    const response = await api.get(`/hotel-order/${orderId}`);
    return response.data;
  },

  // 创建酒店订单
  createOrder: async (orderData: HotelOrderCreate): Promise<HotelOrder> => {
    const response = await api.post('/hotel-order/', orderData);
    return response.data;
  },

  // 更新酒店订单
  updateOrder: async (orderId: number, orderData: HotelOrderUpdate): Promise<HotelOrder> => {
    const response = await api.put(`/hotel-order/${orderId}`, orderData);
    return response.data;
  },

  // 删除酒店订单
  deleteOrder: async (orderId: number): Promise<void> => {
    await api.delete(`/hotel-order/${orderId}`);
  },

  // Excel上传
  uploadExcel: async (projectId: number, file: File): Promise<ExcelUploadResponse> => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('project_id', projectId.toString());
    const response = await api.post(`/hotel-order/upload-excel`, formData, {
      isFormData: true,
    });
    return response.data;
  },

  // Excel验证
  validateExcel: async (projectId: number, file: File): Promise<ExcelValidationResponse> => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('project_id', projectId.toString());
    const response = await api.post('/hotel-order/validate-excel', formData, {
      isFormData: true,
    });
    return response.data;
  },

  // 预订酒店订单
  bookOrders: async (projectId: number, bookingData: BookingRequest): Promise<BookingResponse> => {
    const response = await api.post(`/hotel-order/project/${projectId}/book`, bookingData);
    return response.data;
  },

  // 创建预订任务
  createBookingTask: async (projectId: number, taskData: CreateBookingTaskRequest): Promise<CreateBookingTaskResponse> => {
    const response = await api.post(`/hotel-order/project/${projectId}/create-booking-task`, taskData);
    return response.data;
  },

  // 清空订单
  clearOrders: async (projectId: number): Promise<ClearOrdersResponse> => {
    const response = await api.delete(`/hotel-order/project/${projectId}/clear`);
    return response.data;
  },

  // 获取项目订单统计
  getProjectStats: async (projectId: number): Promise<ProjectOrderStatsResponse> => {
    const response = await api.get(`/hotel-order/project/${projectId}/stats`);
    return response.data;
  },

  // 获取项目订单详细统计
  getProjectDetailStats: async (projectId: number): Promise<ProjectOrderDetailStatsResponse> => {
    const response = await api.get(`/hotel-order/project/${projectId}/detail-stats`);
    return response.data;
  },

  // 获取任务订单统计
  getTaskStats: async (taskId: string): Promise<TaskOrderStatsResponse> => {
    const response = await api.get(`/hotel-order/task/${taskId}/stats`);
    return response.data;
  },

  // 导出项目订单数据
  exportProjectOrders: async (
    projectId: number,
    params: {
      orderStatus?: string;
      guestName?: string;
      mobilePhone?: string;
      contactPhone?: string;
      exportFormat?: 'xlsx' | 'csv';
      selectedFields?: string[];
      maxRows?: number;
    } = {}
  ): Promise<Blob> => {
    const searchParams = new URLSearchParams();
    
    if (params.orderStatus) searchParams.append('order_status', params.orderStatus);
    if (params.guestName) searchParams.append('guest_name', params.guestName);
    if (params.mobilePhone) searchParams.append('mobile_phone', params.mobilePhone);
    if (params.contactPhone) searchParams.append('contact_phone', params.contactPhone);
    if (params.exportFormat) searchParams.append('export_format', params.exportFormat);
    if (params.selectedFields && params.selectedFields.length > 0) {
      searchParams.append('selected_fields', params.selectedFields.join(','));
    }
    if (params.maxRows) searchParams.append('max_rows', params.maxRows.toString());

    const response = await api.get(`/hotel-order/project/${projectId}/export?${searchParams}`, {
      responseType: 'blob'
    });
    
    return response.data;
  },

  // 格式转换
  convertFormat: async (file: File): Promise<Blob> => {
    const formData = new FormData();
    formData.append('file', file);

    const response = await api.post('/hotel-order/convert-format', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      responseType: 'blob'
    });

    return response.data;
  },

  // 暂停已提交的订单
  pauseSubmittedOrders: async (projectId: number, data: PauseOrdersRequest): Promise<PauseOrdersResponse> => {
    const response = await api.post(`/hotel-order/project/${projectId}/pause-submitted`, data);
    return response.data;
  },

  // 重置已暂停的订单
  resetPausedOrders: async (projectId: number, data: ResetPausedOrdersRequest): Promise<ResetPausedOrdersResponse> => {
    const response = await api.post(`/hotel-order/project/${projectId}/reset-paused`, data);
    return response.data;
  },

  // 重置预定失败的订单
  resetFailedOrders: async (projectId: number, data: ResetFailedOrdersRequest): Promise<ResetFailedOrdersResponse> => {
    const response = await api.post(`/hotel-order/project/${projectId}/reset-failed`, data);
    return response.data;
  },
};

// 暂停订单请求接口
export interface PauseOrdersRequest {
  order_ids?: number[];
}

// 暂停订单响应接口
export interface PauseOrdersResponse {
  paused_count: number;
  message: string;
}

// 重置已暂停订单请求接口
export interface ResetPausedOrdersRequest {
  order_ids?: number[];
}

// 重置已暂停订单响应接口
export interface ResetPausedOrdersResponse {
  reset_count: number;
  message: string;
}

// 重置预定失败订单请求接口
export interface ResetFailedOrdersRequest {
  order_ids?: number[];
}

// 重置预定失败订单响应接口
export interface ResetFailedOrdersResponse {
  reset_count: number;
  message: string;
}