/**
 * 数据分析API接口
 */

import { api } from './request';

// 数据分析相关类型定义
export interface DateRangeRequest {
  start_date: string;
  end_date: string;
}

export interface UserAnalysisRequest extends DateRangeRequest {
  user_id?: number;
  user_name?: string;
}

export interface PassportAnalysisResponse {
  total_recognitions: number;
  unique_users: number;
  success_rate: number;
  daily_stats: Array<{
    date: string;
    count: number;
  }>;
  user_stats: Array<{
    user_id: number;
    username: string;
    department?: string;
    count: number;
  }>;
}

export interface BookingAnalysisResponse {
  total_orders: number;
  completed_orders: number;
  failed_orders: number;
  success_rate: number;
  total_amount: number;
  daily_stats: Array<{
    date: string;
    total_orders: number;
    completed_orders: number;
    success_rate: number;
  }>;
  user_stats: Array<{
    user_id: number;
    username: string;
    total_orders: number;
    completed_orders: number;
    success_rate: number;
  }>;
  project_stats: Array<{
    project_id: number;
    project_name: string;
    total_orders: number;
    completed_orders: number;
    success_rate: number;
  }>;
}

export interface OverallAnalysisResponse {
  passport_analysis: PassportAnalysisResponse;
  train_analysis: BookingAnalysisResponse;
  hotel_analysis: BookingAnalysisResponse;
  flight_analysis: BookingAnalysisResponse;
  summary: {
    total_passport_recognitions: number;
    total_train_orders: number;
    total_hotel_orders: number;
    total_flight_orders: number;
    total_orders: number;
    total_amount: number;
    overall_success_rate: number;
  };
}

// API方法
export const dataAnalysisApi = {
  // 护照识别分析
  analyzePassport: async (params: UserAnalysisRequest): Promise<PassportAnalysisResponse> => {
    const response = await api.get<PassportAnalysisResponse>('/data-analysis/passport', { params });
    return response.data!;
  },

  // 火车票预订分析
  analyzeTrainBooking: async (params: UserAnalysisRequest): Promise<BookingAnalysisResponse> => {
    const response = await api.get<BookingAnalysisResponse>('/data-analysis/train-booking', { params });
    return response.data!;
  },

  // 酒店预订分析
  analyzeHotelBooking: async (params: UserAnalysisRequest): Promise<BookingAnalysisResponse> => {
    const response = await api.get<BookingAnalysisResponse>('/data-analysis/hotel-booking', { params });
    return response.data!;
  },

  // 飞机票预订分析
  analyzeFlightBooking: async (params: UserAnalysisRequest): Promise<BookingAnalysisResponse> => {
    const response = await api.get<BookingAnalysisResponse>('/data-analysis/flight-booking', { params });
    return response.data!;
  },

  // 综合分析
  analyzeOverall: async (params: UserAnalysisRequest): Promise<OverallAnalysisResponse> => {
    const response = await api.get<OverallAnalysisResponse>('/data-analysis/overall', { params });
    return response.data!;
  }
};
