import api from './request';

// 护照识别相关类型定义
export interface PassportRecord {
  id: number;
  task_id: string;
  uploaded_image_url: string;
  // 按指定顺序的字段
  certificate_type: string;
  certificate_number: string;
  surname: string;
  given_names: string;
  sex: string;
  date_of_birth: string;
  nationality: string;
  passenger_type: string;
  country_of_issue: string;
  date_of_issue: string;
  date_of_expiry: string;
  ssr_code: string;
  mrz_line1: string;
  mrz_line2: string;
  viz_mrz_consistency: string;
  // 保留的原有字段
  passport_number: string; // 兼容性保留
  place_of_birth: string;
  authority: string;
  processing_status: string;
  created_at: string;
  updated_at: string;
}

export interface TaskSummary {
  task_id: string;
  total_files: number;
  completed_files: number;
  processing_files: number;
  failed_files: number;
  pending_files: number;
  created_at: string;
  updated_at: string;
  latest_upload_url: string;
}

export interface PassportListResponse {
  total: number;
  items: PassportRecord[];
  page: number;
  size: number;
}

export interface TaskListResponse {
  total: number;
  items: TaskSummary[];
  page: number;
  size: number;
}

export interface UploadResponse {
  task_id: string;
  uploaded_files: number;
  message: string;
}

export interface TaskStatsResponse {
  task_id: string;
  total_count: number;
  pending_count: number;
  processing_count: number;
  completed_count: number;
  failed_count: number;
}

// 护照识别API接口
export const passportApi = {
  // 上传护照图片文件
  uploadPassports: (files: FileList) => {
    const formData = new FormData();
    Array.from(files).forEach(file => {
      formData.append('files', file);
    });
    
    return api.upload<UploadResponse>('/passport/upload', formData);
  },

  // 获取任务的护照记录
  getTaskPassports: (taskId: string, page: number = 1, size: number = 100) => 
    api.get<PassportListResponse>(`/passport/task/${taskId}?page=${page}&size=${size}`),

  // 获取历史任务列表
  getHistoryTasks: (page: number = 1, size: number = 10) => 
    api.get<TaskListResponse>(`/passport/tasks?page=${page}&size=${size}`),

  // 停止任务
  stopTask: (taskId: string) => 
    api.post(`/passport/task/${taskId}/stop`),

  // 删除任务
  deleteTask: (taskId: string) => 
    api.delete(`/passport/task/${taskId}`),

  // 重启任务
  restartTask: (taskId: string) => 
    api.post(`/passport/task/${taskId}/restart`),

  // 获取任务统计
  getTaskStats: (taskId: string) =>
    api.get<TaskStatsResponse>(`/passport/task/${taskId}/stats`),

  // 获取所有护照记录（支持搜索和分页）
  getAllPassports: (page: number = 1, size: number = 20, name?: string, certNumber?: string) => {
    const params = new URLSearchParams({
      page: page.toString(),
      size: size.toString()
    });

    if (name?.trim()) {
      params.append('name', name.trim());
    }

    if (certNumber?.trim()) {
      params.append('cert_number', certNumber.trim());
    }

    return api.get<PassportListResponse>(`/passport/list?${params.toString()}`);
  },

  // 删除单个护照记录
  deletePassport: (passportId: number) =>
    api.delete(`/passport/${passportId}`),

  // 删除所有护照记录
  deleteAllPassports: () =>
    api.delete('/passport/clear-all'),
};

export default passportApi; 