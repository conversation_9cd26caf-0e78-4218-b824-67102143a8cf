import { useState, useEffect } from 'react';
import { useAuth } from '@/store/auth-context';
import { userManagementApi } from '@/services/userManagementApi';

export interface Permission {
  id: number;
  permission_name: string;
  permission_code: string;
  permission_type: string;
  resource_path: string;
  description: string;
  sort_order: number;
}

export interface UserPermissions {
  permissions: Permission[];
  loading: boolean;
  error: string | null;
}

/**
 * 用户权限管理Hook
 * 获取当前用户的有效权限列表
 */
export const usePermissions = (): UserPermissions => {
  const { user, isAuthenticated } = useAuth();
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchUserPermissions = async () => {
      if (!isAuthenticated || !user?.id) {
        setPermissions([]);
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // 获取用户权限详情 - 使用数据库ID
        console.log('usePermissions - 当前用户ID:', user.id);

        const userPermissionDetails = await userManagementApi.getUserPermissionDetails(user.id);

        // 提取有效权限列表 - 使用正确的字段名
        const effectivePermissions = userPermissionDetails.effective_permissions || [];

        // 只保留菜单类型的权限
        const menuPermissions = effectivePermissions.filter(
          (permission: Permission) => permission.permission_type === 'menu'
        );

        setPermissions(menuPermissions);
      } catch (err) {
        console.error('获取用户权限失败:', err);

        // 检查是否是认证错误，如果是则不要重试，避免循环
        if (err && typeof err === 'object' && 'status' in err && err.status === 401) {
          console.warn('权限获取失败：认证错误，停止重试以避免循环');
          setError('认证已过期，请重新登录');
          setPermissions([]);
          // 不要在这里触发认证失败处理，让其他组件处理
          return;
        }

        setError(err instanceof Error ? err.message : '获取权限失败');
        setPermissions([]);
      } finally {
        setLoading(false);
      }
    };

    fetchUserPermissions();
  }, [user?.id, isAuthenticated]);

  return {
    permissions,
    loading,
    error
  };
};

/**
 * 检查用户是否有特定权限
 * @param permissionCode 权限代码
 * @param permissions 用户权限列表
 * @returns 是否有权限
 */
export const hasPermission = (
  permissionCode: string, 
  permissions: Permission[]
): boolean => {
  return permissions.some(permission => permission.permission_code === permissionCode);
};

/**
 * 检查用户是否有访问特定路径的权限
 * @param resourcePath 资源路径
 * @param permissions 用户权限列表
 * @returns 是否有权限
 */
export const hasPathPermission = (
  resourcePath: string, 
  permissions: Permission[]
): boolean => {
  return permissions.some(permission => permission.resource_path === resourcePath);
};

/**
 * 获取用户有权限的菜单项
 * @param menuItems 所有菜单项
 * @param permissions 用户权限列表
 * @returns 有权限的菜单项
 */
export const getAuthorizedMenuItems = <T extends { path: string }>(
  menuItems: T[], 
  permissions: Permission[]
): T[] => {
  return menuItems.filter(item => hasPathPermission(item.path, permissions));
};
