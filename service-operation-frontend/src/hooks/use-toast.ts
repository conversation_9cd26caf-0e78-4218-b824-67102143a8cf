"use client";

import { useState, useCallback, useEffect } from "react";

type ToastType = "default" | "success" | "error" | "warning" | "info" | "destructive";

interface ToastProps {
  title?: string;
  description?: string;
  variant?: ToastType;
  duration?: number;
}

interface Toast extends ToastProps {
  id: string;
}

let toastCount = 0;
let globalToasts: Toast[] = [];
let listeners: Array<(toasts: Toast[]) => void> = [];

const notifyListeners = () => {
  listeners.forEach(listener => listener([...globalToasts]));
};

export function toast(props: ToastProps) {
  const newToast: Toast = {
    ...props,
    id: `toast-${++toastCount}`,
  };
  
  globalToasts.push(newToast);
  notifyListeners();
  
  // 自动移除Toast
  const duration = props.duration !== undefined ? props.duration : 5000;
  if (duration > 0) {
    setTimeout(() => {
      globalToasts = globalToasts.filter(t => t.id !== newToast.id);
      notifyListeners();
    }, duration);
  }
}

export function useToast() {
  const [toasts, setToasts] = useState<Toast[]>(globalToasts);
  
  const updateToasts = useCallback((newToasts: Toast[]) => {
    setToasts(newToasts);
  }, []);
  
  const addToast = useCallback((toastProps: ToastProps) => {
    toast(toastProps);
  }, []);
  
  const removeToast = useCallback((id: string) => {
    globalToasts = globalToasts.filter(t => t.id !== id);
    notifyListeners();
  }, []);
  
  // 注册监听器
  useEffect(() => {
    listeners.push(updateToasts);
    
    return () => {
      listeners = listeners.filter(l => l !== updateToasts);
    };
  }, [updateToasts]);
  
  return { 
    toast: addToast,
    toasts, 
    removeToast 
  };
} 