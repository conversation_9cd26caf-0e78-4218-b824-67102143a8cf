// import { api } from '@/services/api';

// 用户类型定义
export interface User {
  id: string;
  username: string;
  email: string;
  role: string;
}

// 登录响应类型
export interface LoginResponse {
  user: User;
  token: string;
}

// 登录请求参数
export interface LoginRequest {
  email: string;
  password: string;
}

// 认证服务
const authService = {
  // 登录方法
  login: async (credentials: LoginRequest): Promise<User> => {
    // 实际项目中应该调用API
    // const response = await api.post<LoginResponse>('/auth/login', credentials);
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    if (credentials.email === '<EMAIL>' && credentials.password === 'password') {
      const user = {
        id: '1',
        username: 'Admin User',
        email: '<EMAIL>',
        role: 'admin'
      };
      
      // 保存access_token到本地存储（与SSO保持一致）
      localStorage.setItem('access_token', 'mock-jwt-token');
      localStorage.setItem('token', 'mock-jwt-token'); // 保持兼容性
      return user;
    } else {
      throw new Error('邮箱或密码不正确');
    }
  },
  
  // 登出方法
  logout: () => {
    localStorage.removeItem('token');
    localStorage.removeItem('access_token');
    localStorage.removeItem('user');
  },
  
  // 获取当前用户信息
  getCurrentUser: async (): Promise<User | null> => {
    // 实际项目中应该调用API
    // const response = await api.get<User>('/auth/me');
    // return response.data;
    
    // 模拟API调用
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      return JSON.parse(storedUser);
    }
    return null;
  },
  
  // 检查是否已登录
  isAuthenticated: (): boolean => {
    return !!localStorage.getItem('token');
  }
};

export default authService;
