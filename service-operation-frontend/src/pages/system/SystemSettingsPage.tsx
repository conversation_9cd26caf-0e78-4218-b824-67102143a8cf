import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Loader2, Save, Eye, EyeOff, Settings } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import MainLayout from '@/components/layout/MainLayout';
import { getSystemSettingsCredentials, saveSystemSettingsCredentials } from '@/services/system';

interface TongchengCredentials {
  username: string | null;
  password: string | null;
}

const SystemSettingsPage: React.FC = () => {
  const [credentials, setCredentials] = useState<TongchengCredentials>({
    username: '',
    password: ''
  });
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  // 获取当前设置
  // 新版：通过统一api服务获取系统设置
  const fetchCredentials = async () => {
    setLoading(true);
    setError(null);
    try {
      const res = await getSystemSettingsCredentials();
      if (!res.success) {
        // 404或其他错误
        setCredentials({ username: '', password: '' });
        if (res.error && !res.error.includes('404')) {
          throw new Error(res.error);
        }
        return;
      }
      const data = res.data || { username: null, password: null };
      setCredentials({
        username: data.username || '',
        password: data.password || ''
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取设置失败';
      setError(errorMessage);
      toast({
        title: '错误',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };


  // 保存设置
  // 新版：通过统一api服务保存系统设置
  const saveCredentials = async () => {
    setSaving(true);
    setError(null);
    console.log('开始保存设置...');
    try {
      const res = await saveSystemSettingsCredentials({
        username: credentials.username || null,
        password: credentials.password || null,
      });
      if (!res.success) {
        throw new Error(res.error || '保存设置失败');
      }
      setError(null);
      toast({
        title: '成功',
        description: '系统设置已保存',
        variant: 'success',
      });
      console.log('toast已调用');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '保存设置失败';
      setError(errorMessage);
      console.error('保存失败，显示错误toast...', errorMessage);
      toast({
        title: '错误',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };


  // 处理输入变化
  const handleInputChange = (field: keyof TongchengCredentials, value: string) => {
    setCredentials(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // 页面加载时获取设置
  useEffect(() => {
    fetchCredentials();
  }, []);

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">加载系统设置...</span>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      {/* 主框架容器 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 flex flex-col min-h-0">
        {/* 固定头部区域 */}
        <div className="flex-shrink-0 p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-2">
                <Settings className="h-8 w-8" />
                系统设置
              </h1>
              {/* <p className="text-gray-600 mt-2">
                配置系统的各项参数和第三方服务凭证
              </p> */}
            </div>
          </div>
        </div>

        {/* 内容区域 */}
        <div className="flex-1 p-8 overflow-y-auto">
          <div className="w-full">
            {error && (
              <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
                <p className="text-red-800">{error}</p>
              </div>
            )}

            <div className="space-y-6">
        {/* 同程管家设置 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              同程管家设置
            </CardTitle>
            <p className="text-sm text-gray-600">
              配置同程管家的登录凭证，用于自动化预订火车票、飞机票、酒店
            </p>
          </CardHeader>
          <CardContent className="space-y-6 p-8">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-2 gap-6">
              {/* 用户名 */}
              <div className="space-y-2">
                <Label htmlFor="username">用户名</Label>
                <Input
                  id="username"
                  type="text"
                  placeholder="请输入同程管家用户名"
                  value={credentials.username || ''}
                  onChange={(e) => handleInputChange('username', e.target.value)}
                  disabled={saving}
                />
              </div>

              {/* 密码 */}
              <div className="space-y-2">
                <Label htmlFor="password">密码</Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    placeholder="请输入同程管家密码"
                    value={credentials.password || ''}
                    onChange={(e) => handleInputChange('password', e.target.value)}
                    disabled={saving}
                    className="pr-10"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowPassword(!showPassword)}
                    disabled={saving}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>
            </div>

            <div className="pt-4 border-t">
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-600">
                  密码将被加密存储，确保数据安全
                </div>
                <Button
                  onClick={saveCredentials}
                  disabled={saving}
                  className="min-w-[100px]"
                >
                  {saving ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      保存中...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      保存设置
                    </>
                  )}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default SystemSettingsPage; 