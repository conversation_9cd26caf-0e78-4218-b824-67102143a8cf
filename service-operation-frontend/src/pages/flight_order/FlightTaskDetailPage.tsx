import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Pagination } from '@/components/ui/pagination';
import MainLayout from '@/components/layout/MainLayout';
import {
  ArrowLeft,
  Search,
  X,
  Download,
  Eye,
  FileText,
  AlertTriangle,
  Plane
} from 'lucide-react';
import { getFlightOrdersByTask, getTaskFlightOrderStats, FlightOrder, TaskOrderStatsResponse } from '@/api/flightOrder';
import { ProjectTaskService } from '@/services/project';
import { ProjectTask } from '@/types/project-task';
import { useToast } from '@/hooks/use-toast';
import { formatAmount } from '@/utils/formatters';
import FlightOrderDetailModal from '@/components/booking/FlightOrderDetailModal';
import FailureDetailsModal from '@/components/order/FailureDetailsModal';

const FlightTaskDetailPage: React.FC = () => {
  const { taskId } = useParams<{ taskId: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();

  // 基础状态
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [task, setTask] = useState<ProjectTask | null>(null);

  // 订单列表状态
  const [orders, setOrders] = useState<FlightOrder[]>([]);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(1);
  const [pageSize] = useState(20);

  // 搜索状态
  const [searchTravelerName, setSearchTravelerName] = useState('');
  const [searchMobilePhone, setSearchMobilePhone] = useState('');
  const [searchContactPhone, setSearchContactPhone] = useState('');

  // 统计信息状态
  const [stats, setStats] = useState<TaskOrderStatsResponse | null>(null);

  // 订单操作状态
  const [selectedOrder, setSelectedOrder] = useState<FlightOrder | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);

  // 失败详情相关状态
  const [selectedFailedOrder, setSelectedFailedOrder] = useState<FlightOrder | null>(null);
  const [isFailureModalOpen, setIsFailureModalOpen] = useState(false);

  // 获取任务信息
  useEffect(() => {
    const fetchTaskInfo = async () => {
      if (!taskId) return;
      
      try {
        const taskData = await ProjectTaskService.getTaskByTaskId(taskId);
        setTask(taskData);
      } catch (error) {
        console.error('获取任务信息失败:', error);
        setError('无法获取任务信息');
      }
    };

    fetchTaskInfo();
  }, [taskId]);

  // 加载订单数据
  const loadOrders = async (page: number = 1) => {
    if (!taskId) return;
    
    setLoading(true);
    try {
      const params: any = {
        page,
        page_size: pageSize
      };

      // 只添加非空的搜索参数
      if (searchTravelerName?.trim()) {
        params.traveler_name = searchTravelerName.trim();
      }
      if (searchMobilePhone?.trim()) {
        params.mobile_phone = searchMobilePhone.trim();
      }
      if (searchContactPhone?.trim()) {
        params.contact_phone = searchContactPhone.trim();
      }

      const response = await getFlightOrdersByTask(taskId, params);
      
      setOrders(response.items);
      setTotal(response.total);
      setPage(page);
    } catch (error) {
      console.error('加载订单失败:', error);
      toast({
        title: "加载失败",
        description: "无法加载任务订单列表",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // 加载统计信息
  const loadStats = async () => {
    if (!taskId) return;
    
    try {
      const statsData = await getTaskFlightOrderStats(taskId);
      setStats(statsData);
    } catch (error) {
      console.error('加载统计信息失败:', error);
    }
  };

  // 初始加载
  useEffect(() => {
    loadOrders(1);
    loadStats();
  }, [taskId, searchTravelerName, searchMobilePhone, searchContactPhone]);

  // 搜索处理
  const handleSearch = () => {
    loadOrders(1);
  };

  // 清空搜索
  const handleClearSearch = () => {
    setSearchTravelerName('');
    setSearchMobilePhone('');
    setSearchContactPhone('');
  };

  // 获取订单状态显示
  const getOrderStatusDisplay = (status: string) => {
    const statusConfig = {
      'initial': { label: '待提交', bg: 'bg-gray-100', text: 'text-gray-800' },
      'submitted': { label: '已提交', bg: 'bg-blue-100', text: 'text-blue-800' },
      'processing': { label: '处理中', bg: 'bg-yellow-100', text: 'text-yellow-800' },
      'completed': { label: '已完成', bg: 'bg-green-100', text: 'text-green-800' },
      'failed': { label: '预定失败', bg: 'bg-red-100', text: 'text-red-800' },
      'check_failed': { label: '验证失败', bg: 'bg-orange-100', text: 'text-orange-800' },
      'paused': { label: '已暂停', bg: 'bg-purple-100', text: 'text-purple-800' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.initial;
    
    return (
      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${config.bg} ${config.text}`}>
        {config.label}
      </span>
    );
  };

  // 获取任务状态显示
  const getTaskStatusDisplay = (status: string) => {
    const statusConfig = {
      'pending': { label: '待处理', bg: 'bg-gray-100', text: 'text-gray-800' },
      'running': { label: '执行中', bg: 'bg-blue-100', text: 'text-blue-800' },
      'completed': { label: '已完成', bg: 'bg-green-100', text: 'text-green-800' },
      'failed': { label: '失败', bg: 'bg-red-100', text: 'text-red-800' },
      'cancelled': { label: '已取消', bg: 'bg-gray-100', text: 'text-gray-800' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    
    return {
      label: config.label,
      bg: config.bg,
      text: config.text
    };
  };

  // 订单操作处理
  const handleViewOrder = (order: FlightOrder) => {
    setSelectedOrder(order);
    setIsViewModalOpen(true);
  };

  // 处理失败详情点击
  const handleFailureDetailsClick = (order: FlightOrder) => {
    setSelectedFailedOrder(order);
    setIsFailureModalOpen(true);
  };

  if (error) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">加载失败</h2>
            <p className="text-gray-600 mb-4">{error}</p>
            <Button onClick={() => navigate(-1)}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回
            </Button>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="p-4 bg-gray-50 min-h-screen">
        {/* 页面头部 */}
        <div className="mb-4">
          <Card className="bg-white shadow-sm border border-gray-200 border-l-4 border-l-green-500 p-4">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-2">
                  <div className="p-2 bg-green-50 rounded-lg">
                    <Plane className="h-5 w-5 text-green-600" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <h1 className="text-xl font-bold text-gray-900">
                        {task?.task_title || `任务 ${taskId}`}
                      </h1>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => navigate(-1)}
                        className="flex items-center gap-2"
                      >
                        <ArrowLeft className="h-4 w-4" />
                        返回
                      </Button>
                    </div>
                    <p className="text-gray-600">飞机票预订任务订单详情</p>
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-4">
                  <div>
                    <p className="text-sm text-gray-500">任务ID</p>
                    <p className="font-medium">{taskId}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">任务类型</p>
                    <p className="font-medium">{task?.task_type}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">任务状态</p>
                    {task?.task_status ? (
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getTaskStatusDisplay(task.task_status).bg} ${getTaskStatusDisplay(task.task_status).text}`}>
                        {getTaskStatusDisplay(task.task_status).label}
                      </span>
                    ) : (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        未知状态
                      </span>
                    )}
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">创建时间</p>
                    <p className="font-medium">
                      {task?.created_at ? new Date(task.created_at).toLocaleDateString('zh-CN') : '-'}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </div>

        {/* 统计信息卡片 */}
        {stats && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <Card className="p-4">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-full mr-3">
                  <FileText className="h-4 w-4 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">总订单数</p>
                  <p className="text-xl font-bold text-blue-700">{stats.total_orders}</p>
                </div>
              </div>
            </Card>
            <Card className="p-4">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-full mr-3">
                  <FileText className="h-4 w-4 text-green-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">已完成</p>
                  <p className="text-xl font-bold text-green-700">{stats.completed_orders}</p>
                </div>
              </div>
            </Card>
            <Card className="p-4">
              <div className="flex items-center">
                <div className="p-2 bg-yellow-100 rounded-full mr-3">
                  <FileText className="h-4 w-4 text-yellow-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">处理中</p>
                  <p className="text-xl font-bold text-yellow-700">{stats.processing_orders}</p>
                </div>
              </div>
            </Card>
            <Card className="p-4">
              <div className="flex items-center">
                <div className="p-2 bg-red-100 rounded-full mr-3">
                  <FileText className="h-4 w-4 text-red-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">失败订单</p>
                  <p className="text-xl font-bold text-red-700">{stats.failed_orders}</p>
                </div>
              </div>
            </Card>
          </div>
        )}

        {/* 订单列表 */}
        <Card>
          <div className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900">订单列表</h2>
            </div>

            {/* 搜索筛选 */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">出行人姓名</label>
                <input
                  type="text"
                  value={searchTravelerName}
                  onChange={(e) => setSearchTravelerName(e.target.value)}
                  placeholder="搜索出行人姓名"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">手机号</label>
                <input
                  type="text"
                  value={searchMobilePhone}
                  onChange={(e) => setSearchMobilePhone(e.target.value)}
                  placeholder="搜索手机号"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">联系人手机号</label>
                <input
                  type="text"
                  value={searchContactPhone}
                  onChange={(e) => setSearchContactPhone(e.target.value)}
                  placeholder="搜索联系人手机号"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div className="flex items-end gap-2">
                <Button onClick={handleSearch} size="sm" className="bg-blue-600 hover:bg-blue-700 text-white">
                  <Search className="h-4 w-4 mr-1" />
                  搜索
                </Button>
                <Button onClick={handleClearSearch} variant="outline" size="sm">
                  <X className="h-4 w-4 mr-1" />
                  清空
                </Button>
              </div>
            </div>

            {/* 订单表格 */}
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-gray-600">加载中...</p>
              </div>
            ) : orders.length > 0 ? (
              <>
                <div className="overflow-x-auto custom-scrollbar" style={{ maxHeight: '600px' }}>
                  <style dangerouslySetInnerHTML={{
                    __html: `
                      .custom-scrollbar::-webkit-scrollbar {
                        width: 8px;
                        height: 8px;
                      }
                      .custom-scrollbar::-webkit-scrollbar-track {
                        background: #f1f5f9;
                        border-radius: 4px;
                      }
                      .custom-scrollbar::-webkit-scrollbar-thumb {
                        background: #cbd5e1;
                        border-radius: 4px;
                      }
                      .custom-scrollbar::-webkit-scrollbar-thumb:hover {
                        background: #94a3b8;
                      }
                    `
                  }} />
                  <table className="w-full text-xs" style={{ minWidth: '4000px' }}>
                    <thead className="bg-gray-50 border-b border-gray-200">
                      <tr>
                        <th className="text-center p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '60px' }}>序号</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>状态</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '150px' }}>错误信息</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>出行人姓名</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>出行人姓</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>出行人名</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>国籍</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '60px' }}>性别</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>出生日期</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>证件类型</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '150px' }}>证件号码</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>证件有效期</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>手机号国际区号</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>手机号</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>出行日期</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>出发机场名</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>到达机场名</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>航班号</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>出发时间</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>到达时间</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>行程提交项</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>联系人</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>联系人手机号</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '150px' }}>联系人邮箱</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>审批参照人</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>保险名称</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>公司名称</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>代订人</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '200px' }}>出票短信</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>金额</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>订单号</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>账单号</th>
                        <th className="text-center p-2 font-medium text-gray-900 text-xs whitespace-nowrap sticky right-0 bg-white border-l border-gray-200 z-10 shadow-[-4px_0_8px_rgba(0,0,0,0.1)]" style={{ minWidth: '120px' }}>操作</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {orders.map((order, index) => (
                        <tr key={order.id} className="hover:bg-gray-50">
                          <td className="text-center p-2 text-gray-900 text-xs">
                            {(page - 1) * pageSize + index + 1}
                          </td>
                          <td className="p-2 text-xs">
                            {getOrderStatusDisplay(order.order_status)}
                          </td>
                          <td className="p-2 text-xs text-red-600 max-w-[150px] truncate" title={order.fail_reason || ''}>
                            {order.fail_reason || '-'}
                          </td>
                          <td className="p-2 text-xs text-gray-900 font-medium max-w-[100px] truncate" title={order.traveler_full_name}>
                            {order.traveler_full_name}
                          </td>
                          <td className="p-2 text-xs text-gray-600 max-w-[80px] truncate" title={order.traveler_surname || ''}>
                            {order.traveler_surname || '-'}
                          </td>
                          <td className="p-2 text-xs text-gray-600 max-w-[80px] truncate" title={order.traveler_given_name || ''}>
                            {order.traveler_given_name || '-'}
                          </td>
                          <td className="p-2 text-xs text-gray-600 max-w-[80px] truncate" title={order.nationality || ''}>
                            {order.nationality || '-'}
                          </td>
                          <td className="p-2 text-xs text-gray-600 max-w-[60px] truncate" title={order.gender || ''}>
                            {order.gender || '-'}
                          </td>
                          <td className="p-2 text-xs text-gray-600 max-w-[100px] truncate" title={order.birth_date || ''}>
                            {order.birth_date || '-'}
                          </td>
                          <td className="p-2 text-xs text-gray-600 max-w-[80px] truncate" title={order.id_type || ''}>
                            {order.id_type || '-'}
                          </td>
                          <td className="p-2 text-xs text-gray-600 font-mono max-w-[150px] truncate" title={order.id_number || ''}>
                            {order.id_number || '-'}
                          </td>
                          <td className="p-2 text-xs text-gray-600 max-w-[100px] truncate" title={order.id_expiry_date || ''}>
                            {order.id_expiry_date || '-'}
                          </td>
                          <td className="p-2 text-xs text-gray-600 max-w-[80px] truncate" title={order.mobile_country_code || ''}>
                            {order.mobile_country_code || '-'}
                          </td>
                          <td className="p-2 text-xs text-gray-600 font-mono max-w-[120px] truncate" title={order.mobile_phone || ''}>
                            {order.mobile_phone || '-'}
                          </td>
                          <td className="p-2 text-xs text-gray-600 max-w-[100px] truncate" title={order.travel_date || ''}>
                            {order.travel_date || '-'}
                          </td>
                          <td className="p-2 text-xs text-gray-600 max-w-[120px] truncate" title={order.departure_airport || ''}>
                            {order.departure_airport || '-'}
                          </td>
                          <td className="p-2 text-xs text-gray-600 max-w-[120px] truncate" title={order.arrival_airport || ''}>
                            {order.arrival_airport || '-'}
                          </td>
                          <td className="p-2 text-xs text-blue-600 font-medium max-w-[80px] truncate" title={order.flight_number || ''}>
                            {order.flight_number || '-'}
                          </td>
                          <td className="p-2 text-xs text-gray-600 max-w-[80px] truncate" title={order.departure_time || ''}>
                            {order.departure_time || '-'}
                          </td>
                          <td className="p-2 text-xs text-gray-600 max-w-[80px] truncate" title={order.arrival_time || ''}>
                            {order.arrival_time || '-'}
                          </td>
                          <td className="p-2 text-xs text-gray-600 max-w-[120px] truncate" title={order.trip_submission_item || ''}>
                            {order.trip_submission_item || '-'}
                          </td>
                          <td className="p-2 text-xs text-gray-600 max-w-[100px] truncate" title={order.contact_person || ''}>
                            {order.contact_person || '-'}
                          </td>
                          <td className="p-2 text-xs text-gray-600 font-mono max-w-[120px] truncate" title={order.contact_mobile_phone || ''}>
                            {order.contact_mobile_phone || '-'}
                          </td>
                          <td className="p-2 text-xs text-gray-600 max-w-[150px] truncate" title={order.contact_email || ''}>
                            {order.contact_email || '-'}
                          </td>
                          <td className="p-2 text-xs text-gray-600 max-w-[100px] truncate" title={order.approver || ''}>
                            {order.approver || '-'}
                          </td>
                          <td className="p-2 text-xs text-gray-600 max-w-[100px] truncate" title={order.insurance_name || ''}>
                            {order.insurance_name || '-'}
                          </td>
                          <td className="p-2 text-xs text-gray-600 max-w-[100px] truncate" title={order.company_name || ''}>
                            {order.company_name || '-'}
                          </td>
                          <td className="p-2 text-xs text-gray-600 max-w-[80px] truncate" title={order.booking_agent || ''}>
                            {order.booking_agent || '-'}
                          </td>
                          <td className="p-2 text-xs text-gray-600 max-w-[200px] truncate" title={order.ticket_sms || ''}>
                            {order.ticket_sms || '-'}
                          </td>
                          <td className="p-2 text-xs text-green-600 font-medium max-w-[80px] truncate" title={order.amount ? formatAmount(order.amount) : ''}>
                            {order.amount ? formatAmount(order.amount) : '-'}
                          </td>
                          <td className="p-2 text-xs text-gray-600 font-mono max-w-[100px] truncate" title={order.order_number || ''}>
                            {order.order_number || '-'}
                          </td>
                          <td className="p-2 text-xs text-gray-600 font-mono max-w-[100px] truncate" title={order.bill_number || ''}>
                            {order.bill_number || '-'}
                          </td>
                          <td className="text-center p-2 sticky right-0 bg-white border-l border-gray-200 z-10 shadow-[-4px_0_8px_rgba(0,0,0,0.1)]">
                            <div className="flex items-center justify-center gap-1">
                              <button
                                onClick={() => handleViewOrder(order)}
                                className="p-1 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors"
                                title="查看详情"
                              >
                                <Eye className="h-4 w-4" />
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* 分页 */}
                {total > pageSize && (
                  <div className="mt-4 flex justify-center">
                    <Pagination
                      currentPage={page}
                      totalPages={Math.ceil(total / pageSize)}
                      totalItems={total}
                      pageSize={pageSize}
                      onPageChange={(newPage) => loadOrders(newPage)}
                    />
                  </div>
                )}
              </>
            ) : (
              <div className="text-center py-8">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">暂无订单数据</h3>
                <p className="text-gray-600">该任务下还没有飞机票订单</p>
              </div>
            )}
          </div>
        </Card>

        {/* 订单详情模态框 */}
        <FlightOrderDetailModal
          order={selectedOrder}
          isOpen={isViewModalOpen}
          onClose={() => {
            setSelectedOrder(null);
            setIsViewModalOpen(false);
          }}
          onOrderUpdated={() => {
            loadOrders(page);
          }}
        />

        {/* 失败详情模态框 */}
        {selectedFailedOrder && (
          <FailureDetailsModal
            isOpen={isFailureModalOpen}
            onClose={() => setIsFailureModalOpen(false)}
            orderId={selectedFailedOrder.id.toString()}
            taskId={taskId}
            failReason={selectedFailedOrder.fail_reason || undefined}
          />
        )}
      </div>
    </MainLayout>
  );
};

export default FlightTaskDetailPage;
