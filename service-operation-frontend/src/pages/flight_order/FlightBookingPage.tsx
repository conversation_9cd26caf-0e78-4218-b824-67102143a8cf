import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate, useSearchParams } from 'react-router-dom';
import { Card } from '@/components/ui/card';
import MainLayout from '@/components/layout/MainLayout';
import { But<PERSON> } from '@/components/ui/button';

// import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import {
  ArrowLeft,
  Building,
  RefreshCw,
  Eye,
  Edit,
  Search,
  Clock2Icon,
  AlertTriangle,
  AlertCircle,
  Loader,
  Settings,
  X,
  Download,
  CheckCircle,
  XCircle,
  Clock,
  Send,
  DollarSign,
  CreditCard,
  FileText,
  Bell,
  User,
  Upload,
  Plus,
  Trash2
} from 'lucide-react';
import FlightExcelConverter from '@/components/flight_order/FlightExcelConverter';
import FlightBookingContent from '@/components/booking/FlightBookingContent';
import FlightAllOrdersContent from '@/components/booking/FlightAllOrdersContent';
import FlightTasksContent from '@/components/booking/FlightTasksContent';

import { ProjectService } from '@/services/project';
import { Project } from '@/types/project';
import { FlightOrder } from '@/api/flight';
import { flightOrderApi } from '@/api/flightOrder';
import { formatAmount } from '@/utils/formatters';
import { Pagination } from '@/components/ui/pagination';
import * as XLSX from 'xlsx';
import { api } from '@/api/request';

// Tab类型定义
type TabType = 'booking' | 'all-orders' | 'details' | 'reconciliation';

const FlightBookingPage: React.FC = () => {
  const { projectId } = useParams<{ projectId: string }>();
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const requestedTab = searchParams.get('tab') as TabType;
  const initialTab = requestedTab || 'booking';
  const { toast } = useToast();

  const [project, setProject] = useState<Project | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<TabType>(initialTab);

  // 对账单相关状态
  const [reconciliationOrders, setReconciliationOrders] = useState<FlightOrder[]>([]);
  const [reconciliationOrdersTotal, setReconciliationOrdersTotal] = useState(0);
  const [reconciliationOrdersPage, setReconciliationOrdersPage] = useState(1);
  const [reconciliationOrdersPageSize] = useState(20);
  const [reconciliationOrdersLoading, setReconciliationOrdersLoading] = useState(false);

  // 对账单搜索状态
  const [reconciliationSearchTravelerName, setReconciliationSearchTravelerName] = useState('');
  const [reconciliationSearchMobilePhone, setReconciliationSearchMobilePhone] = useState('');
  const [reconciliationSearchContactPhone, setReconciliationSearchContactPhone] = useState('');

  // 对账单编辑状态
  const [editingReconciliationOrder, setEditingReconciliationOrder] = useState<FlightOrder | null>(null);
  const [isReconciliationEditModalOpen, setIsReconciliationEditModalOpen] = useState(false);
  const [reconciliationValidationErrors, setReconciliationValidationErrors] = useState<Record<string, string>>({});
  const [savingReconciliation, setSavingReconciliation] = useState(false);

  // 对账单导出状态
  const [exportingReconciliation, setExportingReconciliation] = useState(false);

  // 对账单查看详情状态
  const [viewingReconciliationOrder, setViewingReconciliationOrder] = useState<FlightOrder | null>(null);
  const [isReconciliationViewModalOpen, setIsReconciliationViewModalOpen] = useState(false);




  


  useEffect(() => {
    if (projectId) {
      loadProject();
    }
  }, [projectId]);

  // 移除自动搜索，改为手动搜索

  // 监听tab切换，加载对账单数据
  useEffect(() => {
    if (activeTab === 'reconciliation' && projectId) {
      loadReconciliationOrders(1);
    }
  }, [activeTab, projectId]);

  const loadProject = async () => {
    try {
      setLoading(true);
      const projectData = await ProjectService.getProject(parseInt(projectId!));
      setProject(projectData.data!);
      setError(null);
    } catch (error: any) {
      console.error('加载项目信息失败:', error);
      setError(error.message || '加载项目信息失败');
    } finally {
      setLoading(false);
    }
  };

  // 加载对账单订单（只显示已完成状态的订单）
  const loadReconciliationOrders = async (page: number = 1) => {
    if (!projectId) return;

    setReconciliationOrdersLoading(true);
    try {
      const params: any = {
        page,
        page_size: reconciliationOrdersPageSize,
        order_status: 'completed', // 只显示已完成状态的订单
        sort_by_failed_first: false
      };

      // 只添加非空的搜索参数
      if (reconciliationSearchTravelerName?.trim()) {
        params.traveler_name = reconciliationSearchTravelerName.trim();
      }
      if (reconciliationSearchMobilePhone?.trim()) {
        params.mobile_phone = reconciliationSearchMobilePhone.trim();
      }
      if (reconciliationSearchContactPhone?.trim()) {
        params.contact_phone = reconciliationSearchContactPhone.trim();
      }

      const response = await flightOrderApi.getOrdersByProject(Number(projectId), params);

      setReconciliationOrders(response.items);
      setReconciliationOrdersTotal(response.total);
      setReconciliationOrdersPage(page);
    } catch (error) {
      console.error('加载对账单订单失败:', error);
      toast({
        title: "加载失败",
        description: "无法加载对账单订单列表",
        variant: "destructive",
      });
    } finally {
      setReconciliationOrdersLoading(false);
    }
  };

  // 对账单搜索功能
  const handleReconciliationSearch = () => {
    loadReconciliationOrders(1);
  };

  // 对账单重置搜索功能
  const handleClearReconciliationSearch = () => {
    setReconciliationSearchTravelerName('');
    setReconciliationSearchMobilePhone('');
    setReconciliationSearchContactPhone('');
    // 重置后自动加载数据
    setTimeout(() => {
      loadReconciliationOrders(1);
    }, 0);
  };

  // 导出对账单Excel
  const exportReconciliationOrders = async () => {
    if (reconciliationOrders.length === 0) {
      toast({
        title: "导出失败",
        description: "没有对账单数据可导出",
        variant: "destructive",
      });
      return;
    }

    try {
      // 准备导出数据
      const exportData = reconciliationOrders.map((order, index) => ({
        '序号': index + 1,
        '出行人姓名': order.traveler_full_name || '-',
        '出行人姓': order.traveler_surname || '-',
        '出行人名': order.traveler_given_name || '-',
        '国籍': order.nationality || '-',
        '性别': order.gender || '-',
        '出生日期': order.birth_date || '-',
        '证件类型': order.id_type || '-',
        '证件号码': order.id_number || '-',
        '证件有效期': order.id_expiry_date || '-',
        '手机号国际区号': order.mobile_country_code || '-',
        '手机号': order.mobile_phone || '-',
        '出行日期': order.travel_date || '-',
        '出发机场名': order.departure_airport || '-',
        '到达机场名': order.arrival_airport || '-',
        '航班号': order.flight_number || '-',
        '出发时间': order.departure_time || '-',
        '到达时间': order.arrival_time || '-',
        '行程提交项': order.trip_submission_item || '-',
        '联系人': order.contact_person || '-',
        '联系人手机号': order.contact_mobile_phone || '-',
        '联系人邮箱': order.contact_email || '-',
        '审批参照人': order.approver || '-',
        '保险名称': order.insurance_name || '-',
        '公司名称': order.company_name || '-',
        '代订人': order.booking_agent || '-',
        '出票短信': order.ticket_sms || '-',
        '金额': order.amount ? formatAmount(order.amount) : '-',
        '订单号': order.order_number || '-',
        '账单号': order.bill_number || '-',
        '创建时间': order.created_at ? new Date(order.created_at).toLocaleString('zh-CN') : '-',
        '更新时间': order.updated_at ? new Date(order.updated_at).toLocaleString('zh-CN') : '-'
      }));

      // 获取列名
      const headers = Object.keys(exportData[0] || {});

      // 创建空工作表
      const ws = XLSX.utils.aoa_to_sheet([]);

      // 添加标题行
      const title = `${project?.project_name || '项目' + projectId} - 飞机票对账单明细`;

      // 第一行：标题
      XLSX.utils.sheet_add_aoa(ws, [[title]], { origin: 'A1' });

      // 合并标题行单元格
      ws['!merges'] = [{ s: { r: 0, c: 0 }, e: { r: 0, c: headers.length - 1 } }];

      // 第二行：列名
      XLSX.utils.sheet_add_aoa(ws, [headers], { origin: 'A2' });

      // 第三行开始：数据
      const dataRows = exportData.map(row => headers.map(header => (row as any)[header]));
      XLSX.utils.sheet_add_aoa(ws, dataRows, { origin: 'A3' });

      // 设置列宽
      const colWidths = headers.map(() => ({ wch: 15 })); // 统一列宽
      ws['!cols'] = colWidths;

      // 获取工作表范围
      const range = XLSX.utils.decode_range(ws['!ref'] || 'A1');

      // 设置样式
      for (let row = range.s.r; row <= range.e.r; row++) {
        for (let col = range.s.c; col <= range.e.c; col++) {
          const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });

          if (!ws[cellAddress]) continue;

          // 初始化样式对象
          if (!ws[cellAddress].s) {
            ws[cellAddress].s = {};
          }

          // 添加边框
          ws[cellAddress].s.border = {
            top: { style: 'thin', color: { rgb: '000000' } },
            bottom: { style: 'thin', color: { rgb: '000000' } },
            left: { style: 'thin', color: { rgb: '000000' } },
            right: { style: 'thin', color: { rgb: '000000' } }
          };

          // 为标题行添加特殊样式
          if (row === 0) {
            ws[cellAddress].s.fill = {
              patternType: 'solid',
              fgColor: { rgb: 'E3F2FD' }
            };
            ws[cellAddress].s.font = {
              name: '微软雅黑',
              sz: 14,
              bold: true
            };
            ws[cellAddress].s.alignment = {
              horizontal: 'center',
              vertical: 'center'
            };
          }

          // 为列名行添加特殊样式
          else if (row === 1) {
            ws[cellAddress].s.fill = {
              patternType: 'solid',
              fgColor: { rgb: 'F3F4F6' }
            };
            ws[cellAddress].s.font = {
              name: '微软雅黑',
              sz: 10,
              bold: true
            };
            ws[cellAddress].s.alignment = {
              horizontal: 'center',
              vertical: 'center',
              wrapText: true
            };
          }
        }
      }

      // 创建工作簿
      const wb = XLSX.utils.book_new();

      // 添加工作表
      XLSX.utils.book_append_sheet(wb, ws, '飞机票对账单');

      // 生成文件名
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0];

      // 添加搜索条件信息
      const searchConditions = [];
      if (reconciliationSearchTravelerName) searchConditions.push(`出行人(${reconciliationSearchTravelerName})`);
      if (reconciliationSearchMobilePhone) searchConditions.push(`手机(${reconciliationSearchMobilePhone})`);
      if (reconciliationSearchContactPhone) searchConditions.push(`联系人手机(${reconciliationSearchContactPhone})`);

      let filterInfo = '';
      if (searchConditions.length > 0) {
        filterInfo = `_搜索(${searchConditions.join('_')})`;
      }

      const fileName = `飞机票对账单_${project?.project_name || '项目' + projectId}${filterInfo}_${timestamp}.xlsx`;

      // 导出文件
      XLSX.writeFile(wb, fileName);

      toast({
        title: "导出成功",
        description: `成功导出 ${reconciliationOrders.length} 条对账单数据`,
        variant: "default",
      });

    } catch (error) {
      console.error('导出对账单失败:', error);
      toast({
        title: "导出失败",
        description: "导出对账单时发生错误",
        variant: "destructive",
      });
    }
  };

  // 导出对账单Excel功能
  const handleExportReconciliationExcel = async () => {
    setExportingReconciliation(true);
    try {
      // 获取当前筛选条件下的所有对账单数据
      let allReconciliationOrders: FlightOrder[] = [];
      let currentExportPage = 1;
      let hasMore = true;
      const pageSize = 100; // 每次获取100条

      while (hasMore) {
        const exportParams: any = {
          page: currentExportPage,
          page_size: pageSize,
          order_status: 'completed', // 对账单只显示已完成的订单
          sort_by_failed_first: false // 对账单不需要按失败优先排序
        };

        if (reconciliationSearchTravelerName?.trim()) {
          exportParams.traveler_name = reconciliationSearchTravelerName.trim();
        }
        if (reconciliationSearchMobilePhone?.trim()) {
          exportParams.mobile_phone = reconciliationSearchMobilePhone.trim();
        }
        if (reconciliationSearchContactPhone?.trim()) {
          exportParams.contact_phone = reconciliationSearchContactPhone.trim();
        }

        const response = await flightOrderApi.getOrdersByProject(
          Number(projectId),
          exportParams
        );

        allReconciliationOrders = allReconciliationOrders.concat(response.items);

        // 如果返回的数据少于页面大小，说明已经是最后一页
        if (response.items.length < pageSize) {
          hasMore = false;
        } else {
          currentExportPage++;
        }
      }

      // 准备导出数据，与对账单列表的列保持一致
      const exportData = allReconciliationOrders.map((order, index) => ({
        '序号': index + 1,
        '状态': order.order_status === 'completed' ? '已完成' :
               order.order_status === 'failed' ? '预定失败' :
               order.order_status === 'processing' ? '处理中' :
               order.order_status === 'submitted' ? '已提交' :
               order.order_status === 'check_failed' ? '验证失败' :
               order.order_status === 'paused' ? '已暂停' :
               '待提交',
        '错误信息': order.fail_reason || '',
        '出行人姓名': order.traveler_full_name || '',
        '证件类型': order.id_type || '',
        '证件号码': order.id_number || '',
        '手机号国际区号': order.mobile_country_code || '',
        '手机号': order.mobile_phone || '',
        '出行日期': order.travel_date || '',
        '出发机场名': order.departure_airport || '',
        '到达机场名': order.arrival_airport || '',
        '航班号': order.flight_number || '',
        '出发时间': order.departure_time || '',
        '到达时间': order.arrival_time || '',
        '联系人': order.contact_person || '',
        '联系人手机号': order.contact_mobile_phone || '',
        '联系人邮箱': order.contact_email || '',
        '审批参照人': order.approver || '',
        '公司名称': order.company_name || '',
        '代订人': order.booking_agent || '',
        '出票短信': order.ticket_sms || '',
        '金额': order.amount ? formatAmount(order.amount) : '',
        '订单号': order.order_number || '',
        '账单号': order.bill_number || ''
      }));

      if (exportData.length === 0) {
        toast({
          title: "导出失败",
          description: "没有可导出的对账单数据",
          variant: "destructive",
        });
        return;
      }

      // 获取列名
      const headers = Object.keys(exportData[0] || {});

      // 创建空工作表
      const ws = XLSX.utils.aoa_to_sheet([]);

      // 添加标题行
      const title = `项目${projectId} - 飞机票对账单明细`;

      // 第一行：标题
      XLSX.utils.sheet_add_aoa(ws, [[title]], { origin: 'A1' });

      // 合并标题行单元格
      ws['!merges'] = [{ s: { r: 0, c: 0 }, e: { r: 0, c: headers.length - 1 } }];

      // 第二行：列名
      XLSX.utils.sheet_add_aoa(ws, [headers], { origin: 'A2' });

      // 第三行开始：数据
      const dataRows = exportData.map(row => headers.map(header => (row as any)[header]));
      XLSX.utils.sheet_add_aoa(ws, dataRows, { origin: 'A3' });

      // 设置列宽
      const colWidths = headers.map(() => ({ wch: 15 })); // 统一列宽
      ws['!cols'] = colWidths;

      // 获取工作表范围
      const range = XLSX.utils.decode_range(ws['!ref'] || 'A1');

      // 设置样式
      for (let row = range.s.r; row <= range.e.r; row++) {
        for (let col = range.s.c; col <= range.e.c; col++) {
          const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });

          if (!ws[cellAddress]) {
            ws[cellAddress] = { t: 's', v: '' };
          }

          if (!ws[cellAddress].s) {
            ws[cellAddress].s = {};
          }

          // 为所有单元格设置边框
          ws[cellAddress].s.border = {
            top: { style: 'thin', color: { rgb: '000000' } },
            bottom: { style: 'thin', color: { rgb: '000000' } },
            left: { style: 'thin', color: { rgb: '000000' } },
            right: { style: 'thin', color: { rgb: '000000' } }
          };

          // 为标题行添加特殊样式
          if (row === 0) {
            ws[cellAddress].s.fill = {
              patternType: 'solid',
              fgColor: { rgb: '4F46E5' }
            };
            ws[cellAddress].s.font = {
              name: '微软雅黑',
              sz: 14,
              bold: true,
              color: { rgb: 'FFFFFF' }
            };
            ws[cellAddress].s.alignment = {
              horizontal: 'center',
              vertical: 'center'
            };
          }

          // 为列名行添加特殊样式
          else if (row === 1) {
            ws[cellAddress].s.fill = {
              patternType: 'solid',
              fgColor: { rgb: 'F3F4F6' }
            };
            ws[cellAddress].s.font = {
              name: '微软雅黑',
              sz: 10,
              bold: true
            };
            ws[cellAddress].s.alignment = {
              horizontal: 'center',
              vertical: 'center',
              wrapText: true
            };
          }

          // 为数据行添加基本样式
          else {
            ws[cellAddress].s.font = {
              name: '微软雅黑',
              sz: 9
            };
            ws[cellAddress].s.alignment = {
              horizontal: 'left',
              vertical: 'center',
              wrapText: true
            };
          }
        }
      }

      // 创建工作簿
      const wb = XLSX.utils.book_new();

      // 添加工作表
      XLSX.utils.book_append_sheet(wb, ws, '飞机票对账单');

      // 生成文件名
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0];
      let filterInfo = '';

      // 添加搜索条件信息
      const searchConditions = [];
      if (reconciliationSearchTravelerName) searchConditions.push(`出行人(${reconciliationSearchTravelerName})`);
      if (reconciliationSearchMobilePhone) searchConditions.push(`手机(${reconciliationSearchMobilePhone})`);
      if (reconciliationSearchContactPhone) searchConditions.push(`联系人手机(${reconciliationSearchContactPhone})`);
      if (searchConditions.length > 0) {
        filterInfo += `_搜索(${searchConditions.join('_')})`;
      }

      const fileName = `飞机票对账单_项目${projectId}${filterInfo}_${timestamp}.xlsx`;

      // 导出文件
      XLSX.writeFile(wb, fileName);

      toast({
        title: "导出成功",
        description: `成功导出 ${allReconciliationOrders.length} 条对账单数据`,
        variant: "default",
      });

    } catch (error) {
      console.error('导出对账单失败:', error);
      toast({
        title: "导出失败",
        description: "导出对账单时发生错误",
        variant: "destructive",
      });
    } finally {
      setExportingReconciliation(false);
    }
  };

  // 验证对账单表单
  const validateReconciliationForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!editingReconciliationOrder) return false;

    // 金额验证
    if (editingReconciliationOrder.amount && editingReconciliationOrder.amount.trim()) {
      const amount = parseFloat(editingReconciliationOrder.amount);
      if (isNaN(amount) || amount < 0) {
        errors.amount = '金额必须是有效的正数';
      }
    }

    setReconciliationValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // 保存对账单编辑
  const saveReconciliationOrder = async () => {
    if (!editingReconciliationOrder || !validateReconciliationForm()) {
      return;
    }

    setSavingReconciliation(true);
    try {
      // 只提交对账单相关字段
      const reconciliationData = {
        company_name: editingReconciliationOrder.company_name,
        booking_agent: editingReconciliationOrder.booking_agent,
        order_number: editingReconciliationOrder.order_number,
        bill_number: editingReconciliationOrder.bill_number,
        amount: editingReconciliationOrder.amount,
      };

      // 使用专门的对账单更新API
      await flightOrderApi.updateReconciliation(editingReconciliationOrder.id, reconciliationData);

      toast({
        title: "保存成功",
        description: `${editingReconciliationOrder.traveler_full_name} 的对账单信息已成功更新`,
        variant: "default",
      });

      setIsReconciliationEditModalOpen(false);
      setEditingReconciliationOrder(null);
      setReconciliationValidationErrors({});

      // 刷新对账单列表
      await loadReconciliationOrders(reconciliationOrdersPage);
    } catch (error: any) {
      console.error('保存对账单失败:', error);

      // 提取错误信息
      let errorMessage = "保存对账单信息失败";
      if (error.response?.data?.detail) {
        const detail = error.response.data.detail;
        if (detail.includes("不允许编辑")) {
          errorMessage = "当前飞机票系统暂不支持编辑已完成订单的对账单信息。请联系系统管理员。";
        } else {
          errorMessage = detail;
        }
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast({
        title: "保存失败",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setSavingReconciliation(false);
    }
  };

  // 获取订单状态显示
  const getOrderStatusDisplay = (status: string) => {
    const statusConfig = {
      'initial': { label: '待提交', bg: 'bg-gray-100', text: 'text-gray-800' },
      'submitted': { label: '已提交', bg: 'bg-blue-100', text: 'text-blue-800' },
      'processing': { label: '处理中', bg: 'bg-yellow-100', text: 'text-yellow-800' },
      'completed': { label: '已完成', bg: 'bg-green-100', text: 'text-green-800' },
      'failed': { label: '失败', bg: 'bg-red-100', text: 'text-red-800' },
      'check_failed': { label: '校验失败', bg: 'bg-red-100', text: 'text-red-800' },
      'cancelled': { label: '已取消', bg: 'bg-gray-100', text: 'text-gray-800' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.initial;

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.bg} ${config.text}`}>
        {config.label}
      </span>
    );
  };

  // Tab切换处理函数
  const handleTabChange = (tab: TabType) => {
    setActiveTab(tab);
    setSearchParams(prev => {
      prev.set('tab', tab);
      return prev;
    });
  };

  // 渲染Tab内容
  const renderTabContent = () => {
    switch (activeTab) {
      case 'booking':
        return renderBookingContent();
      case 'all-orders':
        return renderAllOrdersTab();
      case 'details':
        return renderDetailsTab();
      case 'reconciliation':
        return renderReconciliationContent();
      default:
        return null;
    }
  };

  // 渲染预订内容
  const renderBookingContent = () => {
    return <FlightBookingContent onNavigateToAllOrders={() => handleTabChange('all-orders')} />;
  };

  // 渲染所有订单Tab
  const renderAllOrdersTab = () => {
    if (!projectId) return null;
    return <FlightAllOrdersContent projectId={projectId} />;
  };

  // 渲染任务详情Tab
  const renderDetailsTab = () => {
    return (
      <FlightTasksContent projectId={projectId!} />
    );
  };

  // 渲染对账单Tab
  const renderReconciliationContent = () => {
    // 计算对账单统计信息
    const totalAmount = reconciliationOrders.reduce((sum, order) => {
      const amount = parseFloat(order.amount) || 0;
      return sum + amount;
    }, 0);

    return (
      <div className="space-y-4">
        {/* 对账单统计卡片 */}
        {reconciliationOrdersTotal > 0 && (
          <Card className="bg-white border border-green-200 mb-6">
            <div className="p-4">
              <h3 className="text-sm font-medium text-green-900 mb-3">对账单统计</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="p-2 bg-green-100 rounded-lg mx-auto w-fit mb-1">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  </div>
                  <p className="text-lg font-medium text-gray-900">{reconciliationOrdersTotal}</p>
                  <p className="text-xs text-gray-500">已完成订单总数</p>
                </div>
                <div className="text-center">
                  <div className="p-2 bg-blue-100 rounded-lg mx-auto w-fit mb-1">
                    <DollarSign className="h-4 w-4 text-blue-600" />
                  </div>
                  <p className="text-lg font-medium text-gray-900">{formatAmount(totalAmount.toString())}</p>
                  <p className="text-xs text-gray-500">订单总金额</p>
                </div>
                <div className="text-center">
                  <div className="p-2 bg-purple-100 rounded-lg mx-auto w-fit mb-1">
                    <CreditCard className="h-4 w-4 text-purple-600" />
                  </div>
                  <p className="text-lg font-medium text-gray-900">{formatAmount((totalAmount / reconciliationOrdersTotal).toString())}</p>
                  <p className="text-xs text-gray-500">平均订单金额</p>
                </div>
              </div>
            </div>
          </Card>
        )}

        {/* 对账单列表 */}
        <Card className="bg-white border border-gray-200">
          <div className="p-4">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-gray-900">飞机票对账单</h3>
              {reconciliationOrdersTotal > 0 && (
                <span className="text-sm text-gray-500">
                  共 {reconciliationOrdersTotal} 条记录
                </span>
              )}
            </div>

            {/* 搜索区域 */}
            <div className="flex flex-col gap-3 mb-6">
              {/* 搜索输入框和操作按钮 */}
              <div className="flex flex-col lg:flex-row gap-4 items-end">
                {/* 左侧：搜索框区域 */}
                <div className="flex-1 grid grid-cols-1 md:grid-cols-3 gap-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">出行人姓名</label>
                    <input
                      type="text"
                      placeholder="请输入出行人姓名"
                      value={reconciliationSearchTravelerName}
                      onChange={(e) => setReconciliationSearchTravelerName(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && handleReconciliationSearch()}
                      className="w-full h-9 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">手机号码</label>
                    <input
                      type="text"
                      placeholder="请输入手机号码"
                      value={reconciliationSearchMobilePhone}
                      onChange={(e) => setReconciliationSearchMobilePhone(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && handleReconciliationSearch()}
                      className="w-full h-9 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">联系人手机号</label>
                    <input
                      type="text"
                      placeholder="请输入联系人手机号"
                      value={reconciliationSearchContactPhone}
                      onChange={(e) => setReconciliationSearchContactPhone(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && handleReconciliationSearch()}
                      className="w-full h-9 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm"
                    />
                  </div>
                </div>

                {/* 右侧：操作按钮区域 */}
                <div className="flex gap-2 flex-shrink-0">
                  <Button
                    onClick={handleReconciliationSearch}
                    size="sm"
                    className="bg-green-600 hover:bg-green-700 text-white flex items-center gap-1"
                  >
                    <Search className="h-4 w-4" />
                    搜索
                  </Button>
                  <Button
                    onClick={handleClearReconciliationSearch}
                    variant="outline"
                    size="sm"
                    className="text-gray-600 hover:text-gray-800"
                  >
                    重置
                  </Button>
                  {reconciliationOrdersTotal > 0 && (
                    <Button
                      onClick={handleExportReconciliationExcel}
                      variant="outline"
                      size="sm"
                      disabled={exportingReconciliation}
                      className="flex items-center gap-1"
                    >
                      {exportingReconciliation ? (
                        <>
                          <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-gray-600 mr-1"></div>
                          导出中...
                        </>
                      ) : (
                        <>
                          <Download className="h-4 w-4" />
                          导出
                        </>
                      )}
                    </Button>
                  )}
                </div>
              </div>
            </div>

            {/* 对账单表格 */}
            {reconciliationOrdersLoading ? (
              <div className="flex justify-center items-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <span className="ml-2 text-gray-600">加载中...</span>
              </div>
            ) : reconciliationOrders.length > 0 ? (
              <>
                <div className="overflow-x-auto custom-scrollbar" style={{ maxHeight: '600px' }}>
                  <style dangerouslySetInnerHTML={{
                    __html: `
                      .custom-scrollbar::-webkit-scrollbar {
                        width: 8px;
                        height: 8px;
                      }
                      .custom-scrollbar::-webkit-scrollbar-track {
                        background: #f1f5f9;
                        border-radius: 4px;
                      }
                      .custom-scrollbar::-webkit-scrollbar-thumb {
                        background: #cbd5e1;
                        border-radius: 4px;
                      }
                      .custom-scrollbar::-webkit-scrollbar-thumb:hover {
                        background: #94a3b8;
                      }
                    `
                  }} />
                  <table className="w-full text-xs" style={{ minWidth: '4000px' }}>
                    <thead className="bg-gray-50 border-b border-gray-200">
                      <tr>
                        <th className="text-center p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '60px' }}>序号</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>状态</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '150px' }}>错误信息</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>出行人姓名</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>证件类型</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '150px' }}>证件号码</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>手机号国际区号</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>手机号</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>出行日期</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>出发机场名</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>到达机场名</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>航班号</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>出发时间</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>到达时间</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>联系人</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>联系人手机号</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '150px' }}>联系人邮箱</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>审批参照人</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>公司名称</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>代订人</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '200px' }}>出票短信</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>金额</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>订单号</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>账单号</th>
                        <th className="text-center p-2 font-medium text-gray-900 text-xs whitespace-nowrap sticky right-0 bg-white border-l border-gray-200 z-10 shadow-[-4px_0_8px_rgba(0,0,0,0.1)]" style={{ minWidth: '100px' }}>操作</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {reconciliationOrders.map((order, index) => (
                        <tr key={order.id} className="hover:bg-gray-50">
                          <td className="text-center p-2 text-gray-900 text-xs">
                            {(reconciliationOrdersPage - 1) * reconciliationOrdersPageSize + index + 1}
                          </td>
                          <td className="p-2 text-xs">
                            {getOrderStatusDisplay(order.order_status)}
                          </td>
                          <td className="p-2 text-xs text-red-600 max-w-[150px] truncate" title={order.fail_reason || ''}>
                            {order.fail_reason || '-'}
                          </td>
                          <td className="p-2 text-xs text-gray-900 font-medium max-w-[100px] truncate" title={order.traveler_full_name}>
                            {order.traveler_full_name}
                          </td>
                          <td className="p-2 text-xs text-gray-600 max-w-[80px] truncate" title={order.id_type || ''}>
                            {order.id_type || '-'}
                          </td>
                          <td className="p-2 text-xs text-gray-600 font-mono max-w-[150px] truncate" title={order.id_number || ''}>
                            {order.id_number || '-'}
                          </td>
                          <td className="p-2 text-xs text-gray-600 max-w-[80px] truncate" title={order.mobile_country_code || ''}>
                            {order.mobile_country_code || '-'}
                          </td>
                          <td className="p-2 text-xs text-gray-600 font-mono max-w-[120px] truncate" title={order.mobile_phone || ''}>
                            {order.mobile_phone || '-'}
                          </td>
                          <td className="p-2 text-xs text-gray-600 max-w-[100px] truncate" title={order.travel_date || ''}>
                            {order.travel_date || '-'}
                          </td>
                          <td className="p-2 text-xs text-gray-600 max-w-[120px] truncate" title={order.departure_airport || ''}>
                            {order.departure_airport || '-'}
                          </td>
                          <td className="p-2 text-xs text-gray-600 max-w-[120px] truncate" title={order.arrival_airport || ''}>
                            {order.arrival_airport || '-'}
                          </td>
                          <td className="p-2 text-xs text-blue-600 font-medium max-w-[80px] truncate" title={order.flight_number || ''}>
                            {order.flight_number || '-'}
                          </td>
                          <td className="p-2 text-xs text-gray-600 max-w-[80px] truncate" title={order.departure_time || ''}>
                            {order.departure_time || '-'}
                          </td>
                          <td className="p-2 text-xs text-gray-600 max-w-[80px] truncate" title={order.arrival_time || ''}>
                            {order.arrival_time || '-'}
                          </td>
                          <td className="p-2 text-xs text-gray-600 max-w-[100px] truncate" title={order.contact_person || ''}>
                            {order.contact_person || '-'}
                          </td>
                          <td className="p-2 text-xs text-gray-600 font-mono max-w-[120px] truncate" title={order.contact_mobile_phone || ''}>
                            {order.contact_mobile_phone || '-'}
                          </td>
                          <td className="p-2 text-xs text-gray-600 max-w-[150px] truncate" title={order.contact_email || ''}>
                            {order.contact_email || '-'}
                          </td>
                          <td className="p-2 text-xs text-gray-600 max-w-[100px] truncate" title={order.approver || ''}>
                            {order.approver || '-'}
                          </td>
                          <td className="p-2 text-xs text-gray-600 max-w-[100px] truncate" title={order.company_name || ''}>
                            {order.company_name || '-'}
                          </td>
                          <td className="p-2 text-xs text-gray-600 max-w-[80px] truncate" title={order.booking_agent || ''}>
                            {order.booking_agent || '-'}
                          </td>
                          <td className="p-2 text-xs text-gray-600 max-w-[200px] truncate" title={order.ticket_sms || ''}>
                            {order.ticket_sms || '-'}
                          </td>
                          <td className="p-2 text-xs text-green-600 font-medium max-w-[80px] truncate" title={order.amount ? formatAmount(order.amount) : ''}>
                            {order.amount ? formatAmount(order.amount) : '-'}
                          </td>
                          <td className="p-2 text-xs text-gray-600 font-mono max-w-[100px] truncate" title={order.order_number || ''}>
                            {order.order_number || '-'}
                          </td>
                          <td className="p-2 text-xs text-gray-600 font-mono max-w-[100px] truncate" title={order.bill_number || ''}>
                            {order.bill_number || '-'}
                          </td>
                          <td className="text-center p-2 sticky right-0 bg-white border-l border-gray-200 z-10 shadow-[-4px_0_8px_rgba(0,0,0,0.1)]">
                            <div className="flex items-center justify-center gap-1">
                              <button
                                onClick={() => {
                                  setViewingReconciliationOrder(order);
                                  setIsReconciliationViewModalOpen(true);
                                }}
                                className="p-1 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors"
                                title="查看详情"
                              >
                                <Eye className="h-4 w-4" />
                              </button>
                              <button
                                onClick={() => {
                                  setEditingReconciliationOrder({ ...order });
                                  setReconciliationValidationErrors({});
                                  setIsReconciliationEditModalOpen(true);
                                }}
                                className="p-1 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded transition-colors"
                                title="编辑对账单信息"
                              >
                                <Edit className="h-4 w-4" />
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* 分页 */}
                {reconciliationOrdersTotal > reconciliationOrdersPageSize && (
                  <div className="mt-4 flex justify-center">
                    <Pagination
                      current={reconciliationOrdersPage}
                      total={reconciliationOrdersTotal}
                      pageSize={reconciliationOrdersPageSize}
                      onChange={(page) => loadReconciliationOrders(page)}
                    />
                  </div>
                )}
              </>
            ) : (
              <div className="text-center py-8">
                <CreditCard className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">暂无对账单数据</h3>
                <p className="text-gray-500">
                  {reconciliationSearchTravelerName || reconciliationSearchMobilePhone || reconciliationSearchContactPhone
                    ? "没有找到符合搜索条件的已完成订单"
                    : "该项目暂无已完成的飞机票订单"
                  }
                </p>
              </div>
            )}
          </div>
        </Card>
      </div>
    );
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center min-h-screen">
          <Loader className="w-8 h-8 animate-spin" />
        </div>
      </MainLayout>
    );
  }

  if (error) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">加载失败</h2>
            <p className="text-gray-600 mb-4">{error}</p>
            <Button onClick={() => window.location.reload()}>
              <RefreshCw className="w-4 h-4 mr-2" />
              重新加载
            </Button>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="p-4 bg-gray-50 min-h-screen">
        {/* 页面头部 - 完全参考酒店预订页面 */}
        <div className="mb-4">
          {/* 项目和任务类型信息卡片 */}
          <Card className="bg-white shadow-sm border border-gray-200 border-l-4 border-l-green-500 p-4">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-2">
                  <div className="p-2 bg-green-50 rounded-lg">
                    <span className="text-lg">✈️</span>
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <div>
                        <h1 className="text-xl font-bold text-gray-900">飞机票预订</h1>
                        <p className="text-sm text-gray-600">
                          项目：{project?.project_name || '加载中...'}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => navigate(`/project-detail/${projectId}`)}
                  className="flex items-center gap-2"
                >
                  <ArrowLeft className="h-4 w-4" />
                  返回项目
                </Button>
              </div>
            </div>
          </Card>
        </div>

        {/* Tab导航 */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {[
                { key: 'booking', label: '预订管理', icon: Send },
                { key: 'all-orders', label: '所有订单', icon: FileText },
                { key: 'details', label: '任务详情', icon: Eye },
                { key: 'reconciliation', label: '对账单', icon: CreditCard }
              ].map(({ key, label, icon: Icon }) => (
                <button
                  key={key}
                  onClick={() => handleTabChange(key as TabType)}
                  className={`flex items-center gap-2 py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === key
                      ? 'border-green-500 text-green-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  <div className="flex items-center gap-1">
                    {label}
                    {key === 'reconciliation' && reconciliationOrdersTotal > 0 && (
                      <span className="ml-1 bg-green-100 text-green-800 text-xs font-medium px-2 py-0.5 rounded-full">
                        {reconciliationOrdersTotal}
                      </span>
                    )}
                  </div>
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Tab内容 */}
        <div className="space-y-6">
          {renderTabContent()}
        </div>
      </div>

      {/* 对账单查看详情模态框 */}
      {isReconciliationViewModalOpen && viewingReconciliationOrder && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* 背景遮罩 */}
          <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" onClick={() => setIsReconciliationViewModalOpen(false)}></div>

          {/* 弹窗内容 */}
          <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden z-50 mx-4">
            {/* 弹窗头部 */}
            <div className="flex justify-between items-center px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-white">
              <h2 className="text-xl font-semibold text-blue-700 flex items-center">
                <Eye className="h-5 w-5 mr-2" />
                飞机票订单详情
              </h2>
              <button
                onClick={() => setIsReconciliationViewModalOpen(false)}
                className="h-8 w-8 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 flex items-center justify-center transition-colors"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            {/* 弹窗内容 */}
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-180px)]">
              <div className="space-y-8">
                {/* 订单状态区域 */}
                <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                  <div className="flex flex-col gap-3">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-medium text-gray-900">订单状态</h3>
                      <div className="flex items-center">
                        {getOrderStatusDisplay(viewingReconciliationOrder.order_status)}
                      </div>
                    </div>

                    {/* 失败原因展示 */}
                    {(viewingReconciliationOrder.order_status === 'check_failed' || viewingReconciliationOrder.order_status === 'failed') && viewingReconciliationOrder.fail_reason && (
                      <div className="text-red-600 text-sm">
                        {viewingReconciliationOrder.fail_reason}
                      </div>
                    )}
                  </div>
                </div>

                {/* 出行人信息 */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200">出行人信息</h3>
                  <div className="overflow-hidden border border-gray-300 rounded-lg">
                    <table className="w-full">
                      <tbody>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">出行人姓名 <span className="text-red-500">*</span></label>
                          </td>
                          <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                            <span className="text-sm text-gray-900">{viewingReconciliationOrder.traveler_full_name || '-'}</span>
                          </td>
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">出行人姓</label>
                          </td>
                          <td className="py-3 px-4 w-1/4">
                            <span className="text-sm text-gray-900">{viewingReconciliationOrder.traveler_surname || '-'}</span>
                          </td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">出行人名</label>
                          </td>
                          <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                            <span className="text-sm text-gray-900">{viewingReconciliationOrder.traveler_given_name || '-'}</span>
                          </td>
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">国籍</label>
                          </td>
                          <td className="py-3 px-4 w-1/4">
                            <span className="text-sm text-gray-900">{viewingReconciliationOrder.nationality || '-'}</span>
                          </td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">性别</label>
                          </td>
                          <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                            <span className="text-sm text-gray-900">{viewingReconciliationOrder.gender || '-'}</span>
                          </td>
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">出生日期</label>
                          </td>
                          <td className="py-3 px-4 w-1/4">
                            <span className="text-sm text-gray-900">{viewingReconciliationOrder.birth_date || '-'}</span>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>

                {/* 证件信息 */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200">证件信息</h3>
                  <div className="overflow-hidden border border-gray-300 rounded-lg">
                    <table className="w-full">
                      <tbody>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">证件类型</label>
                          </td>
                          <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                            <span className="text-sm text-gray-900">{viewingReconciliationOrder.id_type || '-'}</span>
                          </td>
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">证件号码</label>
                          </td>
                          <td className="py-3 px-4 w-1/4">
                            <span className="text-sm text-gray-900 font-mono">{viewingReconciliationOrder.id_number || '-'}</span>
                          </td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">证件有效期</label>
                          </td>
                          <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                            <span className="text-sm text-gray-900">{viewingReconciliationOrder.id_expiry_date || '-'}</span>
                          </td>
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">手机号</label>
                          </td>
                          <td className="py-3 px-4 w-1/4">
                            <span className="text-sm text-gray-900 font-mono">
                              {viewingReconciliationOrder.mobile_country_code && viewingReconciliationOrder.mobile_phone
                                ? `+${viewingReconciliationOrder.mobile_country_code} ${viewingReconciliationOrder.mobile_phone}`
                                : viewingReconciliationOrder.mobile_phone || '-'
                              }
                            </span>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>

                {/* 航班信息 */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200">航班信息</h3>
                  <div className="overflow-hidden border border-gray-300 rounded-lg">
                    <table className="w-full">
                      <tbody>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">出行日期</label>
                          </td>
                          <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                            <span className="text-sm text-gray-900">{viewingReconciliationOrder.travel_date || '-'}</span>
                          </td>
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">航班号</label>
                          </td>
                          <td className="py-3 px-4 w-1/4">
                            <span className="text-sm text-blue-600 font-medium">{viewingReconciliationOrder.flight_number || '-'}</span>
                          </td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">出发机场</label>
                          </td>
                          <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                            <span className="text-sm text-gray-900">{viewingReconciliationOrder.departure_airport || '-'}</span>
                          </td>
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">到达机场</label>
                          </td>
                          <td className="py-3 px-4 w-1/4">
                            <span className="text-sm text-gray-900">{viewingReconciliationOrder.arrival_airport || '-'}</span>
                          </td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">出发时间</label>
                          </td>
                          <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                            <span className="text-sm text-gray-900">{viewingReconciliationOrder.departure_time || '-'}</span>
                          </td>
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">到达时间</label>
                          </td>
                          <td className="py-3 px-4 w-1/4">
                            <span className="text-sm text-gray-900">{viewingReconciliationOrder.arrival_time || '-'}</span>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>

                {/* 联系人信息 */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200">联系人信息</h3>
                  <div className="overflow-hidden border border-gray-300 rounded-lg">
                    <table className="w-full">
                      <tbody>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">联系人</label>
                          </td>
                          <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                            <span className="text-sm text-gray-900">{viewingReconciliationOrder.contact_person || '-'}</span>
                          </td>
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">联系人手机号</label>
                          </td>
                          <td className="py-3 px-4 w-1/4">
                            <span className="text-sm text-gray-900 font-mono">{viewingReconciliationOrder.contact_mobile_phone || '-'}</span>
                          </td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">联系人邮箱</label>
                          </td>
                          <td className="py-3 px-4 w-3/4">
                            <span className="text-sm text-gray-900">{viewingReconciliationOrder.contact_email || '-'}</span>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>

                {/* 其他信息 */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200">其他信息</h3>
                  <div className="overflow-hidden border border-gray-300 rounded-lg">
                    <table className="w-full">
                      <tbody>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">审批参照人</label>
                          </td>
                          <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                            <span className="text-sm text-gray-900">{viewingReconciliationOrder.approver || '-'}</span>
                          </td>
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">行程提交项</label>
                          </td>
                          <td className="py-3 px-4 w-1/4">
                            <span className="text-sm text-gray-900">{viewingReconciliationOrder.trip_submission_item || '-'}</span>
                          </td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">保险名称</label>
                          </td>
                          <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                            <span className="text-sm text-gray-900">{viewingReconciliationOrder.insurance_name || '-'}</span>
                          </td>
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">出票短信</label>
                          </td>
                          <td className="py-3 px-4 w-1/4">
                            <span className="text-sm text-gray-900">{viewingReconciliationOrder.ticket_sms || '-'}</span>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>

                {/* 对账单信息 */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200">对账单信息</h3>
                  <div className="overflow-hidden border border-gray-300 rounded-lg">
                    <table className="w-full table-fixed">
                      <tbody>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 w-1/6 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">公司名称</label>
                          </td>
                          <td className="py-3 px-4 w-1/3 border-r border-gray-200">
                            <span className="text-sm text-gray-900 break-words">{viewingReconciliationOrder.company_name || '-'}</span>
                          </td>
                          <td className="py-3 px-4 w-1/6 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">代订人</label>
                          </td>
                          <td className="py-3 px-4 w-1/3">
                            <span className="text-sm text-gray-900 break-words">{viewingReconciliationOrder.booking_agent || '-'}</span>
                          </td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 w-1/6 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">金额</label>
                          </td>
                          <td className="py-3 px-4 w-1/3 border-r border-gray-200">
                            <span className="text-sm text-green-600 font-medium">
                              {viewingReconciliationOrder.amount ? formatAmount(viewingReconciliationOrder.amount) : '-'}
                            </span>
                          </td>
                          <td className="py-3 px-4 w-1/6 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">订单号</label>
                          </td>
                          <td className="py-3 px-4 w-1/3">
                            <span className="text-sm text-gray-900 font-mono break-all">{viewingReconciliationOrder.order_number || '-'}</span>
                          </td>
                        </tr>
                        <tr>
                          <td className="py-3 px-4 w-1/6 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">账单号</label>
                          </td>
                          <td className="py-3 px-4 w-5/6">
                            <span className="text-sm text-gray-900 font-mono break-all">{viewingReconciliationOrder.bill_number || '-'}</span>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>

            {/* 弹窗底部 */}
            <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end">
              <Button onClick={() => setIsReconciliationViewModalOpen(false)}>
                关闭
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 对账单编辑模态框 */}
      {isReconciliationEditModalOpen && editingReconciliationOrder && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* 背景遮罩 */}
          <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" onClick={() => !savingReconciliation && setIsReconciliationEditModalOpen(false)}></div>

          {/* 弹窗内容 */}
          <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden z-50 mx-4">
            {/* 弹窗头部 */}
            <div className="flex justify-between items-center px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-green-50 to-white">
              <h2 className="text-xl font-semibold text-green-700 flex items-center">
                <Edit className="h-5 w-5 mr-2" />
                编辑对账单信息
              </h2>
              <button
                onClick={() => !savingReconciliation && setIsReconciliationEditModalOpen(false)}
                disabled={savingReconciliation}
                className="h-8 w-8 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 flex items-center justify-center transition-colors disabled:opacity-50"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            {/* 弹窗内容 */}
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-180px)]">
              <div className="space-y-6">
                {/* 出行人信息（只读） */}
                <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                  <h3 className="text-sm font-medium text-gray-900 mb-3">出行人信息</h3>
                  <div className="overflow-hidden border border-gray-300 rounded-lg bg-white">
                    <table className="w-full">
                      <tbody>
                        <tr className="border-b border-gray-200">
                          <td className="py-2 px-3 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">出行人姓名</label>
                          </td>
                          <td className="py-2 px-3 w-1/4 border-r border-gray-200">
                            <span className="text-sm text-gray-900">{editingReconciliationOrder.traveler_full_name}</span>
                          </td>
                          <td className="py-2 px-3 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">手机号</label>
                          </td>
                          <td className="py-2 px-3 w-1/4">
                            <span className="text-sm text-gray-900">{editingReconciliationOrder.mobile_phone || '-'}</span>
                          </td>
                        </tr>
                        <tr>
                          <td className="py-2 px-3 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">航班号</label>
                          </td>
                          <td className="py-2 px-3 w-1/4 border-r border-gray-200">
                            <span className="text-sm text-blue-600 font-medium">{editingReconciliationOrder.flight_number || '-'}</span>
                          </td>
                          <td className="py-2 px-3 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">出行日期</label>
                          </td>
                          <td className="py-2 px-3 w-1/4">
                            <span className="text-sm text-gray-900">{editingReconciliationOrder.travel_date || '-'}</span>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>

                {/* 对账单信息（可编辑） */}
                <div>
                  <h3 className="text-sm font-medium text-gray-900 mb-3">对账单信息</h3>
                  <div className="overflow-hidden border border-gray-300 rounded-lg">
                    <table className="w-full">
                      <tbody>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">公司名称</label>
                          </td>
                          <td className="py-3 px-4 w-3/4">
                            <input
                              type="text"
                              value={editingReconciliationOrder.company_name || ''}
                              onChange={(e) => setEditingReconciliationOrder({
                                ...editingReconciliationOrder,
                                company_name: e.target.value
                              })}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm"
                              placeholder="请输入公司名称"
                              disabled={savingReconciliation}
                            />
                          </td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">代订人</label>
                          </td>
                          <td className="py-3 px-4 w-3/4">
                            <input
                              type="text"
                              value={editingReconciliationOrder.booking_agent || ''}
                              onChange={(e) => setEditingReconciliationOrder({
                                ...editingReconciliationOrder,
                                booking_agent: e.target.value
                              })}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm"
                              placeholder="请输入代订人"
                              disabled={savingReconciliation}
                            />
                          </td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">金额</label>
                          </td>
                          <td className="py-3 px-4 w-3/4">
                            <input
                              type="text"
                              value={editingReconciliationOrder.amount || ''}
                              onChange={(e) => {
                                setEditingReconciliationOrder({
                                  ...editingReconciliationOrder,
                                  amount: e.target.value
                                });
                                // 清除金额验证错误
                                if (reconciliationValidationErrors.amount) {
                                  setReconciliationValidationErrors({
                                    ...reconciliationValidationErrors,
                                    amount: ''
                                  });
                                }
                              }}
                              className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm ${
                                reconciliationValidationErrors.amount
                                  ? 'border-red-300 bg-red-50'
                                  : 'border-gray-300'
                              }`}
                              placeholder="请输入金额"
                              disabled={savingReconciliation}
                            />
                            {reconciliationValidationErrors.amount && (
                              <p className="mt-1 text-sm text-red-600">{reconciliationValidationErrors.amount}</p>
                            )}
                          </td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">订单号</label>
                          </td>
                          <td className="py-3 px-4 w-3/4">
                            <input
                              type="text"
                              value={editingReconciliationOrder.order_number || ''}
                              onChange={(e) => setEditingReconciliationOrder({
                                ...editingReconciliationOrder,
                                order_number: e.target.value
                              })}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm font-mono"
                              placeholder="请输入订单号"
                              disabled={savingReconciliation}
                            />
                          </td>
                        </tr>
                        <tr>
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">账单号</label>
                          </td>
                          <td className="py-3 px-4 w-3/4">
                            <input
                              type="text"
                              value={editingReconciliationOrder.bill_number || ''}
                              onChange={(e) => setEditingReconciliationOrder({
                                ...editingReconciliationOrder,
                                bill_number: e.target.value
                              })}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm font-mono"
                              placeholder="请输入账单号"
                              disabled={savingReconciliation}
                            />
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>

            {/* 弹窗底部 */}
            <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => setIsReconciliationEditModalOpen(false)}
                disabled={savingReconciliation}
              >
                取消
              </Button>
              <Button
                onClick={saveReconciliationOrder}
                disabled={savingReconciliation}
                className="bg-green-600 hover:bg-green-700"
              >
                {savingReconciliation ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    保存中...
                  </>
                ) : (
                  '保存'
                )}
              </Button>
            </div>
          </div>
        </div>
      )}
    </MainLayout>
  );
};

export default FlightBookingPage;
