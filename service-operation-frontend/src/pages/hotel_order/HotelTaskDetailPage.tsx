import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Pagination } from '@/components/ui/pagination';
import MainLayout from '@/components/layout/MainLayout';
import { 
  ArrowLeft, 
  Search,
  X,
  Download,
  Eye,
  FileText,
  AlertTriangle,
  Building,
  Loader
} from 'lucide-react';
import * as XLSX from 'xlsx-js-style';
import { saveAs } from 'file-saver';
import { hotelOrderApi, HotelOrder } from '@/api/hotel';
import { ProjectTaskService } from '@/services/project/projectTaskService';
import { ProjectTask } from '@/types/project-task';
import { useToast } from '@/hooks/use-toast';
import { formatAmount } from '@/utils/formatters';
import HotelOrderDetailModal from '@/components/booking/HotelOrderDetailModal';
import FailureDetailsModal from '@/components/order/FailureDetailsModal';

const HotelTaskDetailPage: React.FC = () => {
  const { taskId } = useParams<{ taskId: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const [task, setTask] = useState<ProjectTask | null>(null);
  const [orders, setOrders] = useState<HotelOrder[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [pageSize] = useState(20);
  const [total, setTotal] = useState(0);

  // 搜索相关状态
  const [searchGuestName, setSearchGuestName] = useState('');
  const [searchMobilePhone, setSearchMobilePhone] = useState('');
  const [searchContactPhone, setSearchContactPhone] = useState('');

  // 订单操作状态
  const [selectedOrder, setSelectedOrder] = useState<HotelOrder | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);

  // 失败详情相关状态
  const [selectedFailedOrder, setSelectedFailedOrder] = useState<HotelOrder | null>(null);
  const [isFailureModalOpen, setIsFailureModalOpen] = useState(false);

  // 获取任务信息
  useEffect(() => {
    const fetchTaskInfo = async () => {
      if (!taskId) return;
      
      try {
        const taskData = await ProjectTaskService.getTaskByTaskId(taskId);
        setTask(taskData);
      } catch (error) {
        console.error('获取任务信息失败:', error);
        setError('无法获取任务信息');
      }
    };

    fetchTaskInfo();
  }, [taskId]);

  // 加载订单数据
  const loadOrders = async (page: number = 1) => {
    if (!taskId) return;
    
    setLoading(true);
    try {
      const response = await hotelOrderApi.getTaskOrders(
        taskId,
        page,
        pageSize,
        undefined, // status
        searchGuestName?.trim() || undefined,
        searchMobilePhone?.trim() || undefined,
        searchContactPhone?.trim() || undefined
      );
      
      setOrders(response.items);
      setTotal(response.total);
      setPage(page);
    } catch (error) {
      console.error('加载订单失败:', error);
      toast({
        title: "加载失败",
        description: "无法加载任务订单列表",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // 初始加载
  useEffect(() => {
    loadOrders(1);
  }, [taskId, searchGuestName, searchMobilePhone, searchContactPhone]);

  // 搜索处理
  const handleSearch = () => {
    loadOrders(1);
  };

  const handleClearSearch = () => {
    setSearchGuestName('');
    setSearchMobilePhone('');
    setSearchContactPhone('');
    loadOrders(1);
  };

  // 格式化日期时间
  const formatDateTime = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return dateString;
    }
  };

  // 获取订单状态文本
  const getOrderStatusText = (status: string) => {
    const statusMap = {
      'initial': '待预订',
      'submitted': '已提交',
      'processing': '处理中',
      'completed': '已完成',
      'failed': '失败',
      'check_failed': '验证失败'
    };
    return statusMap[status as keyof typeof statusMap] || status;
  };

  // 获取任务状态文本和样式
  const getTaskStatusDisplay = (status: string) => {
    const statusConfig = {
      'submitted': { bg: 'bg-blue-100', text: 'text-blue-800', label: '已提交' },
      'processing': { bg: 'bg-yellow-100', text: 'text-yellow-800', label: '处理中' },
      'completed': { bg: 'bg-green-100', text: 'text-green-800', label: '已完成' },
      'failed': { bg: 'bg-red-100', text: 'text-red-800', label: '失败' }
    };
    
    const config = statusConfig[status as keyof typeof statusConfig];
    return config || { bg: 'bg-gray-100', text: 'text-gray-800', label: status };
  };

  // 获取订单状态显示样式
  const getOrderStatusDisplay = (status: string) => {
    const statusConfig = {
      'initial': { bg: 'bg-gray-100', text: 'text-gray-800', label: '待预订' },
      'submitted': { bg: 'bg-blue-100', text: 'text-blue-800', label: '已提交' },
      'processing': { bg: 'bg-yellow-100', text: 'text-yellow-800', label: '处理中' },
      'completed': { bg: 'bg-green-100', text: 'text-green-800', label: '已完成' },
      'failed': { bg: 'bg-red-100', text: 'text-red-800', label: '失败' },
      'check_failed': { bg: 'bg-amber-100', text: 'text-amber-800', label: '验证失败' }
    };
    
    const config = statusConfig[status as keyof typeof statusConfig];
    return config || { bg: 'bg-gray-100', text: 'text-gray-800', label: status };
  };

  // 订单操作处理
  const handleViewOrder = (order: HotelOrder) => {
    setSelectedOrder(order);
    setIsViewModalOpen(true);
  };

  // 导出Excel功能
  const exportToExcel = () => {
    if (orders.length === 0) {
      toast({
        title: "导出失败",
        description: "没有订单数据可导出",
        variant: "destructive",
      });
      return;
    }

    try {
      // 准备导出数据
      const exportData = orders.map((order, index) => ({
        '序号': index + 1,
        '订单状态': getOrderStatusText(order.order_status),
        '入住人姓名': order.guest_full_name || '-',
        '入住人姓': order.guest_surname || '-',
        '入住人名': order.guest_given_name || '-',
        '国籍': order.guest_nationality || '-',
        '性别': order.guest_gender || '-',
        '出生日期': order.guest_birth_date || '-',
        '证件类型': order.guest_id_type || '-',
        '证件号码': order.guest_id_number || '-',
        '手机号': order.guest_mobile_phone || '-',
        '邮箱': order.guest_email || '-',
        '目的地': order.destination || '-',
        '酒店名称': order.hotel_name || '-',
        '房型': order.room_type || '-',
        '房间数量': order.room_count || '-',
        '入住时间': order.check_in_time || '-',
        '离店时间': order.check_out_time || '-',
        '联系人': order.contact_person || '-',
        '联系人手机': order.contact_mobile_phone || '-',
        '成本中心': order.cost_center || '-',
        '审批人': order.approver || '-',
        '金额': order.amount || 0,
        '订单号': order.order_number || '-',
        '失败原因': order.fail_reason || '-',
        '创建时间': formatDateTime(order.created_at),
        '更新时间': formatDateTime(order.updated_at)
      }));

      // 创建工作簿
      const wb = XLSX.utils.book_new();
      const ws = XLSX.utils.json_to_sheet(exportData);
      
      // 设置列宽
      const colWidths = Object.keys(exportData[0] || {}).map(() => ({ wch: 15 }));
      ws['!cols'] = colWidths;

      XLSX.utils.book_append_sheet(wb, ws, '酒店订单');
      
      // 生成文件名
      const filename = `酒店任务订单_${task?.task_title || taskId}_${new Date().toLocaleDateString('zh-CN').replace(/\//g, '-')}.xlsx`;
      const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
      const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      
      saveAs(blob, filename);
      
      toast({
        title: "导出成功",
        description: "Excel文件已成功导出",
        variant: "default",
      });
      
    } catch (error) {
      console.error('导出Excel失败:', error);
      toast({
        title: "导出失败",
        description: "导出Excel文件时发生错误，请重试",
        variant: "destructive",
      });
    }
  };

  // 处理失败详情点击
  const handleFailureDetailsClick = (order: HotelOrder) => {
    setSelectedFailedOrder(order);
    setIsFailureModalOpen(true);
  };

  if (error) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">加载失败</h2>
            <p className="text-gray-600 mb-4">{error}</p>
            <Button onClick={() => navigate(-1)}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回
            </Button>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="p-4 bg-gray-50 min-h-screen">
        {/* 页面头部 */}
        <div className="mb-4">
          <Card className="bg-white shadow-sm border border-gray-200 border-l-4 border-l-blue-500 p-4">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-2">
                  <div className="p-2 bg-blue-50 rounded-lg">
                    <Building className="h-5 w-5 text-blue-600" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <h1 className="text-xl font-bold text-gray-900">
                        {task?.task_title || `任务 ${taskId}`}
                      </h1>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => navigate(-1)}
                        className="flex items-center gap-2"
                      >
                        <ArrowLeft className="h-4 w-4" />
                        返回
                      </Button>
                    </div>
                    <p className="text-gray-600">酒店预订任务订单详情</p>
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-4">
                  <div>
                    <p className="text-sm text-gray-500">任务ID</p>
                    <p className="font-medium">{taskId}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">任务类型</p>
                    <p className="font-medium">{task?.task_type}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">任务状态</p>
                    {task?.task_status ? (
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getTaskStatusDisplay(task.task_status).bg} ${getTaskStatusDisplay(task.task_status).text}`}>
                        {getTaskStatusDisplay(task.task_status).label}
                      </span>
                    ) : (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        未知状态
                      </span>
                    )}
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">订单总数</p>
                    <p className="font-medium text-lg">{total}</p>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </div>

        {/* 搜索和筛选 */}
        <Card className="bg-white shadow-sm border border-gray-200 mb-6">
          <div className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  入住人姓名
                </label>
                <Input
                  placeholder="请输入入住人姓名"
                  value={searchGuestName}
                  onChange={(e) => setSearchGuestName(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  手机号码
                </label>
                <Input
                  placeholder="请输入手机号码"
                  value={searchMobilePhone}
                  onChange={(e) => setSearchMobilePhone(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  联系人手机
                </label>
                <Input
                  placeholder="请输入联系人手机号"
                  value={searchContactPhone}
                  onChange={(e) => setSearchContactPhone(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                />
              </div>
              <div className="flex items-end">
                <Button onClick={handleSearch} className="w-full">
                  <Search className="h-4 w-4 mr-2" />
                  搜索
                </Button>
              </div>
              <div className="flex items-end">
                <Button variant="outline" onClick={handleClearSearch} className="w-full">
                  <X className="h-4 w-4 mr-2" />
                  重置
                </Button>
              </div>
            </div>
          </div>
        </Card>

        {/* 订单列表 */}
        <Card className="bg-white shadow-sm border border-gray-200">
          <div className="p-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">订单列表</h3>
              <div className="flex items-center gap-3">
                <span className="text-sm text-gray-500">
                  共 {total} 条记录
                </span>
                <Button
                  variant="outline"
                  onClick={exportToExcel}
                  disabled={orders.length === 0}
                  size="sm"
                >
                  <Download className="h-4 w-4 mr-2" />
                  导出
                </Button>
              </div>
            </div>

            {loading ? (
              <div className="text-center py-12">
                <Loader className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
                <p className="text-gray-600">加载订单数据中...</p>
              </div>
            ) : orders.length === 0 ? (
              <div className="text-center py-12">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">暂无订单</h3>
                <p className="text-gray-600">该任务还没有关联的订单</p>
              </div>
            ) : (
              <>
                <div className="overflow-x-auto custom-scrollbar" style={{ 
                  scrollbarWidth: 'auto', 
                  scrollbarColor: '#cbd5e1 #f1f5f9'
                }}>
                  <style dangerouslySetInnerHTML={{
                    __html: `
                      .custom-scrollbar::-webkit-scrollbar {
                        height: 12px;
                        background-color: #f1f5f9;
                      }
                      .custom-scrollbar::-webkit-scrollbar-track {
                        background-color: #f1f5f9;
                        border-radius: 6px;
                      }
                      .custom-scrollbar::-webkit-scrollbar-thumb {
                        background-color: #cbd5e1;
                        border-radius: 6px;
                        border: 2px solid #f1f5f9;
                      }
                      .custom-scrollbar::-webkit-scrollbar-thumb:hover {
                        background-color: #94a3b8;
                      }
                    `
                  }} />
                  <table className="w-full text-xs" style={{ minWidth: '3000px' }}>
                    <thead className="bg-gray-50 border-b border-gray-200">
                      <tr>
                        <th className="text-center p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '60px' }}>序号</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>状态</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>入住人姓名</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>入住人姓</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>入住人名</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>国籍</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '60px' }}>性别</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>出生日期</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>证件类型</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '150px' }}>证件号码</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>手机号</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '150px' }}>邮箱</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>目的地</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>酒店ID</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '150px' }}>酒店名称</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>房型</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>房间数量</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>入住时间</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>离店时间</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>团队预订</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>团队预订名称</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>联系人</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>联系人手机</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>成本中心</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>审批人</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>金额</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>订单号</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>账单号</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '150px' }}>失败原因</th>
                        <th className="text-center p-2 font-medium text-gray-900 text-xs whitespace-nowrap sticky right-0 bg-gray-50 border-l border-gray-200" style={{ minWidth: '120px' }}>操作</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {orders.map((order, index) => {
                        const statusDisplay = getOrderStatusDisplay(order.order_status);
                        return (
                          <tr key={order.id} className="hover:bg-gray-50">
                            <td className="p-2 text-center text-gray-600">{index + 1}</td>
                            <td className="p-2">
                              {order.order_status === 'failed' ? (
                                <button
                                  onClick={() => handleFailureDetailsClick(order)}
                                  className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${statusDisplay.bg} ${statusDisplay.text} hover:bg-red-200 transition-colors cursor-pointer underline`}
                                  title="点击查看失败详情"
                                >
                                  {statusDisplay.label}
                                </button>
                              ) : (
                                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${statusDisplay.bg} ${statusDisplay.text}`}>
                                  {statusDisplay.label}
                                </span>
                              )}
                            </td>
                            <td className="p-2 font-medium text-gray-900">{order.guest_full_name || '-'}</td>
                            <td className="p-2 text-gray-900">{order.guest_surname || '-'}</td>
                            <td className="p-2 text-gray-900">{order.guest_given_name || '-'}</td>
                            <td className="p-2 text-gray-900">{order.guest_nationality || '-'}</td>
                            <td className="p-2 text-gray-900">{order.guest_gender || '-'}</td>
                            <td className="p-2 text-gray-900">{order.guest_birth_date || '-'}</td>
                            <td className="p-2 text-gray-900">{order.guest_id_type || '-'}</td>
                            <td className="p-2 text-gray-900">{order.guest_id_number || '-'}</td>
                            <td className="p-2 text-gray-900">{order.guest_mobile_phone || '-'}</td>
                            <td className="p-2 text-gray-900">{order.guest_email || '-'}</td>
                            <td className="p-2 text-gray-900">{order.destination || '-'}</td>
                            <td className="p-2 text-gray-900">{order.hotel_id || '-'}</td>
                            <td className="p-2 text-gray-900">{order.hotel_name || '-'}</td>
                            <td className="p-2 text-gray-900">{order.room_type || '-'}</td>
                            <td className="p-2 text-gray-900">{order.room_count || '-'}</td>
                            <td className="p-2 text-gray-900">{order.check_in_time || '-'}</td>
                            <td className="p-2 text-gray-900">{order.check_out_time || '-'}</td>
                            <td className="p-2 text-gray-900">{order.is_group_booking ? '是' : '否'}</td>
                            <td className="p-2 text-gray-900">{order.group_booking_name || '-'}</td>
                            <td className="p-2 text-gray-900">{order.contact_person || '-'}</td>
                            <td className="p-2 text-gray-900">{order.contact_mobile_phone || '-'}</td>
                            <td className="p-2 text-gray-900">{order.cost_center || '-'}</td>
                            <td className="p-2 text-gray-900">{order.approver || '-'}</td>
                            <td className="p-2 text-gray-900">
                              {order.amount ? formatAmount(order.amount) : '-'}
                            </td>
                            <td className="p-2 text-gray-900">{order.order_number || '-'}</td>
                            <td className="p-2 text-gray-900">{order.bill_number || '-'}</td>
                            <td className="p-2">
                              <span className={order.fail_reason ? 'text-red-600' : 'text-gray-500'}>
                                {order.fail_reason || '-'}
                              </span>
                            </td>
                            <td className="p-2 text-center sticky right-0 bg-white border-l border-gray-200">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleViewOrder(order)}
                                title="查看详情"
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>

                {/* 分页 */}
                {total > pageSize && (
                  <div className="mt-4 flex justify-center">
                    <Pagination
                      currentPage={page}
                      totalPages={Math.ceil(total / pageSize)}
                      totalItems={total}
                      pageSize={pageSize}
                      onPageChange={(newPage) => loadOrders(newPage)}
                    />
                  </div>
                )}
              </>
            )}
          </div>
        </Card>

        {/* 订单详情模态框 */}
        <HotelOrderDetailModal
          order={selectedOrder}
          isOpen={isViewModalOpen}
          onClose={() => {
            setSelectedOrder(null);
            setIsViewModalOpen(false);
          }}
          onOrderUpdated={() => {
            // 订单更新后重新加载数据
            loadOrders(page);
          }}
        />

        {/* 失败详情模态框 */}
        {selectedFailedOrder && (
          <FailureDetailsModal
            isOpen={isFailureModalOpen}
            onClose={() => setIsFailureModalOpen(false)}
            orderId={selectedFailedOrder.id.toString()}
            taskId={taskId}
            failReason={selectedFailedOrder.fail_reason || undefined}
          />
        )}
      </div>
    </MainLayout>
  );
};

export default HotelTaskDetailPage; 