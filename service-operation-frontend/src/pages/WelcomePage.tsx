import React from 'react';
import { useAuth } from '@/store/auth-context';
import { usePermissions } from '@/hooks/usePermissions';
import { Card } from '@/components/ui/card';
import { User, Shield, Home } from 'lucide-react';

/**
 * 欢迎页面
 * 不需要特定权限，所有已认证用户都可以访问
 */
const WelcomePage: React.FC = () => {
  const { user } = useAuth();
  const { permissions, loading } = usePermissions();

  return (
    <div className="p-6 space-y-6">
      {/* 欢迎信息 */}
      <div className="text-center">
        <div className="flex items-center justify-center gap-3 mb-4">
          <Home className="h-8 w-8 text-blue-600" />
          <h1 className="text-3xl font-bold text-gray-900">欢迎使用 DTTrip 服务运营平台</h1>
        </div>
        <p className="text-gray-600">您已成功登录系统</p>
      </div>

      {/* 用户信息卡片 */}
      <Card className="p-6">
        <div className="flex items-center gap-3 mb-4">
          <User className="h-5 w-5 text-blue-600" />
          <h2 className="text-lg font-semibold text-gray-900">用户信息</h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="text-sm font-medium text-gray-500">用户名</label>
            <p className="text-gray-900">{user?.username}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-500">邮箱</label>
            <p className="text-gray-900">{user?.email}</p>
          </div>
          {user?.department && (
            <div>
              <label className="text-sm font-medium text-gray-500">部门</label>
              <p className="text-gray-900">{user.department}</p>
            </div>
          )}
          {user?.work_id && (
            <div>
              <label className="text-sm font-medium text-gray-500">工号</label>
              <p className="text-gray-900">{user.work_id}</p>
            </div>
          )}
        </div>
      </Card>

      {/* 权限状态 */}
      <Card className="p-6">
        <div className="flex items-center gap-3 mb-4">
          <Shield className="h-5 w-5 text-blue-600" />
          <h2 className="text-lg font-semibold text-gray-900">权限状态</h2>
        </div>
        {loading ? (
          <div className="text-center py-4">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto mb-2"></div>
            <p className="text-gray-600">加载权限信息中...</p>
          </div>
        ) : (
          <div>
            <p className="text-gray-700 mb-3">
              您当前拥有 <span className="font-semibold text-blue-600">{permissions.length}</span> 个菜单权限
            </p>
            {permissions.length > 0 ? (
              <div className="space-y-2">
                <p className="text-sm text-gray-600">可访问的功能模块：</p>
                <div className="flex flex-wrap gap-2">
                  {permissions.map((permission) => (
                    <span
                      key={permission.id}
                      className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
                    >
                      {permission.permission_name}
                    </span>
                  ))}
                </div>
              </div>
            ) : (
              <div className="text-center py-4">
                <p className="text-gray-500">暂无可访问的功能模块</p>
                <p className="text-sm text-gray-400 mt-1">请联系管理员分配相应权限</p>
              </div>
            )}
          </div>
        )}
      </Card>

      {/* 快速导航 */}
      {permissions.length > 0 && (
        <Card className="p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">快速导航</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            {permissions.map((permission) => (
              <a
                key={permission.id}
                href={permission.resource_path}
                className="block p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <div className="font-medium text-gray-900">{permission.permission_name}</div>
                <div className="text-sm text-gray-600">{permission.description}</div>
              </a>
            ))}
          </div>
        </Card>
      )}
    </div>
  );
};

export default WelcomePage;
