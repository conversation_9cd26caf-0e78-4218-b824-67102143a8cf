import React from 'react';
import { useAuth } from '@/store/auth-context';
import { usePermissions, hasPermission } from '@/hooks/usePermissions';
import { Shield, User, CheckCircle, XCircle, Eye, Settings } from 'lucide-react';
import { Card } from '@/components/ui/card';

/**
 * 权限测试页面
 * 用于测试和展示权限系统的工作状态
 */
const PermissionTestPage: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const { permissions, loading, error } = usePermissions();

  const testPermissions = [
    { code: 'dttrip:dashboard', name: '应用中心', path: '/dashboard' },
    { code: 'dttrip:projects', name: '团房团票', path: '/projects' },
    { code: 'dttrip:passport', name: '护照识别', path: '/passport-recognition' },
    { code: 'user_mgmt:access', name: '用户管理', path: '/user-management' },
    { code: 'dttrip:settings', name: '系统设置', path: '/settings' },
  ];

  if (!isAuthenticated) {
    return (
      <div className="p-6">
        <Card className="p-6 text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">权限测试页面</h1>
          <p className="text-gray-600">请先登录以查看权限信息</p>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center gap-3">
        <Shield className="h-8 w-8 text-blue-600" />
        <div>
          <h1 className="text-2xl font-bold text-gray-900">权限系统测试</h1>
          <p className="text-gray-600">查看当前用户的权限状态和菜单访问权限</p>
        </div>
      </div>

      {/* 用户信息卡片 */}
      <Card className="p-6">
        <div className="flex items-center gap-3 mb-4">
          <User className="h-5 w-5 text-blue-600" />
          <h2 className="text-lg font-semibold text-gray-900">用户信息</h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div>
            <label className="text-sm font-medium text-gray-500">用户ID</label>
            <p className="text-sm text-gray-900">{user?.id}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-500">用户名</label>
            <p className="text-sm text-gray-900">{user?.username}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-500">邮箱</label>
            <p className="text-sm text-gray-900">{user?.email}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-500">角色</label>
            <p className="text-sm text-gray-900">{user?.role}</p>
          </div>
        </div>
      </Card>

      {/* 权限状态卡片 */}
      <Card className="p-6">
        <div className="flex items-center gap-3 mb-4">
          <Settings className="h-5 w-5 text-blue-600" />
          <h2 className="text-lg font-semibold text-gray-900">权限状态</h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-blue-50 rounded-lg p-4">
            <div className="text-2xl font-bold text-blue-600">{permissions.length}</div>
            <div className="text-sm text-blue-800">有效权限数量</div>
          </div>
          <div className={`rounded-lg p-4 ${loading ? 'bg-yellow-50' : 'bg-green-50'}`}>
            <div className={`text-2xl font-bold ${loading ? 'text-yellow-600' : 'text-green-600'}`}>
              {loading ? '加载中' : '已加载'}
            </div>
            <div className={`text-sm ${loading ? 'text-yellow-800' : 'text-green-800'}`}>
              权限加载状态
            </div>
          </div>
          <div className={`rounded-lg p-4 ${error ? 'bg-red-50' : 'bg-green-50'}`}>
            <div className={`text-2xl font-bold ${error ? 'text-red-600' : 'text-green-600'}`}>
              {error ? '错误' : '正常'}
            </div>
            <div className={`text-sm ${error ? 'text-red-800' : 'text-green-800'}`}>
              系统状态
            </div>
          </div>
        </div>
        {error && (
          <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-sm text-red-700">错误信息: {error}</p>
          </div>
        )}
      </Card>

      {/* 菜单权限测试 */}
      <Card className="p-6">
        <div className="flex items-center gap-3 mb-4">
          <Eye className="h-5 w-5 text-blue-600" />
          <h2 className="text-lg font-semibold text-gray-900">菜单权限测试</h2>
        </div>
        <div className="space-y-3">
          {testPermissions.map((testPerm) => {
            const hasAccess = hasPermission(testPerm.code, permissions);
            return (
              <div
                key={testPerm.code}
                className={`flex items-center justify-between p-4 rounded-lg border ${
                  hasAccess
                    ? 'bg-green-50 border-green-200'
                    : 'bg-red-50 border-red-200'
                }`}
              >
                <div className="flex items-center gap-3">
                  {hasAccess ? (
                    <CheckCircle className="h-5 w-5 text-green-600" />
                  ) : (
                    <XCircle className="h-5 w-5 text-red-600" />
                  )}
                  <div>
                    <div className={`font-medium ${hasAccess ? 'text-green-900' : 'text-red-900'}`}>
                      {testPerm.name}
                    </div>
                    <div className={`text-sm ${hasAccess ? 'text-green-700' : 'text-red-700'}`}>
                      {testPerm.code} → {testPerm.path}
                    </div>
                  </div>
                </div>
                <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                  hasAccess
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                }`}>
                  {hasAccess ? '有权限' : '无权限'}
                </div>
              </div>
            );
          })}
        </div>
      </Card>

      {/* 权限详情 */}
      <Card className="p-6">
        <div className="flex items-center gap-3 mb-4">
          <Shield className="h-5 w-5 text-blue-600" />
          <h2 className="text-lg font-semibold text-gray-900">权限详情</h2>
        </div>
        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
            <p className="text-gray-600">加载权限信息中...</p>
          </div>
        ) : permissions.length === 0 ? (
          <div className="text-center py-8">
            <XCircle className="h-12 w-12 text-gray-400 mx-auto mb-2" />
            <p className="text-gray-600">当前用户没有任何权限</p>
          </div>
        ) : (
          <div className="space-y-3">
            {permissions.map((permission) => (
              <div
                key={permission.id}
                className="bg-blue-50 border border-blue-200 rounded-lg p-4"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium text-blue-900">{permission.permission_name}</div>
                    <div className="text-sm text-blue-700">{permission.permission_code}</div>
                    <div className="text-sm text-blue-600">{permission.resource_path}</div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm text-blue-800">ID: {permission.id}</div>
                    <div className="text-sm text-blue-700">排序: {permission.sort_order}</div>
                  </div>
                </div>
                {permission.description && (
                  <div className="mt-2 text-sm text-blue-600">{permission.description}</div>
                )}
              </div>
            ))}
          </div>
        )}
      </Card>
    </div>
  );
};

export default PermissionTestPage;
