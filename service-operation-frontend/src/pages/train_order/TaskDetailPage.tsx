import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useSearchParams } from 'react-router-dom';
import { ArrowLeft, FileText, Download, RefreshCw, Copy, Check } from 'lucide-react';
import { PassportImage } from '@/components/PassportImage';
import MainLayout from '@/components/layout/MainLayout';
import * as XLSX from 'xlsx-js-style';
import { saveAs } from 'file-saver';
import { Pagination } from '@/components/ui/pagination';
import { API_BASE_URL } from '@/utils/constants';
import { passportApi, TaskStatsResponse } from '@/api/passport';

interface PassportRecord {
  id: number;
  task_id: string;
  uploaded_image_url: string;
  // 按指定顺序的字段
  certificate_type: string;
  certificate_number: string;
  surname: string;
  given_names: string;
  sex: string;
  date_of_birth: string;
  nationality: string;
  passenger_type: string;
  country_of_issue: string;
  date_of_issue: string;
  date_of_expiry: string;
  ssr_code: string;
  mrz_line1: string;
  mrz_line2: string;
  viz_mrz_consistency: string;
  // 保留的原有字段
  passport_number: string; // 兼容性保留
  place_of_birth: string;
  authority: string;
  processing_status: string;
  created_at: string;
  updated_at: string;
}

interface APIResponse {
  total: number;
  items: PassportRecord[];
  page: number;
  size: number;
}

const TaskDetailPage: React.FC = () => {
  const { taskId } = useParams<{ taskId: string }>();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [loading, setLoading] = useState(true);
  const [passports, setPassports] = useState<PassportRecord[]>([]);
  const [error, setError] = useState<string>('');
  const [refreshing, setRefreshing] = useState(false);
  const [copiedRowId, setCopiedRowId] = useState<number | null>(null);
  
  // 添加分页相关状态
  const [currentPage, setCurrentPage] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [pageSize] = useState(10);
  
  // 添加统计数据状态
  const [taskStats, setTaskStats] = useState<TaskStatsResponse | null>(null);
  const [statsLoading, setStatsLoading] = useState(false);

  useEffect(() => {
    if (taskId) {
      loadTaskPassports();
      loadTaskStats();
    }
  }, [taskId, currentPage]); // 添加currentPage依赖

  // 加载任务统计数据
  const loadTaskStats = async () => {
    if (!taskId) return;
    
    try {
      setStatsLoading(true);
      const response = await passportApi.getTaskStats(taskId);
      setTaskStats(response.data);
    } catch (error) {
      console.error('加载任务统计失败:', error);
      // 统计加载失败不影响主要功能，只记录错误
    } finally {
      setStatsLoading(false);
    }
  };

  const loadTaskPassports = async () => {
    try {
      setLoading(true);
      setError('');
      
      const token = localStorage.getItem('access_token');
      if (!token) {
        setError('请先登录');
        return;
      }

      const response = await fetch(`${API_BASE_URL}/passport/task/${taskId}?page=${currentPage}&size=${pageSize}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('获取任务详情失败');
      }

      const data: APIResponse = await response.json();
      console.log('任务详情数据:', data);
      setPassports(data.items || []);
      setTotalItems(data.total || 0);
    } catch (error) {
      console.error('加载任务详情失败:', error);
      setError(error instanceof Error ? error.message : '加载失败');
    } finally {
      setLoading(false);
    }
  };

  // 刷新任务数据
  const refreshTaskData = async () => {
    try {
      setRefreshing(true);
      
      // 显示加载状态
      const loadingToast = document.createElement('div');
      loadingToast.className = 'fixed top-4 right-4 bg-blue-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 flex items-center gap-2';
      loadingToast.innerHTML = `
        <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
        <span>正在刷新任务数据...</span>
      `;
      document.body.appendChild(loadingToast);

      await loadTaskPassports();
      await loadTaskStats();
      
      // 移除加载提示
      document.body.removeChild(loadingToast);
      
      // 显示成功提示
      const successToast = document.createElement('div');
      successToast.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 flex items-center gap-2';
      successToast.innerHTML = `
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
        <span>任务数据已刷新</span>
      `;
      document.body.appendChild(successToast);
      
      setTimeout(() => {
        successToast.style.transform = 'translateX(100%)';
        successToast.style.opacity = '0';
        setTimeout(() => document.body.removeChild(successToast), 300);
      }, 2000);
      
    } catch (error) {
      // 移除可能存在的加载提示
      const loadingToast = document.querySelector('.fixed.top-4.right-4.bg-blue-500');
      if (loadingToast) {
        document.body.removeChild(loadingToast);
      }
      
      console.error('刷新任务数据失败:', error);
      
      // 显示错误提示
      const errorToast = document.createElement('div');
      errorToast.className = 'fixed top-4 right-4 bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 flex items-center gap-2';
      errorToast.innerHTML = `
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
        <span>刷新失败，请重试</span>
      `;
      document.body.appendChild(errorToast);
      
      setTimeout(() => {
        errorToast.style.transform = 'translateX(100%)';
        errorToast.style.opacity = '0';
        setTimeout(() => document.body.removeChild(errorToast), 300);
      }, 2000);
    } finally {
      setRefreshing(false);
    }
  };

  // 检查任务是否完成

  // 复制护照信息到剪切板
  const copyPassportInfo = async (passport: PassportRecord) => {
    try {
      const formatValue = (value: string | undefined) => value || '-';
      
      const formattedText = [
        `证件类型：${formatValue(passport.certificate_type)}`,
        `证件号码：${formatValue(passport.certificate_number || passport.passport_number)}`,
        `姓氏：${formatValue(passport.surname)}`,
        `名字：${formatValue(passport.given_names)}`,
        `性别：${formatSex(passport.sex)}`,
        `出生日期：${formatPassportDate(passport.date_of_birth)}`,
        `国籍：${formatValue(passport.nationality)}`,
        `旅客类型：${formatValue(passport.passenger_type)}`,
        `签发国：${formatValue(passport.country_of_issue)}`,
        `签发日期：${formatPassportDate(passport.date_of_issue)}`,
        `有效期至：${formatPassportDate(passport.date_of_expiry)}`,
        `SSR DOCS码：${formatValue(passport.ssr_code)}`,
        `MRZ第一行：${formatValue(passport.mrz_line1)}`,
        `MRZ第二行：${formatValue(passport.mrz_line2)}`,
        `一致性检查：${formatConsistency(passport.viz_mrz_consistency).text}`,
        `处理状态：${getStatusText(passport.processing_status)}`,
        `任务ID：${passport.task_id}`,
        `创建时间：${formatDate(passport.created_at)}`
      ].join('\n');

      await navigator.clipboard.writeText(formattedText);
      setCopiedRowId(passport.id);
      
      // 显示成功提示
      const toast = document.createElement('div');
      toast.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 flex items-center gap-2';
      toast.innerHTML = `
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
        <span>护照信息已复制到剪贴板</span>
      `;
      document.body.appendChild(toast);
      
      setTimeout(() => {
        toast.style.transform = 'translateX(100%)';
        toast.style.opacity = '0';
        setTimeout(() => document.body.removeChild(toast), 300);
      }, 2000);
      
      // 2秒后重置复制状态
      setTimeout(() => setCopiedRowId(null), 2000);
      
    } catch (error) {
      console.error('复制失败:', error);
      
      // 显示错误提示
      const toast = document.createElement('div');
      toast.className = 'fixed top-4 right-4 bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 flex items-center gap-2';
      toast.innerHTML = `
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
        <span>复制失败，请重试</span>
      `;
      document.body.appendChild(toast);
      
      setTimeout(() => {
        toast.style.transform = 'translateX(100%)';
        toast.style.opacity = '0';
        setTimeout(() => document.body.removeChild(toast), 300);
      }, 3000);
    }
  };

  // 复制单个字段值到剪切板
  const copyFieldValue = async (value: string | undefined, fieldName: string) => {
    if (!value || value === '-') {
      return; // 不复制空值或"-"
    }

    try {
      await navigator.clipboard.writeText(value);
      
      // 显示成功提示
      const toast = document.createElement('div');
      toast.className = 'fixed top-4 right-4 bg-blue-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 flex items-center gap-2';
      toast.innerHTML = `
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
        <span>${fieldName}已复制：${value}</span>
      `;
      document.body.appendChild(toast);
      
      setTimeout(() => {
        toast.style.transform = 'translateX(100%)';
        toast.style.opacity = '0';
        setTimeout(() => document.body.removeChild(toast), 300);
      }, 1500);
      
    } catch (error) {
      console.error('复制字段值失败:', error);
    }
  };

  const getStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
      'pending': '待处理',
      'processing': '处理中',
      'completed': '已完成',
      'failed': '处理失败'
    };
    return statusMap[status] || status;
  };

  const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      'pending': 'text-yellow-600 bg-yellow-50',
      'processing': 'text-blue-600 bg-blue-50',
      'completed': 'text-green-600 bg-green-50',
      'failed': 'text-red-600 bg-red-50'
    };
    return colorMap[status] || 'text-gray-600 bg-gray-50';
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return '-';
    try {
      return new Date(dateString).toLocaleDateString('zh-CN');
    } catch {
      return dateString;
    }
  };

  // 格式化护照日期字段（只显示日期部分）
  const formatPassportDate = (dateString: string) => {
    if (!dateString) return '-';
    try {
      // 手动格式化确保 YYYY-MM-DD 格式
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return dateString;
      
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    } catch {
      return dateString;
    }
  };

  // 性别格式化函数
  const formatSex = (sex: string) => {
    if (!sex) return '-';
    if (sex.toUpperCase() === 'M') return '男';
    if (sex.toUpperCase() === 'F') return '女';
    return sex;
  };

  // 一致性检查格式化函数
  const formatConsistency = (consistency: string) => {
    if (!consistency) return { text: '-', color: 'bg-gray-100 text-gray-600' };
    
    const lowerConsistency = consistency.toLowerCase();
    // 只有完全一致才显示绿色
    if (lowerConsistency === '一致' || lowerConsistency === 'consistent' || lowerConsistency === 'ok') {
      return { text: consistency, color: 'bg-green-100 text-green-700' };
    } else {
      // 其他所有情况都显示红色（包括部分一致、不一致等）
      return { text: consistency, color: 'bg-red-100 text-red-700' };
    }
  };

  const exportToExcel = () => {
    if (passports.length === 0) {
      alert('暂无数据可导出');
      return;
    }

    try {
      // 准备导出数据
      const exportData = passports.map((passport, index) => ({
        '序号': index + 1,
        '任务ID': passport.task_id,
        '证件类型': passport.certificate_type || '-',
        '证件号码': passport.certificate_number || passport.passport_number || '-',
        '姓氏': passport.surname || '-',
        '名字': passport.given_names || '-',
        '性别': formatSex(passport.sex),
        '出生日期': formatPassportDate(passport.date_of_birth),
        '国籍': passport.nationality || '-',
        '旅客类型': passport.passenger_type || '-',
        '签发国': passport.country_of_issue || '-',
        '签发日期': formatPassportDate(passport.date_of_issue),
        '有效期至': formatPassportDate(passport.date_of_expiry),
        'SSR DOCS码': passport.ssr_code || '-',
        'MRZ第一行': passport.mrz_line1 || '-',
        'MRZ第二行': passport.mrz_line2 || '-',
        '一致性检查': formatConsistency(passport.viz_mrz_consistency).text,
        '处理状态': getStatusText(passport.processing_status),
        '创建时间': formatDate(passport.created_at),
        '更新时间': formatDate(passport.updated_at)
      }));

      // 创建工作簿
      const ws = XLSX.utils.json_to_sheet(exportData);
      const wb = XLSX.utils.book_new();
      
      // 设置列宽
      const colWidths = [
        { wch: 6 },   // 序号
        { wch: 20 },  // 任务ID
        { wch: 10 },  // 证件类型
        { wch: 15 },  // 证件号码
        { wch: 12 },  // 姓氏
        { wch: 12 },  // 名字
        { wch: 6 },   // 性别
        { wch: 12 },  // 出生日期
        { wch: 10 },  // 国籍
        { wch: 10 },  // 旅客类型
        { wch: 10 },  // 签发国
        { wch: 12 },  // 签发日期
        { wch: 12 },  // 有效期至
        { wch: 40 },  // SSR DOCS码
        { wch: 30 },  // MRZ第一行
        { wch: 30 },  // MRZ第二行
        { wch: 15 },  // 一致性检查
        { wch: 10 },  // 处理状态
        { wch: 18 },  // 创建时间
        { wch: 18 }   // 更新时间
      ];
      ws['!cols'] = colWidths;

      // 获取工作表范围
      const range = XLSX.utils.decode_range(ws['!ref'] || 'A1');
      
      // 定义完整的边框样式
      const borderAll = {
        style: 'thin',
        color: { rgb: '000000' }
      };
      
      // 为所有单元格添加边框和样式
      for (let row = range.s.r; row <= range.e.r; row++) {
        for (let col = range.s.c; col <= range.e.c; col++) {
          const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
          if (!ws[cellAddress]) ws[cellAddress] = { t: 's', v: '' };
          
          // 设置单元格样式
          ws[cellAddress].s = {
            border: {
              top: borderAll,
              bottom: borderAll,
              left: borderAll,
              right: borderAll
            },
            alignment: { 
              horizontal: 'left', 
              vertical: 'center',
              wrapText: true 
            },
            font: { 
              name: '微软雅黑', 
              sz: 10 
            }
          };
          
          // 为标题行添加特殊样式
          if (row === 0) {
            ws[cellAddress].s.fill = {
              patternType: 'solid',
              fgColor: { rgb: 'F3F4F6' }
            };
            ws[cellAddress].s.font = {
              name: '微软雅黑',
              sz: 10,
              bold: true
            };
            ws[cellAddress].s.alignment = {
              horizontal: 'center',
              vertical: 'center',
              wrapText: true
            };
          }
        }
      }

      // 添加工作表
      XLSX.utils.book_append_sheet(wb, ws, '护照识别结果');
      
      // 生成Excel文件
      const excelBuffer = XLSX.write(wb, { 
        bookType: 'xlsx', 
        type: 'array',
        cellStyles: true
      });
      const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      
      // 下载文件
      const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
      const filename = `护照识别结果_${taskId}_${timestamp}.xlsx`;
      saveAs(blob, filename);
      
      // 显示成功提示
      const toast = document.createElement('div');
      toast.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 flex items-center gap-2';
      toast.innerHTML = `
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
        <span>Excel文件导出成功</span>
      `;
      document.body.appendChild(toast);
      
      setTimeout(() => {
        toast.style.transform = 'translateX(100%)';
        toast.style.opacity = '0';
        setTimeout(() => document.body.removeChild(toast), 300);
      }, 3000);
      
    } catch (error) {
      console.error('导出Excel失败:', error);
      
      // 显示错误提示
      const toast = document.createElement('div');
      toast.className = 'fixed top-4 right-4 bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 flex items-center gap-2';
      toast.innerHTML = `
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
        <span>导出失败，请重试</span>
      `;
      document.body.appendChild(toast);
      
      setTimeout(() => {
        toast.style.transform = 'translateX(100%)';
        toast.style.opacity = '0';
        setTimeout(() => document.body.removeChild(toast), 300);
      }, 3000);
    }
  };

  return (
    <MainLayout>
      {/* 主框架容器 */}
      <div className="h-[calc(100vh-2rem)] bg-white rounded-lg shadow-sm border border-gray-200 flex flex-col">
        {/* 固定头部区域 */}
        <div className="flex-shrink-0 p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">任务详情</h1>
              <p className="text-gray-600 mt-2">任务ID: {taskId}</p>
            </div>
            <button
              onClick={() => {
                const returnTab = searchParams.get('returnTab');
                if (returnTab === 'history') {
                  navigate('/passport-recognition?tab=history');
                } else {
                  navigate('/passport-recognition');
                }
              }}
              className="flex items-center px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors border border-gray-200"
            >
              <ArrowLeft className="h-5 w-5 mr-2" />
              返回
            </button>
          </div>
        </div>

        {/* 统计信息卡片 - 固定高度 */}
        <div className="flex-shrink-0 p-6 border-b border-gray-200">
          <div className="grid grid-cols-2 lg:grid-cols-5 gap-4">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <FileText className="h-6 w-6 text-gray-400" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-500">总记录</p>
                  <p className="text-xl font-bold text-gray-900">
                    {statsLoading ? '...' : (taskStats?.total_count || totalItems)}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="h-6 w-6 bg-green-100 rounded-full flex items-center justify-center">
                    <div className="h-3 w-3 bg-green-600 rounded-full"></div>
                  </div>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-500">已完成</p>
                  <p className="text-xl font-bold text-green-600">
                    {statsLoading ? '...' : (taskStats?.completed_count || 0)}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="h-6 w-6 bg-blue-100 rounded-full flex items-center justify-center">
                    <div className="h-3 w-3 bg-blue-600 rounded-full"></div>
                  </div>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-500">处理中</p>
                  <p className="text-xl font-bold text-blue-600">
                    {statsLoading ? '...' : (taskStats?.processing_count || 0)}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="h-6 w-6 bg-yellow-100 rounded-full flex items-center justify-center">
                    <div className="h-3 w-3 bg-yellow-600 rounded-full"></div>
                  </div>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-500">待处理</p>
                  <p className="text-xl font-bold text-yellow-600">
                    {statsLoading ? '...' : (taskStats?.pending_count || 0)}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="h-6 w-6 bg-red-100 rounded-full flex items-center justify-center">
                    <div className="h-3 w-3 bg-red-600 rounded-full"></div>
                  </div>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-500">失败</p>
                  <p className="text-xl font-bold text-red-600">
                    {statsLoading ? '...' : (taskStats?.failed_count || 0)}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 表格区域 */}
        <div className="flex-1 flex flex-col bg-white">
          {/* 表格头部 */}
          <div className="flex-shrink-0 px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">护照识别结果</h3>
                <p className="text-sm text-gray-600 mt-1">任务 {taskId} 的详细识别结果</p>
              </div>
              <div className="flex items-center space-x-3">
                {/* 刷新按钮 */}
                <button
                  onClick={refreshTaskData}
                  disabled={refreshing}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                  {refreshing ? '刷新中...' : '刷新数据'}
                </button>
                
                {/* 导出Excel按钮 */}
                {passports.length > 0 && (
                  <button
                    onClick={exportToExcel}
                    className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    导出
                  </button>
                )}
              </div>
            </div>
          </div>

          {/* 表格内容区域 */}
          <div className="flex-1 p-6">
            {loading ? (
              <div className="flex items-center justify-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
              </div>
            ) : error ? (
              <div className="bg-red-50 border border-red-200 text-red-700 p-6 rounded-lg">
                <p className="font-medium">加载失败</p>
                <p className="text-sm mt-1">{error}</p>
              </div>
            ) : passports.length === 0 ? (
              <div className="flex items-center justify-center h-64 text-gray-500">
                <div className="text-center">
                  <FileText className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                  <h4 className="text-lg font-medium text-gray-900 mb-2">暂无护照记录</h4>
                  <p>该任务下还没有护照记录</p>
                </div>
              </div>
            ) : (
              <div className="space-y-6">
                <div className="overflow-x-auto">
                  <div className="inline-block min-w-full align-middle">
                    <div className="border border-gray-200 rounded-lg">
                      <table className="min-w-full divide-y divide-gray-200 relative">
                        <thead className="bg-gray-50">
                          <tr>
                            <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">序号</th>
                            <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">护照图片</th>
                            <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">状态</th>
                            <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">一致性检查</th>
                            <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">证件类型</th>
                            <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">证件号码</th>
                            <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">姓氏</th>
                            <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">名字</th>
                            <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">性别</th>
                            <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">出生日期</th>
                            <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">国籍</th>
                            <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">旅客类型</th>
                            <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">签发国</th>
                            <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">签发日期</th>
                            <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">有效期至</th>
                            <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">SSR DOCS 码</th>
                            <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">MRZ第一行</th>
                            <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">MRZ第二行</th>
                            <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap sticky right-0 bg-gray-50/95 backdrop-blur-sm border-l border-gray-200 shadow-lg z-20">复制</th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {passports.map((passport, index) => (
                            <tr key={passport.id} className="hover:bg-gray-50">
                              <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-500 text-center cursor-pointer hover:bg-gray-50"
                                  onDoubleClick={() => copyFieldValue((index + 1).toString(), '序号')}>
                                {(currentPage - 1) * pageSize + index + 1}
                              </td>
                              <td className="px-3 py-4 whitespace-nowrap">
                                <PassportImage
                                  imageUrl={passport.uploaded_image_url}
                                  passportData={passport}
                                  allPassports={passports}
                                  currentIndex={index}
                                  className="w-16 h-10 object-cover rounded border"
                                />
                              </td>
                              <td className="px-3 py-4 whitespace-nowrap cursor-pointer hover:bg-gray-50"
                                  onDoubleClick={() => copyFieldValue(getStatusText(passport.processing_status), '状态')}>
                                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(passport.processing_status)}`}>
                                  {getStatusText(passport.processing_status)}
                                </span>
                              </td>
                              <td className="px-3 py-4 whitespace-nowrap cursor-pointer hover:bg-gray-50"
                                  onDoubleClick={() => copyFieldValue(formatConsistency(passport.viz_mrz_consistency).text, '一致性检查')}>
                                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${formatConsistency(passport.viz_mrz_consistency).color}`}>
                                  {formatConsistency(passport.viz_mrz_consistency).text}
                                </span>
                              </td>
                              <td className="px-3 py-4 text-sm text-gray-900 whitespace-nowrap cursor-pointer hover:bg-gray-50" 
                                  title={passport.certificate_type || '-'}
                                  onDoubleClick={() => copyFieldValue(passport.certificate_type, '证件类型')}>
                                {passport.certificate_type || '-'}
                              </td>
                              <td className="px-3 py-4 text-sm font-medium text-gray-900 whitespace-nowrap cursor-pointer hover:bg-gray-50" 
                                  title={passport.certificate_number || passport.passport_number || '-'}
                                  onDoubleClick={() => copyFieldValue(passport.certificate_number || passport.passport_number, '证件号码')}>
                                {passport.certificate_number || passport.passport_number || '-'}
                              </td>
                              <td className="px-3 py-4 text-sm text-gray-900 whitespace-nowrap cursor-pointer hover:bg-gray-50" 
                                  title={passport.surname || '-'}
                                  onDoubleClick={() => copyFieldValue(passport.surname, '姓氏')}>
                                {passport.surname || '-'}
                              </td>
                              <td className="px-3 py-4 text-sm text-gray-900 whitespace-nowrap cursor-pointer hover:bg-gray-50" 
                                  title={passport.given_names || '-'}
                                  onDoubleClick={() => copyFieldValue(passport.given_names, '名字')}>
                                {passport.given_names || '-'}
                              </td>
                              <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-center cursor-pointer hover:bg-gray-50"
                                  onDoubleClick={() => copyFieldValue(formatSex(passport.sex), '性别')}>
                                {formatSex(passport.sex)}
                              </td>
                              <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900 cursor-pointer hover:bg-gray-50"
                                  onDoubleClick={() => copyFieldValue(formatPassportDate(passport.date_of_birth), '出生日期')}>
                                {formatPassportDate(passport.date_of_birth)}
                              </td>
                              <td className="px-3 py-4 text-sm text-gray-900 whitespace-nowrap cursor-pointer hover:bg-gray-50" 
                                  title={passport.nationality || '-'}
                                  onDoubleClick={() => copyFieldValue(passport.nationality, '国籍')}>
                                {passport.nationality || '-'}
                              </td>
                              <td className="px-3 py-4 text-sm text-gray-900 whitespace-nowrap cursor-pointer hover:bg-gray-50" 
                                  title={passport.passenger_type || '-'}
                                  onDoubleClick={() => copyFieldValue(passport.passenger_type, '旅客类型')}>
                                {passport.passenger_type || '-'}
                              </td>
                              <td className="px-3 py-4 text-sm text-gray-900 whitespace-nowrap cursor-pointer hover:bg-gray-50" 
                                  title={passport.country_of_issue || '-'}
                                  onDoubleClick={() => copyFieldValue(passport.country_of_issue, '签发国')}>
                                {passport.country_of_issue || '-'}
                              </td>
                              <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900 cursor-pointer hover:bg-gray-50"
                                  onDoubleClick={() => copyFieldValue(formatPassportDate(passport.date_of_issue), '签发日期')}>
                                {formatPassportDate(passport.date_of_issue)}
                              </td>
                              <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900 cursor-pointer hover:bg-gray-50"
                                  onDoubleClick={() => copyFieldValue(formatPassportDate(passport.date_of_expiry), '有效期至')}>
                                {formatPassportDate(passport.date_of_expiry)}
                              </td>
                              <td className="px-3 py-4 text-sm text-gray-900 cursor-pointer hover:bg-gray-50 font-mono text-xs whitespace-nowrap" 
                                  title={passport.ssr_code || '-'}
                                  onDoubleClick={() => copyFieldValue(passport.ssr_code, 'SSR码')}>
                                {passport.ssr_code || '-'}
                              </td>
                              <td className="px-3 py-4 text-sm text-gray-900 cursor-pointer hover:bg-gray-50 font-mono text-xs whitespace-nowrap" 
                                  title={passport.mrz_line1 || '-'}
                                  onDoubleClick={() => copyFieldValue(passport.mrz_line1, 'MRZ第一行')}>
                                {passport.mrz_line1 || '-'}
                              </td>
                              <td className="px-3 py-4 text-sm text-gray-900 cursor-pointer hover:bg-gray-50 font-mono text-xs whitespace-nowrap" 
                                  title={passport.mrz_line2 || '-'}
                                  onDoubleClick={() => copyFieldValue(passport.mrz_line2, 'MRZ第二行')}>
                                {passport.mrz_line2 || '-'}
                              </td>
                              <td className="px-3 py-4 whitespace-nowrap sticky right-0 bg-white/95 backdrop-blur-sm border-l border-gray-200 shadow-lg z-10">
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    copyPassportInfo(passport);
                                  }}
                                  className="text-blue-600 hover:text-blue-900 transition-colors p-1 hover:bg-blue-50 rounded"
                                  title="复制全部信息"
                                >
                                  {copiedRowId === passport.id ? (
                                    <Check className="h-4 w-4 text-green-600" />
                                  ) : (
                                    <Copy className="h-4 w-4" />
                                  )}
                                </button>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>

                {/* 分页组件 */}
                {totalItems > pageSize && (
                  <div className="flex justify-center pt-4">
                    <Pagination
                      currentPage={currentPage}
                      totalPages={Math.ceil(totalItems / pageSize)}
                      totalItems={totalItems}
                      pageSize={pageSize}
                      onPageChange={setCurrentPage}
                    />
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default TaskDetailPage; 