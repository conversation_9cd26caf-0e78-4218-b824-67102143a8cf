import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

import { Pagination } from '@/components/ui/pagination';
import MainLayout from '@/components/layout/MainLayout';
import {
  ArrowLeft,
  Search,
  X,
  Download,
  Eye,
  Edit,
  FileText,
  AlertTriangle
} from 'lucide-react';
import * as XLSX from 'xlsx-js-style';
import { saveAs } from 'file-saver';
import { trainOrderApi, TrainOrder, TrainOrderListResponse } from '@/api/trainOrder';
import { ProjectTaskService } from '@/services/project';
import { ProjectTask } from '@/types/project-task';
import { useToast } from '@/hooks/use-toast';
import { formatAmount } from '@/utils/formatters';
import FailureDetailsModal from '@/components/order/FailureDetailsModal';
import EditOrderForm from '@/components/order/EditOrderForm';


const TaskOrderDetailPage: React.FC = () => {
  const { taskId } = useParams<{ taskId: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const [task, setTask] = useState<ProjectTask | null>(null);
  const [orders, setOrders] = useState<TrainOrder[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [pageSize] = useState(20);
  const [total, setTotal] = useState(0);

  // 搜索相关状态
  const [searchTravelerName, setSearchTravelerName] = useState('');
  const [searchMobilePhone, setSearchMobilePhone] = useState('');
  const [searchContactPhone, setSearchContactPhone] = useState('');

  // 订单操作状态
  const [selectedOrder, setSelectedOrder] = useState<TrainOrder | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  // 失败详情模态框状态
  const [isFailureModalOpen, setIsFailureModalOpen] = useState(false);
  const [selectedFailedOrder, setSelectedFailedOrder] = useState<TrainOrder | null>(null);

  // 验证规则定义 - 与火车票预订页面保持一致
  const getFieldValidationRule = (field: keyof TrainOrder) => {
    const rules: Record<string, {
      required?: boolean;
      pattern?: RegExp;
      message?: string;
      maxLength?: number;
      minLength?: number;
      enumValues?: string[];
    }> = {
      // 基础必填字段（所有情况下都必填）
      traveler_full_name: { required: true, message: '出行人姓名不能为空', maxLength: 50 },
      id_type: {
        required: true,
        message: '证件类型不能为空',
        maxLength: 20,
        enumValues: ['身份证', '公务护照', '普通护照', '港澳通行证', '台胞证', '回乡证', '军人证', '海员证', '台湾通行证', '外国永久居留证', '港澳台居民居住证', '其他']
      },
      id_number: {
        required: true,
        message: '证件号码不能为空',
        maxLength: 50
      },
      mobile_phone: {
        required: true,
        message: '手机号不能为空',
        maxLength: 20
      },
      travel_date: { required: true, message: '出行日期不能为空' },
      departure_station: { required: true, message: '出发站名不能为空', maxLength: 50 },
      arrival_station: { required: true, message: '到达站名不能为空', maxLength: 50 },
      train_number: { required: true, message: '车次不能为空', maxLength: 20 },
      seat_type: {
        required: true,
        message: '座位类型不能为空',
        maxLength: 20,
        enumValues: ['硬座', '硬卧', '软卧', '二等座', '一等座', '商务座', '特等座']
      },
      departure_time: { required: true, message: '出发时间不能为空' },
      arrival_time: { required: true, message: '到达时间不能为空' },
      contact_person: { required: true, message: '联系人不能为空', maxLength: 50 },
      contact_phone: {
        required: true,
        message: '联系人手机号不能为空',
        maxLength: 20
      },
      approval_reference: { required: true, message: '审批参考人不能为空', maxLength: 50 },

      // 证件类型不为身份证时的额外必填字段（动态必填）
      traveler_surname: { maxLength: 25 },
      traveler_given_name: { maxLength: 25 },
      nationality: { maxLength: 20 },
      gender: {
        maxLength: 10,
        enumValues: ['男', '女']
      },
      birth_date: {},
      id_expiry_date: {},

      // 手机号国际区号（自动补齐为86，非必填）
      mobile_phone_country_code: { maxLength: 10 },

      // 非必填字段
      contact_email: {
        pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        message: '联系人邮箱格式不正确',
        maxLength: 100
      },
      company_name: { maxLength: 100 },
      booking_agent: { maxLength: 50 },
      order_number: { maxLength: 50 },
      bill_number: { maxLength: 50 },
      amount: {
        pattern: /^\d+(\.\d{1,2})?$/,
        message: '金额格式不正确（最多两位小数）'
      },
      sequence_number: { maxLength: 50 },
      trip_submission_item: { maxLength: 200 },
      ticket_sms: { maxLength: 500 }
    };

    return rules[field] || {};
  };

  // 验证单个字段
  const validateField = (field: keyof TrainOrder, value: string | number | boolean | null | undefined, idType?: string): string | null => {
    const rule = getFieldValidationRule(field);

    // 处理boolean类型字段
    if (typeof value === 'boolean') {
      return null;
    }

    const stringValue = String(value || '').trim();

    // 动态必填字段：当证件类型不为身份证时，这些字段变为必填
    const dynamicRequiredFields = ['traveler_surname', 'traveler_given_name', 'nationality', 'gender', 'birth_date', 'id_expiry_date'];
    let isRequired = rule.required;

    if (dynamicRequiredFields.includes(field) && idType && idType !== '身份证') {
      isRequired = true;
    }

    // 必填验证
    if (isRequired && !stringValue) {
      const fieldNames: Record<string, string> = {
        // 基础必填字段
        traveler_full_name: '出行人姓名',
        id_type: '证件类型',
        id_number: '证件号码',
        mobile_phone: '手机号',
        travel_date: '出行日期',
        departure_station: '出发站名',
        arrival_station: '到达站名',
        train_number: '车次',
        seat_type: '座位类型',
        departure_time: '出发时间',
        arrival_time: '到达时间',
        cost_center: '成本中心',
        contact_person: '联系人',
        contact_phone: '联系人手机号',
        approval_reference: '审批参考人',
        // 动态必填字段
        traveler_surname: '出行人姓',
        traveler_given_name: '出行人名',
        nationality: '国籍',
        gender: '性别',
        birth_date: '出生日期',
        id_expiry_date: '证件有效期'
      };
      return fieldNames[field] ? `${fieldNames[field]}不能为空` : (rule.message || `${field}不能为空`);
    }

    // 如果值为空且非必填，跳过其他验证
    if (!stringValue && !isRequired) {
      return null;
    }

    // 长度验证
    if (rule.maxLength && stringValue.length > rule.maxLength) {
      const fieldNames: Record<string, string> = {
        traveler_full_name: '出行人姓名',
        id_type: '证件类型',
        id_number: '证件号码',
        mobile_phone: '手机号',
        travel_date: '出行日期',
        departure_station: '出发站名',
        arrival_station: '到达站名',
        train_number: '车次',
        seat_type: '座位类型',
        departure_time: '出发时间',
        arrival_time: '到达时间',
        cost_center: '成本中心',
        contact_person: '联系人',
        contact_phone: '联系人手机号',
        contact_email: '联系人邮箱',
        approval_reference: '审批参考人',
        company_name: '公司名称',
        booking_agent: '代订人',
        order_number: '订单号',
        bill_number: '账单号',
        sequence_number: '内部序列号',
        travel_order_number: '差旅单号',
        trip_submission_item: '行程提交项',
        ticket_sms: '出票短信'
      };
      const fieldDisplayName = fieldNames[field] || field;
      return `${fieldDisplayName}长度不能超过${rule.maxLength}个字符`;
    }

    if (rule.minLength && stringValue.length < rule.minLength) {
      const fieldNames: Record<string, string> = {
        traveler_full_name: '出行人姓名',
        id_type: '证件类型',
        id_number: '证件号码',
        mobile_phone: '手机号',
        travel_date: '出行日期',
        departure_station: '出发站名',
        arrival_station: '到达站名',
        train_number: '车次',
        seat_type: '座位类型',
        departure_time: '出发时间',
        arrival_time: '到达时间',
        cost_center: '成本中心',
        contact_person: '联系人',
        contact_phone: '联系人手机号',
        contact_email: '联系人邮箱',
        approval_reference: '审批参考人',
        company_name: '公司名称',
        booking_agent: '代订人',
        order_number: '订单号',
        bill_number: '账单号',
        sequence_number: '内部序列号',
        travel_order_number: '差旅单号',
        trip_submission_item: '行程提交项',
        ticket_sms: '出票短信'
      };
      const fieldDisplayName = fieldNames[field] || field;
      return `${fieldDisplayName}长度不能少于${rule.minLength}个字符`;
    }

    // 格式验证
    if (rule.pattern && !rule.pattern.test(stringValue)) {
      return rule.message || `${field}格式不正确`;
    }

    // 身份证号码特殊验证：只在证件类型为身份证时验证格式
    if (field === 'id_number' && stringValue && idType === '身份证') {
      const idNumberPattern = /^[0-9Xx]{15}$|^[0-9Xx]{18}$/;
      if (!idNumberPattern.test(stringValue)) {
        return '身份证号码格式不正确（15位或18位数字或字母X）';
      }
    }

    // 枚举值验证
    if (rule.enumValues && rule.enumValues.length > 0 && !rule.enumValues.includes(stringValue)) {
      return `${field}值不在允许的范围内`;
    }

    return null;
  };

  // 验证所有字段
  const validateAllFields = (order: TrainOrder): Record<string, string> => {
    const errors: Record<string, string> = {};

    // 基础必填字段（所有情况下都必填）
    const basicRequiredFields: (keyof TrainOrder)[] = [
      'traveler_full_name', 'id_type', 'id_number', 'mobile_phone', 'travel_date',
      'departure_station', 'arrival_station', 'train_number', 'seat_type',
      'departure_time', 'arrival_time', 'contact_person',
      'contact_phone', 'approval_reference'
    ];

    // 证件类型不为身份证时的额外必填字段
    const dynamicRequiredFields: (keyof TrainOrder)[] = [
      'traveler_surname', 'traveler_given_name', 'nationality',
      'gender', 'birth_date', 'id_expiry_date'
    ];

    // 验证基础必填字段
    basicRequiredFields.forEach(field => {
      const error = validateField(field, order[field], order.id_type);
      if (error) {
        errors[field] = error;
      }
    });

    // 验证动态必填字段（仅当证件类型不为身份证时）
    if (order.id_type && order.id_type !== '身份证') {
      dynamicRequiredFields.forEach(field => {
        const error = validateField(field, order[field], order.id_type);
        if (error) {
          errors[field] = error;
        }
      });
    }

    // 验证非必填字段的格式（如果有值的话）
    const optionalFields: (keyof TrainOrder)[] = ['contact_email', 'amount'];
    optionalFields.forEach(field => {
      if (order[field]) {
        const error = validateField(field, order[field], order.id_type);
        if (error) {
          errors[field] = error;
        }
      }
    });

    return errors;
  };

  // 获取任务信息
  useEffect(() => {
    const fetchTaskInfo = async () => {
      if (!taskId) return;
      
      try {
        const taskData = await ProjectTaskService.getTaskByTaskId(taskId);
        setTask(taskData);
      } catch (error) {
        console.error('获取任务信息失败:', error);
        setError('无法获取任务信息');
      }
    };

    fetchTaskInfo();
  }, [taskId]);

  // 加载订单数据
  const loadOrders = async (page: number = 1) => {
    if (!taskId) return;
    
    setLoading(true);
    try {
      const response = await trainOrderApi.getOrdersByTask(
        taskId,
        page,
        pageSize,
        undefined, // status
        searchTravelerName?.trim() || undefined,
        searchMobilePhone?.trim() || undefined,
        searchContactPhone?.trim() || undefined
      );
      
      const data = response.data as TrainOrderListResponse;
      setOrders(data.items);
      setTotal(data.total);
      setPage(page);
    } catch (error) {
      console.error('加载订单失败:', error);
      toast({
        title: "加载失败",
        description: "无法加载任务订单列表",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // 初始加载
  useEffect(() => {
    loadOrders(1);
  }, [taskId, searchTravelerName, searchMobilePhone, searchContactPhone]);

  // 搜索处理
  const handleSearch = () => {
    loadOrders(1);
  };

  const handleClearSearch = () => {
    setSearchTravelerName('');
    setSearchMobilePhone('');
    setSearchContactPhone('');
    loadOrders(1);
  };

  // 格式化日期时间
  const formatDateTime = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return dateString;
    }
  };

  // 获取订单状态文本
  const getOrderStatusText = (status: string) => {
    const statusMap = {
      'initial': '待预订',
      'submitted': '已提交',
      'processing': '处理中',
      'completed': '已完成',
      'failed': '失败'
    };
    return statusMap[status as keyof typeof statusMap] || status;
  };

  // 获取任务状态文本和样式
  const getTaskStatusDisplay = (status: string) => {
    const statusConfig = {
      'submitted': { bg: 'bg-blue-100', text: 'text-blue-800', label: '已提交' },
      'processing': { bg: 'bg-yellow-100', text: 'text-yellow-800', label: '处理中' },
      'completed': { bg: 'bg-green-100', text: 'text-green-800', label: '已完成' },
      'failed': { bg: 'bg-red-100', text: 'text-red-800', label: '失败' }
    };
    
    const config = statusConfig[status as keyof typeof statusConfig];
    return config || { bg: 'bg-gray-100', text: 'text-gray-800', label: status };
  };

  // 获取订单状态显示样式
  const getOrderStatusDisplay = (status: string) => {
    const statusConfig = {
      'initial': { bg: 'bg-gray-100', text: 'text-gray-800', label: '待预订' },
      'submitted': { bg: 'bg-blue-100', text: 'text-blue-800', label: '已提交' },
      'processing': { bg: 'bg-yellow-100', text: 'text-yellow-800', label: '处理中' },
      'completed': { bg: 'bg-green-100', text: 'text-green-800', label: '已完成' },
      'failed': { bg: 'bg-red-100', text: 'text-red-800', label: '失败' }
    };

    const config = statusConfig[status as keyof typeof statusConfig];
    return config || { bg: 'bg-gray-100', text: 'text-gray-800', label: status };
  };

  // 处理失败详情点击
  const handleFailureDetailsClick = (order: TrainOrder) => {
    setSelectedFailedOrder(order);
    setIsFailureModalOpen(true);
  };

  const getOrderStatusInfo = (status: string) => {
    switch (status) {
      case 'initial':
        return { className: 'px-2 py-1 bg-gray-100 text-gray-600 rounded-full text-xs', text: '待提交' };
      case 'submitted':
        return { className: 'px-2 py-1 bg-blue-100 text-blue-700 rounded-full text-xs', text: '已提交' };
      case 'processing':
        return { className: 'px-2 py-1 bg-yellow-100 text-yellow-700 rounded-full text-xs', text: '处理中' };
      case 'completed':
        return { className: 'px-2 py-1 bg-green-100 text-green-700 rounded-full text-xs', text: '已完成' };
      case 'failed':
        return { className: 'px-2 py-1 bg-red-100 text-red-700 rounded-full text-xs', text: '失败' };
      default:
        return { className: 'px-2 py-1 bg-gray-100 text-gray-600 rounded-full text-xs', text: status };
    }
  };

  // 订单操作处理
  const handleViewOrder = (order: TrainOrder) => {
    setSelectedOrder(order);
    setIsViewModalOpen(true);
  };

  const handleEditOrder = (order: TrainOrder) => {
    setSelectedOrder(order);
    setIsEditModalOpen(true);
  };







  // 导出Excel功能
  const exportToExcel = () => {
    if (orders.length === 0) {
      toast({
        title: "导出失败",
        description: "没有订单数据可导出",
        variant: "destructive",
      });
      return;
    }

    try {
      // 准备导出数据 - 包含所有数据库字段
      const exportData = orders.map((order, index) => ({
        '序号': index + 1,
        '订单状态': getOrderStatusText(order.order_status),
        '出行人姓名': order.traveler_full_name || '-',
        '出行人姓': order.traveler_surname || '-',
        '出行人名': order.traveler_given_name || '-',
        '国籍': order.nationality || '-',
        '性别': order.gender || '-',
        '出生日期': order.birth_date || '-',
        '证件类型': order.id_type || '-',
        '证件号码': order.id_number || '-',
        '证件有效期至': order.id_expiry_date || '-',
        '手机号': order.mobile_phone || '-',
        '手机号国际区号': order.mobile_phone_country_code || '-',
        '出行日期': order.travel_date || '-',
        '出发站名': order.departure_station || '-',
        '到达站名': order.arrival_station || '-',
        '车次': order.train_number || '-',
        '座位类型': order.seat_type || '-',
        '出发时间': order.departure_time || '-',
        '到达时间': order.arrival_time || '-',
        '成本中心': order.cost_center || '-',
        '行程提交项': order.trip_submission_item || '-',
        '联系人': order.contact_person || '-',
        '联系人手机号': order.contact_phone || '-',
        '联系人邮箱': order.contact_email || '-',
        '审批参考人': order.approval_reference || '-',
        '公司名称': order.company_name || '-',
        '代订人': order.booking_agent || '-',
        '出票短信': order.ticket_sms || '-',
        '金额': order.amount || 0,
        '订单号': order.order_number || '-',
        '账单号': order.bill_number || '-',
        '失败原因': order.fail_reason || '-',
        '创建时间': formatDateTime(order.created_at),
        '更新时间': formatDateTime(order.updated_at)
      }));

      // 创建空工作簿
      const wb = XLSX.utils.book_new();
      const ws = XLSX.utils.aoa_to_sheet([]);
      
      // 获取数据列数和列名
      const dataColumns = Object.keys(exportData[0] || {}).length;
      const columnHeaders = Object.keys(exportData[0] || {});
      
      // 第一行：添加标题
      const titleText = `${task?.task_title || `任务 ${taskId}`} - 订单明细`;
      XLSX.utils.sheet_add_aoa(ws, [[titleText]], { origin: 'A1' });
      
      // 第二行：添加列名
      XLSX.utils.sheet_add_aoa(ws, [columnHeaders], { origin: 'A2' });
      
      // 第三行开始：添加数据
      const dataRows = exportData.map(row => columnHeaders.map(header => (row as any)[header]));
      XLSX.utils.sheet_add_aoa(ws, dataRows, { origin: 'A3' });
      
      // 设置列宽
      const colWidths = [
        { wch: 6 },   // 序号
        { wch: 10 },  // 订单状态
        { wch: 12 },  // 出行人姓名
        { wch: 10 },  // 出行人姓
        { wch: 10 },  // 出行人名
        { wch: 8 },   // 国籍
        { wch: 6 },   // 性别
        { wch: 12 },  // 出生日期
        { wch: 10 },  // 证件类型
        { wch: 18 },  // 证件号码
        { wch: 12 },  // 证件有效期至
        { wch: 12 },  // 手机号
        { wch: 8 },   // 手机号国际区号
        { wch: 12 },  // 出行日期
        { wch: 12 },  // 出发站名
        { wch: 12 },  // 到达站名
        { wch: 10 },  // 车次
        { wch: 10 },  // 座位类型
        { wch: 10 },  // 出发时间
        { wch: 10 },  // 到达时间
        { wch: 15 },  // 成本中心
        { wch: 15 },  // 行程提交项
        { wch: 10 },  // 联系人
        { wch: 12 },  // 联系人手机号
        { wch: 20 },  // 联系人邮箱
        { wch: 12 },  // 审批参考人
        { wch: 15 },  // 公司名称
        { wch: 10 },  // 代订人
        { wch: 15 },  // 出票短信
        { wch: 10 },  // 金额
        { wch: 15 },  // 订单号
        { wch: 15 },  // 账单号
        { wch: 20 },  // 失败原因
        { wch: 18 },  // 创建时间
        { wch: 18 }   // 更新时间
      ];
      ws['!cols'] = colWidths;

      // 更新工作表范围（标题行 + 列名行 + 数据行）
      const totalRows = 1 + 1 + exportData.length; // 标题行 + 列名行 + 数据行数
      ws['!ref'] = `A1:${XLSX.utils.encode_col(dataColumns - 1)}${totalRows}`;
      const range = XLSX.utils.decode_range(ws['!ref']);
      
      // 定义完整的边框样式
      const borderAll = {
        style: 'thin',
        color: { rgb: '000000' }
      };
      
      // 为标题行设置合并单元格和样式
      ws['!merges'] = [{ s: { r: 0, c: 0 }, e: { r: 0, c: dataColumns - 1 } }];
      
      // 为所有单元格添加边框和样式
      for (let row = range.s.r; row <= range.e.r; row++) {
        for (let col = range.s.c; col <= range.e.c; col++) {
          const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
          if (!ws[cellAddress]) ws[cellAddress] = { t: 's', v: '' };
          
          // 设置单元格样式
          ws[cellAddress].s = {
            border: {
              top: borderAll,
              bottom: borderAll,
              left: borderAll,
              right: borderAll
            },
            alignment: { 
              horizontal: 'left', 
              vertical: 'center',
              wrapText: true 
            },
            font: { 
              name: '微软雅黑', 
              sz: 10 
            }
          };
          
          // 为标题行(第1行)添加特殊样式
          if (row === 0) {
            ws[cellAddress].s.fill = {
              patternType: 'solid',
              fgColor: { rgb: 'E3F2FD' }
            };
            ws[cellAddress].s.font = {
              name: '微软雅黑',
              sz: 14,
              bold: true
            };
            ws[cellAddress].s.alignment = {
              horizontal: 'center',
              vertical: 'center',
              wrapText: true
            };
          }
          
          // 为列名行(第2行)添加特殊样式
          if (row === 1) {
            ws[cellAddress].s.fill = {
              patternType: 'solid',
              fgColor: { rgb: 'F3F4F6' }
            };
            ws[cellAddress].s.font = {
              name: '微软雅黑',
              sz: 10,
              bold: true
            };
            ws[cellAddress].s.alignment = {
              horizontal: 'center',
              vertical: 'center',
              wrapText: true
            };
          }
        }
      }

      // 添加工作表
      XLSX.utils.book_append_sheet(wb, ws, '火车票订单');
      
      // 生成Excel文件
      const filename = `任务订单_${task?.task_title || taskId}_${new Date().toLocaleDateString('zh-CN').replace(/\//g, '-')}.xlsx`;
      const excelBuffer = XLSX.write(wb, { 
        bookType: 'xlsx', 
        type: 'array',
        cellStyles: true
      });
      const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      
      // 下载文件
      saveAs(blob, filename);
      
      // 显示成功提示
      toast({
        title: "导出成功",
        description: "Excel文件已成功导出",
        variant: "default",
      });
      
    } catch (error) {
      console.error('导出Excel失败:', error);
      toast({
        title: "导出失败",
        description: "导出Excel文件时发生错误，请重试",
        variant: "destructive",
      });
    }
  };

  if (error) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">加载失败</h2>
            <p className="text-gray-600 mb-4">{error}</p>
            <Button onClick={() => navigate(-1)}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回
            </Button>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="p-4 bg-gray-50 min-h-screen">
        {/* 页面头部 */}
        <div className="mb-4">
          {/* 任务信息卡片 */}
          <Card className="bg-white shadow-sm border border-gray-200 border-l-4 border-l-blue-500 p-4">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-2">
                  <div className="p-2 bg-blue-50 rounded-lg">
                    <FileText className="h-5 w-5 text-blue-600" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <h1 className="text-xl font-bold text-gray-900">
                        {task?.task_title || `任务 ${taskId}`}
                      </h1>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => navigate(-1)}
                        className="flex items-center gap-2"
                      >
                        <ArrowLeft className="h-4 w-4" />
                        返回
                      </Button>
                    </div>
                    <p className="text-gray-600">任务订单详情</p>
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-4">
                  <div>
                    <p className="text-sm text-gray-500">任务ID</p>
                    <p className="font-medium">{taskId}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">任务类型</p>
                    <p className="font-medium">{task?.task_type}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">任务状态</p>
                    {task?.task_status ? (
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getTaskStatusDisplay(task.task_status).bg} ${getTaskStatusDisplay(task.task_status).text}`}>
                        {getTaskStatusDisplay(task.task_status).label}
                      </span>
                    ) : (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        未知状态
                      </span>
                    )}
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">订单总数</p>
                    <p className="font-medium text-lg">{total}</p>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </div>

        {/* 搜索和筛选 */}
        <Card className="bg-white shadow-sm border border-gray-200 mb-6">
          <div className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  出行人姓名
                </label>
                <Input
                  placeholder="请输入出行人姓名"
                  value={searchTravelerName}
                  onChange={(e) => setSearchTravelerName(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  手机号码
                </label>
                <Input
                  placeholder="请输入手机号码"
                  value={searchMobilePhone}
                  onChange={(e) => setSearchMobilePhone(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  联系人手机
                </label>
                <Input
                  placeholder="请输入联系人手机号"
                  value={searchContactPhone}
                  onChange={(e) => setSearchContactPhone(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                />
              </div>
              <div className="flex items-end">
                <Button onClick={handleSearch} className="w-full">
                  <Search className="h-4 w-4 mr-2" />
                  搜索
                </Button>
              </div>
              <div className="flex items-end">
                <Button variant="outline" onClick={handleClearSearch} className="w-full">
                  <X className="h-4 w-4 mr-2" />
                  重置
                </Button>
              </div>
            </div>
          </div>
        </Card>

        {/* 订单列表 */}
        <Card className="bg-white shadow-sm border border-gray-200">
          <div className="p-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">订单列表</h3>
              <div className="flex items-center gap-3">
                <span className="text-sm text-gray-500">
                  共 {total} 条记录
                </span>
                <Button
                  variant="outline"
                  onClick={exportToExcel}
                  disabled={orders.length === 0}
                  size="sm"
                >
                  <Download className="h-4 w-4 mr-2" />
                  导出
                </Button>
              </div>
            </div>

            {loading ? (
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-gray-600">加载订单数据中...</p>
              </div>
            ) : orders.length === 0 ? (
              <div className="text-center py-12">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">暂无订单</h3>
                <p className="text-gray-600">该任务还没有关联的订单</p>
              </div>
            ) : (
              <>
                <div className="overflow-x-auto custom-scrollbar" style={{ 
                  scrollbarWidth: 'auto', 
                  scrollbarColor: '#cbd5e1 #f1f5f9'
                }}>
                  <style dangerouslySetInnerHTML={{
                    __html: `
                      .custom-scrollbar::-webkit-scrollbar {
                        height: 12px;
                        background-color: #f1f5f9;
                      }
                      .custom-scrollbar::-webkit-scrollbar-track {
                        background-color: #f1f5f9;
                        border-radius: 6px;
                      }
                      .custom-scrollbar::-webkit-scrollbar-thumb {
                        background-color: #cbd5e1;
                        border-radius: 6px;
                        border: 2px solid #f1f5f9;
                      }
                      .custom-scrollbar::-webkit-scrollbar-thumb:hover {
                        background-color: #94a3b8;
                      }
                    `
                  }} />
                  <table className="w-full text-sm" style={{ minWidth: '2600px' }}>
                    <thead className="bg-gray-50 border-b border-gray-200">
                      <tr>
                        <th className="text-left p-3 font-medium text-gray-900 whitespace-nowrap" style={{ minWidth: '60px' }}>序号</th>
                        <th className="text-left p-3 font-medium text-gray-900 whitespace-nowrap" style={{ minWidth: '80px' }}>状态</th>
                        <th className="text-left p-3 font-medium text-gray-900 whitespace-nowrap" style={{ minWidth: '100px' }}>出行人姓名</th>
                        <th className="text-left p-3 font-medium text-gray-900 whitespace-nowrap" style={{ minWidth: '80px' }}>出行人姓</th>
                        <th className="text-left p-3 font-medium text-gray-900 whitespace-nowrap" style={{ minWidth: '80px' }}>出行人名</th>
                        <th className="text-left p-3 font-medium text-gray-900 whitespace-nowrap" style={{ minWidth: '80px' }}>国籍</th>
                        <th className="text-left p-3 font-medium text-gray-900 whitespace-nowrap" style={{ minWidth: '60px' }}>性别</th>
                        <th className="text-left p-3 font-medium text-gray-900 whitespace-nowrap" style={{ minWidth: '120px' }}>出生日期</th>
                        <th className="text-left p-3 font-medium text-gray-900 whitespace-nowrap" style={{ minWidth: '80px' }}>证件类型</th>
                        <th className="text-left p-3 font-medium text-gray-900 whitespace-nowrap" style={{ minWidth: '150px' }}>证件号码</th>
                        <th className="text-left p-3 font-medium text-gray-900 whitespace-nowrap" style={{ minWidth: '120px' }}>证件有效期</th>
                        <th className="text-left p-3 font-medium text-gray-900 whitespace-nowrap" style={{ minWidth: '120px' }}>手机号</th>
                        <th className="text-left p-3 font-medium text-gray-900 whitespace-nowrap" style={{ minWidth: '80px' }}>手机号国家码</th>
                        <th className="text-left p-3 font-medium text-gray-900 whitespace-nowrap" style={{ minWidth: '160px' }}>出行日期</th>
                        <th className="text-left p-3 font-medium text-gray-900 whitespace-nowrap" style={{ minWidth: '120px' }}>出发站</th>
                        <th className="text-left p-3 font-medium text-gray-900 whitespace-nowrap" style={{ minWidth: '120px' }}>到达站</th>
                        <th className="text-left p-3 font-medium text-gray-900 whitespace-nowrap" style={{ minWidth: '80px' }}>车次</th>
                        <th className="text-left p-3 font-medium text-gray-900 whitespace-nowrap" style={{ minWidth: '80px' }}>座位类型</th>
                        <th className="text-left p-3 font-medium text-gray-900 whitespace-nowrap" style={{ minWidth: '80px' }}>出发时间</th>
                        <th className="text-left p-3 font-medium text-gray-900 whitespace-nowrap" style={{ minWidth: '80px' }}>到达时间</th>
                        <th className="text-left p-3 font-medium text-gray-900 whitespace-nowrap" style={{ minWidth: '120px' }}>成本中心</th>
                        <th className="text-left p-3 font-medium text-gray-900 whitespace-nowrap" style={{ minWidth: '150px' }}>行程提交项</th>
                        <th className="text-left p-3 font-medium text-gray-900 whitespace-nowrap" style={{ minWidth: '100px' }}>联系人</th>
                        <th className="text-left p-3 font-medium text-gray-900 whitespace-nowrap" style={{ minWidth: '120px' }}>联系人手机</th>
                        <th className="text-left p-3 font-medium text-gray-900 whitespace-nowrap" style={{ minWidth: '150px' }}>联系人邮箱</th>
                        <th className="text-left p-3 font-medium text-gray-900 whitespace-nowrap" style={{ minWidth: '100px' }}>审批参考人</th>
                        <th className="text-left p-3 font-medium text-gray-900 whitespace-nowrap" style={{ minWidth: '150px' }}>公司名称</th>
                        <th className="text-left p-3 font-medium text-gray-900 whitespace-nowrap" style={{ minWidth: '100px' }}>预订代理</th>
                        <th className="text-left p-3 font-medium text-gray-900 whitespace-nowrap" style={{ minWidth: '150px' }}>票务短信</th>
                        <th className="text-left p-3 font-medium text-gray-900 whitespace-nowrap" style={{ minWidth: '80px' }}>金额</th>
                        <th className="text-left p-3 font-medium text-gray-900 whitespace-nowrap" style={{ minWidth: '120px' }}>订单号</th>
                        <th className="text-left p-3 font-medium text-gray-900 whitespace-nowrap" style={{ minWidth: '120px' }}>账单号</th>
                        <th className="text-left p-3 font-medium text-gray-900 whitespace-nowrap" style={{ minWidth: '150px' }}>失败原因</th>
                        <th className="text-left p-3 font-medium text-gray-900 whitespace-nowrap" style={{ minWidth: '130px' }}>创建时间</th>
                        <th className="text-left p-3 font-medium text-gray-900 whitespace-nowrap" style={{ minWidth: '130px' }}>更新时间</th>
                        <th className="text-center p-3 font-medium text-gray-900 whitespace-nowrap sticky right-0 bg-gray-50 border-l border-gray-200" style={{ minWidth: '120px' }}>操作</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {orders.map((order) => {
                        const statusDisplay = getOrderStatusDisplay(order.order_status);
                        return (
                          <tr key={order.id} className="hover:bg-gray-50">
                            <td className="p-3 text-gray-900">{order.sequence_number}</td>
                            <td className="p-3">
                              {order.order_status === 'failed' ? (
                                <button
                                  onClick={() => handleFailureDetailsClick(order)}
                                  className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${statusDisplay.bg} ${statusDisplay.text} hover:bg-red-200 transition-colors cursor-pointer underline`}
                                  title="点击查看失败详情"
                                >
                                  {statusDisplay.label}
                                </button>
                              ) : (
                                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${statusDisplay.bg} ${statusDisplay.text}`}>
                                  {statusDisplay.label}
                                </span>
                              )}
                            </td>
                            <td className="p-3 font-medium text-gray-900">{order.traveler_full_name}</td>
                            <td className="p-3 text-gray-900">{order.traveler_surname || '-'}</td>
                            <td className="p-3 text-gray-900">{order.traveler_given_name || '-'}</td>
                            <td className="p-3 text-gray-900">{order.nationality || '-'}</td>
                            <td className="p-3 text-gray-900">{order.gender || '-'}</td>
                            <td className="p-3 text-gray-900 whitespace-nowrap">{order.birth_date || '-'}</td>
                            <td className="p-3 text-gray-900">{order.id_type || '-'}</td>
                            <td className="p-3 text-gray-900">{order.id_number || '-'}</td>
                            <td className="p-3 text-gray-900 whitespace-nowrap">{order.id_expiry_date || '-'}</td>
                            <td className="p-3 text-gray-900">{order.mobile_phone || '-'}</td>
                            <td className="p-3 text-gray-900">{order.mobile_phone_country_code || '-'}</td>
                            <td className="p-3 text-gray-900 whitespace-nowrap">{order.travel_date || '-'}</td>
                            <td className="p-3 text-gray-900">{order.departure_station || '-'}</td>
                            <td className="p-3 text-gray-900">{order.arrival_station || '-'}</td>
                            <td className="p-3 text-gray-900">{order.train_number || '-'}</td>
                            <td className="p-3 text-gray-900">{order.seat_type || '-'}</td>
                            <td className="p-3 text-gray-900">{order.departure_time || '-'}</td>
                            <td className="p-3 text-gray-900">{order.arrival_time || '-'}</td>
                            <td className="p-3 text-gray-900">{order.cost_center || '-'}</td>
                            <td className="p-3 text-gray-900">{order.trip_submission_item || '-'}</td>
                            <td className="p-3 text-gray-900">{order.contact_person || '-'}</td>
                            <td className="p-3 text-gray-900">{order.contact_phone || '-'}</td>
                            <td className="p-3 text-gray-900">{order.contact_email || '-'}</td>
                            <td className="p-3 text-gray-900">{order.approval_reference || '-'}</td>
                            <td className="p-3 text-gray-900">{order.company_name || '-'}</td>
                            <td className="p-3 text-gray-900">{order.booking_agent || '-'}</td>
                            <td className="p-3 text-gray-900">{order.ticket_sms || '-'}</td>
                            <td className="p-3 text-gray-900">
                              {order.amount ? formatAmount(order.amount) : '-'}
                            </td>
                            <td className="p-3 text-gray-900">{order.order_number || '-'}</td>
                            <td className="p-3 text-gray-900">{order.bill_number || '-'}</td>
                            <td className="p-3">
                              <span className={order.fail_reason ? 'text-red-600' : 'text-gray-500'}>
                                {order.fail_reason || '-'}
                              </span>
                            </td>
                            <td className="p-3 text-gray-900 whitespace-nowrap">{formatDateTime(order.created_at)}</td>
                            <td className="p-3 text-gray-900 whitespace-nowrap">{formatDateTime(order.updated_at)}</td>
                            <td className="p-3 text-center sticky right-0 bg-white border-l border-gray-200">
                              <div className="flex items-center justify-center gap-1">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleViewOrder(order)}
                                  title="查看详情"
                                >
                                  <Eye className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleEditOrder(order)}
                                  title="编辑订单"
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                              </div>
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>

                {/* 分页 */}
                {total > pageSize && (
                  <div className="mt-4 flex justify-center">
                    <Pagination
                      currentPage={page}
                      totalPages={Math.ceil(total / pageSize)}
                      totalItems={total}
                      pageSize={pageSize}
                      onPageChange={(newPage) => loadOrders(newPage)}
                    />
                  </div>
                )}
              </>
            )}
          </div>
        </Card>

        {/* 查看订单详情模态框 */}
        {isViewModalOpen && selectedOrder && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] flex flex-col">
              <div className="p-6 border-b border-gray-200 flex-shrink-0">
                <div className="flex items-center justify-between">
                  <h2 className="text-xl font-semibold text-gray-900">订单详情</h2>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsViewModalOpen(false)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              
              <div className="p-6 overflow-y-auto flex-1">
                <div className="space-y-6">
                  {/* 订单状态 - 置顶显示 */}
                  <div className="bg-gray-50 rounded-lg p-4 border-l-4 border-blue-500">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <span className="text-sm font-medium text-gray-700">订单状态：</span>
                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                          selectedOrder.order_status === 'initial' ? 'bg-gray-100 text-gray-800 border border-gray-300' :
                          selectedOrder.order_status === 'submitted' ? 'bg-blue-100 text-blue-800 border border-blue-300' :
                          selectedOrder.order_status === 'processing' ? 'bg-yellow-100 text-yellow-800 border border-yellow-300' :
                          selectedOrder.order_status === 'completed' ? 'bg-green-100 text-green-800 border border-green-300' :
                          selectedOrder.order_status === 'failed' ? 'bg-red-100 text-red-800 border border-red-300' :
                          'bg-gray-100 text-gray-800 border border-gray-300'
                        }`}>
                          <div className={`w-2 h-2 rounded-full mr-2 ${
                            selectedOrder.order_status === 'initial' ? 'bg-gray-500' :
                            selectedOrder.order_status === 'submitted' ? 'bg-blue-500' :
                            selectedOrder.order_status === 'processing' ? 'bg-yellow-500' :
                            selectedOrder.order_status === 'completed' ? 'bg-green-500' :
                            selectedOrder.order_status === 'failed' ? 'bg-red-500' :
                            'bg-gray-500'
                          }`}></div>
                          {getOrderStatusInfo(selectedOrder.order_status).text}
                        </span>
                      </div>
                      <div className="text-xs text-gray-500">
                        订单 #{selectedOrder.sequence_number}
                      </div>
                    </div>
                  </div>

                  {/* 出行人基础信息 */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200">出行人基础信息</h3>
                    <div className="overflow-hidden border border-gray-300 rounded-lg">
                      <table className="w-full">
                        <tbody>
                          <tr className="border-b border-gray-200">
                            <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                              <span className="text-sm font-medium text-gray-700">出行人姓名</span>
                            </td>
                            <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                              <span className="text-sm text-gray-900 font-medium">{selectedOrder.traveler_full_name}</span>
                            </td>
                            <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                              <span className="text-sm font-medium text-gray-700">出行人姓</span>
                            </td>
                            <td className="py-3 px-4 w-1/4">
                              <span className="text-sm text-gray-900">{selectedOrder.traveler_surname || '-'}</span>
                            </td>
                          </tr>
                          <tr className="border-b border-gray-200">
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <span className="text-sm font-medium text-gray-700">出行人名</span>
                            </td>
                            <td className="py-3 px-4 border-r border-gray-200">
                              <span className="text-sm text-gray-900">{selectedOrder.traveler_given_name || '-'}</span>
                            </td>
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <span className="text-sm font-medium text-gray-700">国籍</span>
                            </td>
                            <td className="py-3 px-4">
                              <span className="text-sm text-gray-900">{selectedOrder.nationality || '-'}</span>
                            </td>
                          </tr>
                          <tr className="border-b border-gray-200">
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <span className="text-sm font-medium text-gray-700">性别</span>
                            </td>
                            <td className="py-3 px-4 border-r border-gray-200">
                              <span className="text-sm text-gray-900">{selectedOrder.gender || '-'}</span>
                            </td>
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <span className="text-sm font-medium text-gray-700">出生日期</span>
                            </td>
                            <td className="py-3 px-4">
                              <span className="text-sm text-gray-900">{selectedOrder.birth_date || '-'}</span>
                            </td>
                          </tr>
                          <tr className="border-b border-gray-200">
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <span className="text-sm font-medium text-gray-700">证件类型</span>
                            </td>
                            <td className="py-3 px-4 border-r border-gray-200">
                              <span className="text-sm text-gray-900">{selectedOrder.id_type || '-'}</span>
                            </td>
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <span className="text-sm font-medium text-gray-700">证件号码</span>
                            </td>
                            <td className="py-3 px-4">
                              <span className="text-sm text-gray-900 font-mono">{selectedOrder.id_number || '-'}</span>
                            </td>
                          </tr>
                          <tr className="border-b border-gray-200">
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <span className="text-sm font-medium text-gray-700">证件有效期至</span>
                            </td>
                            <td className="py-3 px-4 border-r border-gray-200">
                              <span className="text-sm text-gray-900">{selectedOrder.id_expiry_date || '-'}</span>
                            </td>
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <span className="text-sm font-medium text-gray-700">手机号国际区号</span>
                            </td>
                            <td className="py-3 px-4">
                              <span className="text-sm text-gray-900">{selectedOrder.mobile_phone_country_code || '+86'}</span>
                            </td>
                          </tr>
                          <tr>
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <span className="text-sm font-medium text-gray-700">手机号</span>
                            </td>
                            <td className="py-3 px-4">
                              <span className="text-sm text-gray-900">{selectedOrder.mobile_phone || '-'}</span>
                            </td>
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200"></td>
                            <td className="py-3 px-4"></td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>

                  {/* 出行信息 */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200">出行信息</h3>
                    <div className="overflow-hidden border border-gray-300 rounded-lg">
                      <table className="w-full">
                        <tbody>
                          <tr className="border-b border-gray-200">
                            <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                              <span className="text-sm font-medium text-gray-700">差旅单号</span>
                            </td>
                            <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                              <span className="text-sm text-gray-900">{selectedOrder.trip_submission_item || '-'}</span>
                            </td>
                            <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                              <span className="text-sm font-medium text-gray-700">出行日期</span>
                            </td>
                            <td className="py-3 px-4 w-1/4">
                              <span className="text-sm text-gray-900">{selectedOrder.travel_date || '-'}</span>
                            </td>
                          </tr>
                          <tr className="border-b border-gray-200">
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <span className="text-sm font-medium text-gray-700">出发站名</span>
                            </td>
                            <td className="py-3 px-4 border-r border-gray-200">
                              <span className="text-sm text-gray-900">{selectedOrder.departure_station || '-'}</span>
                            </td>
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <span className="text-sm font-medium text-gray-700">到达站名</span>
                            </td>
                            <td className="py-3 px-4">
                              <span className="text-sm text-gray-900">{selectedOrder.arrival_station || '-'}</span>
                            </td>
                          </tr>
                          <tr className="border-b border-gray-200">
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <span className="text-sm font-medium text-gray-700">车次</span>
                            </td>
                            <td className="py-3 px-4 border-r border-gray-200">
                              <span className="text-sm text-gray-900 font-medium text-blue-600">{selectedOrder.train_number || '-'}</span>
                            </td>
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <span className="text-sm font-medium text-gray-700">座位类型</span>
                            </td>
                            <td className="py-3 px-4">
                              <span className="text-sm text-gray-900">{selectedOrder.seat_type || '-'}</span>
                            </td>
                          </tr>
                          <tr className="border-b border-gray-200">
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <span className="text-sm font-medium text-gray-700">出发时间</span>
                            </td>
                            <td className="py-3 px-4 border-r border-gray-200">
                              <span className="text-sm text-gray-900">{selectedOrder.departure_time || '-'}</span>
                            </td>
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <span className="text-sm font-medium text-gray-700">到达时间</span>
                            </td>
                            <td className="py-3 px-4">
                              <span className="text-sm text-gray-900">{selectedOrder.arrival_time || '-'}</span>
                            </td>
                          </tr>
                          <tr>
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <span className="text-sm font-medium text-gray-700">成本中心</span>
                            </td>
                            <td className="py-3 px-4">
                              <span className="text-sm text-gray-900">{selectedOrder.cost_center || '-'}</span>
                            </td>
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200"></td>
                            <td className="py-3 px-4"></td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>

                  {/* 对账单信息 */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200">对账单信息</h3>
                    <div className="overflow-hidden border border-gray-300 rounded-lg">
                      <table className="w-full">
                        <tbody>
                          <tr className="border-b border-gray-200">
                            <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                              <span className="text-sm font-medium text-gray-700">公司名称</span>
                            </td>
                            <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                              <span className="text-sm text-gray-900">{selectedOrder.company_name || '-'}</span>
                            </td>
                            <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                              <span className="text-sm font-medium text-gray-700">代订人</span>
                            </td>
                            <td className="py-3 px-4 w-1/4">
                              <span className="text-sm text-gray-900">{selectedOrder.booking_agent || '-'}</span>
                            </td>
                          </tr>
                          <tr className="border-b border-gray-200">
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <span className="text-sm font-medium text-gray-700">金额</span>
                            </td>
                            <td className="py-3 px-4 border-r border-gray-200">
                              <span className="text-sm text-gray-900 font-medium text-green-600">{formatAmount(selectedOrder.amount)}</span>
                            </td>
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <span className="text-sm font-medium text-gray-700">订单号</span>
                            </td>
                            <td className="py-3 px-4">
                              <span className="text-sm text-gray-900 font-mono">{selectedOrder.order_number || '-'}</span>
                            </td>
                          </tr>
                          <tr className={selectedOrder.ticket_sms ? "border-b border-gray-200" : ""}>
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <span className="text-sm font-medium text-gray-700">账单号</span>
                            </td>
                            <td className="py-3 px-4">
                              <span className="text-sm text-gray-900 font-mono">{selectedOrder.bill_number || '-'}</span>
                            </td>
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200"></td>
                            <td className="py-3 px-4"></td>
                          </tr>
                          {/* 出票短信 - 占据整行 */}
                          {selectedOrder.ticket_sms && (
                            <tr>
                              <td className="py-3 px-4 bg-yellow-50 border-r border-gray-200">
                                <span className="text-sm font-medium text-yellow-800">出票短信</span>
                              </td>
                              <td className="py-3 px-4 bg-yellow-50" colSpan={3}>
                                <div className="text-sm text-gray-900 bg-yellow-50 p-2 rounded border max-h-24 overflow-y-auto">
                                  <pre className="whitespace-pre-wrap text-xs">{selectedOrder.ticket_sms}</pre>
                                </div>
                              </td>
                            </tr>
                          )}
                        </tbody>
                      </table>
                    </div>
                  </div>

                  {/* 失败原因 - 单独显示 */}
                  {selectedOrder.fail_reason && (
                    <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                      <div className="flex items-start gap-3">
                        <div className="flex-shrink-0">
                          <AlertTriangle className="h-5 w-5 text-red-500" />
                        </div>
                        <div className="flex-1">
                          <h4 className="font-medium text-sm text-red-800">
                            {selectedOrder.order_status === 'check_failed' ? '验证失败' : '预定失败'}
                          </h4>
                          <p className="mt-1 text-sm text-red-700">
                            {selectedOrder.fail_reason}
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}


        {/* 失败详情模态框 */}
        {selectedFailedOrder && (
          <FailureDetailsModal
            isOpen={isFailureModalOpen}
            onClose={() => setIsFailureModalOpen(false)}
            orderId={selectedFailedOrder.id.toString()}
            taskId={taskId}
            failReason={selectedFailedOrder.fail_reason || undefined}
          />
        )}
      </div>
    </MainLayout>
  );
};

export default TaskOrderDetailPage; 