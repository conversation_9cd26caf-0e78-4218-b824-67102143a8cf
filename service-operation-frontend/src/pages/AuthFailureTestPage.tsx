import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuth } from '@/store/auth-context';
import { api } from '@/api/request';

/**
 * 认证失败测试页面
 * 用于测试各种认证失败场景的处理
 */
const AuthFailureTestPage: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const [testResults, setTestResults] = useState<string[]>([]);

  const addTestResult = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setTestResults(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 9)]);
  };

  // 测试1: 手动触发认证失败事件
  const testManualAuthFailure = () => {
    addTestResult('开始测试：手动触发认证失败事件');
    
    const authFailureEvent = new CustomEvent('authenticationFailure', {
      detail: {
        reason: 'manual_test',
        message: '手动测试认证失败',
        forceRedirect: true
      }
    });
    
    window.dispatchEvent(authFailureEvent);
    addTestResult('已触发认证失败事件');
  };

  // 测试2: 清除token并发起API请求
  const testTokenExpiredAPI = async () => {
    addTestResult('开始测试：清除token后发起API请求');

    // 清除token
    localStorage.removeItem('access_token');
    localStorage.removeItem('app_token');
    addTestResult('已清除本地token');

    try {
      // 发起一个需要认证的API请求
      await api.get('/user-management/users');
      addTestResult('API请求成功（意外）');
    } catch (error) {
      addTestResult(`API请求失败: ${error.message}`);
    }
  };

  // 测试5: 测试权限获取API
  const testPermissionAPI = async () => {
    addTestResult('开始测试：权限获取API');

    try {
      // 测试获取用户权限详情
      if (user?.id) {
        const response = await api.get(`/user-management/user-permissions/${user.id}`);
        addTestResult('权限API请求成功');
      } else {
        addTestResult('无用户ID，跳过权限API测试');
      }
    } catch (error) {
      addTestResult(`权限API请求失败: ${error.message}`);
    }
  };

  // 测试3: 模拟401错误
  const testSimulate401Error = async () => {
    addTestResult('开始测试：模拟401错误');
    
    try {
      // 使用无效token发起请求
      const response = await fetch('/api/test-endpoint', {
        headers: {
          'Authorization': 'Bearer invalid-token'
        }
      });
      
      if (response.status === 401) {
        addTestResult('收到401响应，触发认证失败处理');
        
        const authFailureEvent = new CustomEvent('authenticationFailure', {
          detail: {
            reason: 'simulated_401',
            message: '模拟401错误',
            forceRedirect: true
          }
        });
        
        window.dispatchEvent(authFailureEvent);
      } else {
        addTestResult(`收到响应状态: ${response.status}`);
      }
    } catch (error) {
      addTestResult(`请求异常: ${error.message}`);
      
      // 即使是网络错误，也可以测试认证失败处理
      const authFailureEvent = new CustomEvent('authenticationFailure', {
        detail: {
          reason: 'network_error_test',
          message: '网络错误测试',
          forceRedirect: true
        }
      });
      
      window.dispatchEvent(authFailureEvent);
    }
  };

  // 测试4: 直接清除认证状态
  const testClearAuthState = () => {
    addTestResult('开始测试：直接清除认证状态');
    
    // 清除所有认证相关数据
    const authKeys = ['access_token', 'app_token', 'token', 'user', 'userInfo', 'sso_state'];
    authKeys.forEach(key => {
      localStorage.removeItem(key);
    });
    
    addTestResult('已清除所有认证数据');
    
    // 触发认证失败事件
    const authFailureEvent = new CustomEvent('authenticationFailure', {
      detail: {
        reason: 'clear_auth_test',
        message: '清除认证状态测试',
        forceRedirect: true
      }
    });
    
    window.dispatchEvent(authFailureEvent);
    addTestResult('已触发认证失败事件');
  };

  // 清除测试结果
  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>认证失败处理测试</CardTitle>
          <CardDescription>
            测试各种认证失败场景的处理机制，确保能够正确跳转到登录页面
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 当前认证状态 */}
          <div className="p-4 bg-gray-50 rounded-lg">
            <h3 className="font-medium mb-2">当前认证状态</h3>
            <p>认证状态: {isAuthenticated ? '✅ 已认证' : '❌ 未认证'}</p>
            <p>用户信息: {user ? `${user.username} (${user.email})` : '无'}</p>
            <p>Access Token: {localStorage.getItem('access_token') ? '存在' : '不存在'}</p>
          </div>

          {/* 测试按钮 */}
          <div className="grid grid-cols-2 gap-4">
            <Button onClick={testManualAuthFailure} variant="outline">
              测试1: 手动触发认证失败
            </Button>
            <Button onClick={testTokenExpiredAPI} variant="outline">
              测试2: Token过期API请求
            </Button>
            <Button onClick={testSimulate401Error} variant="outline">
              测试3: 模拟401错误
            </Button>
            <Button onClick={testClearAuthState} variant="outline">
              测试4: 清除认证状态
            </Button>
            <Button onClick={testPermissionAPI} variant="outline">
              测试5: 权限获取API
            </Button>
          </div>

          {/* 测试结果 */}
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <h3 className="font-medium">测试结果</h3>
              <Button onClick={clearResults} variant="ghost" size="sm">
                清除结果
              </Button>
            </div>
            <div className="bg-black text-green-400 p-4 rounded-lg font-mono text-sm max-h-60 overflow-y-auto">
              {testResults.length === 0 ? (
                <p className="text-gray-500">暂无测试结果</p>
              ) : (
                testResults.map((result, index) => (
                  <div key={index}>{result}</div>
                ))
              )}
            </div>
          </div>

          {/* 说明 */}
          <div className="p-4 bg-blue-50 rounded-lg">
            <h3 className="font-medium mb-2">测试说明</h3>
            <ul className="text-sm space-y-1">
              <li>• 测试1: 直接触发认证失败事件，测试事件处理机制</li>
              <li>• 测试2: 清除token后发起API请求，测试API层面的认证处理</li>
              <li>• 测试3: 模拟401错误响应，测试HTTP错误处理</li>
              <li>• 测试4: 直接清除认证状态，测试状态变化监听</li>
              <li>• 测试5: 测试权限获取API，验证后端权限验证是否正常工作</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AuthFailureTestPage;
