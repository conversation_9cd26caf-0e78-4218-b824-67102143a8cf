import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { autoAuthService, AuthError } from '@/services/auth';
import { api } from '@/services/system';
import { DEVELOPER_KEY, DEVELOPER_SECRET } from '@/utils/constants';

interface TokenInfo {
  hasToken: boolean;
  isValid: boolean;
  userInfo: any;
  expiresIn: number;
}

export const AuthTestPage: React.FC = () => {
  const [tokenInfo, setTokenInfo] = useState<TokenInfo>({
    hasToken: false,
    isValid: false,
    userInfo: null,
    expiresIn: 0,
  });
  
  const [testResult, setTestResult] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // 刷新token信息
  const refreshTokenInfo = () => {
    setTokenInfo(autoAuthService.getTokenInfo());
  };

  // 组件挂载时获取token信息
  useEffect(() => {
    refreshTokenInfo();
  }, []);

  // 获取新token
  const handleGetToken = async () => {
    setIsLoading(true);
    try {
      const token = await autoAuthService.getValidToken();
      setTestResult(`✅ 成功获取token: ${token.substring(0, 50)}...`);
      refreshTokenInfo();
    } catch (error) {
      if (error instanceof AuthError) {
        setTestResult(`❌ 认证失败: ${error.message}`);
      } else {
        setTestResult(`❌ 获取token失败: ${error}`);
      }
    } finally {
      setIsLoading(false);
    }
  };

  // 测试API调用
  const handleTestApi = async () => {
    setIsLoading(true);
    try {
      const response = await api.get('/project/', {
        params: { page: '1', page_size: '5' }
      });
      const total = (response.data as any)?.total || 0;
      setTestResult(`✅ API测试成功: 获取到 ${total} 个项目`);
    } catch (error: any) {
      setTestResult(`❌ API测试失败: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  // 清除token
  const handleClearToken = () => {
    autoAuthService.clearToken();
    setTestResult('🗑️ 已清除本地token');
    refreshTokenInfo();
  };

  // 格式化剩余时间
  const formatExpiresIn = (seconds: number): string => {
    if (seconds <= 0) return '已过期';
    
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    return `${hours}小时${minutes}分钟${secs}秒`;
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <h1 className="text-2xl font-bold mb-6">自动认证功能测试</h1>
      
      {/* 环境变量配置状态 */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>配置状态</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <div className="flex items-center gap-2 mb-2">
                <span className="font-medium">开发者密钥:</span>
                <Badge variant={DEVELOPER_KEY ? "default" : "destructive"}>
                  {DEVELOPER_KEY ? "已配置" : "未配置"}
                </Badge>
              </div>
              {DEVELOPER_KEY && (
                <div className="text-sm text-gray-600">
                  Key: {DEVELOPER_KEY.substring(0, 10)}...
                </div>
              )}
            </div>
            
            <div>
              <div className="flex items-center gap-2 mb-2">
                <span className="font-medium">开发者密钥秘钥:</span>
                <Badge variant={DEVELOPER_SECRET ? "default" : "destructive"}>
                  {DEVELOPER_SECRET ? "已配置" : "未配置"}
                </Badge>
              </div>
            </div>
          </div>
          
          {(!DEVELOPER_KEY || !DEVELOPER_SECRET) && (
            <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <p className="text-yellow-800 text-sm">
                <strong>配置提示:</strong> 请在 .env 文件中配置 VITE_DEVELOPER_KEY 和 VITE_DEVELOPER_SECRET
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Token状态 */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Token状态
            <Button variant="outline" size="sm" onClick={refreshTokenInfo}>
              刷新
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <div className="flex items-center gap-2 mb-2">
                <span className="font-medium">是否存在:</span>
                <Badge variant={tokenInfo.hasToken ? "default" : "secondary"}>
                  {tokenInfo.hasToken ? "是" : "否"}
                </Badge>
              </div>
            </div>
            
            <div>
              <div className="flex items-center gap-2 mb-2">
                <span className="font-medium">是否有效:</span>
                <Badge variant={tokenInfo.isValid ? "default" : "destructive"}>
                  {tokenInfo.isValid ? "有效" : "无效"}
                </Badge>
              </div>
            </div>
            
            <div>
              <div className="flex items-center gap-2 mb-2">
                <span className="font-medium">剩余时间:</span>
                <span className="text-sm">{formatExpiresIn(tokenInfo.expiresIn)}</span>
              </div>
            </div>
          </div>
          
          {tokenInfo.userInfo && (
            <div className="mt-4 p-3 bg-gray-50 rounded-lg">
              <h4 className="font-medium mb-2">Token信息:</h4>
              <pre className="text-xs text-gray-600 overflow-x-auto">
                {JSON.stringify(tokenInfo.userInfo, null, 2)}
              </pre>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 操作按钮 */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>测试操作</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-3">
            <Button 
              onClick={handleGetToken} 
              disabled={isLoading}
              variant="default"
            >
              {isLoading ? "获取中..." : "获取/刷新Token"}
            </Button>
            
            <Button 
              onClick={handleTestApi} 
              disabled={isLoading}
              variant="outline"
            >
              {isLoading ? "测试中..." : "测试API调用"}
            </Button>
            
            <Button 
              onClick={handleClearToken}
              variant="destructive"
            >
              清除Token
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 测试结果 */}
      {testResult && (
        <Card>
          <CardHeader>
            <CardTitle>测试结果</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="p-3 bg-gray-50 rounded-lg">
              <pre className="text-sm whitespace-pre-wrap">{testResult}</pre>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default AuthTestPage;
