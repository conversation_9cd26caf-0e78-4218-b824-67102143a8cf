import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/store/auth-context';

const LoginPage: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [loginError, setLoginError] = useState<string | null>(null);

  const navigate = useNavigate();
  const { login, loginWithSSO } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoginError(null);

    if (!email || !password) {
      setLoginError('请填写所有必填字段');
      return;
    }

    try {
      setIsSubmitting(true);
      await login(email, password);
      navigate('/dashboard');
    } catch (error) {
      setLoginError(error instanceof Error ? error.message : '登录失败，请重试');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="flex min-h-screen">
      {/* 左侧装饰区 */}
      <div className="hidden md:flex flex-1 items-center justify-center relative overflow-hidden">
        {/* 渐变背景+装饰 */}
        <div className="absolute inset-0 z-0 bg-gradient-to-br from-blue-400 via-cyan-300 to-green-200" />
        {/* 云朵/山峰SVG装饰，可替换为你自己的插画 */}
        <svg className="absolute left-0 bottom-0 w-full h-48" viewBox="0 0 1440 320" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path fill="#7dd3fc" fillOpacity="0.5" d="M0,224L60,197.3C120,171,240,117,360,117.3C480,117,600,171,720,197.3C840,224,960,224,1080,197.3C1200,171,1320,117,1380,90.7L1440,64L1440,320L1380,320C1320,320,1200,320,1080,320C960,320,840,320,720,320C600,320,480,320,360,320C240,320,120,320,60,320L0,320Z" />
          <path fill="#38bdf8" fillOpacity="0.3" d="M0,288L80,272C160,256,320,224,480,197.3C640,171,800,149,960,154.7C1120,160,1280,192,1360,208L1440,224L1440,320L1360,320C1280,320,1120,320,960,320C800,320,640,320,480,320C320,320,160,320,80,320L0,320Z" />
        </svg>
        <div className="relative z-10 text-white text-3xl font-bold tracking-wide select-none w-full">
          <img src="/logo.png" alt="Logo" className="h-28 w-auto mb-6 mx-auto" />
          <div className="text-center text-2xl font-semibold drop-shadow">服务运营自动化平台</div>
          <div className="text-center text-base font-light mt-2 opacity-90 drop-shadow">让服务更高效 · 更智能 · 更有价值</div>
        </div>
      </div>
      {/* 右侧登录表单区 */}
      <div className="flex-1 flex items-center justify-center bg-white">
        <div className="w-full max-w-md p-8">
          <Card className="shadow-2xl border-0 rounded-2xl bg-white">
            <CardHeader className="space-y-1">
              <CardTitle className="text-2xl font-bold text-center mb-2">欢迎登录</CardTitle>
              <CardDescription className="text-center">
                请输入您的邮箱和密码登录
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loginError && (
                <div className="mb-4 p-3 bg-red-50 border border-red-200 text-red-600 rounded-md text-sm">
                  {loginError}
                </div>
              )}
              <form onSubmit={handleSubmit}>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="email">邮箱</Label>
                    <Input
                      id="email"
                      type="email"
                      placeholder=""
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      autoComplete="username"
                      required
                      className="rounded-lg border-gray-300 focus:border-green-500 focus:ring-green-200 h-12 text-base"
                    />
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="password">密码</Label>
                    </div>
                    <Input
                      id="password"
                      type="password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      autoComplete="current-password"
                      required
                      className="rounded-lg border-gray-300 focus:border-green-500 focus:ring-green-200 h-12 text-base"
                    />
                  </div>
                  <Button
                    type="submit"
                    className="w-full bg-green-500 hover:bg-green-600 text-white rounded-lg h-12 text-base font-bold shadow-sm transition"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? '登录中...' : '登录'}
                  </Button>
                  <div className="relative my-4">
                    <div className="absolute inset-0 flex items-center">
                      <span className="w-full border-t border-gray-300" />
                    </div>
                    <div className="relative flex justify-center text-xs uppercase">
                      <span className="bg-white px-2 text-gray-500">或</span>
                    </div>
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    className="w-full rounded-lg h-12 text-base font-bold border-green-500 text-green-600 hover:bg-green-50 transition"
                    onClick={() => loginWithSSO()}
                    disabled={isSubmitting}
                  >
                    同程管家统一登录
                  </Button>
                </div>
              </form>
            </CardContent>
            <CardFooter className="flex justify-center">
              <p className="text-sm text-gray-400">
              </p>
            </CardFooter>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
