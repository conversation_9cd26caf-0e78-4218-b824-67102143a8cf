import React from 'react';
import { useAuth } from '@/store/auth-context';
import { Card } from '@/components/ui/card';

/**
 * 认证调试页面
 * 用于调试认证状态和用户信息
 */
const AuthDebugPage: React.FC = () => {
  const { user, isAuthenticated, isLoading } = useAuth();

  // 获取本地存储的信息
  const localStorageInfo = {
    userInfo: localStorage.getItem('userInfo'),
    accessToken: localStorage.getItem('access_token'),
    appToken: localStorage.getItem('app_token'),
    user: localStorage.getItem('user'),
  };

  return (
    <div className="p-6 space-y-6">
      <h1 className="text-2xl font-bold text-gray-900">认证状态调试</h1>

      {/* 认证状态 */}
      <Card className="p-6">
        <h2 className="text-lg font-semibold mb-4">认证状态</h2>
        <div className="space-y-2">
          <div><strong>已认证:</strong> {isAuthenticated ? '是' : '否'}</div>
          <div><strong>加载中:</strong> {isLoading ? '是' : '否'}</div>
        </div>
      </Card>

      {/* 用户信息 */}
      <Card className="p-6">
        <h2 className="text-lg font-semibold mb-4">用户信息</h2>
        {user ? (
          <div className="space-y-2">
            <div><strong>ID:</strong> {user.id}</div>
            <div><strong>用户名:</strong> {user.username}</div>
            <div><strong>邮箱:</strong> {user.email}</div>
            <div><strong>角色:</strong> {user.role}</div>
            <div><strong>部门:</strong> {user.department}</div>
            <div><strong>工号:</strong> {user.work_id}</div>
          </div>
        ) : (
          <div>无用户信息</div>
        )}
      </Card>

      {/* 本地存储信息 */}
      <Card className="p-6">
        <h2 className="text-lg font-semibold mb-4">本地存储信息</h2>
        <div className="space-y-4">
          {Object.entries(localStorageInfo).map(([key, value]) => (
            <div key={key}>
              <strong>{key}:</strong>
              <pre className="mt-1 p-2 bg-gray-100 rounded text-xs overflow-auto">
                {value || '(空)'}
              </pre>
            </div>
          ))}
        </div>
      </Card>

      {/* 解析后的用户信息 */}
      <Card className="p-6">
        <h2 className="text-lg font-semibold mb-4">解析后的用户信息</h2>
        {localStorageInfo.userInfo ? (
          <pre className="p-2 bg-gray-100 rounded text-xs overflow-auto">
            {JSON.stringify(JSON.parse(localStorageInfo.userInfo), null, 2)}
          </pre>
        ) : (
          <div>无用户信息</div>
        )}
      </Card>
    </div>
  );
};

export default AuthDebugPage;
