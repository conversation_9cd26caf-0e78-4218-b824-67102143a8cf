import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Search, Grid, List, Plus, Trash2 } from 'lucide-react';
import { Project, ViewMode, ProjectQueryParams } from '@/types/project';
import { ProjectService } from '@/services/project';
import { trainOrderApi, ProjectOrderStatsResponse } from '@/api/trainOrder';
import ProjectTable from '@/components/project/ProjectTable';
import ProjectCard from '@/components/project/ProjectCard';
import CreateProjectCard from '@/components/project/CreateProjectCard';
import CreateProjectModal from '@/components/project/CreateProjectModal';
import EditProjectModal from '@/components/project/EditProjectModal';
import Pagination from '@/components/common/Pagination';
import MainLayout from '@/components/layout/MainLayout';

const ProjectManagementPage: React.FC = () => {
  const navigate = useNavigate();

  // 状态管理
  const [projects, setProjects] = useState<Project[]>([]);
  const [projectStats, setProjectStats] = useState<Map<number, ProjectOrderStatsResponse>>(new Map());
  const [loading, setLoading] = useState(false);
  const [viewMode, setViewMode] = useState<ViewMode>(ViewMode.CARD);
  const [searchTerm, setSearchTerm] = useState('');
  const [clientFilter, setClientFilter] = useState('');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [createLoading, setCreateLoading] = useState(false);
  const [editLoading, setEditLoading] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [projectToDelete, setProjectToDelete] = useState<Project | null>(null);

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(11);
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(0);

  // 获取项目列表
  const fetchProjects = async () => {
    setLoading(true);
    try {
      const params: ProjectQueryParams = {
        page: currentPage,
        page_size: pageSize,
      };

      if (searchTerm.trim()) {
        params.project_name = searchTerm.trim();
      }

      if (clientFilter.trim()) {
        params.client_name = clientFilter.trim();
      }

      const response = await ProjectService.getProjects(params);
      
      if (response.success && response.data) {
        setProjects(response.data.items);
        setTotalItems(response.data.total);
        setTotalPages(Math.ceil(response.data.total / pageSize));
        
        // 获取每个项目的火车票统计数据
        const statsMap = new Map<number, ProjectOrderStatsResponse>();
        await Promise.allSettled(
          response.data.items.map(async (project) => {
            try {
              const statsResponse = await trainOrderApi.getProjectStats(project.id);
              statsMap.set(project.id, statsResponse.data);
            } catch (error) {
              console.warn(`获取项目${project.id}统计失败:`, error);
              // 设置默认值
              statsMap.set(project.id, {
                project_id: project.id,
                total_orders: 0,
                completed_orders: 0,
                completed_amount: "0"
              });
            }
          })
        );
        setProjectStats(statsMap);
      } else {
        console.error('获取项目列表失败:', response.error);
        setProjects([]);
        setTotalItems(0);
        setTotalPages(0);
        setProjectStats(new Map());
      }
    } catch (error) {
      console.error('获取项目列表异常:', error);
      setProjects([]);
      setTotalItems(0);
      setTotalPages(0);
      setProjectStats(new Map());
    } finally {
      setLoading(false);
    }
  };

  // 创建项目
  const handleCreateProject = async (data: any) => {
    setCreateLoading(true);
    try {
      console.log('准备创建项目，数据:', {
        ...data,
        creator_user_id_type: typeof data.creator_user_id,
        creator_user_id_value: data.creator_user_id
      });

      const response = await ProjectService.createProject(data);
      
      if (response.success) {
        setShowCreateModal(false);
        // 刷新列表
        setCurrentPage(1);
        await fetchProjects();
      } else {
        console.error('创建项目失败:', response.error);
        // 使用更友好的错误提示
        let errorMessage = '创建项目失败';
        if (response.error === '创建用户不存在') {
          errorMessage = '创建失败：当前登录用户信息无效，请尝试重新登录';
        } else {
          errorMessage = `创建失败：${response.error}`;
        }
        alert(errorMessage);
      }
    } catch (error) {
      console.error('创建项目异常:', error);
      alert('创建项目失败，请稍后重试');
    } finally {
      setCreateLoading(false);
    }
  };

  // 处理搜索
  const handleSearch = () => {
    setCurrentPage(1);
    fetchProjects();
  };

  // 处理清空
  const handleClear = () => {
    setSearchTerm('');
    setClientFilter('');
    setCurrentPage(1);
    // 延迟一下让状态更新后再获取数据
    setTimeout(() => {
      fetchProjects();
    }, 100);
  };

  // 处理分页
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePageSizeChange = (size: number) => {
    setPageSize(size);
    setCurrentPage(1);
  };

  // 初始加载和依赖更新
  useEffect(() => {
    fetchProjects();
  }, [currentPage, pageSize]);

  // 项目操作处理
  const handleViewProject = (project: Project) => {
    console.log('查看项目:', project);
    navigate(`/project-detail/${project.id}`);
  };

  // 处理编辑项目
  const handleEditProject = (project: Project) => {
    setSelectedProject(project);
    setShowEditModal(true);
  };

  // 保存编辑的项目
  const handleSaveEdit = async (data: any) => {
    setEditLoading(true);
    try {
      // 保留原有的 creator_user_id
      const updateData = {
        ...data,
        creator_user_id: selectedProject?.creator_user_id,
      };
      
      const response = await ProjectService.updateProject(data.id, updateData);
      
      if (response.success) {
        setShowEditModal(false);
        setSelectedProject(null);
        // 刷新列表
        await fetchProjects();
      } else {
        console.error('更新项目失败:', response.error);
        alert('更新项目失败：' + response.error);
      }
    } catch (error) {
      console.error('更新项目异常:', error);
      alert('更新项目失败');
    } finally {
      setEditLoading(false);
    }
  };

  const handleDeleteProject = (project: Project) => {
    setProjectToDelete(project);
    setShowDeleteModal(true);
  };

  const confirmDeleteProject = async () => {
    if (!projectToDelete) return;
    setDeleteLoading(true);
    try {
      const response = await ProjectService.deleteProject(projectToDelete.id);
      if (response.success) {
        setShowDeleteModal(false);
        setProjectToDelete(null);
        await fetchProjects();
      } else {
        alert('删除失败：' + response.error);
      }
    } catch (error) {
      console.error('删除项目异常:', error);
      alert('删除失败');
    } finally {
      setDeleteLoading(false);
    }
  };

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* 页面头部 - 合并所有控件到一行 */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 gap-4">
            {/* 左侧：标题 */}
            <div className="flex-shrink-0">
              <h1 className="text-xl font-bold text-gray-900">项目管理</h1>
              <p className="text-sm text-gray-500">
                高效管理您的团队住宿和交通预订
              </p>
            </div>

            {/* 中间：搜索区域 */}
            <div className="flex-1 flex flex-col md:flex-row md:items-center gap-3 max-w-2xl">
              <div className="flex-1 max-w-xs">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    type="text"
                    placeholder="搜索项目名称..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                    className="w-full h-10 pl-10 pr-4 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                  />
                </div>
              </div>
              
              <div className="flex-1 max-w-xs">
                <input
                  type="text"
                  placeholder="客户名称..."
                  value={clientFilter}
                  onChange={(e) => setClientFilter(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  className="w-full h-10 px-4 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                />
              </div>

              <button
                onClick={handleSearch}
                className="h-10 px-4 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm font-medium whitespace-nowrap"
              >
                搜索
              </button>

              <button
                onClick={handleClear}
                className="h-10 px-4 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors text-sm font-medium whitespace-nowrap"
              >
                重置
              </button>
            </div>

            {/* 右侧：视图切换和新建按钮 */}
            <div className="flex items-center space-x-3 flex-shrink-0">
              {/* 视图切换 */}
              <div className="flex bg-gray-100 rounded-md p-1">
                <button
                  onClick={() => setViewMode(ViewMode.LIST)}
                  className={`p-2 rounded transition-colors ${
                    viewMode === ViewMode.LIST
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                  title="列表视图"
                >
                  <List className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setViewMode(ViewMode.CARD)}
                  className={`p-2 rounded transition-colors ${
                    viewMode === ViewMode.CARD
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                  title="卡片视图"
                >
                  <Grid className="w-4 h-4" />
                </button>
              </div>

              {/* 新建项目按钮 */}
              <button
                onClick={() => setShowCreateModal(true)}
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm font-medium whitespace-nowrap"
              >
                <Plus className="w-4 h-4 mr-2" />
                新建项目
              </button>
            </div>
          </div>
        </div>

        {/* 内容区域 */}
        {loading ? (
          <div className="bg-white rounded-lg border border-gray-200 p-8 text-center">
            <p className="text-gray-500">加载中...</p>
          </div>
        ) : (
          <>
            {viewMode === ViewMode.LIST ? (
              <ProjectTable
                projects={projects}
                onView={handleViewProject}
                onEdit={handleEditProject}
                onDelete={handleDeleteProject}
              />
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {/* 创建新项目卡片 */}
                <CreateProjectCard onClick={() => setShowCreateModal(true)} />
                
                {/* 项目卡片 */}
                {projects.map((project) => (
                  <ProjectCard
                    key={project.id}
                    project={project}
                    projectStats={projectStats.get(project.id)}
                    onView={handleViewProject}
                    onEdit={handleEditProject}
                    onDelete={handleDeleteProject}
                  />
                ))}
              </div>
            )}

            {/* 分页 */}
            {totalItems > 0 && (
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                totalItems={totalItems}
                itemsPerPage={pageSize}
                onPageChange={handlePageChange}
                onPageSizeChange={handlePageSizeChange}
              />
            )}
          </>
        )}

        {/* 创建项目模态框 */}
        <CreateProjectModal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          onSubmit={handleCreateProject}
          loading={createLoading}
        />

        {/* 编辑项目模态框 */}
        <EditProjectModal
          isOpen={showEditModal}
          project={selectedProject}
          onClose={() => {
            setShowEditModal(false);
            setSelectedProject(null);
          }}
          onSubmit={handleSaveEdit}
          loading={editLoading}
        />

        {/* 删除项目确认弹窗 */}
        {showDeleteModal && projectToDelete && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
              <div className="p-6">
                <div className="flex items-center justify-center w-12 h-12 mx-auto mb-4 bg-red-100 rounded-full">
                  <Trash2 className="h-6 w-6 text-red-600" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 text-center mb-4">确认删除项目</h3>
                <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                  <div className="text-sm space-y-2">
                    <p><span className="font-medium">项目名称：</span>{projectToDelete.project_name}</p>
                    <p><span className="font-medium">客户名称：</span>{projectToDelete.client_name}</p>
                    <p><span className="font-medium">项目编号：</span>{projectToDelete.project_number}</p>
                  </div>
                </div>
                <p className="text-sm text-gray-600 text-center mb-6">
                  此操作不可撤销，确定要删除这个项目吗？
                </p>
                <div className="flex justify-center gap-3">
                  <button
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    onClick={() => setShowDeleteModal(false)}
                    disabled={deleteLoading}
                  >
                    取消
                  </button>
                  <button
                    className={`px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 ${deleteLoading ? 'opacity-75 cursor-not-allowed' : ''}`}
                    onClick={confirmDeleteProject}
                    disabled={deleteLoading}
                  >
                    {deleteLoading ? '删除中...' : '确认删除'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  );
};

export default ProjectManagementPage; 