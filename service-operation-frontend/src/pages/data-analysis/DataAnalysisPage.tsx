import React, { useState, useEffect, useRef } from 'react';
import { useSearchParams } from 'react-router-dom';
import * as echarts from 'echarts';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import MainLayout from '@/components/layout/MainLayout';
// 暂时不使用图表库，使用简单的CSS实现
// import {
//   LineChart,
//   Line,
//   XAxis,
//   YAxis,
//   CartesianGrid,
//   Tooltip,
//   ResponsiveContainer
// } from 'recharts';
import {
  TrendingUp,
  Users,
  FileText,
  Calendar,
  BarChart3,
  PieChart as PieChartIcon,
  Download,
  Plane,
  Train,
  Building,
  CreditCard
} from 'lucide-react';

import { dataAnalysisApi, type OverallAnalysisResponse, type UserAnalysisRequest, type PassportAnalysisResponse, type BookingAnalysisResponse } from '@/api/dataAnalysis';
import ErrorLogsTab from '@/components/data-analysis/ErrorLogsTab';

// 数据接口定义
interface DailyData {
  date: string;
  count: number;
  total_orders?: number;
  completed_orders?: number;
  success_rate?: number;
}

// ECharts柱状图组件
interface EChartsBarChartProps {
  data: DailyData[];
}

const EChartsBarChart: React.FC<EChartsBarChartProps> = ({ data }) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);

  useEffect(() => {
    if (!chartRef.current) return;

    // 初始化图表
    if (!chartInstance.current) {
      chartInstance.current = echarts.init(chartRef.current);
    }

    // 准备数据
    const dates = data.map(item => item.date);
    const counts = data.map(item => item.count || 0);

    // 调试日志
    console.log('ECharts data:', { data, dates, counts });

    // 配置选项
    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: function(params: any) {
          const data = params[0];
          return `${data.name}<br/>${data.seriesName}: ${data.value} 次`;
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: dates,
        axisTick: {
          alignWithLabel: true
        },
        axisLabel: {
          rotate: 45,
          fontSize: 12
        }
      },
      yAxis: {
        type: 'value',
        minInterval: 1,
        axisLabel: {
          fontSize: 12
        }
      },
      series: [
        {
          name: '使用次数',
          type: 'bar',
          barWidth: '60%',
          data: counts,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#a855f7' },
              { offset: 1, color: '#7c3aed' }
            ]),
            borderRadius: [4, 4, 0, 0]
          },
          emphasis: {
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#9333ea' },
                { offset: 1, color: '#6b21a8' }
              ])
            }
          },
          label: {
            show: true,
            position: 'top',
            fontSize: 12,
            fontWeight: 'bold',
            color: '#374151'
          }
        }
      ]
    };

    // 设置配置
    chartInstance.current.setOption(option);

    // 响应式处理
    const handleResize = () => {
      chartInstance.current?.resize();
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [data]);

  // 组件卸载时销毁图表
  useEffect(() => {
    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose();
        chartInstance.current = null;
      }
    };
  }, []);

  return <div ref={chartRef} className="w-full h-64" />;
};

// Tab类型定义
type TabType = 'passport' | 'train' | 'hotel' | 'flight' | 'error-logs';

const DataAnalysisPage: React.FC = () => {
  const { toast } = useToast();
  const [searchParams, setSearchParams] = useSearchParams();
  const [activeTab, setActiveTab] = useState<TabType>('passport');
  const [loading, setLoading] = useState(false);

  // 各个Tab的数据状态
  const [passportData, setPassportData] = useState<PassportAnalysisResponse | null>(null);
  const [trainData, setTrainData] = useState<BookingAnalysisResponse | null>(null);
  const [hotelData, setHotelData] = useState<BookingAnalysisResponse | null>(null);
  const [flightData, setFlightData] = useState<BookingAnalysisResponse | null>(null);
  
  // 查询参数
  const [startDate, setStartDate] = useState(() => {
    const date = new Date();
    date.setMonth(date.getMonth() - 3); // 改为3个月前，确保能覆盖到有数据的时间段
    return date.toISOString().split('T')[0];
  });
  const [endDate, setEndDate] = useState(() => {
    return new Date().toISOString().split('T')[0];
  });
  const [userName, setUserName] = useState<string>('');

  // 图表颜色配置
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  // 从URL参数获取当前Tab
  useEffect(() => {
    const tab = searchParams.get('tab') as TabType;
    if (tab && ['passport', 'train', 'hotel', 'flight', 'error-logs'].includes(tab)) {
      setActiveTab(tab);
    }
  }, [searchParams]);

  // 当Tab切换时加载对应数据（错误排查Tab不需要加载）
  useEffect(() => {
    if (activeTab !== 'error-logs') {
      loadAnalysisData();
    }
  }, [activeTab]);

  // 处理Tab切换
  const handleTabChange = (tab: TabType) => {
    setActiveTab(tab);
    setSearchParams({ tab });
  };

  const loadAnalysisData = async () => {
    setLoading(true);
    try {
      const params: UserAnalysisRequest = {
        start_date: startDate,
        end_date: endDate,
        ...(userName && { user_name: userName.trim() })
      };

      switch (activeTab) {
        case 'passport':
          const passportResult = await dataAnalysisApi.analyzePassport(params);
          setPassportData(passportResult);
          break;
        case 'train':
          const trainResult = await dataAnalysisApi.analyzeTrainBooking(params);
          setTrainData(trainResult);
          break;
        case 'hotel':
          const hotelResult = await dataAnalysisApi.analyzeHotelBooking(params);
          setHotelData(hotelResult);
          break;
        case 'flight':
          const flightResult = await dataAnalysisApi.analyzeFlightBooking(params);
          setFlightData(flightResult);
          break;
      }
    } catch (error: any) {
      console.error('加载数据分析失败:', error);
      toast({
        title: "加载失败",
        description: error.message || "加载数据分析失败",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    loadAnalysisData();
  };



  const handleExportUserStats = () => {
    if (!userStats || userStats.length === 0) {
      toast({
        title: "无数据",
        description: "没有可导出的用户统计数据",
        variant: "destructive",
      });
      return;
    }

    try {
      // 生成HTML表格格式，Excel可以正确识别并显示边框
      const htmlContent = `
        <html>
          <head>
            <meta charset="utf-8">
            <style>
              table {
                border-collapse: collapse;
                width: 100%;
                font-family: Arial, sans-serif;
              }
              th, td {
                border: 1px solid #000000;
                padding: 8px;
                text-align: left;
              }
              th {
                background-color: #f2f2f2;
                font-weight: bold;
                text-align: center;
              }
              .center {
                text-align: center;
              }
            </style>
          </head>
          <body>
            <table>
              <thead>
                <tr>
                  <th>序号</th>
                  <th>部门</th>
                  <th>姓名</th>
                  <th>使用次数</th>
                </tr>
              </thead>
              <tbody>
                ${userStats.map((user, index) => `
                  <tr>
                    <td class="center">${index + 1}</td>
                    <td>${user.department || '-'}</td>
                    <td>${user.username}</td>
                    <td class="center">${user.count}</td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </body>
        </html>
      `;

      // 创建Blob并下载为Excel文件
      const blob = new Blob(['\uFEFF' + htmlContent], {
        type: 'application/vnd.ms-excel;charset=utf-8;'
      });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `护照识别用户使用统计_${new Date().toISOString().split('T')[0]}.xls`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast({
        title: "导出成功",
        description: "用户使用统计已导出到Excel文件",
        variant: "default",
      });
    } catch (error) {
      console.error('导出失败:', error);
      toast({
        title: "导出失败",
        description: "导出用户统计时发生错误",
        variant: "destructive",
      });
    }
  };

  // 导出项目统计
  const handleExportProjectStats = () => {
    if (!projectData || projectData.length === 0) {
      toast({
        title: "无数据",
        description: "没有可导出的项目统计数据",
        variant: "destructive",
      });
      return;
    }

    try {
      const htmlContent = `
        <html>
          <head>
            <meta charset="utf-8">
            <style>
              table {
                border-collapse: collapse;
                width: 100%;
                font-family: Arial, sans-serif;
              }
              th, td {
                border: 1px solid #000000;
                padding: 8px;
                text-align: left;
              }
              th {
                background-color: #f2f2f2;
                font-weight: bold;
                text-align: center;
              }
              .center {
                text-align: center;
              }
            </style>
          </head>
          <body>
            <table>
              <thead>
                <tr>
                  <th>序号</th>
                  <th>项目名称</th>
                  <th>创建人</th>
                  <th>总订单数</th>
                  <th>已完成订单</th>
                  <th>成功率</th>
                </tr>
              </thead>
              <tbody>
                ${projectData.map((project, index) => `
                  <tr>
                    <td class="center">${index + 1}</td>
                    <td>${project.project_name}</td>
                    <td>${project.creator_name || '-'}</td>
                    <td class="center">${project.total_orders}</td>
                    <td class="center">${project.completed_orders}</td>
                    <td class="center">${project.success_rate.toFixed(1)}%</td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </body>
        </html>
      `;

      const blob = new Blob(['\uFEFF' + htmlContent], {
        type: 'application/vnd.ms-excel;charset=utf-8;'
      });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `${activeTab}项目统计_${new Date().toISOString().split('T')[0]}.xls`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast({
        title: "导出成功",
        description: "项目统计已导出到Excel文件",
        variant: "default",
      });
    } catch (error) {
      console.error('导出失败:', error);
      toast({
        title: "导出失败",
        description: "导出项目统计时发生错误",
        variant: "destructive",
      });
    }
  };

  // 导出用户订单统计
  const handleExportUserOrderStats = () => {
    if (!userStats || userStats.length === 0) {
      toast({
        title: "无数据",
        description: "没有可导出的用户订单统计数据",
        variant: "destructive",
      });
      return;
    }

    try {
      const htmlContent = `
        <html>
          <head>
            <meta charset="utf-8">
            <style>
              table {
                border-collapse: collapse;
                width: 100%;
                font-family: Arial, sans-serif;
              }
              th, td {
                border: 1px solid #000000;
                padding: 8px;
                text-align: left;
              }
              th {
                background-color: #f2f2f2;
                font-weight: bold;
                text-align: center;
              }
              .center {
                text-align: center;
              }
            </style>
          </head>
          <body>
            <table>
              <thead>
                <tr>
                  <th>序号</th>
                  <th>创建人</th>
                  <th>总订单数</th>
                  <th>已完成订单</th>
                  <th>成功率</th>
                </tr>
              </thead>
              <tbody>
                ${userStats.map((user, index) => `
                  <tr>
                    <td class="center">${index + 1}</td>
                    <td>${user.creator_name}</td>
                    <td class="center">${user.total_orders}</td>
                    <td class="center">${user.completed_orders}</td>
                    <td class="center">${user.success_rate.toFixed(1)}%</td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </body>
        </html>
      `;

      const blob = new Blob(['\uFEFF' + htmlContent], {
        type: 'application/vnd.ms-excel;charset=utf-8;'
      });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `${activeTab}用户订单统计_${new Date().toISOString().split('T')[0]}.xls`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast({
        title: "导出成功",
        description: "用户订单统计已导出到Excel文件",
        variant: "default",
      });
    } catch (error) {
      console.error('导出失败:', error);
      toast({
        title: "导出失败",
        description: "导出用户订单统计时发生错误",
        variant: "destructive",
      });
    }
  };

  // 准备当前Tab的数据
  const getCurrentTabData = () => {
    switch (activeTab) {
      case 'passport':
        return passportData;
      case 'train':
        return trainData;
      case 'hotel':
        return hotelData;
      case 'flight':
        return flightData;
      default:
        return null;
    }
  };

  // 准备图表数据
  const prepareChartData = () => {
    const currentData = getCurrentTabData();
    if (!currentData) return { dailyData: [], summaryData: [], projectData: [], userStats: [] };

    let dailyData: any[] = [];
    let summaryData: any[] = [];
    let projectData: any[] = [];
    let userStats: any[] = [];

    if (activeTab === 'passport') {
      const data = currentData as PassportAnalysisResponse;
      // 护照识别每日数据
      dailyData = data.daily_stats.map(item => ({
        date: item.date,
        count: item.count
      }));

      // 护照识别汇总数据
      summaryData = [
        { name: '总识别次数', value: data.total_recognitions, color: COLORS[0] },
        { name: '使用用户数', value: data.unique_users, color: COLORS[1] },
        { name: '成功率', value: data.success_rate, color: COLORS[2] }
      ];

      userStats = data.user_stats;
    } else {
      const data = currentData as BookingAnalysisResponse;
      // 预订业务每日数据
      dailyData = data.daily_stats.map(item => ({
        date: item.date,
        count: item.total_orders || 0,  // ECharts组件需要count字段
        total_orders: item.total_orders,
        completed_orders: item.completed_orders,
        success_rate: item.success_rate
      }));

      // 调试日志
      console.log('Booking daily data:', { data: data.daily_stats, dailyData });

      // 预订业务汇总数据
      summaryData = [
        { name: '总订单数', value: data.total_orders, color: COLORS[0] },
        { name: '已完成订单', value: data.completed_orders, color: COLORS[1] },
        { name: '失败订单', value: data.failed_orders, color: COLORS[2] },
        { name: '总金额', value: data.total_amount, color: COLORS[3] }
      ];

      projectData = data.project_stats;
      userStats = data.user_stats;
    }

    return { dailyData, summaryData, projectData, userStats };
  };

  const { dailyData, summaryData, projectData, userStats } = prepareChartData();
  const currentData = getCurrentTabData();

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* 页面头部 */}
        <div className="flex items-center gap-4">
          <div className="p-3 bg-purple-50 rounded-lg">
            <BarChart3 className="h-6 w-6 text-purple-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">数据分析</h1>
            <p className="text-gray-600">系统使用情况统计分析</p>
          </div>
        </div>

        {/* Tab导航 */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8" aria-label="Tabs">
              <button
                onClick={() => handleTabChange('passport')}
                className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors duration-200 ${
                  activeTab === 'passport'
                    ? 'border-purple-500 text-purple-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center gap-2">
                  <CreditCard className="h-4 w-4" />
                  护照识别分析
                </div>
              </button>

              <button
                onClick={() => handleTabChange('train')}
                className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors duration-200 ${
                  activeTab === 'train'
                    ? 'border-purple-500 text-purple-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center gap-2">
                  <Train className="h-4 w-4" />
                  火车票预订分析
                </div>
              </button>

              <button
                onClick={() => handleTabChange('hotel')}
                className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors duration-200 ${
                  activeTab === 'hotel'
                    ? 'border-purple-500 text-purple-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center gap-2">
                  <Building className="h-4 w-4" />
                  酒店预订分析
                </div>
              </button>

              <button
                onClick={() => handleTabChange('flight')}
                className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors duration-200 ${
                  activeTab === 'flight'
                    ? 'border-purple-500 text-purple-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center gap-2">
                  <Plane className="h-4 w-4" />
                  飞机票预订分析
                </div>
              </button>

              <button
                onClick={() => handleTabChange('error-logs')}
                className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors duration-200 ${
                  activeTab === 'error-logs'
                    ? 'border-purple-500 text-purple-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center gap-2">
                  <TrendingUp className="h-4 w-4" />
                  错误排查
                </div>
              </button>
            </nav>
          </div>
        </div>



        {/* 查询条件 - 错误排查Tab不显示 */}
        {activeTab !== 'error-logs' && (
          <Card className="mb-6 p-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 items-end">
            <div>
              <Label htmlFor="start-date">开始日期</Label>
              <Input
                id="start-date"
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor="end-date">结束日期</Label>
              <Input
                id="end-date"
                type="date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
                className="mt-1"
              />
            </div>
            {activeTab === 'passport' && (
              <div>
                <Label htmlFor="user-name">用户姓名（可选）</Label>
                <Input
                  id="user-name"
                  type="text"
                  placeholder="如：郭伟"
                  value={userName}
                  onChange={(e) => setUserName(e.target.value)}
                  className="mt-1"
                />
              </div>
            )}
            <div className="flex gap-2">
              <Button
                onClick={handleSearch}
                disabled={loading}
                className="bg-purple-600 hover:bg-purple-700 flex-1"
              >
                {loading ? '查询中...' : '查询'}
              </Button>
              {activeTab === 'passport' && userName && (
                <Button
                  onClick={() => setUserName('')}
                  variant="outline"
                >
                  清除
                </Button>
              )}
            </div>
          </div>
        </Card>
        )}

        {/* 错误排查Tab内容 */}
        {activeTab === 'error-logs' ? (
          <ErrorLogsTab />
        ) : loading ? (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
            <span className="ml-2 text-gray-600">加载中...</span>
          </div>
        ) : currentData ? (
          <div className="space-y-6">
            {/* 汇总卡片 */}
            <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${summaryData.length}, 1fr)` }}>
              {summaryData.map((item, index) => (
                <Card key={index} className="p-6 flex-1">
                  <div className="flex items-center justify-between h-full">
                    <div className="flex-1">
                      <p className="text-sm text-gray-600 mb-2">{item.name}</p>
                      <p className="text-3xl font-bold" style={{ color: item.color }}>
                        {activeTab === 'passport' && item.name === '成功率'
                          ? `${item.value.toFixed(1)}%`
                          : activeTab !== 'passport' && item.name === '总金额'
                          ? `¥${item.value.toLocaleString()}`
                          : item.value.toLocaleString()
                        }
                      </p>
                    </div>
                    <div className="ml-4">
                      {activeTab === 'passport' ? (
                        <FileText className="h-10 w-10" style={{ color: item.color }} />
                      ) : (
                        <TrendingUp className="h-10 w-10" style={{ color: item.color }} />
                      )}
                    </div>
                  </div>
                </Card>
              ))}
            </div>

            {/* 数据表格区域 */}
            <div className="grid grid-cols-1 gap-6">
              {/* 近日使用统计 */}
              {activeTab === 'passport' && (
                <Card className="p-6">
                  <h3 className="text-lg font-semibold mb-4">近日使用统计（近一周）</h3>
                  <EChartsBarChart data={dailyData} />
                </Card>
              )}

              {/* 其他Tab的近日使用统计（近一周） */}
              {activeTab !== 'passport' && (
                <Card className="p-6">
                  <h3 className="text-lg font-semibold mb-4">近日使用统计（近一周）</h3>
                  <EChartsBarChart data={dailyData} />
                </Card>
              )}
            </div>

            {/* 项目统计表格 - 只在预订业务Tab中显示 */}
            {activeTab !== 'passport' && projectData.length > 0 && (
              <Card className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold">项目统计</h3>
                  <Button
                    onClick={handleExportProjectStats}
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-2"
                  >
                    <Download className="h-4 w-4" />
                    导出
                  </Button>
                </div>
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="text-left p-3 font-medium text-gray-900">项目名称</th>
                        <th className="text-left p-3 font-medium text-gray-900">创建人</th>
                        <th className="text-center p-3 font-medium text-gray-900">总订单数</th>
                        <th className="text-center p-3 font-medium text-gray-900">已完成订单</th>
                        <th className="text-center p-3 font-medium text-gray-900">成功率</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {projectData.map((project) => (
                        <tr key={project.project_id} className="hover:bg-gray-50">
                          <td className="p-3 text-gray-900">{project.project_name}</td>
                          <td className="p-3 text-gray-900">{project.creator_name || '-'}</td>
                          <td className="p-3 text-center text-gray-900">{project.total_orders}</td>
                          <td className="p-3 text-center text-gray-900">{project.completed_orders}</td>
                          <td className="p-3 text-center">
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              project.success_rate >= 80 ? 'bg-green-100 text-green-800' :
                              project.success_rate >= 60 ? 'bg-yellow-100 text-yellow-800' :
                              'bg-red-100 text-red-800'
                            }`}>
                              {project.success_rate.toFixed(1)}%
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </Card>
            )}

            {/* 用户订单统计表格 - 只在预订业务Tab中显示 */}
            {activeTab !== 'passport' && userStats.length > 0 && (
              <Card className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold">用户订单统计</h3>
                  <Button
                    onClick={handleExportUserOrderStats}
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-2"
                  >
                    <Download className="h-4 w-4" />
                    导出
                  </Button>
                </div>
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="text-left p-3 font-medium text-gray-900">创建人</th>
                        <th className="text-center p-3 font-medium text-gray-900">总订单数</th>
                        <th className="text-center p-3 font-medium text-gray-900">已完成订单</th>
                        <th className="text-center p-3 font-medium text-gray-900">成功率</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {userStats.map((user, index) => (
                        <tr key={index} className="hover:bg-gray-50">
                          <td className="p-3 text-gray-900">{user.creator_name}</td>
                          <td className="p-3 text-center text-gray-900">{user.total_orders}</td>
                          <td className="p-3 text-center text-gray-900">{user.completed_orders}</td>
                          <td className="p-3 text-center">
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              user.success_rate >= 80 ? 'bg-green-100 text-green-800' :
                              user.success_rate >= 60 ? 'bg-yellow-100 text-yellow-800' :
                              'bg-red-100 text-red-800'
                            }`}>
                              {user.success_rate.toFixed(1)}%
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </Card>
            )}

            {/* 用户统计表格 - 在护照识别Tab中显示 */}
            {activeTab === 'passport' && (
              <Card className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold">用户使用统计</h3>
                  {userStats && userStats.length > 0 && (
                    <Button
                      onClick={handleExportUserStats}
                      variant="outline"
                      size="sm"
                      className="flex items-center gap-2"
                    >
                      <Download className="h-4 w-4" />
                      导出
                    </Button>
                  )}
                </div>


                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="text-center p-3 font-medium text-gray-900">序号</th>
                        <th className="text-left p-3 font-medium text-gray-900">部门</th>
                        <th className="text-left p-3 font-medium text-gray-900">姓名</th>
                        <th className="text-center p-3 font-medium text-gray-900">使用次数</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {userStats && userStats.length > 0 ? (
                        userStats.map((user, index) => (
                          <tr key={user.user_id} className="hover:bg-gray-50">
                            <td className="p-3 text-center text-gray-900">{index + 1}</td>
                            <td className="p-3 text-gray-900">{user.department || '-'}</td>
                            <td className="p-3 text-gray-900">{user.username}</td>
                            <td className="p-3 text-center text-gray-900">{user.count}</td>
                          </tr>
                        ))
                      ) : (
                        <tr>
                          <td colSpan={4} className="p-8 text-center text-gray-500">
                            {userName ? `未找到用户"${userName}"的使用记录` : '暂无用户使用数据'}
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              </Card>
            )}
          </div>
        ) : (
          <Card className="p-12 text-center">
            <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">暂无数据</h3>
            <p className="text-gray-500">请选择日期范围并点击查询按钮</p>
          </Card>
        )}
      </div>
    </MainLayout>
  );
};

export default DataAnalysisPage;
