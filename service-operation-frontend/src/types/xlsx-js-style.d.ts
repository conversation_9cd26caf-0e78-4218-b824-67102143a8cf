declare module 'xlsx-js-style' {
  import * as XLSX from 'xlsx';
  
  // 扩展XLSX的类型以支持样式
  interface CellStyle {
    border?: {
      top?: { style: string | number; color?: { rgb: string } };
      bottom?: { style: string | number; color?: { rgb: string } };
      left?: { style: string | number; color?: { rgb: string } };
      right?: { style: string | number; color?: { rgb: string } };
    };
    fill?: {
      patternType?: string;
      fgColor?: { rgb: string };
    };
    font?: {
      name?: string;
      sz?: number;
      bold?: boolean;
    };
    alignment?: {
      horizontal?: string;
      vertical?: string;
      wrapText?: boolean;
    };
  }

  interface StyledCell extends XLSX.CellObject {
    s?: CellStyle;
  }

  interface StyledWorkSheet extends XLSX.WorkSheet {
    [key: string]: any;
  }

  // 导出所有XLSX的功能
  export const utils: typeof XLSX.utils;
  export const write: typeof XLSX.write;
  export const read: typeof XLSX.read;
  export const writeFile: typeof XLSX.writeFile;
  export const readFile: typeof XLSX.readFile;
  
  // 导出所有其他XLSX类型和功能
  export * from 'xlsx';
} 