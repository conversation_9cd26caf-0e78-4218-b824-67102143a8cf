// 任务状态枚举
export enum TaskStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

// 任务类型枚举
export enum TaskType {
  HOTEL_BOOKING = '酒店预订',
  FLIGHT_BOOKING = '机票预订',
  TRAIN_BOOKING = '火车票预订',
  TRANSPORT = '交通安排',
  MEETING = '会议安排',
  OTHER = '其他'
}

// 项目任务基础类型
export interface ProjectTask {
  id: number;
  task_id: string;
  project_id: number;
  creator_user_id: number;
  creator_name: string;
  task_type: string;
  task_title: string;
  task_description?: string;
  task_status: string;
  created_at: string;
  updated_at: string;
}

// 创建任务请求
export interface CreateTaskRequest {
  project_id: number;
  task_type: string;
  task_title: string;
  task_description?: string;
  task_status?: string;
}

// 更新任务请求
export interface UpdateTaskRequest {
  task_type?: string;
  task_title?: string;
  task_description?: string;
  task_status?: string;
}

// 任务列表响应
export interface TaskListResponse {
  total: number;
  items: ProjectTask[];
}

// 任务查询参数
export interface TaskQueryParams {
  page?: number;
  page_size?: number;
  project_id?: number;
  task_type?: string;
  task_status?: string;
  creator_name?: string;
}

// 任务统计信息
export interface TaskStats {
  total_tasks: number;
  pending_tasks: number;
  completed_tasks: number;
  message: string;
}

// 任务卡片扩展信息（后续通过具体订单计算）
export interface TaskCardInfo {
  task: ProjectTask;
  order_count: number;        // 订单数
  total_amount: number;       // 总金额
  total_people: number;       // 总人数
  booking_status: string;     // 预订情况
  has_exception: boolean;     // 是否有异常
}

// 项目详情页面数据
export interface ProjectDetail {
  project: {
    id: number;
    project_number: string;    // 更新为字符串类型
    project_name: string;
    creator_user_id: number;
    creator_name: string;
    project_description?: string;
    client_name: string;
    project_date: string;
    created_at: string;
    updated_at: string;
  };
  tasks: TaskCardInfo[];
  total_order_amount: number; // 累计订单金额
} 