// 项目状态枚举
export enum ProjectStatus {
  PLANNING = '计划中',
  IN_PROGRESS = '进行中',
  COMPLETED = '已完成',
  CANCELLED = '已取消',
  ON_HOLD = '暂停'
}

// 项目类型
export interface Project {
  id: number;
  project_number: number;
  project_name: string;
  creator_user_id: number;
  creator_name: string;
  creator_department?: string;
  project_description?: string;
  client_name: string;
  cost_center?: string;
  project_date: string;
  created_at: string;
  updated_at: string;
}

// 创建项目请求
export interface CreateProjectRequest {
  project_name: string;
  project_description?: string;
  client_name: string;
  cost_center?: string;
  project_date: string;
}

// 更新项目请求
export interface UpdateProjectRequest {
  project_name?: string;
  project_description?: string;
  client_name?: string;
  cost_center?: string;
  project_date?: string;
}

// 项目列表响应
export interface ProjectListResponse {
  total: number;
  items: Project[];
}

// 项目查询参数
export interface ProjectQueryParams {
  page?: number;
  page_size?: number;
  project_name?: string;
  client_name?: string;
  creator_name?: string;
}

// 项目统计
export interface ProjectStats {
  total_projects: number;
  message: string;
}

// 视图模式
export enum ViewMode {
  LIST = 'list',
  CARD = 'card'
} 