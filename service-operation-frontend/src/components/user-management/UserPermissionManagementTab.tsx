import React, { useState, useEffect } from 'react';
import {
  UserCheck,
  Search,
  Settings,
  Eye,
  RefreshCw,
  Shield,
  Key
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { toast } from '@/hooks/use-toast';
import { Badge } from '@/components/ui/badge';
import { userManagementApi, User, UserPermissionDetail } from '@/services/userManagementApi';
import UserRolePermissionModal from './UserRolePermissionModal';
import UserPermissionDetailModal from './UserPermissionDetailModal';

// 类型定义已从 userManagementApi 导入

const UserPermissionManagementTab: React.FC = () => {
  const [users, setUsers] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [pageSize] = useState(20);
  const [total, setTotal] = useState(0);
  const [search, setSearch] = useState('');
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [selectedUserDetail, setSelectedUserDetail] = useState<UserPermissionDetail | null>(null);
  const [isPermissionModalOpen, setIsPermissionModalOpen] = useState(false);
  const [isRolePermissionModalOpen, setIsRolePermissionModalOpen] = useState(false);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);

  // 加载用户列表
  const loadUsers = async (currentPage: number = 1) => {
    setLoading(true);
    try {
      const response = await userManagementApi.getUsers(currentPage, pageSize, search);
      // 用户数据已经包含了roles和permissions信息，无需额外处理
      const usersWithStats = response.items;

      setUsers(usersWithStats);
      setTotal(response.total);
      setPage(currentPage);
    } catch (error) {
      console.error('加载用户列表失败:', error);
      toast({
        title: "加载失败",
        description: "无法加载用户列表",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    loadUsers(1);
  }, []);

  // 搜索处理
  const handleSearch = () => {
    setPage(1);
    loadUsers(1);
  };

  // 清空搜索
  const handleClearSearch = () => {
    setSearch('');
    setPage(1);
    loadUsers(1);
  };

  // 查看用户权限详情
  const handleViewUserPermissions = async (userId: number) => {
    try {
      const userDetail = await userManagementApi.getUserPermissionDetails(userId);
      setSelectedUserDetail(userDetail);
      setIsDetailModalOpen(true);
    } catch (error) {
      console.error('获取用户权限详情失败:', error);
      toast({
        title: "获取失败",
        description: "无法获取用户权限详情",
        variant: "destructive",
      });
    }
  };

  // 设置用户角色权限
  const handleAssignPermissions = (userId: number) => {
    const user = users.find(u => u.id === userId);
    if (user) {
      setSelectedUser(user);
      setIsRolePermissionModalOpen(true);
    }
  };

  // 处理角色权限分配成功
  const handleRolePermissionSuccess = () => {
    loadUsers(page); // 重新加载用户列表以更新统计数据
  };

  // 获取状态徽章
  const getStatusBadge = (status: boolean) => {
    return status ? (
      <Badge variant="default" className="bg-green-100 text-green-800">启用</Badge>
    ) : (
      <Badge variant="secondary" className="bg-red-100 text-red-800">禁用</Badge>
    );
  };

  return (
    <div className="space-y-4">
      {/* 操作栏 */}
      <Card className="p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <UserCheck className="h-5 w-5 text-blue-600" />
            <h3 className="text-lg font-medium text-gray-900">用户权限管理</h3>
            {total > 0 && (
              <span className="ml-2 bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full">
                {total}
              </span>
            )}
          </div>
          <div className="flex items-center gap-2">
            {/* 删除了刷新按钮 */}
          </div>
        </div>
        
        {/* 搜索区域 */}
        <div className="flex items-center gap-3">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="搜索用户名、姓名或邮箱..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
          <Button
            onClick={handleSearch}
            size="sm"
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            搜索
          </Button>
          <Button
            onClick={handleClearSearch}
            variant="outline"
            size="sm"
          >
            重置
          </Button>
        </div>
      </Card>

      {/* 用户权限列表 */}
      <Card className="p-0">
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin text-blue-500 mr-2" />
            <span className="text-gray-600">加载用户数据中...</span>
          </div>
        ) : users.length > 0 ? (
          <>
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead className="bg-gray-50 border-b border-gray-200">
                  <tr>
                    <th className="text-left p-4 font-medium text-gray-900">用户信息</th>
                    <th className="text-left p-4 font-medium text-gray-900">角色</th>
                    <th className="text-left p-4 font-medium text-gray-900">权限</th>
                    <th className="text-left p-4 font-medium text-gray-900">状态</th>
                    <th className="text-center p-4 font-medium text-gray-900">操作</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {users.map((user) => (
                    <tr key={user.id} className="hover:bg-gray-50">
                      <td className="p-4">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                              <span className="text-sm font-medium text-blue-600">
                                {user.username.charAt(0).toUpperCase()}
                              </span>
                            </div>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">{user.username}</div>
                            {user.email && <div className="text-sm text-gray-500">{user.email}</div>}
                            {user.department && <div className="text-xs text-gray-400">{user.department}</div>}
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="flex flex-wrap gap-1">
                          {user.roles && user.roles.length > 0 ? (
                            <>
                              {user.roles.slice(0, 2).map((role) => (
                                <Badge
                                  key={role.id}
                                  variant={role.is_system ? "default" : "secondary"}
                                  className="text-xs"
                                >
                                  {role.role_name}
                                </Badge>
                              ))}
                              {user.roles.length > 2 && (
                                <Badge variant="secondary" className="text-xs">
                                  +{user.roles.length - 2}
                                </Badge>
                              )}
                            </>
                          ) : (
                            <span className="text-xs text-gray-400">无角色</span>
                          )}
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="flex flex-wrap gap-1">
                          {user.permissions && user.permissions.length > 0 ? (
                            <>
                              {user.permissions.slice(0, 3).map((permission) => (
                                <Badge key={permission.id} variant="outline" className="text-xs">
                                  {permission.permission_name}
                                </Badge>
                              ))}
                              {user.permissions.length > 3 && (
                                <Badge variant="secondary" className="text-xs">
                                  +{user.permissions.length - 3}
                                </Badge>
                              )}
                            </>
                          ) : (
                            <span className="text-xs text-gray-400">无权限</span>
                          )}
                        </div>
                      </td>
                      <td className="p-4">
                        {getStatusBadge(user.status)}
                      </td>
                      <td className="text-center p-4">
                        <div className="flex items-center justify-center gap-1">
                          <button
                            onClick={() => handleViewUserPermissions(user.id)}
                            className="p-1 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors"
                            title="查看权限详情"
                          >
                            <Eye className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleAssignPermissions(user.id)}
                            className="p-1 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded transition-colors"
                            title="分配权限"
                          >
                            <Settings className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </>
        ) : (
          <div className="text-center py-8">
            <UserCheck className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">暂无用户数据</h3>
            <p className="text-gray-500">
              {search ? "没有找到符合搜索条件的用户" : "系统中暂无用户"}
            </p>
          </div>
        )}
      </Card>

      {/* 用户权限详情模态框 */}
      {isPermissionModalOpen && selectedUser && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" onClick={() => setIsPermissionModalOpen(false)}></div>
          <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden z-50 mx-4">
            <div className="flex justify-between items-center px-6 py-4 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900">
                {selectedUser.user.full_name || selectedUser.user.username} 的权限详情
              </h2>
              <button 
                onClick={() => setIsPermissionModalOpen(false)}
                className="h-8 w-8 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 flex items-center justify-center transition-colors"
              >
                ×
              </button>
            </div>
            
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
              <div className="space-y-6">
                {/* 用户基本信息 */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-3">用户信息</h3>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <span className="text-sm text-gray-500">用户名:</span>
                        <span className="ml-2 text-sm font-medium">{selectedUser.user.username}</span>
                      </div>
                      <div>
                        <span className="text-sm text-gray-500">姓名:</span>
                        <span className="ml-2 text-sm font-medium">{selectedUser.user.full_name || '-'}</span>
                      </div>
                      <div>
                        <span className="text-sm text-gray-500">邮箱:</span>
                        <span className="ml-2 text-sm font-medium">{selectedUser.user.email || '-'}</span>
                      </div>
                      <div>
                        <span className="text-sm text-gray-500">部门:</span>
                        <span className="ml-2 text-sm font-medium">{selectedUser.user.department || '-'}</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 角色权限 */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-3">角色权限</h3>
                  <div className="space-y-4">
                    {selectedUser.role_permissions.map((rolePermission, index) => (
                      <div key={index} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex items-center gap-2 mb-3">
                          <Shield className="h-5 w-5 text-blue-500" />
                          <span className="font-medium">{rolePermission.role.role_name}</span>
                          <Badge variant={rolePermission.role.is_system ? "default" : "secondary"}>
                            {rolePermission.role.is_system ? '系统角色' : '自定义角色'}
                          </Badge>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                          {rolePermission.permissions.map((permission) => (
                            <div key={permission.id} className="text-sm bg-gray-50 px-3 py-2 rounded">
                              {permission.permission_name}
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* 应用权限 */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-3">应用权限</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {selectedUser.applications.map((app) => (
                      <div key={app.id} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex items-center gap-2 mb-2">
                          <Building className="h-5 w-5 text-green-500" />
                          <span className="font-medium">{app.app_name}</span>
                        </div>
                        <div className="text-sm text-gray-500">
                          <div>编码: {app.app_code}</div>
                          {app.app_url && <div>地址: {app.app_url}</div>}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* 特殊权限 */}
                {selectedUser.special_permissions.length > 0 && (
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-3">特殊权限</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                      {selectedUser.special_permissions.map((permission) => (
                        <div key={permission.id} className="text-sm bg-yellow-50 border border-yellow-200 px-3 py-2 rounded">
                          {permission.permission_name}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 用户权限详情模态框 */}
      <UserPermissionDetailModal
        userDetail={selectedUserDetail}
        isOpen={isDetailModalOpen}
        onClose={() => {
          setIsDetailModalOpen(false);
          setSelectedUserDetail(null);
        }}
      />

      {/* 用户角色权限管理模态框 */}
      <UserRolePermissionModal
        user={selectedUser}
        isOpen={isRolePermissionModalOpen}
        onClose={() => {
          setIsRolePermissionModalOpen(false);
          setSelectedUser(null);
        }}
        onSuccess={handleRolePermissionSuccess}
      />
    </div>
  );
};

export default UserPermissionManagementTab;
