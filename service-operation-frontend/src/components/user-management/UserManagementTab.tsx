import React, { useState, useEffect } from 'react';
import {
  Users,
  Plus,
  Search,
  Edit,
  Trash2,
  Eye,
  UserPlus,
  RefreshCw,
  Download,
  Filter,
  UserCheck,
  UserX,
  Settings
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { toast } from '@/hooks/use-toast';
import { Badge } from '@/components/ui/badge';
import { userManagementApi, User, Role, Application } from '@/services/userManagementApi';
import UserEditModal from './UserEditModal';
import UserRolePermissionModal from './UserRolePermissionModal';
import UserCreateModal from './UserCreateModal';

// 类型定义已从 userManagementApi 导入

const UserManagementTab: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [pageSize] = useState(20);
  const [total, setTotal] = useState(0);
  const [search, setSearch] = useState('');
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [isRolePermissionModalOpen, setIsRolePermissionModalOpen] = useState(false);
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);
  const [userToDelete, setUserToDelete] = useState<User | null>(null);

  // 加载用户列表
  const loadUsers = async (currentPage: number = 1) => {
    setLoading(true);
    try {
      const response = await userManagementApi.getUsers(currentPage, pageSize, search);
      setUsers(response.items);
      setTotal(response.total);
      setPage(currentPage);
    } catch (error) {
      console.error('加载用户列表失败:', error);
      toast({
        title: "加载失败",
        description: "无法加载用户列表",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    loadUsers(1);
  }, []);

  // 搜索处理
  const handleSearch = () => {
    setPage(1);
    loadUsers(1);
  };

  // 清空搜索
  const handleClearSearch = () => {
    setSearch('');
    setPage(1);
    loadUsers(1);
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  // 获取状态徽章
  const getStatusBadge = (status: boolean) => {
    return status ? (
      <Badge variant="default" className="bg-green-100 text-green-800">启用</Badge>
    ) : (
      <Badge variant="secondary" className="bg-red-100 text-red-800">已停用</Badge>
    );
  };

  // 处理创建用户
  const handleCreateUser = () => {
    setIsCreateModalOpen(true);
  };

  // 处理编辑用户
  const handleEditUser = (user: User) => {
    setSelectedUser(user);
    setIsEditModalOpen(true);
  };

  // 编辑成功后的回调
  const handleEditSuccess = () => {
    loadUsers(page);
  };

  // 处理查看用户
  const handleViewUser = (user: User) => {
    setSelectedUser(user);
    setIsViewModalOpen(true);
  };

  // 处理用户角色权限管理
  const handleManageRolePermission = (user: User) => {
    setSelectedUser(user);
    setIsRolePermissionModalOpen(true);
  };

  // 处理创建用户成功
  const handleCreateSuccess = () => {
    loadUsers(1); // 重新加载第一页
    setPage(1); // 重置到第一页
  };

  // 处理删除用户
  const handleDeleteUser = (user: User) => {
    setUserToDelete(user);
    setIsDeleteConfirmOpen(true);
  };

  // 确认删除用户
  const confirmDeleteUser = async () => {
    if (!userToDelete) return;

    try {
      await userManagementApi.deleteUser(userToDelete.id);

      toast({
        title: "删除成功",
        description: `用户 ${userToDelete.username} 已被永久删除`,
      });

      // 重新加载用户列表
      loadUsers(page);

      // 关闭确认对话框
      setIsDeleteConfirmOpen(false);
      setUserToDelete(null);
    } catch (error) {
      console.error('删除用户失败:', error);
      toast({
        title: "删除失败",
        description: error instanceof Error ? error.message : "删除用户时发生错误",
        variant: "destructive",
      });
    }
  };

  // 取消删除
  const cancelDeleteUser = () => {
    setIsDeleteConfirmOpen(false);
    setUserToDelete(null);
  };

  // 处理用户状态切换（启用/停用）
  const handleToggleUserStatus = async (user: User) => {
    try {
      const newStatus = !user.status;
      await userManagementApi.updateUserStatus(user.id, newStatus);

      const action = user.status ? "停用" : "启用";
      toast({
        title: `${action}成功`,
        description: `用户 ${user.username} 已被${action}`,
      });

      // 重新加载用户列表
      loadUsers(page);
    } catch (error) {
      console.error('切换用户状态失败:', error);
      const action = user.status ? "停用" : "启用";
      toast({
        title: `${action}失败`,
        description: `${action}用户时发生错误`,
        variant: "destructive",
      });
    }
  };

  return (
    <div className="space-y-4">
      {/* 操作栏 */}
      <Card className="p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <Users className="h-5 w-5 text-blue-600" />
            <h3 className="text-lg font-medium text-gray-900">用户管理</h3>
            {total > 0 && (
              <span className="ml-2 bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full">
                {total}
              </span>
            )}
          </div>
          <div className="flex items-center gap-2">
            <Button
              onClick={() => setIsCreateModalOpen(true)}
              className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              新建用户
            </Button>
          </div>
        </div>
        
        {/* 搜索区域 */}
        <div className="flex items-center gap-3">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="搜索用户名、姓名或邮箱..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
          <Button
            onClick={handleSearch}
            size="sm"
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            搜索
          </Button>
          <Button
            onClick={handleClearSearch}
            variant="outline"
            size="sm"
          >
            重置
          </Button>
        </div>
      </Card>

      {/* 用户列表 */}
      <Card className="p-0">
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin text-blue-500 mr-2" />
            <span className="text-gray-600">加载用户数据中...</span>
          </div>
        ) : users.length > 0 ? (
          <>
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead className="bg-gray-50 border-b border-gray-200">
                  <tr>
                    <th className="text-left p-4 font-medium text-gray-900">用户名</th>
                    <th className="text-left p-4 font-medium text-gray-900">姓名</th>
                    <th className="text-left p-4 font-medium text-gray-900">工号</th>
                    <th className="text-left p-4 font-medium text-gray-900">邮箱</th>
                    <th className="text-left p-4 font-medium text-gray-900">部门</th>
                    <th className="text-left p-4 font-medium text-gray-900">角色</th>
                    <th className="text-left p-4 font-medium text-gray-900">权限</th>
                    <th className="text-left p-4 font-medium text-gray-900">状态</th>
                    <th className="text-left p-4 font-medium text-gray-900">创建时间</th>
                    <th className="text-center p-4 font-medium text-gray-900">操作</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {users.map((user) => (
                    <tr key={user.id} className="hover:bg-gray-50">
                      <td className="p-4">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-8 w-8">
                            <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                              <span className="text-xs font-medium text-blue-600">
                                {user.username ? user.username.charAt(0).toUpperCase() : (user as any).member_id?.charAt(0).toUpperCase() || 'U'}
                              </span>
                            </div>
                          </div>
                          <div className="ml-3">
                            <div className="text-sm font-medium text-gray-900">{(user as any).member_id || user.username || '-'}</div>
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="text-sm text-gray-900">{user.username || '-'}</div>
                      </td>
                      <td className="p-4">
                        <div className="text-sm text-gray-900">{(user as any).work_id || '-'}</div>
                      </td>
                      <td className="p-4">
                        <div className="text-sm text-gray-900">{user.email || '-'}</div>
                      </td>
                      <td className="p-4">
                        <div className="text-sm text-gray-900">{user.department || '-'}</div>
                      </td>
                      <td className="p-4">
                        <div className="flex flex-wrap gap-1">
                          {user.roles.length > 0 ? (
                            <>
                              {user.roles.slice(0, 2).map((role) => (
                                <Badge
                                  key={role.id}
                                  variant={role.is_system ? "default" : "secondary"}
                                  className="text-xs"
                                >
                                  {role.role_name}
                                </Badge>
                              ))}
                              {user.roles.length > 2 && (
                                <Badge variant="secondary" className="text-xs">
                                  +{user.roles.length - 2}
                                </Badge>
                              )}
                            </>
                          ) : (
                            <span className="text-xs text-gray-400">无角色</span>
                          )}
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="flex flex-wrap gap-1">
                          {user.permissions && user.permissions.length > 0 ? (
                            <>
                              {user.permissions.slice(0, 3).map((permission) => (
                                <Badge key={permission.id} variant="outline" className="text-xs">
                                  {permission.permission_name}
                                </Badge>
                              ))}
                              {user.permissions.length > 3 && (
                                <Badge variant="secondary" className="text-xs">
                                  +{user.permissions.length - 3}
                                </Badge>
                              )}
                            </>
                          ) : (
                            <span className="text-xs text-gray-400">无权限</span>
                          )}
                        </div>
                      </td>
                      <td className="p-4">
                        {getStatusBadge(user.status)}
                      </td>
                      <td className="p-4">
                        <div className="text-sm text-gray-900">{formatDate(user.created_at)}</div>
                      </td>
                      <td className="text-center p-4">
                        <div className="flex items-center justify-center gap-1">
                          <button
                            onClick={() => handleManageRolePermission(user)}
                            className="p-1 text-gray-600 hover:text-purple-600 hover:bg-purple-50 rounded transition-colors"
                            title="角色权限管理"
                          >
                            <Settings className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleEditUser(user)}
                            className="p-1 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors"
                            title="编辑用户"
                          >
                            <Edit className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleToggleUserStatus(user)}
                            className={`p-1 text-gray-600 rounded transition-colors ${
                              user.status
                                ? 'hover:text-red-600 hover:bg-red-50'
                                : 'hover:text-green-600 hover:bg-green-50'
                            }`}
                            title={user.status ? "停用用户" : "启用用户"}
                          >
                            {user.status ? (
                              <UserX className="h-4 w-4" />
                            ) : (
                              <UserCheck className="h-4 w-4" />
                            )}
                          </button>
                          <button
                            onClick={() => handleDeleteUser(user)}
                            className="p-1 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded transition-colors"
                            title="删除用户"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            
            {/* 分页 */}
            {Math.ceil(total / pageSize) > 1 && (
              <div className="flex items-center justify-between p-4 border-t border-gray-200">
                <div className="text-sm text-gray-700">
                  显示第 {(page - 1) * pageSize + 1} 到 {Math.min(page * pageSize, total)} 条，共 {total} 条记录
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => loadUsers(page - 1)}
                    disabled={page <= 1 || loading}
                  >
                    上一页
                  </Button>
                  <span className="text-sm text-gray-700">
                    第 {page} 页，共 {Math.ceil(total / pageSize)} 页
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => loadUsers(page + 1)}
                    disabled={page >= Math.ceil(total / pageSize) || loading}
                  >
                    下一页
                  </Button>
                </div>
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-8">
            <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">暂无用户数据</h3>
            <p className="text-gray-500">
              {search ? "没有找到符合搜索条件的用户" : "系统中暂无用户"}
            </p>
          </div>
        )}
      </Card>

      {/* 用户编辑模态框 */}
      <UserEditModal
        user={selectedUser}
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false);
          setSelectedUser(null);
        }}
        onSuccess={handleEditSuccess}
      />

      {/* 用户角色权限管理模态框 */}
      <UserRolePermissionModal
        user={selectedUser}
        isOpen={isRolePermissionModalOpen}
        onClose={() => {
          setIsRolePermissionModalOpen(false);
          setSelectedUser(null);
        }}
        onSuccess={() => {
          // 角色权限分配成功后可以选择是否刷新用户列表
          // 这里暂时不刷新，因为用户列表本身不显示角色权限信息
        }}
      />

      {/* 新建用户模态框 */}
      <UserCreateModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSuccess={handleCreateSuccess}
      />

      {/* 删除确认对话框 */}
      {isDeleteConfirmOpen && userToDelete && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Card className="w-full max-w-md bg-white">
            <div className="p-6">
              {/* 标题 */}
              <div className="flex items-center gap-3 mb-4">
                <div className="p-2 bg-red-100 rounded-lg">
                  <Trash2 className="h-5 w-5 text-red-600" />
                </div>
                <div>
                  <h2 className="text-xl font-semibold text-gray-900">确认删除用户</h2>
                  <p className="text-sm text-gray-600">此操作不可撤销</p>
                </div>
              </div>

              {/* 警告信息 */}
              <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0">
                    <div className="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center">
                      <span className="text-red-600 text-sm font-bold">!</span>
                    </div>
                  </div>
                  <div className="flex-1">
                    <h3 className="text-sm font-medium text-red-800 mb-2">⚠️ 高风险操作警告</h3>
                    <div className="text-sm text-red-700 space-y-1">
                      <p>您即将永久删除用户：<strong>{userToDelete.username}</strong></p>
                      <p>此操作将会：</p>
                      <ul className="list-disc list-inside ml-2 space-y-1">
                        <li>永久删除用户账户信息</li>
                        <li>删除所有角色和权限分配</li>
                        <li>删除所有应用权限关联</li>
                        <li>此操作无法撤销</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              {/* 用户信息确认 */}
              <div className="bg-gray-50 rounded-lg p-4 mb-6">
                <div className="flex items-center gap-3">
                  <div className="flex-shrink-0 h-10 w-10">
                    <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                      <span className="text-sm font-medium text-blue-600">
                        {userToDelete.username.charAt(0).toUpperCase()}
                      </span>
                    </div>
                  </div>
                  <div className="flex-1">
                    <div className="text-sm font-medium text-gray-900">{userToDelete.username}</div>
                    {userToDelete.email && (
                      <div className="text-sm text-gray-500">{userToDelete.email}</div>
                    )}
                    {userToDelete.department && (
                      <div className="text-xs text-gray-400">{userToDelete.department}</div>
                    )}
                  </div>
                </div>
              </div>

              {/* 操作按钮 */}
              <div className="flex items-center justify-end gap-3">
                <Button
                  variant="outline"
                  onClick={cancelDeleteUser}
                >
                  取消
                </Button>
                <Button
                  onClick={confirmDeleteUser}
                  className="bg-red-600 hover:bg-red-700 text-white"
                >
                  确认删除
                </Button>
              </div>
            </div>
          </Card>
        </div>
      )}
    </div>
  );
};

export default UserManagementTab;
