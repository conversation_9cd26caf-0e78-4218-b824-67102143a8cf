import React, { useState, useEffect } from 'react';
import { X, Save, Shield } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { toast } from '@/hooks/use-toast';
import { userManagementApi, Role as RoleType } from '@/services/userManagementApi';

interface RoleEditModalProps {
  role: RoleType | null;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

interface RoleFormData {
  role_name: string;
  role_code: string;
  description: string;
}

const RoleEditModal: React.FC<RoleEditModalProps> = ({
  role,
  isOpen,
  onClose,
  onSuccess
}) => {
  const [formData, setFormData] = useState<RoleFormData>({
    role_name: '',
    role_code: '',
    description: ''
  });
  const [loading, setLoading] = useState(false);

  // 当角色数据变化时更新表单
  useEffect(() => {
    if (role) {
      setFormData({
        role_name: role.role_name || '',
        role_code: role.role_code || '',
        description: role.description || ''
      });
    }
  }, [role]);

  const handleInputChange = (field: keyof RoleFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!role) return;

    setLoading(true);
    try {
      await userManagementApi.updateRole(role.id, formData);
      
      toast({
        title: "更新成功",
        description: `角色 ${formData.role_name} 信息已更新`,
      });
      
      onSuccess();
      onClose();
    } catch (error) {
      console.error('更新角色失败:', error);
      toast({
        title: "更新失败",
        description: error instanceof Error ? error.message : "更新角色信息时发生错误",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen || !role) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto bg-white">
        <div className="p-6">
          {/* 标题栏 */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Shield className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900">编辑角色</h2>
                <p className="text-sm text-gray-600">修改角色基本信息</p>
              </div>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={onClose}
              className="flex items-center gap-2"
            >
              <X className="h-4 w-4" />
              关闭
            </Button>
          </div>

          {/* 系统角色提示 */}
          {role.is_system && (
            <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-center gap-2">
                <Shield className="h-4 w-4 text-yellow-600" />
                <span className="text-sm font-medium text-yellow-800">系统角色</span>
              </div>
              <p className="text-sm text-yellow-700 mt-1">
                这是系统内置角色，某些字段可能无法修改
              </p>
            </div>
          )}

          {/* 表单 */}
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* 角色名称 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  角色名称 <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={formData.role_name}
                  onChange={(e) => handleInputChange('role_name', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                  disabled={role.is_system}
                />
              </div>

              {/* 角色代码 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  角色代码 <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={formData.role_code}
                  onChange={(e) => handleInputChange('role_code', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                  disabled={role.is_system}
                  placeholder="如: admin, user, manager"
                />
              </div>

              {/* 角色描述 */}
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  角色描述
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="请输入角色描述..."
                />
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex justify-end gap-3 pt-4 border-t">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={loading}
              >
                取消
              </Button>
              <Button
                type="submit"
                disabled={loading || role.is_system}
                className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2"
              >
                <Save className="h-4 w-4" />
                {loading ? '保存中...' : '保存'}
              </Button>
            </div>
          </form>
        </div>
      </Card>
    </div>
  );
};

export default RoleEditModal;
