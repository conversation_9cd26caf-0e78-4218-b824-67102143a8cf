import React, { useState, useEffect } from 'react';
import {
  Key,
  Plus,
  Search,
  Edit,
  Trash2,
  RefreshCw,
  X
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { toast } from '@/hooks/use-toast';
import { Badge } from '@/components/ui/badge';
import { userManagementApi, Permission } from '@/services/userManagementApi';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { usePermissions, hasPermission } from '@/hooks/usePermissions';

// 类型定义已从 userManagementApi 导入

const PermissionManagementTab: React.FC = () => {
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [pageSize] = useState(20);
  const [total, setTotal] = useState(0);
  const [search, setSearch] = useState('');

  // 获取当前用户权限
  const { permissions: userPermissions, loading: userPermissionsLoading } = usePermissions();

  // 模态框状态
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedPermission, setSelectedPermission] = useState<Permission | null>(null);

  // 表单数据
  const [formData, setFormData] = useState({
    permission_name: '',
    permission_code: '',
    permission_type: 'menu',
    parent_id: 0,
    app_id: 1,
    resource_path: '',
    description: '',
    sort_order: 0
  });

  // 检查用户是否有管理特定权限的权限
  const canManagePermission = (permission: Permission): boolean => {
    // 如果用户有 user_mgmt:access 权限，可以管理所有权限
    if (hasPermission('user_mgmt:access', userPermissions)) {
      return true;
    }

    // 用户只能管理他们自己拥有的权限
    return hasPermission(permission.permission_code, userPermissions);
  };

  // 加载权限列表（根据用户权限过滤）
  const loadPermissions = async (currentPage: number = 1) => {
    setLoading(true);
    try {
      const response = await userManagementApi.getPermissions(currentPage, pageSize, search);

      // 根据用户权限过滤可管理的权限
      const filteredPermissions = response.items.filter(permission =>
        canManagePermission(permission)
      );

      setPermissions(filteredPermissions);
      setTotal(filteredPermissions.length);
      setPage(currentPage);
    } catch (error) {
      console.error('加载权限列表失败:', error);
      toast({
        title: "加载失败",
        description: "无法加载权限列表",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载 - 等待用户权限加载完成
  useEffect(() => {
    if (!userPermissionsLoading) {
      loadPermissions(1);
    }
  }, [userPermissionsLoading]);

  // 搜索处理
  const handleSearch = () => {
    setPage(1);
    loadPermissions(1);
  };

  // 清空搜索
  const handleClearSearch = () => {
    setSearch('');
    setPage(1);
    loadPermissions(1);
  };

  // 重置表单
  const resetForm = () => {
    setFormData({
      permission_name: '',
      permission_code: '',
      permission_type: 'menu',
      parent_id: 0,
      app_id: 1,
      resource_path: '',
      description: '',
      sort_order: 0
    });
  };

  // 检查是否有创建权限的权限
  const canCreatePermission = (): boolean => {
    return hasPermission('user_mgmt:access', userPermissions);
  };

  // 打开新增模态框
  const handleCreate = () => {
    if (!canCreatePermission()) {
      toast({
        title: "权限不足",
        description: "您没有创建权限的权限",
        variant: "destructive",
      });
      return;
    }
    resetForm();
    setIsCreateModalOpen(true);
  };

  // 打开编辑模态框
  const handleEdit = (permission: Permission) => {
    if (!canManagePermission(permission)) {
      toast({
        title: "权限不足",
        description: "您没有编辑此权限的权限",
        variant: "destructive",
      });
      return;
    }

    setSelectedPermission(permission);
    setFormData({
      permission_name: permission.permission_name,
      permission_code: permission.permission_code,
      permission_type: permission.permission_type,
      parent_id: permission.parent_id,
      app_id: permission.app_id || 1,
      resource_path: permission.resource_path || '',
      description: permission.description || '',
      sort_order: permission.sort_order
    });
    setIsEditModalOpen(true);
  };

  // 打开删除确认对话框
  const handleDelete = (permission: Permission) => {
    if (!canManagePermission(permission)) {
      toast({
        title: "权限不足",
        description: "您没有删除此权限的权限",
        variant: "destructive",
      });
      return;
    }

    setSelectedPermission(permission);
    setIsDeleteDialogOpen(true);
  };

  // 提交创建权限
  const handleCreateSubmit = async () => {
    try {
      await userManagementApi.createPermission({
        permission_name: formData.permission_name,
        permission_code: formData.permission_code,
        permission_type: formData.permission_type,
        parent_id: formData.parent_id,
        app_id: formData.app_id,
        resource_path: formData.resource_path,
        description: formData.description,
        sort_order: formData.sort_order
      });
      toast({
        title: "创建成功",
        description: "权限创建成功",
      });
      setIsCreateModalOpen(false);
      resetForm();
      loadPermissions(page);
    } catch (error) {
      console.error('创建权限失败:', error);
      toast({
        title: "创建失败",
        description: "权限创建失败",
        variant: "destructive",
      });
    }
  };

  // 提交编辑权限
  const handleEditSubmit = async () => {
    if (!selectedPermission) return;

    try {
      await userManagementApi.updatePermission(selectedPermission.id, {
        permission_name: formData.permission_name,
        permission_type: formData.permission_type,
        parent_id: formData.parent_id,
        app_id: formData.app_id,
        resource_path: formData.resource_path,
        description: formData.description,
        sort_order: formData.sort_order
      });
      toast({
        title: "更新成功",
        description: "权限更新成功",
      });
      setIsEditModalOpen(false);
      setSelectedPermission(null);
      resetForm();
      loadPermissions(page);
    } catch (error) {
      console.error('更新权限失败:', error);
      toast({
        title: "更新失败",
        description: "权限更新失败",
        variant: "destructive",
      });
    }
  };

  // 确认删除权限
  const handleDeleteConfirm = async () => {
    if (!selectedPermission) return;

    try {
      await userManagementApi.deletePermission(selectedPermission.id);
      toast({
        title: "删除成功",
        description: "权限删除成功",
      });
      setIsDeleteDialogOpen(false);
      setSelectedPermission(null);
      loadPermissions(page);
    } catch (error) {
      console.error('删除权限失败:', error);
      toast({
        title: "删除失败",
        description: "权限删除失败",
        variant: "destructive",
      });
    }
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  // 获取状态徽章
  const getStatusBadge = (status: boolean) => {
    return status ? (
      <Badge variant="default" className="bg-green-100 text-green-800">启用</Badge>
    ) : (
      <Badge variant="secondary" className="bg-red-100 text-red-800">禁用</Badge>
    );
  };

  // 获取权限类型徽章（只显示应用权限）
  const getPermissionTypeBadge = (type: string) => {
    // 现在只显示菜单权限，统一显示为"应用权限"
    return (
      <Badge variant="secondary" className="bg-blue-100 text-blue-800">
        应用权限
      </Badge>
    );
  };

  return (
    <div className="space-y-4">
      {/* 操作栏 */}
      <Card className="p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <Key className="h-5 w-5 text-blue-600" />
            <h3 className="text-lg font-medium text-gray-900">权限管理</h3>
            {total > 0 && (
              <span className="ml-2 bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full">
                {total}
              </span>
            )}
          </div>
          <div className="flex items-center gap-2">
            {/* 只有有权限的用户才能看到新增按钮 */}
            {canCreatePermission() && (
              <Button
                onClick={handleCreate}
                size="sm"
                className="bg-green-600 hover:bg-green-700 text-white flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                新增权限
              </Button>
            )}
          </div>
        </div>
        
        {/* 搜索区域 */}
        <div className="flex items-center gap-3">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="搜索权限名称或编码..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
          <Button
            onClick={handleSearch}
            size="sm"
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            搜索
          </Button>
          <Button
            onClick={handleClearSearch}
            variant="outline"
            size="sm"
          >
            重置
          </Button>

        </div>
      </Card>

      {/* 权限列表 */}
      <Card className="p-0">
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin text-blue-500 mr-2" />
            <span className="text-gray-600">加载权限数据中...</span>
          </div>
        ) : permissions.length > 0 ? (
          <>
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead className="bg-gray-50 border-b border-gray-200">
                  <tr>
                    <th className="text-left p-4 font-medium text-gray-900">权限信息</th>
                    <th className="text-left p-4 font-medium text-gray-900">权限编码</th>
                    <th className="text-left p-4 font-medium text-gray-900">类型</th>
                    <th className="text-left p-4 font-medium text-gray-900">所属应用</th>
                    <th className="text-left p-4 font-medium text-gray-900">资源路径</th>
                    <th className="text-left p-4 font-medium text-gray-900">状态</th>
                    <th className="text-center p-4 font-medium text-gray-900">操作</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {permissions.map((permission) => (
                    <tr key={permission.id} className="hover:bg-gray-50">
                      <td className="p-4">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{permission.permission_name}</div>
                          {permission.description && (
                            <div className="text-sm text-gray-500 mt-1">{permission.description}</div>
                          )}
                          <div className="text-xs text-gray-400 mt-1">
                            排序: {permission.sort_order} | 父级ID: {permission.parent_id}
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <code className="text-sm bg-gray-100 px-2 py-1 rounded">{permission.permission_code}</code>
                      </td>
                      <td className="p-4">
                        {getPermissionTypeBadge(permission.permission_type)}
                      </td>
                      <td className="p-4">
                        {permission.app_name ? (
                          <Badge variant="outline">{permission.app_name}</Badge>
                        ) : (
                          <span className="text-gray-400">-</span>
                        )}
                      </td>
                      <td className="p-4">
                        {permission.resource_path ? (
                          <code className="text-xs bg-gray-100 px-2 py-1 rounded">{permission.resource_path}</code>
                        ) : (
                          <span className="text-gray-400">-</span>
                        )}
                      </td>
                      <td className="p-4">
                        {getStatusBadge(permission.status)}
                      </td>
                      <td className="text-center p-4">
                        <div className="flex items-center justify-center gap-1">
                          {canManagePermission(permission) ? (
                            <>
                              <button
                                onClick={() => handleEdit(permission)}
                                className="p-1 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors"
                                title="编辑权限"
                              >
                                <Edit className="h-4 w-4" />
                              </button>
                              <button
                                onClick={() => handleDelete(permission)}
                                className="p-1 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded transition-colors"
                                title="删除权限"
                              >
                                <Trash2 className="h-4 w-4" />
                              </button>
                            </>
                          ) : (
                            <span className="text-xs text-gray-400">无权限操作</span>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </>
        ) : (
          <div className="text-center py-8">
            <Key className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">暂无权限数据</h3>
            <p className="text-gray-500">
              {search ? "没有找到符合搜索条件的权限" : "系统中暂无权限"}
            </p>
          </div>
        )}
      </Card>

      {/* 新增权限模态框 */}
      <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Plus className="h-5 w-5 text-green-600" />
              新增权限
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="permission_name">权限名称</Label>
              <Input
                id="permission_name"
                value={formData.permission_name}
                onChange={(e) => setFormData({...formData, permission_name: e.target.value})}
                placeholder="请输入权限名称"
              />
            </div>
            <div>
              <Label htmlFor="permission_code">权限编码</Label>
              <Input
                id="permission_code"
                value={formData.permission_code}
                onChange={(e) => setFormData({...formData, permission_code: e.target.value})}
                placeholder="请输入权限编码"
              />
            </div>
            <div>
              <Label htmlFor="resource_path">资源路径</Label>
              <Input
                id="resource_path"
                value={formData.resource_path}
                onChange={(e) => setFormData({...formData, resource_path: e.target.value})}
                placeholder="请输入资源路径"
              />
            </div>
            <div>
              <Label htmlFor="description">描述</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
                placeholder="请输入权限描述"
                rows={3}
              />
            </div>
            <div>
              <Label htmlFor="sort_order">排序</Label>
              <Input
                id="sort_order"
                type="number"
                value={formData.sort_order}
                onChange={(e) => setFormData({...formData, sort_order: parseInt(e.target.value) || 0})}
                placeholder="请输入排序号"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateModalOpen(false)}>
              取消
            </Button>
            <Button onClick={handleCreateSubmit} className="bg-green-600 hover:bg-green-700">
              创建
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 编辑权限模态框 */}
      <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Edit className="h-5 w-5 text-blue-600" />
              编辑权限
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="edit_permission_name">权限名称</Label>
              <Input
                id="edit_permission_name"
                value={formData.permission_name}
                onChange={(e) => setFormData({...formData, permission_name: e.target.value})}
                placeholder="请输入权限名称"
              />
            </div>
            <div>
              <Label htmlFor="edit_permission_code">权限编码</Label>
              <Input
                id="edit_permission_code"
                value={formData.permission_code}
                onChange={(e) => setFormData({...formData, permission_code: e.target.value})}
                placeholder="请输入权限编码"
              />
            </div>
            <div>
              <Label htmlFor="edit_resource_path">资源路径</Label>
              <Input
                id="edit_resource_path"
                value={formData.resource_path}
                onChange={(e) => setFormData({...formData, resource_path: e.target.value})}
                placeholder="请输入资源路径"
              />
            </div>
            <div>
              <Label htmlFor="edit_description">描述</Label>
              <Textarea
                id="edit_description"
                value={formData.description}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
                placeholder="请输入权限描述"
                rows={3}
              />
            </div>
            <div>
              <Label htmlFor="edit_sort_order">排序</Label>
              <Input
                id="edit_sort_order"
                type="number"
                value={formData.sort_order}
                onChange={(e) => setFormData({...formData, sort_order: parseInt(e.target.value) || 0})}
                placeholder="请输入排序号"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditModalOpen(false)}>
              取消
            </Button>
            <Button onClick={handleEditSubmit} className="bg-blue-600 hover:bg-blue-700">
              保存
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Trash2 className="h-5 w-5 text-red-600" />
              删除权限
            </DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p className="text-gray-600">
              确定要删除权限 <span className="font-medium text-gray-900">"{selectedPermission?.permission_name}"</span> 吗？
            </p>
            <p className="text-sm text-red-600 mt-2">
              此操作不可撤销，请谨慎操作。
            </p>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handleDeleteConfirm} variant="destructive">
              删除
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default PermissionManagementTab;
