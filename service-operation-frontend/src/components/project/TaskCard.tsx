import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  Users, 
  Plane, 
  Train, 
  Building2, 
  Calendar,
  CreditCard,
  CheckCircle2,
  AlertTriangle,
  Clock,
  MoreVertical
} from 'lucide-react';
import { TaskCardInfo, TaskStatus } from '@/types/project-task';

interface TaskCardProps {
  taskInfo: TaskCardInfo;
  onEdit?: () => void;
  onDelete?: () => void;
  onStart?: () => void;
}

// 获取任务类型的图标和颜色配置
const getTaskTypeConfig = (taskType: string) => {
  const configs = {
    '酒店预订': {
      icon: Building2,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      borderColor: 'border-purple-200',
      label: '酒店预订'
    },
    '机票预订': {
      icon: Plane,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200',
      label: '机票预订'
    },
    '火车票预订': {
      icon: Train,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200',
      label: '火车票预订'
    },
    '交通安排': {
      icon: Train,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      borderColor: 'border-orange-200',
      label: '交通安排'
    },
    default: {
      icon: Calendar,
      color: 'text-gray-600',
      bgColor: 'bg-gray-50',
      borderColor: 'border-gray-200',
      label: taskType
    }
  };
  
  return configs[taskType as keyof typeof configs] || configs.default;
};

// 获取任务状态配置
const getTaskStatusConfig = (status: string) => {
  switch (status) {
    case TaskStatus.PENDING:
      return { 
        icon: Clock, 
        color: 'text-yellow-600', 
        bg: 'bg-yellow-50',
        label: '待处理'
      };
    case TaskStatus.IN_PROGRESS:
      return { 
        icon: Clock, 
        color: 'text-blue-600', 
        bg: 'bg-blue-50',
        label: '进行中'
      };
    case TaskStatus.COMPLETED:
      return { 
        icon: CheckCircle2, 
        color: 'text-green-600', 
        bg: 'bg-green-50',
        label: '已完成'
      };
    case TaskStatus.CANCELLED:
      return { 
        icon: AlertTriangle, 
        color: 'text-red-600', 
        bg: 'bg-red-50',
        label: '已取消'
      };
    default:
      return { 
        icon: Clock, 
        color: 'text-gray-600', 
        bg: 'bg-gray-50',
        label: status
      };
  }
};

// 获取预订状态颜色
const getBookingStatusColor = (status: string, hasOrders: boolean) => {
  if (!hasOrders) return 'text-gray-500';
  
  switch (status) {
    case '已确认':
      return 'text-green-600';
    case '待确认':
      return 'text-yellow-600';
    case '已取消':
      return 'text-red-600';
    default:
      return 'text-blue-600';
  }
};

const TaskCard: React.FC<TaskCardProps> = ({ taskInfo, onEdit, onStart }) => {
  const { task } = taskInfo;
  const config = getTaskTypeConfig(task.task_type);
  const statusConfig = getTaskStatusConfig(task.task_status);
  const IconComponent = config.icon;
  const StatusIcon = statusConfig.icon;
  const hasOrders = taskInfo.order_count > 0;

  return (
    <Card className="bg-white border border-gray-200 hover:shadow-md transition-all duration-200 overflow-hidden">
      {/* 卡片头部 */}
      <div className="p-4 border-b border-gray-100">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <div className={`p-2 ${config.bgColor} rounded-lg`}>
              <IconComponent className={`h-5 w-5 ${config.color}`} />
            </div>
            <div>
              <h3 className="font-medium text-gray-900">{config.label}</h3>
              <p className="text-sm text-gray-500">
                {hasOrders ? `${taskInfo.order_count}次预订` : '0次预订'}
              </p>
            </div>
          </div>
          
          {/* 状态和菜单 */}
          <div className="flex items-center gap-2">
            <div className={`p-1.5 ${statusConfig.bg} rounded-lg`}>
              <StatusIcon className={`h-3 w-3 ${statusConfig.color}`} />
            </div>
            {taskInfo.has_exception && (
              <div className="p-1.5 bg-red-50 rounded-lg">
                <AlertTriangle className="h-3 w-3 text-red-500" />
              </div>
            )}
            <button className="p-1 hover:bg-gray-100 rounded">
              <MoreVertical className="h-4 w-4 text-gray-400" />
            </button>
          </div>
        </div>
      </div>

      {/* 卡片内容 */}
      <div className="p-4">
        {/* 统计信息 */}
        <div className="grid grid-cols-3 gap-4 mb-4">
          <div className="text-center">
            <div className="flex items-center justify-center mb-1">
              <Users className="h-4 w-4 text-gray-400" />
            </div>
            <div className="text-lg font-semibold text-gray-900">{taskInfo.total_people}</div>
            <div className="text-xs text-gray-500">总人数</div>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center mb-1">
              <Calendar className="h-4 w-4 text-gray-400" />
            </div>
            <div className="text-lg font-semibold text-gray-900">{taskInfo.order_count}</div>
            <div className="text-xs text-gray-500">总票数</div>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center mb-1">
              <CreditCard className="h-4 w-4 text-gray-400" />
            </div>
            <div className="text-lg font-semibold text-blue-600">
              ¥{(taskInfo.total_amount / 1000).toFixed(0)}K
            </div>
            <div className="text-xs text-gray-500">总金额</div>
          </div>
        </div>

        {/* 预订状态 */}
        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-gray-600">订单状态</span>
            <span className={`text-sm font-medium ${getBookingStatusColor(taskInfo.booking_status, hasOrders)}`}>
              {hasOrders ? taskInfo.booking_status : '暂无订单'}
            </span>
          </div>
          
          {/* 简化的状态指示器 */}
          <div className="flex items-center gap-1">
            <div className={`h-2 w-2 rounded-full ${hasOrders ? 'bg-blue-500' : 'bg-gray-300'}`}></div>
            <div className="h-1 flex-1 bg-gray-200 rounded">
              <div 
                className={`h-full rounded transition-all duration-300 ${
                  hasOrders ? 'bg-blue-500' : 'bg-gray-300'
                }`}
                style={{ width: hasOrders ? '70%' : '10%' }}
              ></div>
            </div>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="space-y-2">
          {!hasOrders ? (
            <Button 
              className="w-full bg-blue-600 hover:bg-blue-700 text-white"
              onClick={onStart}
            >
              开始{config.label}
            </Button>
          ) : (
            <div className="grid grid-cols-2 gap-2">
              <Button 
                variant="outline" 
                size="sm"
                onClick={onEdit}
                className="text-gray-600 border-gray-300 hover:bg-gray-50"
              >
                继续预订
              </Button>
              <Button 
                size="sm"
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                查看订单
              </Button>
            </div>
          )}
        </div>

        {/* 任务描述 */}
        {task.task_description && (
          <div className="mt-4 pt-4 border-t border-gray-100">
            <p className="text-sm text-gray-600 line-clamp-2">
              {task.task_description}
            </p>
          </div>
        )}
      </div>
    </Card>
  );
};

export default TaskCard; 