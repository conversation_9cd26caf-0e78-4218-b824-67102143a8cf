import React, { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import { CreateProjectRequest } from '@/types/project';

interface CreateProjectModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: CreateProjectRequest) => void;
  loading?: boolean;
}

const CreateProjectModal: React.FC<CreateProjectModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  loading = false
}) => {
  const [formData, setFormData] = useState<CreateProjectRequest>({
    project_name: '',
    project_description: '',
    client_name: '',
    cost_center: '',
    project_date: new Date().toISOString().split('T')[0]
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // 重置表单
  const resetForm = () => {
    setFormData({
      project_name: '',
      project_description: '',
      client_name: '',
      cost_center: '',
      project_date: new Date().toISOString().split('T')[0]
    });
    setErrors({});
  };

  // 当模态框打开时重置表单
  useEffect(() => {
    if (isOpen) {
      resetForm();
    }
  }, [isOpen]);

  // 验证表单
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.project_name.trim()) {
      newErrors.project_name = '项目名称不能为空';
    }

    if (!formData.client_name.trim()) {
      newErrors.client_name = '客户名称不能为空';
    }

    // if (!formData.cost_center.trim()) {
    //   newErrors.cost_center = '成本中心不能为空';
    // }

    if (!formData.project_date) {
      newErrors.project_date = '项目日期不能为空';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 处理表单提交
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (validateForm()) {
      onSubmit(formData);
    }
  };

  // 处理输入变化
  const handleChange = (field: keyof CreateProjectRequest, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  // 处理关闭
  const handleClose = () => {
    resetForm();
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">创建新项目</h2>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* 表单 */}
        <form onSubmit={handleSubmit} className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* 项目名称 */}
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                项目名称 <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={formData.project_name}
                onChange={(e) => handleChange('project_name', e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.project_name ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="请输入项目名称"
              />
              {errors.project_name && (
                <p className="mt-1 text-sm text-red-500">{errors.project_name}</p>
              )}
            </div>

            {/* 客户名称 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                客户名称 <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={formData.client_name}
                onChange={(e) => handleChange('client_name', e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.client_name ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="请输入客户名称"
              />
              {errors.client_name && (
                <p className="mt-1 text-sm text-red-500">{errors.client_name}</p>
              )}
            </div>

            {/* 成本中心 */}
            {/* <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                成本中心 <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={formData.cost_center}
                onChange={(e) => handleChange('cost_center', e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.cost_center ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="请输入成本中心"
              />
              {errors.cost_center && (
                <p className="mt-1 text-sm text-red-500">{errors.cost_center}</p>
              )}
            </div> */}

            {/* 项目日期 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                项目日期 <span className="text-red-500">*</span>
              </label>
              <input
                type="date"
                value={formData.project_date}
                onChange={(e) => handleChange('project_date', e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.project_date ? 'border-red-500' : 'border-gray-300'
                }`}
              />
              {errors.project_date && (
                <p className="mt-1 text-sm text-red-500">{errors.project_date}</p>
              )}
            </div>

            {/* 项目描述 */}
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                项目描述
              </label>
              <textarea
                value={formData.project_description}
                onChange={(e) => handleChange('project_description', e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="请输入项目描述（选填）"
              />
            </div>
          </div>

          {/* 按钮组 */}
          <div className="flex justify-end space-x-4 mt-6">
            <button
              type="button"
              onClick={handleClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              取消
            </button>
            <button
              type="submit"
              disabled={loading}
              className={`px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${
                loading ? 'opacity-75 cursor-not-allowed' : ''
              }`}
            >
              {loading ? '创建中...' : '创建项目'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CreateProjectModal; 