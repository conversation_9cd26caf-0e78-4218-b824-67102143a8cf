import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>alogContent, <PERSON>alogHeader, <PERSON>alogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { Upload, Download, FileText } from 'lucide-react';
import * as XLSX from 'xlsx-js-style';

interface FlightExcelConverterProps {
  onConversionComplete?: (file: File) => void;
}

const FlightExcelConverter: React.FC<FlightExcelConverterProps> = ({ onConversionComplete }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [converting, setConverting] = useState(false);
  const { toast } = useToast();

  // 飞机票字段映射配置
  const fieldMappings = [
    { source: '编号', target: '序号', required: false },
    { source: '姓名', target: '出行人姓名', required: true },
    { source: '姓', target: '出行人姓', required: false },
    { source: '名', target: '出行人名', required: false },
    { source: '国籍', target: '国籍', required: false },
    { source: '性别', target: '性别', required: false },
    { source: '出生日期', target: '出生日期', required: false },
    { source: '证件类型', target: '证件类型', required: false },
    { source: '证件号码', target: '证件号码', required: true },
    { source: '证件有效期', target: '证件有效期至', required: false },
    { source: '手机号国际区号', target: '手机号国际区号', required: false },
    { source: '手机号', target: '手机号', required: true },
    { source: '出行日期', target: '出行日期', required: true },
    { source: '出发机场', target: '出发机场名', required: true },
    { source: '到达机场', target: '到达机场名', required: true },
    { source: '航班号', target: '航班号', required: true },
    { source: '出发时间', target: '出发时间', required: false },
    { source: '到达时间', target: '到达时间', required: false },
    { source: '行程提交项', target: '行程提交项', required: false },
    { source: '联系人', target: '联系人', required: false },
    { source: '联系人手机号', target: '联系人手机号', required: false },
    { source: '联系人邮箱', target: '联系人邮箱', required: false },
    { source: '审批参照人', target: '审批参照人', required: false },
    { source: '公司名称', target: '公司名称', required: false },
    { source: '代订人', target: '代订人', required: false },
    { source: '出票短信', target: '出票短信', required: false },
    { source: '金额', target: '金额', required: false },
    { source: '订单号', target: '订单号', required: false },
    { source: '账单号', target: '账单号', required: false },
  ];

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
    }
  };

  const downloadTemplate = () => {
    const link = document.createElement('a');
    link.href = '/templates/flight_template.xlsx';
    link.download = 'flight_template.xlsx';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const convertExcel = async () => {
    if (!selectedFile) {
      toast({
        title: "错误",
        description: "请先选择要转换的文件",
        variant: "destructive",
      });
      return;
    }

    try {
      setConverting(true);

      // 读取源文件
      const sourceBuffer = await selectedFile.arrayBuffer();
      const sourceWorkbook = XLSX.read(sourceBuffer, { type: 'array' });
      const sourceSheet = sourceWorkbook.Sheets[sourceWorkbook.SheetNames[0]];
      const sourceData = XLSX.utils.sheet_to_json(sourceSheet, { header: 1 }) as any[][];

      if (sourceData.length < 2) {
        throw new Error('源文件数据不足，至少需要包含表头和一行数据');
      }

      // 获取模板文件
      const templateResponse = await fetch('/templates/flight_template.xlsx');
      if (!templateResponse.ok) {
        throw new Error('无法获取模板文件');
      }
      const templateBuffer = await templateResponse.arrayBuffer();
      const templateWorkbook = XLSX.read(templateBuffer, { type: 'array' });
      const templateSheet = templateWorkbook.Sheets[templateWorkbook.SheetNames[0]];

      // 读取模板的前三行（格式行）
      const templateData = XLSX.utils.sheet_to_json(templateSheet, { header: 1 }) as any[][];
      const headerRows = templateData.slice(0, 3); // 保留前三行

      // 获取源文件的表头（假设在第一行或第二行）
      let sourceHeaderRowIndex = 0;
      let sourceHeaders: string[] = [];
      
      // 尝试找到包含最多非空值的行作为表头
      for (let i = 0; i < Math.min(3, sourceData.length); i++) {
        const row = sourceData[i];
        const nonEmptyCount = row.filter(cell => cell && cell.toString().trim() !== '').length;
        if (nonEmptyCount > sourceHeaders.length) {
          sourceHeaders = row.map(cell => cell ? cell.toString().trim() : '');
          sourceHeaderRowIndex = i;
        }
      }

      // 获取目标表头（模板的第二行）
      const targetHeaders = headerRows[1] || [];

      // 创建字段映射
      const columnMapping: { [key: number]: number } = {};
      
      sourceHeaders.forEach((sourceHeader, sourceIndex) => {
        // 查找对应的目标列
        const mapping = fieldMappings.find(m => 
          m.source === sourceHeader || 
          sourceHeader.includes(m.source) || 
          m.source.includes(sourceHeader)
        );
        
        if (mapping) {
          const targetIndex = targetHeaders.findIndex((header: string) => header === mapping.target);
          if (targetIndex !== -1) {
            columnMapping[sourceIndex] = targetIndex;
          }
        }
      });

      // 转换数据
      const convertedData = [...headerRows]; // 保留模板的前三行
      
      // 处理数据行（从表头行的下一行开始）
      for (let i = sourceHeaderRowIndex + 1; i < sourceData.length; i++) {
        const sourceRow = sourceData[i];
        const targetRow = new Array(targetHeaders.length).fill('');
        
        // 映射数据
        Object.entries(columnMapping).forEach(([sourceColStr, targetCol]) => {
          const sourceCol = parseInt(sourceColStr);
          if (sourceRow[sourceCol] !== undefined && sourceRow[sourceCol] !== null) {
            let value = sourceRow[sourceCol].toString().trim();
            
            // 特殊处理
            const targetHeader = targetHeaders[targetCol];
            
            // 自动填充国籍
            if (targetHeader === '国籍' && (!value || value === '')) {
              value = '中国';
            }
            
            // 手机号国际区号处理
            if (targetHeader === '手机号国际区号' && (!value || value === '')) {
              value = '86';
            }
            
            // 金额处理
            if (targetHeader === '金额' && value) {
              try {
                const numValue = parseFloat(value.replace(/[^\d.]/g, ''));
                if (!isNaN(numValue)) {
                  value = numValue.toFixed(2);
                }
              } catch (e) {
                // 保持原值
              }
            }
            
            targetRow[targetCol] = value;
          }
        });
        
        // 自动生成序号
        const sequenceIndex = targetHeaders.findIndex((header: string) => header === '序号');
        if (sequenceIndex !== -1 && (!targetRow[sequenceIndex] || targetRow[sequenceIndex] === '')) {
          targetRow[sequenceIndex] = (i - sourceHeaderRowIndex).toString();
        }
        
        convertedData.push(targetRow);
      }

      // 创建新的工作簿
      const newWorkbook = XLSX.utils.book_new();
      const newSheet = XLSX.utils.aoa_to_sheet(convertedData);
      
      // 设置列宽
      const colWidths = targetHeaders.map(() => ({ wch: 15 }));
      newSheet['!cols'] = colWidths;
      
      // 添加边框样式
      const range = XLSX.utils.decode_range(newSheet['!ref'] || 'A1');
      for (let row = range.s.r; row <= range.e.r; row++) {
        for (let col = range.s.c; col <= range.e.c; col++) {
          const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
          if (!newSheet[cellAddress]) {
            newSheet[cellAddress] = { t: 's', v: '' };
          }
          if (!newSheet[cellAddress].s) {
            newSheet[cellAddress].s = {};
          }
          newSheet[cellAddress].s.border = {
            top: { style: 'thin', color: { rgb: '000000' } },
            bottom: { style: 'thin', color: { rgb: '000000' } },
            left: { style: 'thin', color: { rgb: '000000' } },
            right: { style: 'thin', color: { rgb: '000000' } }
          };
        }
      }
      
      XLSX.utils.book_append_sheet(newWorkbook, newSheet, 'Sheet1');
      
      // 生成文件
      const convertedBuffer = XLSX.write(newWorkbook, { type: 'array', bookType: 'xlsx' });
      const convertedFile = new File([convertedBuffer], `converted_${selectedFile.name}`, {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      });

      toast({
        title: "转换成功",
        description: `已成功转换 ${convertedData.length - 3} 条记录`,
      });

      // 回调通知父组件
      if (onConversionComplete) {
        onConversionComplete(convertedFile);
      }

      setIsOpen(false);
      setSelectedFile(null);

    } catch (error: any) {
      console.error('转换失败:', error);
      toast({
        title: "转换失败",
        description: error.message || "文件转换过程中发生错误",
        variant: "destructive",
      });
    } finally {
      setConverting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline">
          <FileText className="h-4 w-4 mr-2" />
          格式转换
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>飞机票Excel格式转换</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div className="text-sm text-muted-foreground">
            将其他格式的飞机票Excel文件转换为系统标准模板格式
          </div>
          
          <div>
            <Label htmlFor="source-file">选择源文件</Label>
            <Input
              id="source-file"
              type="file"
              accept=".xlsx,.xls"
              onChange={handleFileSelect}
            />
          </div>
          
          <div className="flex space-x-2">
            <Button
              variant="outline"
              onClick={downloadTemplate}
              className="flex-1"
            >
              <Download className="h-4 w-4 mr-2" />
              下载模板
            </Button>
            <Button
              onClick={convertExcel}
              disabled={!selectedFile || converting}
              className="flex-1"
            >
              <Upload className="h-4 w-4 mr-2" />
              {converting ? '转换中...' : '转换'}
            </Button>
          </div>
          
          <div className="text-xs text-muted-foreground">
            <p>转换说明：</p>
            <ul className="list-disc list-inside space-y-1 mt-1">
              <li>系统会自动匹配相似的列名</li>
              <li>自动填充国籍为"中国"</li>
              <li>自动设置手机号国际区号为"86"</li>
              <li>保留模板格式和样式</li>
            </ul>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default FlightExcelConverter;
