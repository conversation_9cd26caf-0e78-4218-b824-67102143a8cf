import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useParams } from 'react-router-dom';
import {
  Upload,
  FileSpreadsheet,
  Download,
  Plane,
  Trash2,
  Eye,
  FileText,
  Send,
  AlertTriangle,
  Search,
  X,
  Check,
  RotateCcw,
  PenTool,
  Save,
  Edit,
  RefreshCw
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import * as XLSX from 'xlsx-js-style';
import { saveAs } from 'file-saver';
import { formatAmount, formatHotelOrderDate, formatDateForInput } from '../../utils/formatters';

// 使用飞机票订单API模块
import {
  flightOrderApi,
  type FlightOrder,
  type ProjectOrderStatsResponse,
  type ExcelValidationResponse,
  type CreateBookingTaskRequest,
  type ResetPausedOrdersRequest,
  type ResetFailedOrdersRequest
} from '@/api/flightOrder';
import { ProjectTaskService } from '@/services/project/projectTaskService';
import { getSystemSettingsCredentials } from '@/services/system/system-settings-service';
import FlightExcelConverter from '../flight_order/FlightExcelConverter';
import FormatConverterModal from './FormatConverterModal';
import FlightOrderDetailModal from './FlightOrderDetailModal';

// Tab类型定义
type TabType = 'input' | 'text';

interface FlightBookingContentProps {
  onNavigateToAllOrders?: () => void;
}

// 证件类型选项
const ID_TYPE_OPTIONS = [
  { value: '身份证', label: '身份证' },
  { value: '公务护照', label: '公务护照' },
  { value: '普通护照', label: '普通护照' },
  { value: '港澳通行证', label: '港澳通行证' },
  { value: '台胞证', label: '台胞证' },
  { value: '回乡证', label: '回乡证' },
  { value: '军人证', label: '军人证' },
  { value: '海员证', label: '海员证' },
  { value: '台湾通行证', label: '台湾通行证' },
  { value: '外国永久居留证', label: '外国永久居留证' },
  { value: '港澳台居民居住证', label: '港澳台居民居住证' },
  { value: '其他', label: '其他' }
];

// 性别选项
const GENDER_OPTIONS = [
  { value: '男', label: '男' },
  { value: '女', label: '女' }
];

const FlightBookingContent: React.FC<FlightBookingContentProps> = ({ onNavigateToAllOrders }) => {
  const { projectId } = useParams<{ projectId: string }>();
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 格式化日期为YYYY-MM-DD格式
  const formatDateToYearMonthDay = (dateStr: string | undefined) => {
    if (!dateStr) return '-';
    try {
      const date = new Date(dateStr);
      if (isNaN(date.getTime())) return dateStr;
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    } catch {
      return dateStr;
    }
  };

  // 格式化时间为HH:MM格式
  const formatTimeToHourMinute = (timeStr: string | undefined) => {
    if (!timeStr) return '-';
    try {
      // 如果是完整的日期时间格式，提取时间部分
      if (timeStr.includes(' ')) {
        const timePart = timeStr.split(' ')[1];
        if (timePart) {
          return timePart.substring(0, 5); // 返回 HH:MM
        }
      }
      // 如果已经是时间格式，直接返回前5位
      if (timeStr.includes(':')) {
        return timeStr.substring(0, 5);
      }
      return timeStr;
    } catch {
      return timeStr;
    }
  };

  // 状态管理
  const [activeTab, setActiveTab] = useState<TabType>('input');
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [validating, setValidating] = useState(false);
  const [clearing, setClearing] = useState(false);
  const [creating, setCreating] = useState(false);

  // 文件相关状态
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [validationResult, setValidationResult] = useState<ExcelValidationResponse | null>(null);
  const [showValidationDialog, setShowValidationDialog] = useState(false);
  const [validationDialogData, setValidationDialogData] = useState<any>(null);

  // 统计数据
  const [stats, setStats] = useState<ProjectOrderStatsResponse | null>(null);

  // 订单列表相关状态
  const [orders, setOrders] = useState<FlightOrder[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(20);
  const [totalCount, setTotalCount] = useState(0);
  const [pageLoading, setPageLoading] = useState(false);

  // 状态筛选
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>(['check_failed', 'initial', 'paused', 'failed']);

  // 搜索状态
  const [searchTravelerName, setSearchTravelerName] = useState('');
  const [searchMobilePhone, setSearchMobilePhone] = useState('');
  const [searchContactPhone, setSearchContactPhone] = useState('');

  // 任务创建相关状态
  const [taskTitle, setTaskTitle] = useState('');
  const [taskDescription, setTaskDescription] = useState('');
  const [bookingType, setBookingType] = useState('book_only');
  const [smsNotify, setSmsNotify] = useState(false);
  const [hasAgent, setHasAgent] = useState(false);
  const [agentName, setAgentName] = useState('');
  const [agentPhone, setAgentPhone] = useState('');
  const [settingsSource, setSettingsSource] = useState<'project_history' | 'localStorage' | 'default'>('default');

  // 文本解析相关状态
  const [textInput, setTextInput] = useState('');
  const [parsedData, setParsedData] = useState<any[]>([]);
  const [parsing, setParsing] = useState(false);

  // 对话框状态
  const [showClearDialog, setShowClearDialog] = useState(false);
  const [showTaskDialog, setShowTaskDialog] = useState(false);
  const [showFormatConverter, setShowFormatConverter] = useState(false);
  const [showGeneralConfirmDialog, setShowGeneralConfirmDialog] = useState(false);
  const [generalConfirmDialogData, setGeneralConfirmDialogData] = useState<any>(null);

  // 编辑相关状态
  const [selectedOrder, setSelectedOrder] = useState<FlightOrder | null>(null);
  const [editingOrder, setEditingOrder] = useState<FlightOrder | null>(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [shouldStartInEditMode, setShouldStartInEditMode] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  // 删除操作状态管理
  const [deletingOrderId, setDeletingOrderId] = useState<number | null>(null);

  // 清空订单状态
  const [clearingOrders, setClearingOrders] = useState(false);

  // 重置订单状态
  const [resettingPausedOrders, setResettingPausedOrders] = useState(false);
  const [resettingFailedOrders, setResettingFailedOrders] = useState(false);

  // 导出状态
  const [exporting, setExporting] = useState(false);

  // 格式转换工具状态
  const [showFormatConverterModal, setShowFormatConverterModal] = useState(false);

  // 生成预订验证提示信息
  const generateBookingValidationMessage = () => {
    const validationItems = [];
    
    // 1. 短信通知验证
    if (smsNotify) {
      validationItems.push({
        icon: '✅',
        text: '短信通知：已启用',
        color: 'text-green-600'
      });
    } else {
      validationItems.push({
        icon: '⚠️',
        text: '短信通知：未启用',
        color: 'text-orange-600'
      });
    }
    
    // 2. 代订人信息验证
    if (hasAgent) {
      if (agentPhone.trim() && agentName.trim()) {
        validationItems.push({
          icon: '✅',
          text: `代订人：${agentName} (${agentPhone})`,
          color: 'text-green-600'
        });
      } else {
        validationItems.push({
          icon: '❌',
          text: '代订人：姓名或手机号码未填写',
          color: 'text-red-600'
        });
      }
    } else {
      validationItems.push({
        icon: '⚠️',
        text: '代订人：未启用',
        color: 'text-orange-600'
      });
    }
    
    // 3. 行程提交项提示（仅做提示，不验证数据）
    validationItems.push({
      icon: 'ℹ️',
      text: '行程提交项：请确保已填写完整',
      color: 'text-blue-600'
    });
    
    return validationItems;
  };

  // 暂时让TypeScript知道这个变量会被使用（将来可能显示设置来源信息）
  void settingsSource;

  // 从localStorage读取全局设置的fallback函数
  const getLocalStorageSettings = () => {
    try {
      const data = localStorage.getItem('flight_booking_settings');
      if (data) {
        return JSON.parse(data);
      }
    } catch {}
    return {};
  };

  // 获取项目的历史任务设置，优先级：项目历史 > localStorage > 默认值
  const getProjectFlightBookingSettings = async (projectId: string) => {
    try {
      // 首先尝试从项目历史任务获取设置（不限制任务类型，获取该项目最新的任务设置）
      const projectSettings = await ProjectTaskService.getProjectLatestSettings(parseInt(projectId));

      if (projectSettings.has_history) {
        console.log('🔍 已获取项目历史任务设置:', projectSettings.message);
        return {
          smsNotify: projectSettings.sms_notify,
          hasAgent: projectSettings.has_agent,
          agentPhone: projectSettings.agent_phone || '',
          agentName: projectSettings.agent_name || '',
          source: 'project_history',
          lastTaskTitle: projectSettings.last_task_title
        };
      }
    } catch (error) {
      console.warn('获取项目历史设置失败，将使用localStorage设置:', error);
    }

    // 如果项目没有历史任务，返回空值（不使用localStorage）
    return {
      smsNotify: false,
      hasAgent: false,
      agentPhone: '',
      agentName: '',
      source: 'default'
    };
  };

  // 加载项目设置的函数
  const loadProjectSettings = async () => {
    if (!projectId) return;

    try {
      const settings = await getProjectFlightBookingSettings(projectId);
      setSmsNotify(settings.smsNotify);
      setHasAgent(settings.hasAgent);
      setAgentPhone(settings.agentPhone);
      setAgentName(settings.agentName || '');
      setSettingsSource(settings.source as 'project_history' | 'localStorage');

      if (settings.source === 'project_history' && settings.lastTaskTitle) {
        console.log(`✅ 已加载项目历史任务设置（来自任务：${settings.lastTaskTitle}）`);
      } else if (settings.source === 'localStorage') {
        console.log('📱 已加载localStorage设置');
      } else if (settings.source === 'default') {
        console.log('🔄 项目无历史任务，使用默认空值');
      }
    } catch (error) {
      console.warn('加载项目历史任务设置失败，使用默认值:', error);
      // 如果出错，使用默认值
      setSmsNotify(false);
      setHasAgent(false);
      setAgentPhone('');
      setAgentName('');
      setSettingsSource('default');
    }
  };

  // 在项目ID可用时加载项目设置
  useEffect(() => {
    if (projectId) {
      loadProjectSettings();
    }
  }, [projectId]);

  // 初始化数据加载
  useEffect(() => {
    if (projectId) {
      loadProjectData();
      loadFlightOrders(1);
    }
  }, [projectId]);

  // 加载项目数据
  const loadProjectData = async () => {
    if (!projectId) return;

    try {
      console.log('项目ID:', projectId);
    } catch (error) {
      console.error('获取项目信息失败:', error);
      toast({
        title: "错误",
        description: "获取项目信息失败",
        variant: "destructive"
      });
    }
  };

  // 加载飞机票订单数据
  const loadFlightOrders = async (page: number) => {
    if (!projectId) return;

    setPageLoading(true);
    try {
      const statusFilter = selectedStatuses.length > 0 ? selectedStatuses.join(',') : undefined;

      const params: any = {
        page,
        page_size: pageSize,
        sort_by_failed_first: true
      };

      // 只添加非空的参数
      if (statusFilter) {
        params.order_status = statusFilter;
      }
      if (searchTravelerName && searchTravelerName.trim()) {
        params.traveler_name = searchTravelerName.trim();
      }
      if (searchMobilePhone && searchMobilePhone.trim()) {
        params.mobile_phone = searchMobilePhone.trim();
      }
      if (searchContactPhone && searchContactPhone.trim()) {
        params.contact_phone = searchContactPhone.trim();
      }

      const response = await flightOrderApi.getOrdersByProject(
        parseInt(projectId),
        params
      );

      setOrders(response.items);
      setTotalCount(response.total);
      setCurrentPage(page);

      console.log('飞机票订单加载成功:', {
        total: response.total,
        page: response.page,
        items: response.items.length,
        firstItem: response.items[0]
      });

      // 同时加载统计数据
      const statsData = await flightOrderApi.getProjectStats(parseInt(projectId));
      setStats(statsData);

      console.log('飞机票统计数据:', statsData);
    } catch (error) {
      console.error('加载飞机票订单失败:', error);
      toast({
        title: "加载失败",
        description: "加载飞机票订单失败",
        variant: "destructive",
      });
    } finally {
      setPageLoading(false);
    }
  };

  // 文件选择处理
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setUploadedFile(file);
      setValidationResult(null);
    }
  };

  // 下载模板
  const handleDownloadTemplate = () => {
    const link = document.createElement('a');
    link.href = '/templates/flight_template.xlsx';
    link.download = 'flight_template.xlsx';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // 移除上传的文件
  const removeUploadedFile = () => {
    setUploadedFile(null);
    setValidationResult(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // 拖拽处理
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      const file = files[0];
      if (file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
        setUploadedFile(file);
        setValidationResult(null);
      } else {
        toast({
          title: "文件格式错误",
          description: "请选择Excel文件（.xlsx或.xls格式）",
          variant: "destructive",
        });
      }
    }
  };

  // 文本解析处理
  const handleTextParse = async () => {
    if (!textInput.trim()) return;

    setParsing(true);
    try {
      // TODO: 实现文本解析功能
      toast({
        title: "功能开发中",
        description: "文本解析功能正在开发中",
        variant: "default",
      });
    } catch (error) {
      console.error('文本解析失败:', error);
      toast({
        title: "解析失败",
        description: "文本解析失败，请检查输入内容",
        variant: "destructive",
      });
    } finally {
      setParsing(false);
    }
  };

  // 验证取消处理
  const handleValidationCancel = () => {
    setShowValidationDialog(false);
    setValidationDialogData(null);
  };

  // 搜索处理函数
  const handleSearch = () => {
    loadFlightOrders(1);
  };

  // 清空搜索和重置筛选
  const handleClearSearch = () => {
    setSearchTravelerName('');
    setSearchMobilePhone('');
    setSearchContactPhone('');
    setSelectedStatuses(['check_failed', 'initial', 'paused', 'failed']); // 重置到默认状态筛选
    loadFlightOrders(1);
  };

  // 验证并上传Excel文件
  const handleValidateAndUpload = async () => {
    if (!uploadedFile) return;

    try {
      setValidating(true);
      const validationResponse = await flightOrderApi.validateExcel(Number(projectId), uploadedFile);

      // 显示验证结果
      setValidationDialogData(validationResponse);
      setShowValidationDialog(true);

    } catch (error) {
      console.error('Excel处理失败:', error);
      toast({
        title: "处理失败",
        description: "Excel文件处理失败，请检查文件格式",
        variant: "destructive",
      });
    } finally {
      setValidating(false);
    }
  };

  // 确认导入
  const handleValidationConfirm = async () => {
    if (!validationDialogData || !uploadedFile) return;

    try {
      setUploading(true);
      const response = await flightOrderApi.uploadExcel(Number(projectId), uploadedFile);

      // 显示导入结果，包括验证失败的记录
      toast({
        title: "导入完成",
        description: response.message,
      });

      // 重新加载订单列表
      loadFlightOrders(1);

      // 清理上传状态
      setUploadedFile(null);
      setShowValidationDialog(false);
      setValidationDialogData(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (error: any) {
      console.error('上传失败:', error);
      // 尝试从错误响应中获取详细信息
      let errorMessage = "Excel文件上传失败";
      if (error.response?.data?.detail) {
        errorMessage = error.response.data.detail;
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast({
        title: "上传失败",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setUploading(false);
    }
  };

  // 状态筛选处理
  const handleStatusChange = (status: string, checked: boolean) => {
    if (checked) {
      setSelectedStatuses(prev => [...prev, status]);
    } else {
      setSelectedStatuses(prev => prev.filter(s => s !== status));
    }
  };

  // 显示通用确认对话框
  const showGeneralConfirm = (options: {
    title: string;
    message: string;
    confirmText?: string;
    cancelText?: string;
    onConfirm: () => void;
    type?: 'info' | 'warning' | 'danger';
    showCancel?: boolean;
  }) => {
    setGeneralConfirmDialogData({
      title: options.title,
      message: options.message,
      confirmText: options.confirmText || '确定',
      cancelText: options.cancelText || '取消',
      onConfirm: options.onConfirm,
      type: options.type || 'info',
      showCancel: options.showCancel !== false // 默认显示取消按钮，除非明确设置为false
    });
    setShowGeneralConfirmDialog(true);
  };

  // 处理通用确认对话框的确认操作
  const handleGeneralConfirm = () => {
    if (generalConfirmDialogData?.onConfirm) {
      generalConfirmDialogData.onConfirm();
    }
    setShowGeneralConfirmDialog(false);
    setGeneralConfirmDialogData(null);
  };

  // 处理通用确认对话框的取消操作
  const handleGeneralCancel = () => {
    setShowGeneralConfirmDialog(false);
    setGeneralConfirmDialogData(null);
  };

  // 查看订单详情
  const handleViewOrder = (order: FlightOrder) => {
    setSelectedOrder(order);
    setShouldStartInEditMode(false);
    setShowDetailModal(true);
  };

  // 编辑订单
  const handleEditOrder = (order: FlightOrder) => {
    setSelectedOrder(order);
    setShouldStartInEditMode(true);
    setShowDetailModal(true);
  };

  // 删除订单
  const handleDeleteOrder = async (orderId: number, orderName: string) => {
    showGeneralConfirm({
      title: '确认删除订单',
      message: `确定要删除 ${orderName} 的飞机票订单吗？删除后将无法恢复。`,
      confirmText: '确认删除',
      cancelText: '取消',
      type: 'danger',
      onConfirm: async () => {
        setDeletingOrderId(orderId);
        try {
          await flightOrderApi.deleteOrder(orderId);

          toast({
            title: "删除成功",
            description: `已成功删除 ${orderName} 的飞机票订单`,
          });

          await loadFlightOrders(currentPage);
        } catch (error) {
          console.error('删除订单失败:', error);
          toast({
            title: "删除失败",
            description: "删除订单时发生错误，请重试",
            variant: "destructive"
          });
        } finally {
          setDeletingOrderId(null);
        }
      }
    });
  };

  // 关闭详情模态框
  const handleCloseDetailModal = () => {
    setShowDetailModal(false);
    setSelectedOrder(null);
    setShouldStartInEditMode(false);
    setEditingOrder(null);
    setIsEditMode(false);
  };

  // 订单更新后的回调
  const handleOrderUpdated = () => {
    loadFlightOrders(currentPage);
  };

  // 清空订单功能
  const handleClearOrders = async () => {
    if (!projectId) return;

    // 使用自定义确认对话框替换系统默认confirm
    showGeneralConfirm({
      title: '确认清空订单',
      message: `确定要清空所有飞机票订单吗？删除后将从数据库中永久移除，无法恢复。\n\n将清空以下状态的订单：\n• 验证失败\n• 待提交\n• 已暂停\n• 预定失败`,
      confirmText: '确认清空',
      cancelText: '取消',
      type: 'danger',
      onConfirm: async () => {
        try {
          setClearingOrders(true);

          const response = await flightOrderApi.clearSpecificOrders(parseInt(projectId));

          toast({
            title: "清空成功",
            description: response.message,
            variant: "default",
          });

          // 重新加载订单列表
          loadFlightOrders(1);

        } catch (error: any) {
          console.error('清空订单失败:', error);
          toast({
            title: "清空失败",
            description: error.response?.data?.detail || "清空订单时发生错误，请重试",
            variant: "destructive",
          });
        } finally {
          setClearingOrders(false);
        }
      }
    });
  };

  // 重置已暂停订单功能
  const handleResetPausedOrders = async () => {
    if (!projectId) return;

    // 获取当前展示列表中的已暂停订单ID
    const pausedOrderIds = orders
      .filter(order => order.order_status === 'paused')
      .map(order => order.id);

    if (pausedOrderIds.length === 0) {
      toast({
        title: "无需重置",
        description: "当前列表中没有已暂停的订单",
        variant: "default",
      });
      return;
    }

    // 使用自定义确认对话框
    showGeneralConfirm({
      title: '确认重置已暂停订单',
      message: `确定要重置当前列表中的 ${pausedOrderIds.length} 条已暂停飞机票订单吗？\n\n重置后这些订单将变为待提交状态，可以重新提交预订。`,
      confirmText: '确认重置',
      cancelText: '取消',
      type: 'warning',
      onConfirm: async () => {
        try {
          setResettingPausedOrders(true);

          const response = await flightOrderApi.resetPausedOrders(parseInt(projectId), {
            order_ids: pausedOrderIds
          });

          toast({
            title: "重置成功",
            description: response.message,
            variant: "default",
          });

          // 重新加载订单列表
          loadFlightOrders(currentPage);

        } catch (error: any) {
          console.error('重置已暂停订单失败:', error);
          toast({
            title: "重置失败",
            description: error.response?.data?.detail || "重置已暂停订单时发生错误，请重试",
            variant: "destructive",
          });
        } finally {
          setResettingPausedOrders(false);
        }
      }
    });
  };

  // 重置预定失败订单功能
  const handleResetFailedOrders = async () => {
    if (!projectId) return;

    // 获取当前展示列表中的预定失败订单ID
    const failedOrderIds = orders
      .filter(order => order.order_status === 'failed')
      .map(order => order.id);

    if (failedOrderIds.length === 0) {
      toast({
        title: "无需重置",
        description: "当前列表中没有预定失败的订单",
        variant: "default",
      });
      return;
    }

    // 使用自定义确认对话框
    showGeneralConfirm({
      title: '确认重置预定失败订单',
      message: `确定要重置当前列表中的 ${failedOrderIds.length} 条预定失败飞机票订单吗？\n\n重置后这些订单将变为待提交状态，可以重新提交预订。`,
      confirmText: '确认重置',
      cancelText: '取消',
      type: 'warning',
      onConfirm: async () => {
        try {
          setResettingFailedOrders(true);

          const response = await flightOrderApi.resetFailedOrders(parseInt(projectId), {
            order_ids: failedOrderIds
          });

          toast({
            title: "重置成功",
            description: response.message,
            variant: "default",
          });

          // 重新加载订单列表
          loadFlightOrders(currentPage);

        } catch (error: any) {
          console.error('重置预定失败订单失败:', error);
          toast({
            title: "重置失败",
            description: error.response?.data?.detail || "重置预定失败订单时发生错误，请重试",
            variant: "destructive",
          });
        } finally {
          setResettingFailedOrders(false);
        }
      }
    });
  };

  // 预定功能
  const handleBookOrders = async () => {
    if (!projectId) return;

    // 获取当前页面的待提交订单
    const initialOrders = orders.filter(order => order.order_status === 'initial');

    // 检查是否有可提交的订单
    if (initialOrders.length === 0) {
      toast({
        title: "无法提交预订",
        description: "当前页面没有可提交的订单（状态为待提交的订单）。",
        variant: "default",
      });
      return;
    }

    // 生成验证提示信息
    const validationItems = generateBookingValidationMessage();
    const validationMessage = validationItems.map(item => 
      `${item.icon} ${item.text}`
    ).join('\n');
    
    // 使用通用确认对话框
    showGeneralConfirm({
      title: '确认预订',
      message: `确定要预订当前页面的 ${initialOrders.length} 条待提交飞机票订单吗？\n\n${validationMessage}`,
      confirmText: '确认预订',
      cancelText: '取消',
      type: 'info',
      onConfirm: async () => {
        try {
          const taskData: CreateBookingTaskRequest = {
            booking_type: 'book',
            task_title: `飞机票预订 - ${new Date().toLocaleDateString()}`,
            task_description: `预订 ${initialOrders.length} 条飞机票订单`,
            sms_notify: smsNotify,
            has_agent: hasAgent,
            agent_phone: hasAgent ? agentPhone : undefined,
            agent_name: hasAgent ? agentName : "",
            order_ids: initialOrders.map(order => order.id) // 只传递待提交状态的订单ID列表
          };

          const response = await flightOrderApi.createBookingTask(Number(projectId), taskData);

          toast({
            title: "预订成功",
            description: response.message || `已创建预订任务，任务ID: ${response.task_id}`,
          });

          // 保存设置到localStorage
          try {
            const settings = {
              smsNotify,
              hasAgent,
              agentPhone: hasAgent ? agentPhone : '',
              agentName: hasAgent ? agentName : ''
            };
            localStorage.setItem('flight_booking_settings', JSON.stringify(settings));
          } catch (error) {
            console.warn('保存飞机票预订设置到localStorage失败:', error);
          }

          // 预订成功后跳转到所有订单tab
          if (onNavigateToAllOrders) {
            setTimeout(() => {
              onNavigateToAllOrders();
            }, 1000);
          }

          loadFlightOrders(1);
        } catch (error) {
          console.error('创建预订任务失败:', error);
          toast({
            title: "预订失败",
            description: "创建预订任务失败",
            variant: "destructive",
          });
        }
      }
    });
  };

  // 预定且提交行程功能
  const handleBookAndSubmitOrders = async () => {
    if (!projectId) return;

    // 获取当前页面的待提交订单
    const initialOrders = orders.filter(order => order.order_status === 'initial');

    // 检查是否有可提交的订单
    if (initialOrders.length === 0) {
      toast({
        title: "无法提交预订",
        description: "当前页面没有可提交的订单（状态为待提交的订单）。",
        variant: "default",
      });
      return;
    }

    // 生成验证提示信息
    const validationItems = generateBookingValidationMessage();
    const validationMessage = validationItems.map(item => 
      `${item.icon} ${item.text}`
    ).join('\n');
    
    // 使用通用确认对话框
    showGeneralConfirm({
      title: '确认预订且提交行程',
      message: `确定要预订且提交行程当前页面的 ${initialOrders.length} 条待提交飞机票订单吗？\n\n${validationMessage}`,
      confirmText: '确认预订且提交行程',
      cancelText: '取消',
      type: 'warning',
      onConfirm: async () => {
        try {
          const taskData: CreateBookingTaskRequest = {
            booking_type: 'book_and_issue',
            task_title: `飞机票预订且提交行程 - ${new Date().toLocaleDateString()}`,
            task_description: `预订且提交行程 ${initialOrders.length} 条飞机票订单`,
            sms_notify: smsNotify,
            has_agent: hasAgent,
            agent_phone: hasAgent ? agentPhone : undefined,
            agent_name: hasAgent ? agentName : "",
            order_ids: initialOrders.map(order => order.id) // 只传递待提交状态的订单ID列表
          };

          const response = await flightOrderApi.createBookingTask(Number(projectId), taskData);

          toast({
            title: "预订且提交行程成功",
            description: response.message || `已创建预订任务，任务ID: ${response.task_id}`,
          });

          // 保存设置到localStorage
          try {
            const settings = {
              smsNotify,
              hasAgent,
              agentPhone: hasAgent ? agentPhone : '',
              agentName: hasAgent ? agentName : ''
            };
            localStorage.setItem('flight_booking_settings', JSON.stringify(settings));
          } catch (error) {
            console.warn('保存飞机票预订设置到localStorage失败:', error);
          }

          // 预订成功后跳转到所有订单tab
          if (onNavigateToAllOrders) {
            setTimeout(() => {
              onNavigateToAllOrders();
            }, 1000);
          }

          loadFlightOrders(1);
        } catch (error) {
          console.error('创建预订任务失败:', error);
          toast({
            title: "预订失败",
            description: "创建预订任务失败",
            variant: "destructive",
          });
        }
      }
    });
  };

  // 导出Excel功能
  const handleExportExcel = async () => {
    setExporting(true);
    try {
      // 获取当前筛选条件下的所有订单数据
      let allOrders: FlightOrder[] = [];
      let currentExportPage = 1;
      let hasMore = true;
      const pageSize = 100; // 每次获取100条

      while (hasMore) {
        const statusFilter = selectedStatuses.length > 0 ? selectedStatuses.join(',') : undefined;

        const params: any = {
          page: currentExportPage,
          page_size: pageSize,
          sort_by_failed_first: true
        };

        // 只添加非空的参数
        if (statusFilter) {
          params.order_status = statusFilter;
        }
        if (searchTravelerName && searchTravelerName.trim()) {
          params.traveler_name = searchTravelerName.trim();
        }
        if (searchMobilePhone && searchMobilePhone.trim()) {
          params.mobile_phone = searchMobilePhone.trim();
        }
        if (searchContactPhone && searchContactPhone.trim()) {
          params.contact_phone = searchContactPhone.trim();
        }

        const response = await flightOrderApi.getOrdersByProject(
          parseInt(projectId!),
          params
        );

        allOrders = [...allOrders, ...response.items];
        hasMore = response.items.length === pageSize;
        currentExportPage++;
      }

      if (allOrders.length === 0) {
        toast({
          title: "无数据",
          description: "当前筛选条件下暂无订单数据可导出",
          variant: "destructive",
        });
        return;
      }

      // 准备导出数据
      const exportData = allOrders.map((order, index) => ({
        '序号': index + 1,
        '状态': order.order_status === 'completed' ? '已完成' :
               order.order_status === 'failed' ? '预定失败' :
               order.order_status === 'processing' ? '处理中' :
               order.order_status === 'submitted' ? '已提交' :
               order.order_status === 'check_failed' ? '验证失败' :
               order.order_status === 'paused' ? '已暂停' :
               '待提交',
        '错误信息': order.fail_reason || '',
        '出行人姓名': order.traveler_full_name || '',
        '出行人姓': order.traveler_surname || '',
        '出行人名': order.traveler_given_name || '',
        '国籍': order.nationality || '',
        '性别': order.gender || '',
        '出生日期': order.birth_date || '',
        '证件类型': order.id_type || '',
        '证件号码': order.id_number || '',
        '证件有效期至': formatDateToYearMonthDay(order.id_expiry_date),
        '手机号国际区号': order.mobile_country_code || '',
        '手机号': order.mobile_phone || '',
        '出行日期': formatDateToYearMonthDay(order.travel_date),
        '出发机场名': order.departure_airport || '',
        '到达机场名': order.arrival_airport || '',
        '航班号': order.flight_number || '',
        '出发时间': formatTimeToHourMinute(order.departure_time),
        '到达时间': formatTimeToHourMinute(order.arrival_time),
        '行程提交项': order.trip_submission_item || '',
        '联系人': order.contact_person || '',
        '联系人手机号': order.contact_mobile_phone || '',
        '联系人邮箱': order.contact_email || '',
        '审批参照人': order.approver || '',
        '保险名称': order.insurance_name || '',
        '公司名称': order.company_name || '',
        '代订人': order.booking_agent || '',
        '出票短信': order.ticket_sms || '',
        '金额': order.amount || '',
        '订单号': order.order_number || '',
        '账单号': order.bill_number || ''
      }));

      // 创建工作表
      const ws = XLSX.utils.json_to_sheet(exportData);

      // 设置列宽
      const colWidths = [
        { wch: 8 },   // 序号
        { wch: 12 },  // 状态
        { wch: 20 },  // 错误信息
        { wch: 12 },  // 出行人姓名
        { wch: 8 },   // 出行人姓
        { wch: 8 },   // 出行人名
        { wch: 8 },   // 国籍
        { wch: 6 },   // 性别
        { wch: 12 },  // 出生日期
        { wch: 10 },  // 证件类型
        { wch: 20 },  // 证件号码
        { wch: 12 },  // 证件有效期至
        { wch: 12 },  // 手机号国际区号
        { wch: 15 },  // 手机号
        { wch: 12 },  // 出行日期
        { wch: 20 },  // 出发机场名
        { wch: 20 },  // 到达机场名
        { wch: 10 },  // 航班号
        { wch: 10 },  // 出发时间
        { wch: 10 },  // 到达时间
        { wch: 20 },  // 行程提交项
        { wch: 10 },  // 联系人
        { wch: 15 },  // 联系人手机号
        { wch: 20 },  // 联系人邮箱
        { wch: 12 },  // 审批参照人
        { wch: 12 },  // 保险名称
        { wch: 15 },  // 公司名称
        { wch: 10 },  // 代订人
        { wch: 20 },  // 出票短信
        { wch: 10 },  // 金额
        { wch: 15 },  // 订单号
        { wch: 15 }   // 账单号
      ];
      ws['!cols'] = colWidths;

      // 设置样式
      const range = XLSX.utils.decode_range(ws['!ref'] || 'A1');
      for (let row = range.s.r; row <= range.e.r; row++) {
        for (let col = range.s.c; col <= range.e.c; col++) {
          const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
          if (!ws[cellAddress]) continue;

          // 初始化样式
          if (!ws[cellAddress].s) {
            ws[cellAddress].s = {};
          }

          // 设置边框
          ws[cellAddress].s.border = {
            top: { style: 'thin', color: { rgb: '000000' } },
            bottom: { style: 'thin', color: { rgb: '000000' } },
            left: { style: 'thin', color: { rgb: '000000' } },
            right: { style: 'thin', color: { rgb: '000000' } }
          };

          // 为表头行添加特殊样式
          if (row === 0) {
            ws[cellAddress].s.fill = {
              patternType: 'solid',
              fgColor: { rgb: 'F3F4F6' }
            };
            ws[cellAddress].s.font = {
              name: '微软雅黑',
              sz: 10,
              bold: true
            };
            ws[cellAddress].s.alignment = {
              horizontal: 'center',
              vertical: 'center',
              wrapText: true
            };
          } else {
            ws[cellAddress].s.font = {
              name: '微软雅黑',
              sz: 10
            };
            ws[cellAddress].s.alignment = {
              horizontal: 'left',
              vertical: 'center',
              wrapText: true
            };
          }
        }
      }

      // 创建工作簿
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, '飞机票订单');

      // 生成文件名
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0];

      // 添加筛选条件信息到文件名
      let filterInfo = '';
      const statusNames = selectedStatuses.map(status => {
        switch(status) {
          case 'completed': return '已完成';
          case 'failed': return '预定失败';
          case 'processing': return '处理中';
          case 'submitted': return '已提交';
          case 'check_failed': return '验证失败';
          case 'paused': return '已暂停';
          case 'initial': return '待提交';
          default: return status;
        }
      });
      if (statusNames.length > 0 && statusNames.length < 7) {
        filterInfo += `_状态(${statusNames.join('_')})`;
      }

      // 添加搜索条件信息
      const searchConditions = [];
      if (searchTravelerName) searchConditions.push(`出行人(${searchTravelerName})`);
      if (searchMobilePhone) searchConditions.push(`手机(${searchMobilePhone})`);
      if (searchContactPhone) searchConditions.push(`联系人手机(${searchContactPhone})`);
      if (searchConditions.length > 0) {
        filterInfo += `_搜索(${searchConditions.join('_')})`;
      }

      const fileName = `飞机票订单_项目${projectId}${filterInfo}_${timestamp}.xlsx`;

      // 导出文件
      XLSX.writeFile(wb, fileName);

      toast({
        title: "导出成功",
        description: `成功导出 ${allOrders.length} 条筛选后的订单数据`,
        variant: "default",
      });

    } catch (error) {
      console.error('导出Excel失败:', error);
      toast({
        title: "导出失败",
        description: "导出Excel文件时发生错误，请重试",
        variant: "destructive",
      });
    } finally {
      setExporting(false);
    }
  };

  // 渲染数据录入tab内容
  const renderInputTab = () => (
    <div className="p-6">
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        {/* 下载模板按钮 */}
        <div className="mb-4 flex justify-end">
          <Button
            variant="outline"
            size="sm"
            onClick={handleDownloadTemplate}
            className="text-blue-600 hover:text-blue-700 border-blue-200 hover:border-blue-300"
          >
            <Download className="h-4 w-4 mr-2" />
            下载Excel模板
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Left side: Upload area */}
          <div className="h-full flex flex-col">
            {!uploadedFile ? (
              <div
                className="relative flex flex-col items-center justify-center flex-1 min-h-[150px] border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-500 transition-colors cursor-pointer group bg-gray-50"
                onClick={() => fileInputRef.current?.click()}
                onDragOver={handleDragOver}
                onDragEnter={handleDragEnter}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
              >
                <Upload className="h-10 w-10 text-gray-400 mb-3 transition-colors group-hover:text-blue-500" />
                <p className="text-sm font-medium text-gray-700 transition-colors group-hover:text-blue-600 mb-2">点击或拖拽文件到此区域上传</p>
                <p className="text-xs text-gray-500">支持 .xlsx, .xls</p>
              </div>
            ) : (
              <div className="p-6 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3 overflow-hidden">
                    <FileSpreadsheet className="h-8 w-8 text-blue-600 flex-shrink-0" />
                    <div className="overflow-hidden">
                      <p className="text-sm font-medium text-blue-800 truncate" title={uploadedFile.name}>{uploadedFile.name}</p>
                      <p className="text-xs text-blue-600">{(uploadedFile.size / 1024).toFixed(2)} KB</p>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="text-blue-500 hover:text-red-500 flex-shrink-0"
                    onClick={removeUploadedFile}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
          </div>

          {/* Right side: Info and buttons */}
          <div className="space-y-4 flex flex-col justify-between">
            <div>
              <h4 className="font-semibold text-gray-800 mb-3">上传说明</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li className="flex items-start gap-2">
                  <span className="w-1.5 h-1.5 bg-gray-400 rounded-full mt-2 flex-shrink-0"></span>
                  <span>请使用文件格式正确的Excel文件。</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="w-1.5 h-1.5 bg-gray-400 rounded-full mt-2 flex-shrink-0"></span>
                  <span>系统会自动验证数据格式，请确保数据完整性。</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="w-1.5 h-1.5 bg-gray-400 rounded-full mt-2 flex-shrink-0"></span>
                  <span>支持拖拽文件至上传区域或点击区域选择文件上传。</span>
                </li>
              </ul>
            </div>

            {/* 操作按钮 */}
            <div className="space-y-3">
              <Button
                onClick={handleValidateAndUpload}
                disabled={!uploadedFile || uploading || validating}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white h-10"
              >
                {uploading ? (
                  <div className="flex items-center gap-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b border-white"></div>
                    <span>上传中...</span>
                  </div>
                ) : validating ? (
                  <div className="flex items-center gap-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b border-white"></div>
                    <span>验证中...</span>
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <Upload className="h-4 w-4" />
                    <span>验证并上传</span>
                  </div>
                )}
              </Button>

              {/* 暂时隐藏格式转换工具按钮 */}
              {false && (
                <Button
                  variant="outline"
                  onClick={() => setShowFormatConverterModal(true)}
                  className="w-full text-orange-600 hover:text-orange-700 border-orange-200 hover:border-orange-300 h-10"
                >
                  <RotateCcw className="h-4 w-4 mr-2" />
                  格式转换工具
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* 隐藏的文件输入 */}
      <input
        ref={fileInputRef}
        type="file"
        accept=".xlsx,.xls"
        onChange={handleFileSelect}
        className="hidden"
      />

      {/* 格式转换工具模态框 */}
      <FormatConverterModal
        isOpen={showFormatConverterModal}
        onClose={() => setShowFormatConverterModal(false)}
      />
    </div>
  );

  // 渲染文本内容tab内容
  const renderTextTab = () => (
    <div className="p-6">
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Left side: Text area */}
          <div className="h-full flex flex-col">
            <Label htmlFor="text-input" className="mb-2 font-medium text-gray-700">粘贴文本内容</Label>
            <Textarea
              id="text-input"
              placeholder="粘贴飞机票预订信息，系统将自动识别出行人姓名、联系方式、航班信息等。"
              className="flex-1 bg-white border-gray-300 resize-none min-h-[150px]"
              value={textInput}
              onChange={(e) => setTextInput(e.target.value)}
            />
          </div>

          {/* Right side: Actions and info */}
          <div className="space-y-4 flex flex-col justify-between">
            <div>
              <h4 className="font-semibold text-gray-800">文本解析说明</h4>
              <ul className="list-disc list-inside text-sm text-gray-600 mt-2 space-y-1">
                <li>支持自动识别出行人姓名、联系方式、航班信息等。</li>
                <li>请确保每条记录信息完整，并用换行分隔。</li>
                <li>解析结果将显示在下方的订单列表中。</li>
              </ul>
            </div>

            <Button
              onClick={handleTextParse}
              disabled={!textInput.trim()}
              className="w-full bg-green-600 hover:bg-green-700 text-white"
            >
              <FileText className="h-4 w-4 mr-2" />
              解析文本
            </Button>
          </div>
        </div>
      </div>
    </div>
  );

  // 渲染订单列表
  const renderOrderList = () => (
    <Card>
      <CardHeader>
        <div className="space-y-4">
          {/* 第一行：标题和操作按钮 */}
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2 text-lg font-medium">
              <Plane className="h-5 w-5 text-blue-600" />
              <span>飞机票订单列表</span>
              {totalCount > 0 && (
                <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-0.5 rounded-full ml-1">
                  {totalCount}
                </span>
              )}
            </CardTitle>

            {/* 右侧操作按钮 */}
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleClearOrders}
                disabled={clearingOrders || orders.length === 0}
                className="text-red-600 border-red-200 hover:bg-red-50"
              >
                {clearingOrders ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-2"></div>
                    清空中...
                  </>
                ) : (
                  <>
                    <Trash2 className="h-4 w-4 mr-2" />
                    清空订单
                  </>
                )}
              </Button>

              {/* 重置已暂停订单按钮 */}
              <Button
                onClick={handleResetPausedOrders}
                variant="outline"
                size="sm"
                className="text-orange-600 border-orange-300 hover:bg-orange-50 hover:border-orange-400"
                disabled={resettingPausedOrders}
              >
                {resettingPausedOrders ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-orange-600 mr-2"></div>
                    重置中...
                  </>
                ) : (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2" />
                    重置已暂停订单
                  </>
                )}
              </Button>

              {/* 重置预定失败订单按钮 */}
              <Button
                onClick={handleResetFailedOrders}
                variant="outline"
                size="sm"
                className="text-red-600 border-red-300 hover:bg-red-50 hover:border-red-400"
                disabled={resettingFailedOrders}
              >
                {resettingFailedOrders ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-2"></div>
                    重置中...
                  </>
                ) : (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2" />
                    重置预定失败订单
                  </>
                )}
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={handleBookOrders}
              >
                <FileText className="h-4 w-4 mr-2" />
                预订
              </Button>

              <Button
                size="sm"
                onClick={handleBookAndSubmitOrders}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                <Send className="h-4 w-4 mr-2" />
                预订且提交行程
              </Button>
            </div>
          </div>

          {/* 第二行：短信通知和代订人设置 */}
          <div className="flex items-center gap-6 text-sm">
            {/* 短信通知 */}
            <div className="flex items-center gap-2">
              <Checkbox
                id="sms-notify"
                checked={smsNotify}
                onCheckedChange={(checked) => setSmsNotify(!!checked)}
              />
              <Label htmlFor="sms-notify" className="text-gray-700">短信通知</Label>
            </div>

            {/* 代订人设置 */}
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Checkbox
                  id="has-agent"
                  checked={hasAgent}
                  onCheckedChange={(checked) => setHasAgent(!!checked)}
                />
                <Label htmlFor="has-agent" className="text-gray-700">有代订人</Label>
              </div>

              {hasAgent && (
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    <Label className="text-gray-600 text-xs">代订人姓名：</Label>
                    <input
                      type="text"
                      placeholder="代订人姓名"
                      value={agentName}
                      onChange={(e) => setAgentName(e.target.value)}
                      className="h-7 px-2 border border-gray-300 rounded text-xs w-24 focus:outline-none focus:ring-1 focus:ring-blue-500"
                    />
                  </div>
                  <div className="flex items-center gap-2">
                    <Label className="text-gray-600 text-xs">代订人手机号码：</Label>
                    <input
                      type="text"
                      placeholder="代订人手机号"
                      value={agentPhone}
                      onChange={(e) => setAgentPhone(e.target.value)}
                      className="h-7 px-2 border border-gray-300 rounded text-xs w-32 focus:outline-none focus:ring-1 focus:ring-blue-500"
                    />
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* 第三行：搜索和筛选 */}
          <div className="flex flex-col md:flex-row md:items-center gap-3 pt-2 border-t border-gray-100">
            <div className="flex items-center gap-2 flex-1 max-w-xs">
              <label className="text-sm text-gray-600 whitespace-nowrap min-w-fit">出行人姓名：</label>
              <input
                type="text"
                placeholder="请输入出行人姓名"
                value={searchTravelerName}
                onChange={(e) => setSearchTravelerName(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                className="flex-1 h-9 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
              />
            </div>

            <div className="flex items-center gap-2 flex-1 max-w-xs">
              <label className="text-sm text-gray-600 whitespace-nowrap min-w-fit">手机号码：</label>
              <input
                type="text"
                placeholder="请输入手机号码"
                value={searchMobilePhone}
                onChange={(e) => setSearchMobilePhone(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                className="flex-1 h-9 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
              />
            </div>

            <div className="flex items-center gap-2 ml-auto">
              <Button
                onClick={handleSearch}
                size="sm"
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                <Search className="h-4 w-4 mr-1" />
                搜索
              </Button>
              <Button
                onClick={handleClearSearch}
                variant="outline"
                size="sm"
                className="text-gray-600"
              >
                <RotateCcw className="h-4 w-4 mr-1" />
                重置
              </Button>
              <Button
                onClick={handleExportExcel}
                variant="outline"
                size="sm"
                className="text-gray-600"
                disabled={exporting}
              >
                {exporting ? (
                  <>
                    <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-gray-600 mr-1"></div>
                    导出中...
                  </>
                ) : (
                  <>
                    <FileSpreadsheet className="h-4 w-4 mr-1" />
                    导出
                  </>
                )}
              </Button>
            </div>
          </div>

          {/* 第四行：状态筛选 */}
          <div className="flex flex-wrap items-center gap-4 pt-2 border-t border-gray-100">
            <span className="text-sm text-gray-600 min-w-fit">状态筛选：</span>

            {[
              { value: 'check_failed', label: '验证失败', color: 'text-red-600' },
              { value: 'initial', label: '待提交', color: 'text-gray-600' },
              { value: 'paused', label: '已暂停', color: 'text-orange-600' },
              { value: 'failed', label: '预定失败', color: 'text-red-600' }
            ].map((status) => (
              <div key={status.value} className="flex items-center gap-1.5">
                <Checkbox
                  id={`status-${status.value}`}
                  checked={selectedStatuses.includes(status.value)}
                  onCheckedChange={(checked) => handleStatusChange(status.value, !!checked)}
                />
                <Label
                  htmlFor={`status-${status.value}`}
                  className={`text-xs font-medium cursor-pointer ${status.color}`}
                >
                  {status.label}
                </Label>
              </div>
            ))}
          </div>
        </div>
      </CardHeader>

      <CardContent className="p-0">
        {pageLoading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">加载中...</p>
          </div>
        ) : orders.length > 0 ? (
          <>
            <div className="overflow-x-auto relative">
              <table className="w-full text-xs" style={{ minWidth: '2000px' }}>
                <thead className="bg-gray-50 border-b border-gray-200">
                  <tr>
                    <th className="text-center p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '60px' }}>序号</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>状态</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '150px' }}>错误信息</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>出行人姓名</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>出行人姓</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>出行人名</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '60px' }}>国籍</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '50px' }}>性别</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>出生日期</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>证件类型</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>证件号码</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>证件有效期至</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>手机号国际区号</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>手机号</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>出行日期</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>出发机场名</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>到达机场名</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>航班号</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>出发时间</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>到达时间</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>行程提交项</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>联系人</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>联系人手机号</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>联系人邮箱</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>审批参照人</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>保险名称</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>公司名称</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>代订人</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>出票短信</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>金额</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>订单号</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>账单号</th>
                    <th className="text-center p-2 font-medium text-gray-900 text-xs whitespace-nowrap sticky right-0 bg-white border-l border-gray-200 z-10 shadow-[-4px_0_8px_rgba(0,0,0,0.1)]" style={{ minWidth: '120px' }}>操作</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {orders.map((order, index) => (
                    <tr key={order.id} className="hover:bg-gray-50">
                      <td className="text-center p-2 text-gray-900 text-xs">
                        {(currentPage - 1) * pageSize + index + 1}
                      </td>
                      <td className="p-2 text-xs">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          order.order_status === 'completed' ? 'bg-green-100 text-green-800' :
                          order.order_status === 'failed' ? 'bg-red-100 text-red-800' :
                          order.order_status === 'processing' ? 'bg-yellow-100 text-yellow-800' :
                          order.order_status === 'submitted' ? 'bg-blue-100 text-blue-800' :
                          order.order_status === 'check_failed' ? 'bg-red-100 text-red-800' :
                          order.order_status === 'paused' ? 'bg-orange-100 text-orange-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {order.order_status === 'completed' ? '已完成' :
                           order.order_status === 'failed' ? '预定失败' :
                           order.order_status === 'processing' ? '处理中' :
                           order.order_status === 'submitted' ? '已提交' :
                           order.order_status === 'check_failed' ? '验证失败' :
                           order.order_status === 'paused' ? '已暂停' :
                           '待提交'}
                        </span>
                      </td>
                      <td className="p-2 text-gray-900 text-xs">
                        {order.fail_reason || '-'}
                      </td>
                      <td className="p-2 text-gray-900 text-xs">
                        {order.traveler_full_name || '-'}
                      </td>
                      <td className="p-2 text-gray-900 text-xs">
                        {order.traveler_surname || '-'}
                      </td>
                      <td className="p-2 text-gray-900 text-xs">
                        {order.traveler_given_name || '-'}
                      </td>
                      <td className="p-2 text-gray-900 text-xs">
                        {order.nationality || '-'}
                      </td>
                      <td className="p-2 text-gray-900 text-xs">
                        {order.gender || '-'}
                      </td>
                      <td className="p-2 text-gray-900 text-xs">
                        {order.birth_date || '-'}
                      </td>
                      <td className="p-2 text-gray-900 text-xs">
                        {order.id_type || '-'}
                      </td>
                      <td className="p-2 text-gray-900 text-xs">
                        {order.id_number || '-'}
                      </td>
                      <td className="p-2 text-gray-900 text-xs">
                        {formatDateToYearMonthDay(order.id_expiry_date)}
                      </td>
                      <td className="p-2 text-gray-900 text-xs">
                        {order.mobile_country_code || '-'}
                      </td>
                      <td className="p-2 text-gray-900 text-xs">
                        {order.mobile_phone || '-'}
                      </td>
                      <td className="p-2 text-gray-900 text-xs">
                        {formatDateToYearMonthDay(order.travel_date)}
                      </td>
                      <td className="p-2 text-gray-900 text-xs">
                        {order.departure_airport || '-'}
                      </td>
                      <td className="p-2 text-gray-900 text-xs">
                        {order.arrival_airport || '-'}
                      </td>
                      <td className="p-2 text-gray-900 text-xs">
                        {order.flight_number || '-'}
                      </td>
                      <td className="p-2 text-gray-900 text-xs">
                        {formatTimeToHourMinute(order.departure_time)}
                      </td>
                      <td className="p-2 text-gray-900 text-xs">
                        {formatTimeToHourMinute(order.arrival_time)}
                      </td>
                      <td className="p-2 text-gray-900 text-xs">
                        {order.trip_submission_item || '-'}
                      </td>
                      <td className="p-2 text-gray-900 text-xs">
                        {order.contact_person || '-'}
                      </td>
                      <td className="p-2 text-gray-900 text-xs">
                        {order.contact_mobile_phone || '-'}
                      </td>
                      <td className="p-2 text-gray-900 text-xs">
                        {order.contact_email || '-'}
                      </td>
                      <td className="p-2 text-gray-900 text-xs">
                        {order.approver || '-'}
                      </td>
                      <td className="p-2 text-gray-900 text-xs">
                        {order.insurance_name || '-'}
                      </td>
                      <td className="p-2 text-gray-900 text-xs">
                        {order.company_name || '-'}
                      </td>
                      <td className="p-2 text-gray-900 text-xs">
                        {order.booking_agent || '-'}
                      </td>
                      <td className="p-2 text-gray-900 text-xs">
                        {order.ticket_sms || '-'}
                      </td>
                      <td className="p-2 text-gray-900 text-xs">
                        {order.amount || '-'}
                      </td>
                      <td className="p-2 text-gray-900 text-xs">
                        {order.order_number || '-'}
                      </td>
                      <td className="p-2 text-gray-900 text-xs">
                        {order.bill_number || '-'}
                      </td>
                      <td className="text-center p-2 sticky right-0 bg-white border-l border-gray-200 z-10 shadow-[-4px_0_8px_rgba(0,0,0,0.1)]">
                        <div className="flex items-center justify-center gap-1">
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-6 w-6 text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                            onClick={() => handleViewOrder(order)}
                            title="查看详情"
                          >
                            <Eye className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-6 w-6 text-green-600 hover:text-green-700 hover:bg-green-50"
                            onClick={() => handleEditOrder(order)}
                            title="编辑订单"
                          >
                            <Edit className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-7 w-7 text-red-600 hover:text-red-700 hover:bg-red-50"
                            onClick={() => handleDeleteOrder(order.id, order.traveler_full_name || '未知用户')}
                            disabled={deletingOrderId === order.id}
                            title="删除订单"
                          >
                            {deletingOrderId === order.id ? (
                              <div className="animate-spin rounded-full h-3 w-3 border-b border-red-600"></div>
                            ) : (
                              <Trash2 className="h-3 w-3" />
                            )}
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* 分页 */}
            {totalCount > pageSize && (
              <div className="flex items-center justify-between p-4 border-t border-gray-200">
                <span className="text-sm text-gray-700">
                  显示 {(currentPage - 1) * pageSize + 1} 到 {Math.min(currentPage * pageSize, totalCount)} 条，共 {totalCount} 条记录
                </span>

                <div className="flex items-center space-x-1">
                  {(() => {
                    const totalPages = Math.ceil(totalCount / pageSize);
                    const pages = [];
                    const maxVisiblePages = 5;

                    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
                    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

                    if (endPage - startPage + 1 < maxVisiblePages) {
                      startPage = Math.max(1, endPage - maxVisiblePages + 1);
                    }

                    for (let i = startPage; i <= endPage; i++) {
                      pages.push(
                        <button
                          key={i}
                          onClick={() => loadFlightOrders(i)}
                          disabled={pageLoading}
                          className={`px-3 py-1 text-sm border rounded ${
                            i === currentPage
                              ? 'bg-green-600 text-white border-green-600'
                              : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                          } ${pageLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                        >
                          {i}
                        </button>
                      );
                    }

                    return pages;
                  })()}
                </div>
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-8">
            <Plane className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">暂无飞机票订单数据</p>
          </div>
        )}
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 min-h-screen flex flex-col">
      {/* Tab导航 */}
      <div className="bg-white border-b border-gray-200">
        <div className="grid grid-cols-2">
          <button
            onClick={() => setActiveTab('input')}
            className={`py-4 px-6 text-center font-medium text-sm transition-colors duration-200 border-b-2 ${
              activeTab === 'input'
                ? 'border-green-500 text-green-600 bg-green-50'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'
            }`}
          >
            <div className="flex items-center justify-center gap-2">
              <Upload className="h-4 w-4" />
              Excel文件上传
            </div>
          </button>
          <button
            onClick={() => setActiveTab('text')}
            className={`py-4 px-6 text-center font-medium text-sm transition-colors duration-200 border-b-2 ${
              activeTab === 'text'
                ? 'border-green-500 text-green-600 bg-green-50'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'
            }`}
          >
            <div className="flex items-center justify-center gap-2">
              <FileText className="h-4 w-4" />
              文本内容
            </div>
          </button>
        </div>
      </div>

      {/* Tab内容区域 */}
      <div className="h-auto">
        {activeTab === 'input' && renderInputTab()}
        {activeTab === 'text' && renderTextTab()}
      </div>

      {/* 独立的订单列表区域 */}
      <div className="flex-1 p-6">
        {renderOrderList()}


      </div>

      {/* 验证错误弹框 */}
      {showValidationDialog && validationDialogData && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* 背景遮罩 */}
          <div
            className="absolute inset-0 bg-black/50 backdrop-blur-sm"
            onClick={handleValidationCancel}
          ></div>

          {/* 对话框内容 */}
          <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4 z-50">
            {/* 头部 */}
            <div className="flex justify-between items-center px-6 py-4">
              <h2 className="text-lg font-medium text-gray-900 flex items-center gap-2">
                <FileSpreadsheet className="h-5 w-5" />
                Excel数据验证结果
              </h2>
              <button
                onClick={handleValidationCancel}
                className="text-gray-400 hover:text-gray-500"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            {/* 统计信息 */}
            <div className="grid grid-cols-4 gap-4 px-6 py-4">
              <div className="bg-blue-50 rounded-lg p-3 flex items-center">
                <FileSpreadsheet className="h-5 w-5 text-blue-600 mr-2" />
                <div>
                  <div className="text-sm text-gray-600">总行数</div>
                  <div className="text-xl font-bold text-gray-900">{validationDialogData.total_rows}</div>
                </div>
              </div>

              <div className="bg-green-50 rounded-lg p-3 flex items-center">
                <Check className="h-5 w-5 text-green-600 mr-2" />
                <div>
                  <div className="text-sm text-gray-600">有效行数</div>
                  <div className="text-xl font-bold text-green-600">{validationDialogData.valid_rows}</div>
                </div>
              </div>

              <div className="bg-red-50 rounded-lg p-3 flex items-center">
                <X className="h-5 w-5 text-red-600 mr-2" />
                <div>
                  <div className="text-sm text-gray-600">错误行数</div>
                  <div className="text-xl font-bold text-red-600">{validationDialogData.error_rows}</div>
                </div>
              </div>

              <div className="bg-amber-50 rounded-lg p-3 flex items-center">
                <AlertTriangle className="h-5 w-5 text-amber-600 mr-2" />
                <div>
                  <div className="text-sm text-gray-600">重复行数</div>
                  <div className="text-xl font-bold text-amber-600">{validationDialogData.duplicate_rows?.length || 0}</div>
                </div>
              </div>
            </div>

            {/* 错误信息 */}
            {validationDialogData.errors?.length > 0 && (
              <div className="px-6 py-4 border-t border-gray-200">
                <h3 className="text-sm font-medium text-gray-900 mb-3">验证错误</h3>
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {validationDialogData.errors.map((error: any, index: number) => (
                    <div key={index} className="flex items-start gap-2 text-sm">
                      <X className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
                      <div>
                        <span className="font-medium">第 {error.row} 行</span>
                        <span className="mx-1">-</span>
                        <span className="text-gray-600">{error.message}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 操作按钮 */}
            <div className="flex justify-end gap-3 px-6 py-4 border-t border-gray-200">
              <Button
                variant="outline"
                onClick={handleValidationCancel}
              >
                关闭
              </Button>
              <Button
                onClick={handleValidationConfirm}
                className="bg-green-600 hover:bg-green-700 text-white"
                disabled={uploading}
              >
                {uploading ? (
                  <div className="flex items-center gap-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b border-white"></div>
                    <span>上传中...</span>
                  </div>
                ) : (
                  '确认上传'
                )}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 飞机票订单详情模态框 */}
      <FlightOrderDetailModal
        order={selectedOrder}
        isOpen={showDetailModal}
        onClose={handleCloseDetailModal}
        onOrderUpdated={handleOrderUpdated}
        initialEditMode={shouldStartInEditMode}
      />

      {/* 通用确认对话框 */}
      {showGeneralConfirmDialog && generalConfirmDialogData && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* 背景遮罩 */}
          <div
            className="absolute inset-0 bg-black/50 backdrop-blur-sm"
            onClick={handleGeneralCancel}
          ></div>

          {/* 对话框内容 */}
          <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4 z-50">
            {/* 头部 */}
            <div className="flex justify-between items-center px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900 flex items-center gap-2">
                {generalConfirmDialogData.type === 'danger' && <AlertTriangle className="h-5 w-5 text-red-600" />}
                {generalConfirmDialogData.type === 'warning' && <AlertTriangle className="h-5 w-5 text-orange-600" />}
                {generalConfirmDialogData.type === 'info' && <AlertTriangle className="h-5 w-5 text-blue-600" />}
                {generalConfirmDialogData.title}
              </h2>
              <button
                onClick={handleGeneralCancel}
                className="text-gray-400 hover:text-gray-500"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            {/* 内容 */}
            <div className="px-6 py-4">
              <div className="text-left space-y-2">
                {generalConfirmDialogData.message.split('\n').filter(line => line.trim()).map((line, index) => {
                  // 检查是否是验证提示行（包含图标）
                  const isValidationLine = line.includes('✅') || line.includes('⚠️') || line.includes('❌') || line.includes('ℹ️');
                  
                  if (isValidationLine) {
                    // 解析图标和颜色
                    let icon = null;
                    let color = 'text-gray-600';
                    
                    if (line.includes('✅')) {
                      icon = <Check className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />;
                      color = 'text-green-600';
                    } else if (line.includes('⚠️')) {
                      icon = <AlertTriangle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />;
                      color = 'text-orange-600';
                    } else if (line.includes('❌')) {
                      icon = <X className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />;
                      color = 'text-red-600';
                    } else if (line.includes('ℹ️')) {
                      icon = <AlertTriangle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />;
                      color = 'text-blue-600';
                    }
                    
                    return (
                      <div key={index} className={`flex items-start gap-2 text-sm ${color}`}>
                        {icon}
                        <span>{line.replace(/[✅⚠️❌ℹ️]/g, '').trim()}</span>
                      </div>
                    );
                  } else {
                    // 普通文本行
                    return (
                      <div key={index} className="text-gray-600">
                        {line}
                      </div>
                    );
                  }
                })}
              </div>
            </div>

            {/* 按钮区域 */}
            <div className="flex justify-end gap-3 px-6 py-4 bg-gray-50 border-t border-gray-200">
              {generalConfirmDialogData.showCancel && (
                <Button
                  variant="outline"
                  onClick={handleGeneralCancel}
                >
                  {generalConfirmDialogData.cancelText}
                </Button>
              )}
              <Button
                variant={generalConfirmDialogData.type === 'danger' ? 'destructive' : 'default'}
                onClick={handleGeneralConfirm}
              >
                {generalConfirmDialogData.confirmText}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default FlightBookingContent;