import React, { useState, useCallback } from 'react';
import { X, Eye, Save, Edit } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { FlightOrder } from '@/api/flightOrder';
import { useToast } from '@/hooks/use-toast';
import { flightOrderApi } from '@/api/flightOrder';

// 可编辑单元格组件
interface EditableCellProps {
  value: any;
  field: string;
  isEditing: boolean;
  onFieldChange: (field: string, value: any) => void;
  validationErrors: Record<string, string>;
  type?: 'text' | 'email' | 'tel' | 'date' | 'datetime-local' | 'time' | 'textarea' | 'select';
  options?: { value: string; label: string }[];
}

const EditableCell: React.FC<EditableCellProps> = ({
  value,
  field,
  isEditing,
  onFieldChange,
  validationErrors,
  type = 'text',
  options = []
}) => {
  const error = validationErrors[field];

  // 处理时间和日期格式转换
  const getDisplayValue = () => {
    if (type === 'time' && value) {
      // 如果是完整的日期时间格式，提取时间部分
      if (typeof value === 'string' && value.includes(' ')) {
        const timePart = value.split(' ')[1];
        if (timePart) {
          // 返回 HH:MM 格式
          return timePart.substring(0, 5);
        }
      }
      // 如果已经是时间格式，直接返回
      return value;
    }

    if (type === 'date' && value) {
      // 如果是完整的日期时间格式，提取日期部分
      if (typeof value === 'string' && value.includes(' ')) {
        return value.split(' ')[0]; // 返回 YYYY-MM-DD 格式
      }
      // 如果已经是日期格式，直接返回
      return value;
    }

    return value;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    let newValue = e.target.value;

    // 如果是时间类型，需要转换为完整格式
    if (type === 'time' && newValue) {
      // 保持原有的日期部分，只更新时间部分
      if (value && typeof value === 'string' && value.includes(' ')) {
        const datePart = value.split(' ')[0];
        newValue = `${datePart} ${newValue}:00`;
      } else {
        // 如果没有原始日期，使用默认日期
        newValue = `${newValue}:00`;
      }
    }

    onFieldChange(field, newValue);
  };

  const inputClassName = `w-full px-3 py-2 text-sm border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
    error ? 'border-red-500 bg-red-50' : 'border-gray-300'
  } ${!isEditing ? 'bg-gray-50 cursor-not-allowed' : ''}`;

  if (!isEditing) {
    const displayValue = getDisplayValue();
    return (
      <div className="text-sm text-gray-900">
        {displayValue || '-'}
      </div>
    );
  }

  if (type === 'select') {
    return (
      <div className="w-full">
        <select
          value={String(value || '')}
          onChange={handleChange}
          className={inputClassName}
        >
          <option value="">请选择</option>
          {options.map(option => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        {error && (
          <div className="mt-1 text-red-600 text-xs">
            {error}
          </div>
        )}
      </div>
    );
  }

  if (type === 'textarea') {
    return (
      <div className="w-full">
        <textarea
          value={String(value || '')}
          onChange={handleChange}
          className={inputClassName}
          rows={4}
        />
        {error && (
          <div className="mt-1 text-red-600 text-xs">
            {error}
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="w-full">
      <input
        type={type}
        value={String(getDisplayValue() || '')}
        onChange={handleChange}
        className={inputClassName}
      />
      {error && (
        <div className="mt-1 text-red-600 text-xs">
          {error}
        </div>
      )}
    </div>
  );
};

interface FlightOrderDetailModalProps {
  order: FlightOrder | null;
  isOpen: boolean;
  onClose: () => void;
  onOrderUpdated?: () => void;
  initialEditMode?: boolean;
  billingInfoOnly?: boolean; // 是否只编辑对账单信息
}

// 订单状态显示组件
const getOrderStatusDisplay = (status: string) => {
  const statusConfig = {
    'initial': { label: '待提交', color: 'bg-gray-100 text-gray-800' },
    'submitted': { label: '已提交', color: 'bg-blue-100 text-blue-800' },
    'processing': { label: '处理中', color: 'bg-yellow-100 text-yellow-800' },
    'completed': { label: '已完成', color: 'bg-green-100 text-green-800' },
    'failed': { label: '预定失败', color: 'bg-red-100 text-red-800' },
    'check_failed': { label: '验证失败', color: 'bg-red-100 text-red-800' },
    'paused': { label: '已暂停', color: 'bg-orange-100 text-orange-800' }
  };

  const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.initial;
  
  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
      {config.label}
    </span>
  );
};

const FlightOrderDetailModal: React.FC<FlightOrderDetailModalProps> = ({
  order,
  isOpen,
  onClose,
  onOrderUpdated,
  initialEditMode = false,
  billingInfoOnly = false
}) => {
  const { toast } = useToast();
  const [isEditMode, setIsEditMode] = useState(false);
  const [editingOrder, setEditingOrder] = useState<FlightOrder | null>(null);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [saving, setSaving] = useState(false);

  // 当order变化时重置状态
  React.useEffect(() => {
    if (order) {
      setEditingOrder({ ...order });
      setIsEditMode(initialEditMode);
      setValidationErrors({});
    }
  }, [order, initialEditMode]);

  const handleFieldChange = useCallback((field: string, value: any) => {
    if (!editingOrder) return;
    
    setEditingOrder(prev => ({
      ...prev!,
      [field]: value
    }));
    
    // 清除该字段的验证错误
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  }, [editingOrder, validationErrors]);

  const validateForm = (): boolean => {
    if (!editingOrder) return false;
    
    const errors: Record<string, string> = {};
    
    // 基础必填字段验证
    if (!editingOrder.traveler_full_name?.trim()) {
      errors.traveler_full_name = '出行人姓名不能为空';
    }
    if (!editingOrder.id_type?.trim()) {
      errors.id_type = '证件类型不能为空';
    }
    if (!editingOrder.id_number?.trim()) {
      errors.id_number = '证件号码不能为空';
    }
    if (!editingOrder.mobile_phone?.trim()) {
      errors.mobile_phone = '手机号不能为空';
    }
    if (!editingOrder.travel_date?.trim()) {
      errors.travel_date = '出行日期不能为空';
    }
    if (!editingOrder.departure_airport?.trim()) {
      errors.departure_airport = '出发机场不能为空';
    }
    if (!editingOrder.arrival_airport?.trim()) {
      errors.arrival_airport = '到达机场不能为空';
    }
    if (!editingOrder.flight_number?.trim()) {
      errors.flight_number = '航班号不能为空';
    }
    if (!editingOrder.departure_time?.trim()) {
      errors.departure_time = '出发时间不能为空';
    }
    if (!editingOrder.arrival_time?.trim()) {
      errors.arrival_time = '到达时间不能为空';
    }
    if (!editingOrder.contact_person?.trim()) {
      errors.contact_person = '联系人不能为空';
    }
    if (!editingOrder.contact_mobile_phone?.trim()) {
      errors.contact_mobile_phone = '联系人手机号不能为空';
    }
    if (!editingOrder.approver?.trim()) {
      errors.approver = '审批参照人不能为空';
    }

    // 非身份证证件类型的额外验证
    if (editingOrder.id_type && editingOrder.id_type !== '身份证') {
      if (!editingOrder.traveler_surname?.trim()) {
        errors.traveler_surname = '出行人姓不能为空';
      }
      if (!editingOrder.traveler_given_name?.trim()) {
        errors.traveler_given_name = '出行人名不能为空';
      }
      if (!editingOrder.nationality?.trim()) {
        errors.nationality = '国籍不能为空';
      }
      if (!editingOrder.gender?.trim()) {
        errors.gender = '性别不能为空';
      }
      if (!editingOrder.birth_date?.trim()) {
        errors.birth_date = '出生日期不能为空';
      }
      if (!editingOrder.id_expiry_date?.trim()) {
        errors.id_expiry_date = '证件有效期不能为空';
      }
    }
    
    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const saveChanges = async () => {
    if (!editingOrder || !validateForm()) {
      return;
    }

    setSaving(true);
    try {
      console.log('开始保存订单，billingInfoOnly:', billingInfoOnly, '订单ID:', editingOrder.id);

      if (billingInfoOnly) {
        // 只更新对账单信息，不受订单状态限制
        const reconciliationData = {
          company_name: editingOrder.company_name,
          booking_agent: editingOrder.booking_agent,
          order_number: editingOrder.order_number,
          bill_number: editingOrder.bill_number,
          amount: editingOrder.amount?.toString(),
          ticket_sms: editingOrder.ticket_sms
        };

        console.log('更新对账单数据:', reconciliationData);
        await flightOrderApi.updateReconciliation(editingOrder.id, reconciliationData);

        toast({
          title: "保存成功",
          description: "对账单信息已更新",
        });
      } else {
        // 更新完整订单信息，保存时将订单状态设置为待提交
        const updatedOrderData = {
          ...editingOrder,
          order_status: 'initial'
        };

        console.log('更新完整订单数据:', updatedOrderData);
        await flightOrderApi.updateOrder(editingOrder.id, updatedOrderData);

        toast({
          title: "保存成功",
          description: "飞机票订单信息已更新，状态已改为待提交",
        });
      }

      setIsEditMode(false);
      onOrderUpdated?.();
    } catch (error: any) {
      console.error('保存订单失败:', error);

      // 提取详细错误信息
      let errorMessage = "保存订单信息时发生错误";

      if (error.response) {
        // HTTP 错误响应
        const status = error.response.status;
        const data = error.response.data;

        if (data?.detail) {
          errorMessage = data.detail;

          // 将英文状态转换为中文 - 更精确的替换
          if (errorMessage.includes('submitted')) {
            errorMessage = errorMessage.replace('submitted', '已提交');
          }
          if (errorMessage.includes('processing')) {
            errorMessage = errorMessage.replace('processing', '处理中');
          }
          if (errorMessage.includes('completed')) {
            errorMessage = errorMessage.replace('completed', '已完成');
          }
          if (errorMessage.includes('initial')) {
            errorMessage = errorMessage.replace('initial', '待提交');
          }
          if (errorMessage.includes('failed')) {
            errorMessage = errorMessage.replace('failed', '失败');
          }

          console.log('原始错误信息:', data.detail);
          console.log('转换后错误信息:', errorMessage);

        } else if (data?.message) {
          errorMessage = data.message;
        } else if (status === 400) {
          errorMessage = "请求参数错误，请检查输入信息";
        } else if (status === 403) {
          errorMessage = "没有权限执行此操作";
        } else if (status === 404) {
          errorMessage = "订单不存在";
        } else if (status === 500) {
          errorMessage = "服务器内部错误";
        }
      } else if (error.request) {
        // 网络错误
        errorMessage = "网络连接失败，请检查网络连接";
      } else {
        // 其他错误
        errorMessage = error.message || "未知错误";
      }

      console.error('保存订单失败:', {
        message: errorMessage,
        status: error.response?.status,
        data: error.response?.data,
        error: error
      });

      toast({
        title: "保存失败",
        description: errorMessage,
        variant: "destructive"
      });
    } finally {
      setSaving(false);
    }
  };

  if (!isOpen || !order || !editingOrder) {
    return null;
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* 背景遮罩 */}
      <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" onClick={onClose}></div>
      
      {/* 弹窗内容 */}
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden z-50 mx-4">
        {/* 弹窗头部 */}
        <div className="flex justify-between items-center px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-white">
          <h2 className="text-xl font-semibold text-blue-700 flex items-center">
            <Eye className="h-5 w-5 mr-2" />
            飞机票订单详情 {isEditMode && <span className="ml-2 text-sm text-orange-600">(编辑模式)</span>}
          </h2>
          <button 
            onClick={onClose}
            className="h-8 w-8 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 flex items-center justify-center transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>
        
        {/* 弹窗内容 */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-180px)]">
          <div className="space-y-8">
            {/* 订单状态区域 */}
            <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
              <div className="flex flex-col gap-3">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium text-gray-900">订单状态</h3>
                  <div className="flex items-center">
                    {getOrderStatusDisplay(order.order_status)}
                  </div>
                </div>

                {/* 失败原因展示 */}
                {(order.order_status === 'check_failed' || order.order_status === 'failed') && order.fail_reason && (
                  <div className="text-red-600 text-sm">
                    {order.fail_reason}
                  </div>
                )}
              </div>
            </div>

            {/* 出行人信息 */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200">出行人信息</h3>
              <div className="overflow-hidden border border-gray-300 rounded-lg">
                <table className="w-full">
                  <tbody>
                    <tr className="border-b border-gray-200">
                      <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">出行人姓名 <span className="text-red-500">*</span></label>
                      </td>
                      <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                        <EditableCell
                          value={editingOrder.traveler_full_name}
                          field="traveler_full_name"
                          isEditing={isEditMode}
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                      <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">出行人姓</label>
                      </td>
                      <td className="py-3 px-4 w-1/4">
                        <EditableCell
                          value={editingOrder.traveler_surname}
                          field="traveler_surname"
                          isEditing={isEditMode}
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                    </tr>
                    <tr className="border-b border-gray-200">
                      <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">出行人名</label>
                      </td>
                      <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                        <EditableCell
                          value={editingOrder.traveler_given_name}
                          field="traveler_given_name"
                          isEditing={isEditMode}
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                      <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">国籍</label>
                      </td>
                      <td className="py-3 px-4 w-1/4">
                        <EditableCell
                          value={editingOrder.nationality}
                          field="nationality"
                          isEditing={isEditMode}
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                    </tr>
                    <tr className="border-b border-gray-200">
                      <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">性别</label>
                      </td>
                      <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                        <EditableCell
                          value={editingOrder.gender}
                          field="gender"
                          isEditing={isEditMode}
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                          type="select"
                          options={[
                            { value: '男', label: '男' },
                            { value: '女', label: '女' }
                          ]}
                        />
                      </td>
                      <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">出生日期</label>
                      </td>
                      <td className="py-3 px-4 w-1/4">
                        <EditableCell
                          value={editingOrder.birth_date}
                          field="birth_date"
                          isEditing={isEditMode}
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                          type="date"
                        />
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            {/* 证件信息 */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200">证件信息</h3>
              <div className="overflow-hidden border border-gray-300 rounded-lg">
                <table className="w-full">
                  <tbody>
                    <tr className="border-b border-gray-200">
                      <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">证件类型 <span className="text-red-500">*</span></label>
                      </td>
                      <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                        <EditableCell
                          value={editingOrder.id_type}
                          field="id_type"
                          isEditing={isEditMode}
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                          type="select"
                          options={[
                            { value: '身份证', label: '身份证' },
                            { value: '公务护照', label: '公务护照' },
                            { value: '普通护照', label: '普通护照' },
                            { value: '港澳通行证', label: '港澳通行证' },
                            { value: '台胞证', label: '台胞证' },
                            { value: '回乡证', label: '回乡证' },
                            { value: '军人证', label: '军人证' },
                            { value: '海员证', label: '海员证' },
                            { value: '台湾通行证', label: '台湾通行证' },
                            { value: '外国永久居留证', label: '外国永久居留证' },
                            { value: '港澳台居民居住证', label: '港澳台居民居住证' },
                            { value: '其他', label: '其他' }
                          ]}
                        />
                      </td>
                      <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">证件号码 <span className="text-red-500">*</span></label>
                      </td>
                      <td className="py-3 px-4 w-1/4">
                        <EditableCell
                          value={editingOrder.id_number}
                          field="id_number"
                          isEditing={isEditMode}
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                    </tr>
                    <tr className="border-b border-gray-200">
                      <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">证件有效期至</label>
                      </td>
                      <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                        <EditableCell
                          value={editingOrder.id_expiry_date}
                          field="id_expiry_date"
                          isEditing={isEditMode}
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                          type="date"
                        />
                      </td>
                      <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">手机号国际区号</label>
                      </td>
                      <td className="py-3 px-4 w-1/4">
                        <EditableCell
                          value={editingOrder.mobile_country_code}
                          field="mobile_country_code"
                          isEditing={isEditMode}
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                    </tr>
                    <tr>
                      <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">手机号 <span className="text-red-500">*</span></label>
                      </td>
                      <td className="py-3 px-4 w-3/4" colSpan={3}>
                        <EditableCell
                          value={editingOrder.mobile_phone}
                          field="mobile_phone"
                          isEditing={isEditMode}
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                          type="tel"
                        />
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            {/* 航班信息 */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200">航班信息</h3>
              <div className="overflow-hidden border border-gray-300 rounded-lg">
                <table className="w-full">
                  <tbody>
                    <tr className="border-b border-gray-200">
                      <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">出行日期 <span className="text-red-500">*</span></label>
                      </td>
                      <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                        <EditableCell
                          value={editingOrder.travel_date}
                          field="travel_date"
                          isEditing={isEditMode}
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                          type="date"
                        />
                      </td>
                      <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">航班号 <span className="text-red-500">*</span></label>
                      </td>
                      <td className="py-3 px-4 w-1/4">
                        <EditableCell
                          value={editingOrder.flight_number}
                          field="flight_number"
                          isEditing={isEditMode}
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                    </tr>
                    <tr className="border-b border-gray-200">
                      <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">出发机场 <span className="text-red-500">*</span></label>
                      </td>
                      <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                        <EditableCell
                          value={editingOrder.departure_airport}
                          field="departure_airport"
                          isEditing={isEditMode}
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                      <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">到达机场 <span className="text-red-500">*</span></label>
                      </td>
                      <td className="py-3 px-4 w-1/4">
                        <EditableCell
                          value={editingOrder.arrival_airport}
                          field="arrival_airport"
                          isEditing={isEditMode}
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                    </tr>
                    <tr className="border-b border-gray-200">
                      <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">出发时间 <span className="text-red-500">*</span></label>
                      </td>
                      <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                        <EditableCell
                          value={editingOrder.departure_time}
                          field="departure_time"
                          isEditing={isEditMode}
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                          type="time"
                        />
                      </td>
                      <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">到达时间 <span className="text-red-500">*</span></label>
                      </td>
                      <td className="py-3 px-4 w-1/4">
                        <EditableCell
                          value={editingOrder.arrival_time}
                          field="arrival_time"
                          isEditing={isEditMode}
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                          type="time"
                        />
                      </td>
                    </tr>
                    <tr>
                      <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">行程提交项</label>
                      </td>
                      <td className="py-3 px-4 w-3/4" colSpan={3}>
                        <EditableCell
                          value={editingOrder.trip_submission_item}
                          field="trip_submission_item"
                          isEditing={isEditMode}
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                          type="textarea"
                        />
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            {/* 联系人信息 */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200">联系人信息</h3>
              <div className="overflow-hidden border border-gray-300 rounded-lg">
                <table className="w-full">
                  <tbody>
                    <tr className="border-b border-gray-200">
                      <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">联系人 <span className="text-red-500">*</span></label>
                      </td>
                      <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                        <EditableCell
                          value={editingOrder.contact_person}
                          field="contact_person"
                          isEditing={isEditMode}
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                      <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">联系人手机号 <span className="text-red-500">*</span></label>
                      </td>
                      <td className="py-3 px-4 w-1/4">
                        <EditableCell
                          value={editingOrder.contact_mobile_phone}
                          field="contact_mobile_phone"
                          isEditing={isEditMode}
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                          type="tel"
                        />
                      </td>
                    </tr>
                    <tr className="border-b border-gray-200">
                      <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">联系人邮箱</label>
                      </td>
                      <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                        <EditableCell
                          value={editingOrder.contact_email}
                          field="contact_email"
                          isEditing={isEditMode}
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                          type="email"
                        />
                      </td>
                      <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">审批参照人 <span className="text-red-500">*</span></label>
                      </td>
                      <td className="py-3 px-4 w-1/4">
                        <EditableCell
                          value={editingOrder.approver}
                          field="approver"
                          isEditing={isEditMode}
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            {/* 其他信息 */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200">其他信息</h3>
              <div className="overflow-hidden border border-gray-300 rounded-lg">
                <table className="w-full">
                  <tbody>
                    <tr className="border-b border-gray-200">
                      <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">保险名称</label>
                      </td>
                      <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                        <EditableCell
                          value={editingOrder.insurance_name}
                          field="insurance_name"
                          isEditing={isEditMode}
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                      <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">公司名称</label>
                      </td>
                      <td className="py-3 px-4 w-1/4">
                        <EditableCell
                          value={editingOrder.company_name}
                          field="company_name"
                          isEditing={isEditMode}
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                    </tr>
                    <tr className="border-b border-gray-200">
                      <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">代订人</label>
                      </td>
                      <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                        <EditableCell
                          value={editingOrder.booking_agent}
                          field="booking_agent"
                          isEditing={isEditMode}
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                      <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">金额</label>
                      </td>
                      <td className="py-3 px-4 w-1/4">
                        <EditableCell
                          value={editingOrder.amount}
                          field="amount"
                          isEditing={isEditMode}
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                    </tr>
                    <tr className="border-b border-gray-200">
                      <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">订单号</label>
                      </td>
                      <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                        <EditableCell
                          value={editingOrder.order_number}
                          field="order_number"
                          isEditing={isEditMode}
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                      <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">账单号</label>
                      </td>
                      <td className="py-3 px-4 w-1/4">
                        <EditableCell
                          value={editingOrder.bill_number}
                          field="bill_number"
                          isEditing={isEditMode}
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                    </tr>
                    <tr>
                      <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">出票短信</label>
                      </td>
                      <td className="py-3 px-4 w-3/4" colSpan={3}>
                        <EditableCell
                          value={editingOrder.ticket_sms}
                          field="ticket_sms"
                          isEditing={isEditMode}
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                          type="textarea"
                        />
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>

        {/* 弹窗底部 */}
        <div className="flex justify-between items-center px-6 py-4 border-t border-gray-200 bg-gray-50">
          <div className="text-sm text-gray-500">
            创建时间: {new Date(order.created_at).toLocaleString()}
            {order.updated_at !== order.created_at && (
              <span className="ml-4">更新时间: {new Date(order.updated_at).toLocaleString()}</span>
            )}
          </div>
          <div className="flex items-center gap-3">
            {isEditMode ? (
              <>
                <Button variant="outline" onClick={() => {
                  setIsEditMode(false);
                  setEditingOrder({ ...order });
                  setValidationErrors({});
                }} disabled={saving}>
                  取消
                </Button>
                <Button onClick={saveChanges} disabled={saving}>
                  {saving ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      保存中...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      保存
                    </>
                  )}
                </Button>
              </>
            ) : (
              <>
                <Button variant="outline" onClick={() => setIsEditMode(true)}>
                  <Edit className="h-4 w-4 mr-2" />
                  编辑
                </Button>
                <Button onClick={onClose}>
                  关闭
                </Button>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default FlightOrderDetailModal;
