import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  RefreshCw,
  Eye,
  Search,
  AlertCircle,
  Loader,
  Download,
  CheckCircle,
  XCircle,
  Clock,
  Send,
  PauseCircle,
  Plane,
  X,
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

import * as XLSX from 'xlsx-js-style';
import { saveAs } from 'file-saver';

import {
  flightOrderApi,
  FlightOrder,
} from '@/api/flightOrder';

import { formatAmount } from '@/utils/formatters';

interface FlightAllOrdersContentProps {
  projectId: string;
}

const FlightAllOrdersContent: React.FC<FlightAllOrdersContentProps> = ({ projectId }) => {
  const { toast } = useToast();

  // 所有订单相关状态
  const [allOrders, setAllOrders] = useState<FlightOrder[]>([]);
  const [allOrdersLoading, setAllOrdersLoading] = useState(false);
  const [allOrdersPage, setAllOrdersPage] = useState(1);
  const [allOrdersPageSize, setAllOrdersPageSize] = useState(20);
  const [allOrdersTotal, setAllOrdersTotal] = useState(0);

  // 搜索相关状态
  const [searchTravelerName, setSearchTravelerName] = useState('');
  const [searchMobilePhone, setSearchMobilePhone] = useState('');
  const [searchContactPhone, setSearchContactPhone] = useState('');

  // 暂停已提交订单状态
  const [pausingSubmittedOrders, setPausingSubmittedOrders] = useState(false);

  // 导出状态
  const [exporting, setExporting] = useState(false);

  // 状态筛选相关状态
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>([
    'check_failed', 'initial', 'submitted', 'paused', 'processing', 'completed', 'failed'
  ]);

  // 查看详情相关状态
  const [viewingOrder, setViewingOrder] = useState<FlightOrder | null>(null);
  const [showDetailModal, setShowDetailModal] = useState(false);

  useEffect(() => {
    if (projectId) {
      loadAllOrders();
    }
  }, [projectId]);

  // 监听状态筛选变化
  useEffect(() => {
    if (projectId) {
      setAllOrdersPage(1);
      loadAllOrders(1);
    }
  }, [selectedStatuses]);

  // 监听pageSize变化
  useEffect(() => {
    if (projectId) {
      setAllOrdersPage(1);
      loadAllOrders(1);
    }
  }, [allOrdersPageSize]);

  // 监听搜索条件变化，自动重新加载数据（参照酒店功能）
  useEffect(() => {
    if (projectId) {
      setAllOrdersPage(1); // 重置到第一页
      loadAllOrders(1);
    }
  }, [searchTravelerName, searchMobilePhone, searchContactPhone]);

  const loadAllOrders = async (page: number = 1) => {
    try {
      setAllOrdersLoading(true);
      const statusParam = selectedStatuses.length > 0 ? selectedStatuses.join(',') : undefined;
      
      // 构建查询参数，只包含有值的字段
      const queryParams: any = {
        page,
        page_size: allOrdersPageSize,
        sort_by_failed_first: true
      };

      if (statusParam) {
        queryParams.order_status = statusParam;
      }
      if (searchTravelerName?.trim()) {
        queryParams.traveler_name = searchTravelerName.trim();
      }
      if (searchMobilePhone?.trim()) {
        queryParams.mobile_phone = searchMobilePhone.trim();
      }
      if (searchContactPhone?.trim()) {
        queryParams.contact_phone = searchContactPhone.trim();
      }

      const response = await flightOrderApi.getOrdersByProject(
        Number(projectId),
        queryParams
      );
      
      setAllOrders(response.items);
      setAllOrdersTotal(response.total);
      setAllOrdersPage(page);
    } catch (error) {
      console.error('加载所有订单失败:', error);
      toast({
        title: "加载失败",
        description: "无法加载订单列表",
        variant: "destructive",
      });
    } finally {
      setAllOrdersLoading(false);
    }
  };

  const handleSearch = () => {
    setAllOrdersPage(1);
    loadAllOrders(1);
  };

  const handleClearSearch = () => {
    setSearchTravelerName('');
    setSearchMobilePhone('');
    setSearchContactPhone('');
    setTimeout(() => {
      setAllOrdersPage(1);
      loadAllOrders(1);
    }, 0);
  };

  // 暂停已提交订单
  const handlePauseSubmittedOrders = async () => {
    if (!projectId) return;

    // 确认对话框
    const confirmed = window.confirm(
      '确定要暂停所有已提交的飞机票订单吗？\n\n暂停后的订单将变为已暂停状态，可以稍后重新提交。'
    );

    if (!confirmed) return;

    try {
      setPausingSubmittedOrders(true);

      // 调用API暂停已提交订单
      await flightOrderApi.pauseSubmittedOrders(Number(projectId), {});

      toast({
        title: "操作成功",
        description: "已提交的飞机票订单已暂停",
        variant: "default",
      });

      // 重新加载订单列表
      loadAllOrders(allOrdersPage);

    } catch (error: any) {
      console.error('暂停已提交订单失败:', error);
      toast({
        title: "操作失败",
        description: error.response?.data?.detail || "暂停已提交订单时发生错误",
        variant: "destructive",
      });
    } finally {
      setPausingSubmittedOrders(false);
    }
  };

  // 编辑订单相关函数
  const handleViewOrder = (order: FlightOrder) => {
    setViewingOrder(order);
    setShowDetailModal(true);
  };

  // 获取状态文本（用于导出）
  const getOrderStatusText = (status: string): string => {
    const statusMap = {
      'check_failed': '验证失败',
      'initial': '待提交',
      'submitted': '已提交',
      'paused': '已暂停',
      'processing': '处理中',
      'completed': '已完成',
      'failed': '预订失败'
    };
    return statusMap[status as keyof typeof statusMap] || status;
  };

  const getOrderStatusDisplay = (status: string) => {
    const statusMap = {
      'check_failed': { text: '验证失败', color: 'text-amber-600', bgColor: 'bg-amber-50' },
      'initial': { text: '待提交', color: 'text-gray-600', bgColor: 'bg-gray-50' },
      'submitted': { text: '已提交', color: 'text-blue-600', bgColor: 'bg-blue-50' },
      'paused': { text: '已暂停', color: 'text-orange-600', bgColor: 'bg-orange-50' },
      'processing': { text: '处理中', color: 'text-purple-600', bgColor: 'bg-purple-50' },
      'completed': { text: '已完成', color: 'text-green-600', bgColor: 'bg-green-50' },
      'failed': { text: '预订失败', color: 'text-red-600', bgColor: 'bg-red-50' }
    };
    const statusConfig = statusMap[status as keyof typeof statusMap] || { text: status, color: 'text-gray-600', bgColor: 'bg-gray-50' };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusConfig.color} ${statusConfig.bgColor}`}>
        {statusConfig.text}
      </span>
    );
  };

  // 格式化金额
  const formatAmount = (amount: number | string | undefined | null): string => {
    if (!amount) return '¥0.00';
    const num = typeof amount === 'string' ? parseFloat(amount) : amount;
    if (isNaN(num)) return '¥0.00';
    return `¥${num.toFixed(2)}`;
  };

  // 格式化日期显示
  const formatDateForDisplay = (dateStr: string | null | undefined): string => {
    if (!dateStr) return '-';
    try {
      const date = new Date(dateStr);
      if (isNaN(date.getTime())) return '-';
      return date.toISOString().split('T')[0]; // 返回YYYY-MM-DD格式
    } catch (error) {
      return '-';
    }
  };

  // 格式化时间显示（HH:MM格式）
  const formatTimeToHourMinute = (timeStr: string | null | undefined): string => {
    if (!timeStr) return '-';
    try {
      // 如果是完整的datetime格式，提取时间部分
      if (timeStr.includes('T')) {
        const timePart = timeStr.split('T')[1];
        return timePart.substring(0, 5); // 返回HH:MM格式
      }
      // 如果已经是时间格式，直接返回前5位
      return timeStr.substring(0, 5);
    } catch (error) {
      return '-';
    }
  };

  // 导出Excel功能 - 参考预订管理的实现
  const handleExportExcel = async () => {
    setExporting(true);
    try {
      // 获取当前筛选条件下的所有订单数据
      let allOrders: FlightOrder[] = [];
      let currentExportPage = 1;
      let hasMore = true;
      const pageSize = 100; // 每次获取100条

      while (hasMore) {
        const exportParams: any = {
          page: currentExportPage,
          page_size: pageSize,
          sort_by_failed_first: true
        };

        if (selectedStatuses.length > 0) {
          exportParams.order_status = selectedStatuses.join(',');
        }
        if (searchTravelerName?.trim()) {
          exportParams.traveler_name = searchTravelerName.trim();
        }
        if (searchMobilePhone?.trim()) {
          exportParams.mobile_phone = searchMobilePhone.trim();
        }
        if (searchContactPhone?.trim()) {
          exportParams.contact_phone = searchContactPhone.trim();
        }

        const response = await flightOrderApi.getOrdersByProject(
          Number(projectId),
          exportParams
        );

        allOrders = allOrders.concat(response.items);

        // 如果返回的数据少于页面大小，说明已经是最后一页
        if (response.items.length < pageSize) {
          hasMore = false;
        } else {
          currentExportPage++;
        }
      }

      // 准备导出数据
      const exportData = allOrders.map((order, index) => ({
        '序号': index + 1,
        '状态': order.order_status === 'completed' ? '已完成' :
               order.order_status === 'failed' ? '预定失败' :
               order.order_status === 'processing' ? '处理中' :
               order.order_status === 'submitted' ? '已提交' :
               order.order_status === 'check_failed' ? '验证失败' :
               order.order_status === 'paused' ? '已暂停' :
               '待提交',
        '错误信息': order.fail_reason || '',
        '出行人姓名': order.traveler_full_name || '',
        '出行人姓': order.traveler_surname || '',
        '出行人名': order.traveler_given_name || '',
        '国籍': order.nationality || '',
        '性别': order.gender || '',
        '出生日期': formatDateForDisplay(order.birth_date),
        '证件类型': order.id_type || '',
        '证件号码': order.id_number || '',
        '证件有效期至': formatDateForDisplay(order.id_expiry_date),
        '手机号国际区号': order.mobile_country_code || '',
        '手机号': order.mobile_phone || '',
        '出行日期': formatDateForDisplay(order.travel_date),
        '航班号': order.flight_number || '',
        '出发机场名': order.departure_airport || '',
        '到达机场名': order.arrival_airport || '',
        '出发时间': formatTimeToHourMinute(order.departure_time),
        '到达时间': formatTimeToHourMinute(order.arrival_time),
        '行程提交项': order.trip_submission_item || '',
        '联系人': order.contact_person || '',
        '联系人手机号': order.contact_mobile_phone || '',
        '联系人邮箱': order.contact_email || '',
        '审批参照人': order.approver || '',
        '保险名称': order.insurance_name || '',
        '公司名称': order.company_name || '',
        '代订人': order.booking_agent || '',
        '出票短信': order.ticket_sms || '',
        '金额': order.amount ? formatAmount(order.amount) : '',
        '订单号': order.order_number || '',
        '账单号': order.bill_number || '',
        '创建时间': order.created_at ? new Date(order.created_at).toLocaleString('zh-CN') : '',
        '更新时间': order.updated_at ? new Date(order.updated_at).toLocaleString('zh-CN') : ''
      }));

      // 获取列名
      const headers = Object.keys(exportData[0] || {});

      // 创建空工作表
      const ws = XLSX.utils.aoa_to_sheet([]);

      // 添加标题行
      const title = `项目${projectId} - 飞机票订单明细`;

      // 第一行：标题
      XLSX.utils.sheet_add_aoa(ws, [[title]], { origin: 'A1' });

      // 合并标题行单元格
      ws['!merges'] = [{ s: { r: 0, c: 0 }, e: { r: 0, c: headers.length - 1 } }];

      // 第二行：列名
      XLSX.utils.sheet_add_aoa(ws, [headers], { origin: 'A2' });

      // 第三行开始：数据
      const dataRows = exportData.map(row => headers.map(header => (row as any)[header]));
      XLSX.utils.sheet_add_aoa(ws, dataRows, { origin: 'A3' });

      // 设置列宽
      const colWidths = headers.map(() => ({ wch: 15 })); // 统一列宽
      ws['!cols'] = colWidths;

      // 获取工作表范围
      const range = XLSX.utils.decode_range(ws['!ref'] || 'A1');

      // 为所有单元格添加边框和基本样式
      for (let row = range.s.r; row <= range.e.r; row++) {
        for (let col = range.s.c; col <= range.e.c; col++) {
          const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });

          if (!ws[cellAddress]) {
            ws[cellAddress] = { v: '', t: 's' };
          }

          // 基本样式
          ws[cellAddress].s = {
            border: {
              top: { style: 'thin', color: { rgb: '000000' } },
              bottom: { style: 'thin', color: { rgb: '000000' } },
              left: { style: 'thin', color: { rgb: '000000' } },
              right: { style: 'thin', color: { rgb: '000000' } }
            },
            font: {
              name: '微软雅黑',
              sz: 10
            },
            alignment: {
              vertical: 'center',
              wrapText: true
            }
          };

          // 为标题行添加特殊样式
          if (row === 0) {
            ws[cellAddress].s.fill = {
              patternType: 'solid',
              fgColor: { rgb: '4F81BD' }
            };
            ws[cellAddress].s.font = {
              name: '微软雅黑',
              sz: 14,
              bold: true,
              color: { rgb: 'FFFFFF' }
            };
            ws[cellAddress].s.alignment = {
              horizontal: 'center',
              vertical: 'center',
              wrapText: true
            };
          }

          // 为列名行添加特殊样式
          else if (row === 1) {
            ws[cellAddress].s.fill = {
              patternType: 'solid',
              fgColor: { rgb: 'F3F4F6' }
            };
            ws[cellAddress].s.font = {
              name: '微软雅黑',
              sz: 10,
              bold: true
            };
            ws[cellAddress].s.alignment = {
              horizontal: 'center',
              vertical: 'center',
              wrapText: true
            };
          }
        }
      }

      // 创建工作簿
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, '飞机票订单');

      // 生成文件名
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0];

      // 添加筛选条件信息到文件名
      let filterInfo = '';
      const statusNames = selectedStatuses.map(status => {
        switch(status) {
          case 'completed': return '已完成';
          case 'failed': return '预定失败';
          case 'processing': return '处理中';
          case 'submitted': return '已提交';
          case 'check_failed': return '验证失败';
          case 'paused': return '已暂停';
          case 'initial': return '待提交';
          default: return status;
        }
      });
      if (statusNames.length > 0 && statusNames.length < 7) {
        filterInfo += `_状态(${statusNames.join('_')})`;
      }

      // 添加搜索条件信息
      const searchConditions = [];
      if (searchTravelerName) searchConditions.push(`出行人(${searchTravelerName})`);
      if (searchMobilePhone) searchConditions.push(`手机(${searchMobilePhone})`);
      if (searchContactPhone) searchConditions.push(`联系人手机(${searchContactPhone})`);
      if (searchConditions.length > 0) {
        filterInfo += `_搜索(${searchConditions.join('_')})`;
      }

      const fileName = `飞机票订单_项目${projectId}${filterInfo}_${timestamp}.xlsx`;

      // 导出文件
      XLSX.writeFile(wb, fileName);

      toast({
        title: "导出成功",
        description: `成功导出 ${allOrders.length} 条筛选后的订单数据`,
        variant: "default",
      });

    } catch (error) {
      console.error('导出Excel失败:', error);
      toast({
        title: "导出失败",
        description: "导出Excel文件时发生错误，请重试",
        variant: "destructive",
      });
    } finally {
      setExporting(false);
    }
  };



  // 计算各状态订单数量（前端计算，用于状态筛选显示）
  const statusCounts = allOrders.reduce((counts, order) => {
    counts[order.order_status] = (counts[order.order_status] || 0) + 1;
    return counts;
  }, {} as Record<string, number>);

  return (
    <div className="space-y-4">
      {/* 订单统计卡片 */}
      {allOrdersTotal > 0 && (
        <Card className="bg-white border border-gray-200">
          <div className="p-4">
            <h3 className="text-sm font-medium text-gray-900 mb-3">订单状态统计</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-9 gap-4">
              {/* 总订单数 */}
              <div className="text-center">
                <div className="p-2 bg-purple-100 rounded-lg mx-auto w-fit mb-1">
                  <Plane className="h-4 w-4 text-purple-600" />
                </div>
                <p className="text-lg font-medium text-gray-900">{allOrdersTotal}</p>
                <p className="text-xs text-gray-500">总订单数</p>
              </div>
              {/* 验证失败 */}
              <div className="text-center">
                <div className="p-2 bg-amber-100 rounded-lg mx-auto w-fit mb-1">
                  <AlertCircle className="h-4 w-4 text-amber-600" />
                </div>
                <p className="text-lg font-medium text-gray-900">{statusCounts['check_failed'] || 0}</p>
                <p className="text-xs text-gray-500">验证失败</p>
              </div>
              {/* 待提交 */}
              <div className="text-center">
                <div className="p-2 bg-gray-100 rounded-lg mx-auto w-fit mb-1">
                  <Clock className="h-4 w-4 text-gray-600" />
                </div>
                <p className="text-lg font-medium text-gray-900">{statusCounts['initial'] || 0}</p>
                <p className="text-xs text-gray-500">待提交</p>
              </div>
              {/* 已提交 */}
              <div className="text-center">
                <div className="p-2 bg-blue-100 rounded-lg mx-auto w-fit mb-1">
                  <Send className="h-4 w-4 text-blue-600" />
                </div>
                <p className="text-lg font-medium text-gray-900">{statusCounts['submitted'] || 0}</p>
                <p className="text-xs text-gray-500">已提交</p>
              </div>
              {/* 已暂停 */}
              <div className="text-center">
                <div className="p-2 bg-orange-100 rounded-lg mx-auto w-fit mb-1">
                  <Clock className="h-4 w-4 text-orange-600" />
                </div>
                <p className="text-lg font-medium text-gray-900">{statusCounts['paused'] || 0}</p>
                <p className="text-xs text-gray-500">已暂停</p>
              </div>
              {/* 处理中 */}
              <div className="text-center">
                <div className="p-2 bg-cyan-100 rounded-lg mx-auto w-fit mb-1">
                  <Loader className="h-4 w-4 text-cyan-600" />
                </div>
                <p className="text-lg font-medium text-gray-900">{statusCounts['processing'] || 0}</p>
                <p className="text-xs text-gray-500">处理中</p>
              </div>
              {/* 已完成 */}
              <div className="text-center">
                <div className="p-2 bg-green-100 rounded-lg mx-auto w-fit mb-1">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                </div>
                <p className="text-lg font-medium text-gray-900">{statusCounts['completed'] || 0}</p>
                <p className="text-xs text-gray-500">已完成</p>
              </div>
              {/* 预订失败 */}
              <div className="text-center">
                <div className="p-2 bg-red-100 rounded-lg mx-auto w-fit mb-1">
                  <XCircle className="h-4 w-4 text-red-600" />
                </div>
                <p className="text-lg font-medium text-gray-900">{statusCounts['failed'] || 0}</p>
                <p className="text-xs text-gray-500">预订失败</p>
              </div>
              {/* 金额统计 */}
              <div className="text-center">
                <div className="p-2 bg-indigo-100 rounded-lg mx-auto w-fit mb-1">
                  <span className="text-indigo-600 text-xs font-bold">¥</span>
                </div>
                <p className="text-lg font-medium text-gray-900">
                  {formatAmount(allOrders.reduce((sum, order) => sum + (parseFloat(order.amount?.toString() || '0') || 0), 0))}
                </p>
                <p className="text-xs text-gray-500">总金额</p>
              </div>
            </div>
          </div>
        </Card>
      )}

      <Card className="bg-white border border-gray-200 rounded-lg shadow-sm">
        <div className="border-b border-gray-200 p-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <Eye className="h-5 w-5 text-gray-600" />
              <h3 className="text-lg font-medium text-gray-900">所有订单</h3>
              {allOrdersTotal > 0 && (
                <span className="ml-2 bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full">
                  {allOrdersTotal}
                </span>
              )}
            </div>
          </div>

          {/* 搜索区域 */}
          <div className="flex flex-col gap-3">
            {/* 第一行：状态筛选 */}
            <div className="flex items-center gap-3">
              <span className="text-sm font-medium text-gray-700 whitespace-nowrap">状态筛选：</span>
              <div className="flex flex-wrap gap-2">
                {[
                  { value: 'check_failed', label: '验证失败', color: 'bg-amber-100 text-amber-700' },
                  { value: 'initial', label: '待提交', color: 'bg-gray-100 text-gray-600' },
                  { value: 'submitted', label: '已提交', color: 'bg-blue-100 text-blue-700' },
                  { value: 'paused', label: '已暂停', color: 'bg-orange-100 text-orange-700' },
                  { value: 'processing', label: '处理中', color: 'bg-cyan-100 text-cyan-700' },
                  { value: 'completed', label: '已完成', color: 'bg-green-100 text-green-700' },
                  { value: 'failed', label: '预订失败', color: 'bg-red-100 text-red-700' },
                ].map((status) => (
                  <label
                    key={status.value}
                    className="flex items-center gap-1 cursor-pointer hover:bg-gray-50 p-1 rounded"
                  >
                    <input
                      type="checkbox"
                      checked={selectedStatuses.includes(status.value)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedStatuses([...selectedStatuses, status.value]);
                        } else {
                          setSelectedStatuses(selectedStatuses.filter(s => s !== status.value));
                        }
                      }}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 focus:ring-offset-0"
                    />
                    <span className={`text-xs px-2 py-1 rounded-full ${status.color}`}>
                      {status.label}
                    </span>
                  </label>
                ))}
              </div>
            </div>

            {/* 第二行：搜索输入框 */}
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2 flex-1 max-w-xs">
                <label className="text-sm text-gray-600 whitespace-nowrap min-w-fit">出行人姓名：</label>
                <input
                  type="text"
                  placeholder="请输入出行人姓名"
                  value={searchTravelerName}
                  onChange={(e) => setSearchTravelerName(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  className="flex-1 h-9 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                />
              </div>

              <div className="flex items-center gap-2 flex-1 max-w-xs">
                <label className="text-sm text-gray-600 whitespace-nowrap min-w-fit">手机号码：</label>
                <input
                  type="text"
                  placeholder="请输入手机号码"
                  value={searchMobilePhone}
                  onChange={(e) => setSearchMobilePhone(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  className="flex-1 h-9 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                />
              </div>

              <div className="flex items-center gap-2 flex-1 max-w-xs">
                <label className="text-sm text-gray-600 whitespace-nowrap min-w-fit">联系人手机：</label>
                <input
                  type="text"
                  placeholder="请输入联系人手机号"
                  value={searchContactPhone}
                  onChange={(e) => setSearchContactPhone(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  className="flex-1 h-9 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                />
              </div>

              <div className="flex items-center gap-2 ml-auto">
                <Button
                  size="sm"
                  onClick={handleSearch}
                  disabled={allOrdersLoading}
                  className="bg-blue-600 hover:bg-blue-700 text-white h-9"
                >
                  <Search className="h-4 w-4 mr-2" />
                  搜索
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleClearSearch}
                  disabled={allOrdersLoading}
                  className="h-9"
                >
                  <X className="h-4 w-4 mr-2" />
                  重置
                </Button>

                {allOrdersTotal > 0 && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleExportExcel}
                    disabled={allOrdersLoading || exporting}
                    className="h-9"
                  >
                    {exporting ? (
                      <>
                        <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-gray-600 mr-2"></div>
                        导出中...
                      </>
                    ) : (
                      <>
                        <Download className="h-4 w-4 mr-2" />
                        导出
                      </>
                    )}
                  </Button>
                )}

                {/* 暂停已提交订单按钮 */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handlePauseSubmittedOrders}
                  disabled={allOrdersLoading || pausingSubmittedOrders}
                  className="h-9 text-orange-600 border-orange-300 hover:bg-orange-50 hover:border-orange-400"
                >
                  {pausingSubmittedOrders ? (
                    <Loader className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Clock className="h-4 w-4 mr-2" />
                  )}
                  暂停已提交订单
                </Button>
              </div>
            </div>
          </div>
        </div>
      </Card>

      <Card className="bg-white border border-gray-200 rounded-lg shadow-sm">
        <div className="p-0">
          {allOrdersLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">加载中...</p>
            </div>
          ) : allOrders.length > 0 ? (
            <>
              <div className="overflow-x-auto custom-scrollbar" style={{
                scrollbarWidth: 'auto',
                scrollbarColor: '#cbd5e1 #f1f5f9'
              }}>
                <style dangerouslySetInnerHTML={{
                  __html: `
                    .custom-scrollbar::-webkit-scrollbar {
                      height: 8px;
                      background-color: #f1f5f9;
                    }
                    .custom-scrollbar::-webkit-scrollbar-thumb {
                      background-color: #cbd5e1;
                      border-radius: 4px;
                    }
                    .custom-scrollbar::-webkit-scrollbar-thumb:hover {
                      background-color: #94a3b8;
                    }
                  `
                }} />
                <table className="w-full text-xs" style={{ minWidth: '4000px' }}>
                  <thead className="bg-gray-50 border-b border-gray-200">
                    <tr>
                      <th className="text-center p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '60px' }}>序号</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>状态</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '150px' }}>错误信息</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>出行人姓名</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>出行人姓</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>出行人名</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '60px' }}>国籍</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '50px' }}>性别</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>出生日期</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>证件类型</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>证件号码</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>证件有效期至</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>手机号国际区号</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>手机号</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>出行日期</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>出发机场名</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>到达机场名</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>航班号</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>出发时间</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>到达时间</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>行程提交项</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>联系人</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>联系人手机号</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>联系人邮箱</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>审批参照人</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>保险名称</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>公司名称</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>代订人</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>出票短信</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>金额</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>订单号</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>账单号</th>
                      <th className="text-center p-2 font-medium text-gray-900 text-xs whitespace-nowrap sticky right-0 bg-white border-l border-gray-200 z-10 shadow-[-4px_0_8px_rgba(0,0,0,0.1)]" style={{ minWidth: '120px' }}>操作</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {allOrders.map((order, index) => (
                      <tr key={order.id} className="hover:bg-gray-50">
                        <td className="text-center p-2 text-gray-900 text-xs">
                          {(allOrdersPage - 1) * allOrdersPageSize + index + 1}
                        </td>
                        <td className="p-2 text-xs">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            order.order_status === 'completed' ? 'bg-green-100 text-green-800' :
                            order.order_status === 'failed' ? 'bg-red-100 text-red-800' :
                            order.order_status === 'processing' ? 'bg-yellow-100 text-yellow-800' :
                            order.order_status === 'submitted' ? 'bg-blue-100 text-blue-800' :
                            order.order_status === 'check_failed' ? 'bg-red-100 text-red-800' :
                            order.order_status === 'paused' ? 'bg-orange-100 text-orange-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {order.order_status === 'completed' ? '已完成' :
                             order.order_status === 'failed' ? '预定失败' :
                             order.order_status === 'processing' ? '处理中' :
                             order.order_status === 'submitted' ? '已提交' :
                             order.order_status === 'check_failed' ? '验证失败' :
                             order.order_status === 'paused' ? '已暂停' :
                             '待提交'}
                          </span>
                        </td>
                        <td className="p-2 text-gray-900 text-xs">
                          {order.fail_reason || '-'}
                        </td>
                        <td className="p-2 text-gray-900 text-xs">
                          {order.traveler_full_name || '-'}
                        </td>
                        <td className="p-2 text-gray-900 text-xs">
                          {order.traveler_surname || '-'}
                        </td>
                        <td className="p-2 text-gray-900 text-xs">
                          {order.traveler_given_name || '-'}
                        </td>
                        <td className="p-2 text-gray-900 text-xs">
                          {order.nationality || '-'}
                        </td>
                        <td className="p-2 text-gray-900 text-xs">
                          {order.gender || '-'}
                        </td>
                        <td className="p-2 text-gray-900 text-xs">
                          {formatDateForDisplay(order.birth_date)}
                        </td>
                        <td className="p-2 text-gray-900 text-xs">
                          {order.id_type || '-'}
                        </td>
                        <td className="p-2 text-gray-900 text-xs">
                          {order.id_number || '-'}
                        </td>
                        <td className="p-2 text-gray-900 text-xs">
                          {formatDateForDisplay(order.id_expiry_date)}
                        </td>
                        <td className="p-2 text-gray-900 text-xs">
                          {order.mobile_country_code || '-'}
                        </td>
                        <td className="p-2 text-gray-900 text-xs">
                          {order.mobile_phone || '-'}
                        </td>
                        <td className="p-2 text-gray-900 text-xs">
                          {formatDateForDisplay(order.travel_date)}
                        </td>
                        <td className="p-2 text-gray-900 text-xs">
                          {order.departure_airport || '-'}
                        </td>
                        <td className="p-2 text-gray-900 text-xs">
                          {order.arrival_airport || '-'}
                        </td>
                        <td className="p-2 text-gray-900 text-xs">
                          {order.flight_number || '-'}
                        </td>
                        <td className="p-2 text-gray-900 text-xs">
                          {formatTimeToHourMinute(order.departure_time)}
                        </td>
                        <td className="p-2 text-gray-900 text-xs">
                          {formatTimeToHourMinute(order.arrival_time)}
                        </td>
                        <td className="p-2 text-gray-900 text-xs">
                          {order.trip_submission_item || '-'}
                        </td>
                        <td className="p-2 text-gray-900 text-xs">
                          {order.contact_person || '-'}
                        </td>
                        <td className="p-2 text-gray-900 text-xs">
                          {order.contact_mobile_phone || '-'}
                        </td>
                        <td className="p-2 text-gray-900 text-xs">
                          {order.contact_email || '-'}
                        </td>
                        <td className="p-2 text-gray-900 text-xs">
                          {order.approver || '-'}
                        </td>
                        <td className="p-2 text-gray-900 text-xs">
                          {order.insurance_name || '-'}
                        </td>
                        <td className="p-2 text-gray-900 text-xs">
                          {order.company_name || '-'}
                        </td>
                        <td className="p-2 text-gray-900 text-xs">
                          {order.booking_agent || '-'}
                        </td>
                        <td className="p-2 text-gray-900 text-xs">
                          {order.ticket_sms || '-'}
                        </td>
                        <td className="p-2 text-gray-900 text-xs">
                          {formatAmount(order.amount)}
                        </td>
                        <td className="p-2 text-gray-900 text-xs">
                          {order.order_number || '-'}
                        </td>
                        <td className="p-2 text-gray-900 text-xs">
                          {order.bill_number || '-'}
                        </td>
                        <td className="text-center p-2 sticky right-0 bg-white border-l border-gray-200 z-10 shadow-[-4px_0_8px_rgba(0,0,0,0.1)]">
                          <div className="flex items-center justify-center gap-1">
                            <button
                              onClick={() => handleViewOrder(order)}
                              className="p-1 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors"
                              title="查看详情"
                            >
                              <Eye className="h-4 w-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>


              {/* 分页 - 始终显示 */}
              <div className="flex items-center justify-between p-4 border-t border-gray-200">
                <div className="text-sm text-gray-700">
                  {allOrdersTotal > 0 ? (
                    <>显示第 {(allOrdersPage - 1) * allOrdersPageSize + 1} 到 {Math.min(allOrdersPage * allOrdersPageSize, allOrdersTotal)} 条，共 {allOrdersTotal} 条记录</>
                  ) : (
                    <>共 0 条记录</>
                  )}
                </div>
                <div className="flex items-center space-x-4">
                  {/* 每页显示数量选择 */}
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-700">每页显示:</span>
                    <select
                      value={allOrdersPageSize}
                      onChange={(e) => {
                        const newPageSize = parseInt(e.target.value);
                        setAllOrdersPageSize(newPageSize);
                        // 页面重置和数据重载由useEffect自动处理
                      }}
                      className="text-sm border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      disabled={allOrdersLoading}
                    >
                      <option value={5}>5条</option>
                      <option value={10}>10条</option>
                      <option value={20}>20条</option>
                      <option value={50}>50条</option>
                    </select>
                  </div>

                  {/* 分页导航 - 只在有多页时显示 */}
                  {Math.ceil(allOrdersTotal / allOrdersPageSize) > 1 && (
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => loadAllOrders(1)}
                        disabled={allOrdersPage <= 1 || allOrdersLoading}
                      >
                        首页
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => loadAllOrders(allOrdersPage - 1)}
                        disabled={allOrdersPage <= 1 || allOrdersLoading}
                      >
                        上一页
                      </Button>

                      {/* 页码显示 */}
                      <div className="flex items-center space-x-1">
                        {(() => {
                          const totalPages = Math.ceil(allOrdersTotal / allOrdersPageSize);
                          const pages = [];
                          const maxVisiblePages = 5;

                          let startPage = Math.max(1, allOrdersPage - Math.floor(maxVisiblePages / 2));
                          let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

                          if (endPage - startPage + 1 < maxVisiblePages) {
                            startPage = Math.max(1, endPage - maxVisiblePages + 1);
                          }

                          for (let i = startPage; i <= endPage; i++) {
                            pages.push(
                              <button
                                key={i}
                                onClick={() => loadAllOrders(i)}
                                disabled={allOrdersLoading}
                                className={`px-3 py-1 text-sm border rounded ${
                                  i === allOrdersPage
                                    ? 'bg-blue-600 text-white border-blue-600'
                                    : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                                }`}
                              >
                                {i}
                              </button>
                            );
                          }

                          return pages;
                        })()}
                      </div>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => loadAllOrders(allOrdersPage + 1)}
                        disabled={allOrdersPage >= Math.ceil(allOrdersTotal / allOrdersPageSize) || allOrdersLoading}
                      >
                        下一页
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => loadAllOrders(Math.ceil(allOrdersTotal / allOrdersPageSize))}
                        disabled={allOrdersPage >= Math.ceil(allOrdersTotal / allOrdersPageSize) || allOrdersLoading}
                      >
                        末页
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </>
          ) : (
            <div className="text-center py-8">
              <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">暂无订单数据</p>
            </div>
          )}
        </div>
      </Card>

      {/* 详情弹窗 */}
      {showDetailModal && viewingOrder && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* 背景遮罩 */}
          <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" onClick={() => setShowDetailModal(false)}></div>

          {/* 弹窗内容 */}
          <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden z-50 mx-4">
            {/* 弹窗头部 */}
            <div className="flex justify-between items-center px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-white">
              <h2 className="text-xl font-semibold text-blue-700 flex items-center">
                <Eye className="h-5 w-5 mr-2" />
                飞机票订单详情
              </h2>
              <button
                onClick={() => setShowDetailModal(false)}
                className="h-8 w-8 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 flex items-center justify-center transition-colors"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            {/* 弹窗内容 */}
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-180px)]">
              <div className="space-y-8">
                {/* 订单状态区域 */}
                <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                  <div className="flex flex-col gap-3">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-medium text-gray-900">订单状态</h3>
                      <div className="flex items-center">
                        {getOrderStatusDisplay(viewingOrder.order_status)}
                      </div>
                    </div>

                    {/* 失败原因展示 */}
                    {(viewingOrder.order_status === 'check_failed' || viewingOrder.order_status === 'failed') && viewingOrder.fail_reason && (
                      <div className="text-red-600 text-sm">
                        {viewingOrder.fail_reason}
                      </div>
                    )}
                  </div>
                </div>

                {/* 出行人信息 */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200">出行人信息</h3>
                  <div className="overflow-hidden border border-gray-300 rounded-lg">
                    <table className="w-full">
                      <tbody>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">出行人姓名 <span className="text-red-500">*</span></label>
                          </td>
                          <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                            <span className="text-sm text-gray-900">{viewingOrder.traveler_full_name || '-'}</span>
                          </td>
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">出行人姓</label>
                          </td>
                          <td className="py-3 px-4 w-1/4">
                            <span className="text-sm text-gray-900">{viewingOrder.traveler_surname || '-'}</span>
                          </td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">出行人名</label>
                          </td>
                          <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                            <span className="text-sm text-gray-900">{viewingOrder.traveler_given_name || '-'}</span>
                          </td>
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">国籍</label>
                          </td>
                          <td className="py-3 px-4 w-1/4">
                            <span className="text-sm text-gray-900">{viewingOrder.nationality || '-'}</span>
                          </td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">性别</label>
                          </td>
                          <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                            <span className="text-sm text-gray-900">{viewingOrder.gender || '-'}</span>
                          </td>
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">出生日期</label>
                          </td>
                          <td className="py-3 px-4 w-1/4">
                            <span className="text-sm text-gray-900">{formatDateForDisplay(viewingOrder.birth_date)}</span>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>

                {/* 证件信息 */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200">证件信息</h3>
                  <div className="overflow-hidden border border-gray-300 rounded-lg">
                    <table className="w-full">
                      <tbody>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">证件类型 <span className="text-red-500">*</span></label>
                          </td>
                          <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                            <span className="text-sm text-gray-900">{viewingOrder.id_type || '-'}</span>
                          </td>
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">证件号码 <span className="text-red-500">*</span></label>
                          </td>
                          <td className="py-3 px-4 w-1/4">
                            <span className="text-sm text-gray-900">{viewingOrder.id_number || '-'}</span>
                          </td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">证件有效期至</label>
                          </td>
                          <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                            <span className="text-sm text-gray-900">{formatDateForDisplay(viewingOrder.id_expiry_date)}</span>
                          </td>
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">手机号国际区号</label>
                          </td>
                          <td className="py-3 px-4 w-1/4">
                            <span className="text-sm text-gray-900">{viewingOrder.mobile_country_code || '-'}</span>
                          </td>
                        </tr>
                        <tr>
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">手机号 <span className="text-red-500">*</span></label>
                          </td>
                          <td className="py-3 px-4 w-3/4" colSpan={3}>
                            <span className="text-sm text-gray-900">{viewingOrder.mobile_phone || '-'}</span>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>

                {/* 航班信息 */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200">航班信息</h3>
                  <div className="overflow-hidden border border-gray-300 rounded-lg">
                    <table className="w-full">
                      <tbody>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">出行日期 <span className="text-red-500">*</span></label>
                          </td>
                          <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                            <span className="text-sm text-gray-900">{formatDateForDisplay(viewingOrder.travel_date)}</span>
                          </td>
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">航班号 <span className="text-red-500">*</span></label>
                          </td>
                          <td className="py-3 px-4 w-1/4">
                            <span className="text-sm text-blue-600 font-medium">{viewingOrder.flight_number || '-'}</span>
                          </td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">出发机场 <span className="text-red-500">*</span></label>
                          </td>
                          <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                            <span className="text-sm text-gray-900">{viewingOrder.departure_airport || '-'}</span>
                          </td>
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">到达机场 <span className="text-red-500">*</span></label>
                          </td>
                          <td className="py-3 px-4 w-1/4">
                            <span className="text-sm text-gray-900">{viewingOrder.arrival_airport || '-'}</span>
                          </td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">出发时间 <span className="text-red-500">*</span></label>
                          </td>
                          <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                            <span className="text-sm text-gray-900">{formatTimeToHourMinute(viewingOrder.departure_time)}</span>
                          </td>
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">到达时间 <span className="text-red-500">*</span></label>
                          </td>
                          <td className="py-3 px-4 w-1/4">
                            <span className="text-sm text-gray-900">{formatTimeToHourMinute(viewingOrder.arrival_time)}</span>
                          </td>
                        </tr>
                        <tr>
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">行程提交项</label>
                          </td>
                          <td className="py-3 px-4 w-3/4" colSpan={3}>
                            <span className="text-sm text-gray-900">{viewingOrder.trip_submission_item || '-'}</span>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>

                {/* 联系人信息 */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200">联系人信息</h3>
                  <div className="overflow-hidden border border-gray-300 rounded-lg">
                    <table className="w-full">
                      <tbody>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">联系人 <span className="text-red-500">*</span></label>
                          </td>
                          <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                            <span className="text-sm text-gray-900">{viewingOrder.contact_person || '-'}</span>
                          </td>
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">联系人手机号 <span className="text-red-500">*</span></label>
                          </td>
                          <td className="py-3 px-4 w-1/4">
                            <span className="text-sm text-gray-900">{viewingOrder.contact_mobile_phone || '-'}</span>
                          </td>
                        </tr>
                        <tr>
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">联系人邮箱</label>
                          </td>
                          <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                            <span className="text-sm text-gray-900">{viewingOrder.contact_email || '-'}</span>
                          </td>
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">审批参照人</label>
                          </td>
                          <td className="py-3 px-4 w-1/4">
                            <span className="text-sm text-gray-900">{viewingOrder.approver || '-'}</span>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>

                {/* 其他信息 */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200">其他信息</h3>
                  <div className="overflow-hidden border border-gray-300 rounded-lg">
                    <table className="w-full">
                      <tbody>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">保险名称</label>
                          </td>
                          <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                            <span className="text-sm text-gray-900">{viewingOrder.insurance_name || '-'}</span>
                          </td>
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">公司名称</label>
                          </td>
                          <td className="py-3 px-4 w-1/4">
                            <span className="text-sm text-gray-900">{viewingOrder.company_name || '-'}</span>
                          </td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">代订人</label>
                          </td>
                          <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                            <span className="text-sm text-gray-900">{viewingOrder.booking_agent || '-'}</span>
                          </td>
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">出票短信</label>
                          </td>
                          <td className="py-3 px-4 w-1/4">
                            <span className="text-sm text-gray-900">{viewingOrder.ticket_sms || '-'}</span>
                          </td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">金额</label>
                          </td>
                          <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                            <span className="text-sm text-gray-900">{formatAmount(viewingOrder.amount)}</span>
                          </td>
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">订单号</label>
                          </td>
                          <td className="py-3 px-4 w-1/4">
                            <span className="text-sm text-gray-900">{viewingOrder.order_number || '-'}</span>
                          </td>
                        </tr>
                        <tr>
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">账单号</label>
                          </td>
                          <td className="py-3 px-4 w-3/4" colSpan={3}>
                            <span className="text-sm text-gray-900">{viewingOrder.bill_number || '-'}</span>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>

                {/* 创建时间 */}
                <div className="text-sm text-gray-500 text-right">
                  创建时间：{viewingOrder.created_at ? new Date(viewingOrder.created_at).toLocaleString('zh-CN') : '-'}
                </div>
              </div>
            </div>

            {/* 弹窗底部 */}
            <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end">
              <Button onClick={() => setShowDetailModal(false)}>
                关闭
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default FlightAllOrdersContent;
