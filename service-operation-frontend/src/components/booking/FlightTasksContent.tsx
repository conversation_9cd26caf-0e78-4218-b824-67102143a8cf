import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  Eye,
  User,
  Plane,
  FileText,
  Calendar
} from 'lucide-react';
import { ProjectTaskService } from '@/services/project';
import { ProjectTask } from '@/types/project-task';
import { getTaskFlightOrderStats, TaskOrderStatsResponse } from '@/api/flightOrder';

interface FlightTasksContentProps {
  projectId: string;
}

const FlightTasksContent: React.FC<FlightTasksContentProps> = ({ projectId }) => {
  const navigate = useNavigate();
  const [bookingTasks, setBookingTasks] = useState<ProjectTask[]>([]);
  const [bookingTasksLoading, setBookingTasksLoading] = useState(false);
  const [taskStats, setTaskStats] = useState<{[key: string]: TaskOrderStatsResponse}>({});

  // 获取任务统计信息
  const getTaskStatistics = async (taskId: string) => {
    try {
      const stats = await getTaskFlightOrderStats(taskId);
      return stats;
    } catch (error) {
      console.error('获取任务统计失败:', error);
      return {
        task_id: taskId,
        total_orders: 0,
        initial_orders: 0,
        submitted_orders: 0,
        processing_orders: 0,
        completed_orders: 0,
        failed_orders: 0
      };
    }
  };

  // 获取飞机票预订任务列表
  const loadBookingTasks = async () => {
    if (!projectId) return;
    
    setBookingTasksLoading(true);
    try {
      const response = await ProjectTaskService.getTasksByProject(parseInt(projectId), {
        page: 1,
        page_size: 100
      });
      
      // 只获取飞机票预订类型的任务，按创建时间倒序排列
      const flightTasks = response.items
        .filter((task: ProjectTask) => task.task_type === '飞机票预订')
        .sort((a: ProjectTask, b: ProjectTask) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
      
      setBookingTasks(flightTasks);

      // 获取每个飞机票任务的统计信息
      const statsPromises = flightTasks.map(async (task) => {
        const stats = await getTaskStatistics(task.task_id);
        return { taskId: task.task_id, stats };
      });

      const statsResults = await Promise.all(statsPromises);
      const statsMap = statsResults.reduce((acc, { taskId, stats }) => {
        acc[taskId] = stats;
        return acc;
      }, {} as {[key: string]: TaskOrderStatsResponse});

      setTaskStats(statsMap);
    } catch (error) {
      console.error('获取飞机票预订任务失败:', error);
    } finally {
      setBookingTasksLoading(false);
    }
  };

  useEffect(() => {
    loadBookingTasks();
  }, [projectId]);

  // 获取任务状态标签
  const getTaskStatusLabel = (task: ProjectTask, stats: TaskOrderStatsResponse) => {
    if (stats.failed_orders > 0) {
      return { label: '预定失败', color: 'bg-red-100 text-red-800' };
    }
    if (stats.processing_orders > 0) {
      return { label: '处理中', color: 'bg-yellow-100 text-yellow-800' };
    }
    if (stats.completed_orders > 0) {
      return { label: '已完成', color: 'bg-green-100 text-green-800' };
    }
    if (stats.submitted_orders > 0) {
      return { label: '已提交', color: 'bg-blue-100 text-blue-800' };
    }
    return { label: '待处理', color: 'bg-gray-100 text-gray-800' };
  };

  // 获取任务类型配置
  const getTaskTypeConfig = (taskType: string) => {
    const configs = {
      '飞机票预订': {
        icon: Plane,
        color: 'text-green-600',
        bgColor: 'bg-green-50',
        buttonColor: 'bg-green-600 hover:bg-green-700'
      }
    };
    return configs[taskType as keyof typeof configs] || configs['飞机票预订'];
  };

  // 渲染基于project_tasks表的任务卡片
  const renderProjectTaskCard = (task: ProjectTask) => {
    const stats = taskStats[task.task_id] || { 
      task_id: task.task_id,
      total_orders: 0, 
      initial_orders: 0,
      submitted_orders: 0,
      processing_orders: 0,
      completed_orders: 0, 
      failed_orders: 0 
    };
    const statusInfo = getTaskStatusLabel(task, stats);
    const typeConfig = getTaskTypeConfig(task.task_type);

    return (
      <Card key={task.task_id} className="bg-white border border-gray-200 hover:shadow-md transition-shadow">
        <div className="p-4">
          {/* 任务头部信息 */}
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className={`p-2 ${typeConfig.bgColor} rounded-lg`}>
                <typeConfig.icon className={`h-5 w-5 ${typeConfig.color}`} />
              </div>
              <div className="flex-1">
                <h3 className="font-medium text-gray-900 mb-1">{task.task_title}</h3>
                <p className="text-sm text-gray-500">{task.task_description}</p>
              </div>
            </div>
            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${statusInfo.color}`}>
              {statusInfo.label}
            </span>
          </div>

          {/* 统计信息 */}
          <div className="grid grid-cols-4 gap-4 mb-4">
            <div className="text-center">
              <div className="text-lg font-bold text-blue-600">{stats.total_orders}</div>
              <div className="text-xs text-gray-500">已提交</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-yellow-600">{stats.processing_orders}</div>
              <div className="text-xs text-gray-500">处理中</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-green-600">{stats.completed_orders}</div>
              <div className="text-xs text-gray-500">已完成</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-red-600">{stats.failed_orders}</div>
              <div className="text-xs text-gray-500">预定失败</div>
            </div>
          </div>

          {/* 任务详细信息 */}
          <div className="space-y-2 mb-4">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-500">任务ID</span>
              <span className="font-mono text-gray-900">{task.task_id}</span>
            </div>
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-500">创建人</span>
              <span className="text-gray-900">{task.creator_name || '系统'}</span>
            </div>
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-500">创建时间</span>
              <span className="text-gray-900">
                {new Date(task.created_at).toLocaleDateString('zh-CN')}
              </span>
            </div>
          </div>

          {/* 标签区域 */}
          <div className="flex items-center gap-2 mb-4">
            <div className="flex items-center gap-1 px-2 py-1 bg-blue-50 rounded text-xs text-blue-600">
              <FileText className="h-3 w-3" />
              <span>{task.task_type}</span>
            </div>
            {(task as any).has_agent && (
              <div className="flex items-center gap-1 px-1.5 py-0.5 bg-green-50 rounded text-xs text-green-600">
                <User className="h-2.5 w-2.5" />
                <span>代订</span>
              </div>
            )}
          </div>

          {/* 单个操作按钮 - 更现代的设计 */}
          <Button
            size="sm"
            className={`w-full ${typeConfig.buttonColor} text-white hover:opacity-90 transition-opacity`}
            onClick={() => {
              navigate(`/flight-task-detail/${task.task_id}`);
            }}
          >
            <Eye className="h-4 w-4 mr-1" />
            查看详情
          </Button>
        </div>
      </Card>
    );
  };

  return (
    <div className="space-y-4">
      <Card className="bg-white border border-gray-200">
        <div className="p-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
              <Calendar className="h-5 w-5 text-green-600" />
              飞机票预订任务
            </h3>
          </div>

          {bookingTasksLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4"></div>
              <p className="text-gray-600">加载任务列表中...</p>
            </div>
          ) : bookingTasks.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {bookingTasks.map(renderProjectTaskCard)}
            </div>
          ) : (
            <div className="text-center py-8">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">暂无飞机票预订任务</h3>
              <p className="text-gray-600">还没有创建任何飞机票预订任务</p>
            </div>
          )}
        </div>
      </Card>
    </div>
  );
};

export default FlightTasksContent;
