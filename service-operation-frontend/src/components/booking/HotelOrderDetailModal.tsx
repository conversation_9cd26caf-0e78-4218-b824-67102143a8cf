import React, { useState, useCallback } from 'react';
import { X, Eye, Save, Edit } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { HotelOrder } from '@/api/hotel';
import { useToast } from '@/hooks/use-toast';
import { hotelOrderApi } from '@/api/hotel';
import { formatHotelOrderDate, formatDateForInput } from '../../utils/formatters';

// 证件类型选项定义3
const ID_TYPE_OPTIONS = [
  { value: '身份证', label: '身份证' },
  { value: '公务护照', label: '公务护照' },
  { value: '普通护照', label: '普通护照' },
  { value: '港澳通行证', label: '港澳通行证' },
  { value: '台胞证', label: '台胞证' },
  { value: '回乡证', label: '回乡证' },
  { value: '军人证', label: '军人证' },
  { value: '海员证', label: '海员证' },
  { value: '台湾通行证', label: '台湾通行证' },
  { value: '外国永久居留证', label: '外国永久居留证' },
  { value: '港澳台居民居住证', label: '港澳台居民居住证' },
  { value: '其他', label: '其他' }
];

// EditableCell组件 - 与HotelBookingContent中的实现保持一致
interface EditableCellProps {
  value: string | number | boolean | null | undefined;
  field: keyof HotelOrder;
  type?: 'text' | 'number' | 'date' | 'datetime-local' | 'email' | 'select' | 'textarea';
  options?: { value: string; label: string }[];
  isEditing: boolean;
  onFieldChange: (field: keyof HotelOrder, value: string | number | boolean) => void;
  validationErrors?: Record<string, string>;

}

const EditableCell: React.FC<EditableCellProps> = ({ 
  value, 
  field, 
  type = 'text', 
  options,
  isEditing, 
  onFieldChange, 
  validationErrors = {}
}) => {
  const error = validationErrors[field];
  
  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    let val: string | number | boolean = e.target.value;

    // 手机号国际区号字段特殊处理，保持为字符串
    if (field.includes('mobile_country_code')) {
      val = e.target.value; // 保持为字符串
    } else if (type === 'number') {
      val = parseFloat(e.target.value) || 0;
    }

    onFieldChange(field, val);
  }, [field, onFieldChange, type]);

  if (!isEditing) {
    let displayValue = value;

    // 特殊格式化处理
    if (field === 'tax_rate' && value) {
      displayValue = `${value}%`;
    }

    return (
      <div className="text-sm text-gray-900 break-words">
        {displayValue || '-'}
      </div>
    );
  }

  const inputClassName = `w-full px-2 py-1 border rounded text-sm focus:outline-none focus:ring-2 ${
    error ? 'border-red-500 focus:ring-red-500' : 'border-gray-300 focus:ring-blue-500'
  }`;

  if (type === 'select' && options) {
    return (
      <div className="w-full">
        <select
          value={String(value || '')}
          onChange={handleChange}
          className={inputClassName}
        >
          <option value="">请选择</option>
          {options.map(option => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        {error && (
          <div className="mt-1 text-red-600 text-xs">
            {error}
          </div>
        )}
      </div>
    );
  }

  if (type === 'textarea') {
    // 所有textarea字段使用统一的行数
    let textareaRows = 4; // 统一使用4行高度

    return (
      <div className="w-full">
        <textarea
          value={String(value || '')}
          onChange={handleChange}
          className={inputClassName}
          rows={textareaRows}
        />
        {error && (
          <div className="mt-1 text-red-600 text-xs">
            {error}
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="w-full">
      <input
        type={type}
        value={String(value || '')}
        onChange={handleChange}
        className={inputClassName}
      />
      {error && (
        <div className="mt-1 text-red-600 text-xs">
          {error}
        </div>
      )}
    </div>
  );
};

interface HotelOrderDetailModalProps {
  order: HotelOrder | null;
  isOpen: boolean;
  onClose: () => void;
  onOrderUpdated?: () => void;
  billingInfoOnly?: boolean; // 是否只编辑对账单信息
  hideEditButton?: boolean; // 是否隐藏编辑按钮
  defaultEditMode?: boolean; // 是否默认进入编辑模式
}

// 下拉框选项定义
const ROOM_TYPE_OPTIONS = [
  { value: '标准大床房', label: '标准大床房' },
  { value: '标准双床房', label: '标准双床房' },
  { value: '豪华大床房', label: '豪华大床房' },
  { value: '豪华双床房', label: '豪华双床房' },
  { value: '行政大床房', label: '行政大床房' },
  { value: '行政双床房', label: '行政双床房' },
  { value: '商务大床房', label: '商务大床房' },
  { value: '商务双床房', label: '商务双床房' }
];

const BREAKFAST_OPTIONS = [
  { value: '无早', label: '无早' },
  { value: '单早', label: '单早' },
  { value: '双早', label: '双早' },
  { value: '多早', label: '多早' }
];

const HALF_DAY_OPTIONS = [
  { value: '是', label: '是' },
  { value: '否', label: '否' }
];

const GROUP_BOOKING_OPTIONS = [
  { value: '团房', label: '团房' },
  { value: '非团房', label: '非团房' }
];

const AGREEMENT_TYPE_OPTIONS = [
  { value: '企业协议', label: '企业协议' },
  { value: '非协议', label: '非协议' },
  { value: '悦享协议', label: '悦享协议' }
];

const CANCELLATION_POLICY_OPTIONS = [
  { value: '免费取消', label: '免费取消' },
  { value: '不可取消', label: '不可取消' },
  { value: '限时取消', label: '限时取消' }
];

const VIOLATION_OPTIONS = [
  { value: '是', label: '是' },
  { value: '否', label: '否' }
];

const PAYMENT_METHOD_OPTIONS = [
  { value: '现付', label: '现付' },
  { value: '预付', label: '预付' }
];

const INVOICE_TYPE_OPTIONS = [
  { value: '普票', label: '普票' },
  { value: '专票', label: '专票' }
];

const TAX_RATE_OPTIONS = [
  { value: '1', label: '1%' },
  { value: '3', label: '3%' },
  { value: '6', label: '6%' }
];

const HotelOrderDetailModal: React.FC<HotelOrderDetailModalProps> = ({
  order,
  isOpen,
  onClose,
  onOrderUpdated,
  billingInfoOnly = false,
  hideEditButton = false,
  defaultEditMode = false
}) => {
  const { toast } = useToast();
  const [isEditMode, setIsEditMode] = useState(false);
  const [editingOrder, setEditingOrder] = useState<HotelOrder | null>(null);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [saving, setSaving] = useState(false);

  // 当order变化时重置状态
  React.useEffect(() => {
    if (order) {
      setEditingOrder({ ...order });
      setIsEditMode(billingInfoOnly); // 对账单模式自动进入编辑模式
      setValidationErrors({});
    }
  }, [order, billingInfoOnly]);

  const handleFieldChange = useCallback((field: keyof HotelOrder, value: string | number | boolean) => {
    setEditingOrder(prev => {
      if (!prev) return prev;
      return { ...prev, [field]: value };
    });
    
    // 清除该字段的验证错误
    setValidationErrors(prev => {
      if (prev[field]) {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      }
      return prev;
    });
  }, []);

  const getOrderStatusDisplay = (status: string) => {
    const statusConfig = {
      'check_failed': { text: '验证失败', color: 'bg-red-100 text-red-700' },
      'initial': { text: '待提交', color: 'bg-gray-100 text-gray-600' },
      'submitted': { text: '已提交', color: 'bg-blue-100 text-blue-700' },
      'processing': { text: '处理中', color: 'bg-cyan-100 text-cyan-700' },
      'completed': { text: '预定完成', color: 'bg-green-100 text-green-700' },
      'failed': { text: '预定失败', color: 'bg-red-100 text-red-700' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || { text: status, color: 'bg-gray-100 text-gray-600' };
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.text}
      </span>
    );
  };

  const validateAllFields = (order: HotelOrder): Record<string, string> => {
    const errors: Record<string, string> = {};
    
    // 基础必填字段验证
    const requiredFields = [
      { field: 'guest_full_name', name: '入住人姓名' },
      { field: 'guest_id_type', name: '入住人证件类型' },
      { field: 'guest_id_number', name: '入住人证件号码' },
      { field: 'guest_mobile_phone', name: '入住人手机号' },
      { field: 'check_in_time', name: '入住时间' },
      { field: 'check_out_time', name: '离店时间' },
      { field: 'hotel_id', name: '酒店ID' },
      { field: 'room_type', name: '房间类型' },
      { field: 'contact_person', name: '联系人' },
      { field: 'contact_mobile_phone', name: '联系人手机号' },
      { field: 'approver', name: '审批人' }
    ];

    for (const { field, name } of requiredFields) {
      const value = (order as any)[field];
      if (!value || String(value).trim() === '') {
        errors[field] = `${name}不能为空`;
      }
    }

    // 手机号格式验证
    if (order.guest_mobile_phone && !/^1[3-9]\d{9}$/.test(order.guest_mobile_phone)) {
      errors.guest_mobile_phone = '入住人手机号格式不正确';
    }
    if (order.contact_mobile_phone && !/^1[3-9]\d{9}$/.test(order.contact_mobile_phone)) {
      errors.contact_mobile_phone = '联系人手机号格式不正确';
    }

    // 邮箱格式验证
    if (order.guest_email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(order.guest_email)) {
      errors.guest_email = '邮箱格式不正确';
    }

    // 入住人证件有效期条件必填验证：证件类型不为身份证时必填
    if (order.guest_id_type && order.guest_id_type !== '身份证') {
      if (!order.guest_id_expiry_date || String(order.guest_id_expiry_date).trim() === '') {
        errors.guest_id_expiry_date = '证件类型不为身份证时，入住人证件有效期至不能为空';
      }
    }

    // 同住人字段条件必填验证：同住人姓名填写后其他字段必填
    if (order.roommate_name && order.roommate_name.trim() !== '') {
      const roommateBasicRequiredFields = [
        { field: 'roommate_surname', name: '同住人姓' },
        { field: 'roommate_given_name', name: '同住人名' },
        { field: 'roommate_nationality', name: '同住人国籍' },
        { field: 'roommate_gender', name: '同住人性别' },
        { field: 'roommate_birth_date', name: '同住人出生日期' },
        { field: 'roommate_id_type', name: '同住人证件类型' },
        { field: 'roommate_id_number', name: '同住人证件号码' },
        { field: 'roommate_mobile_country_code', name: '同住人手机号国际区号' },
        { field: 'roommate_mobile_phone', name: '同住人手机号' }
      ];

      for (const { field, name } of roommateBasicRequiredFields) {
        const value = (order as any)[field];
        if (!value || String(value).trim() === '') {
          errors[field] = `${name}不能为空`;
        }
      }

      // 同住人证件有效期条件必填：证件类型不为身份证时必填
      if (order.roommate_id_type && order.roommate_id_type !== '身份证') {
        if (!order.roommate_id_expiry_date || String(order.roommate_id_expiry_date).trim() === '') {
          errors.roommate_id_expiry_date = '同住人证件类型不为身份证时，同住人证件有效期至不能为空';
        }
      }
    }

    return errors;
  };

  const saveChanges = async () => {
    if (!editingOrder) return;

    // 验证字段
    const errors = validateAllFields(editingOrder);
    if (Object.keys(errors).length > 0) {
      setValidationErrors(errors);
      toast({
        title: "验证失败",
        description: "请检查并修正表单中的错误",
        variant: "destructive",
      });
      return;
    }

    setSaving(true);
    try {
      let updateData;

      if (billingInfoOnly) {
        // 对账单模式：只更新对账单相关字段，不更改订单状态
        updateData = {
          company_name: editingOrder.company_name,
          booking_agent: editingOrder.booking_agent,
          order_number: editingOrder.order_number,
          bill_number: editingOrder.bill_number,
          amount: editingOrder.amount || undefined,
        };
      } else {
        // 完整编辑模式：更新所有字段
        updateData = {
          ...editingOrder,
          amount: editingOrder.amount || undefined,
          room_count: editingOrder.room_count || undefined
        };
      }

      await hotelOrderApi.updateOrder(editingOrder.id, updateData);
      toast({
        title: "保存成功",
        description: billingInfoOnly ? "对账单信息已更新" : "订单信息已更新",
      });
      setIsEditMode(false);
      onOrderUpdated?.();
    } catch (error) {
      console.error('保存订单失败:', error);
      toast({
        title: "保存失败",
        description: "更新订单信息时发生错误",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  if (!isOpen || !order || !editingOrder) {
    return null;
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* 背景遮罩 */}
      <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" onClick={onClose}></div>
      
      {/* 弹窗内容 */}
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden z-50 mx-4">
        {/* 弹窗头部 */}
        <div className="flex justify-between items-center px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-white">
          <h2 className="text-xl font-semibold text-blue-700 flex items-center">
            <Eye className="h-5 w-5 mr-2" />
            {billingInfoOnly ? '编辑对账单信息' : '酒店订单详情'} {isEditMode && <span className="ml-2 text-sm text-orange-600">(编辑模式)</span>}
          </h2>
          <button 
            onClick={onClose}
            className="h-8 w-8 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 flex items-center justify-center transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>
        
        {/* 弹窗内容 */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-180px)]">
          <div className="space-y-8">
            {/* 订单状态区域 */}
            <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
              <div className="flex flex-col gap-3">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium text-gray-900">订单状态</h3>
                  <div className="flex items-center">
                    {getOrderStatusDisplay(order.order_status)}
                  </div>
                </div>
                
                {/* 失败原因展示 */}
                {(order.order_status === 'check_failed' || order.order_status === 'failed') && order.fail_reason && (
                  <div className="text-red-600 text-sm">
                    {order.fail_reason}
                  </div>
                )}
              </div>
            </div>

            {/* 入住人信息 */}
            {!billingInfoOnly && (
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200">入住人信息</h3>
              <div className="overflow-hidden border border-gray-300 rounded-lg">
                <table className="w-full">
                  <tbody>
                    <tr className="border-b border-gray-200">
                      <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">入住人姓名 <span className="text-red-500">*</span></label>
                      </td>
                      <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                        <EditableCell 
                          value={editingOrder.guest_full_name} 
                          field="guest_full_name" 
                          isEditing={isEditMode} 
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                      <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">入住人姓</label>
                      </td>
                      <td className="py-3 px-4 w-1/4">
                        <EditableCell 
                          value={editingOrder.guest_surname} 
                          field="guest_surname" 
                          isEditing={isEditMode} 
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                    </tr>
                    <tr className="border-b border-gray-200">
                      <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">入住人名</label>
                      </td>
                      <td className="py-3 px-4 border-r border-gray-200">
                        <EditableCell 
                          value={editingOrder.guest_given_name} 
                          field="guest_given_name" 
                          isEditing={isEditMode} 
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                      <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">入住人国籍</label>
                      </td>
                      <td className="py-3 px-4">
                        <EditableCell 
                          value={editingOrder.guest_nationality} 
                          field="guest_nationality" 
                          isEditing={isEditMode} 
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                    </tr>
                    <tr className="border-b border-gray-200">
                      <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">入住人性别</label>
                      </td>
                      <td className="py-3 px-4 border-r border-gray-200">
                        <EditableCell 
                          value={editingOrder.guest_gender} 
                          field="guest_gender" 
                          isEditing={isEditMode} 
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                      <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">入住人出生日期</label>
                      </td>
                      <td className="py-3 px-4">
                        <EditableCell 
                          value={editingOrder.guest_birth_date} 
                          field="guest_birth_date" 
                          type="date" 
                          isEditing={isEditMode} 
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                    </tr>
                    <tr className="border-b border-gray-200">
                      <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">入住人证件类型 <span className="text-red-500">*</span></label>
                      </td>
                      <td className="py-3 px-4 border-r border-gray-200">
                        <EditableCell
                          value={editingOrder.guest_id_type}
                          field="guest_id_type"
                          type="select"
                          options={ID_TYPE_OPTIONS}
                          isEditing={isEditMode}
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                      <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">入住人证件号码 <span className="text-red-500">*</span></label>
                      </td>
                      <td className="py-3 px-4">
                        <EditableCell 
                          value={editingOrder.guest_id_number} 
                          field="guest_id_number" 
                          isEditing={isEditMode} 
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                    </tr>
                    <tr className="border-b border-gray-200">
                      <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">入住人证件有效期至</label>
                      </td>
                      <td className="py-3 px-4 border-r border-gray-200">
                        <EditableCell 
                          value={editingOrder.guest_id_expiry_date} 
                          field="guest_id_expiry_date" 
                          type="date" 
                          isEditing={isEditMode} 
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                      <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">入住人手机号国际区号</label>
                      </td>
                      <td className="py-3 px-4">
                        <EditableCell 
                          value={editingOrder.guest_mobile_country_code} 
                          field="guest_mobile_country_code" 
                          isEditing={isEditMode} 
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                    </tr>
                    <tr className="border-b border-gray-200">
                      <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">入住人手机号 <span className="text-red-500">*</span></label>
                      </td>
                      <td className="py-3 px-4 border-r border-gray-200">
                        <EditableCell 
                          value={editingOrder.guest_mobile_phone} 
                          field="guest_mobile_phone" 
                          isEditing={isEditMode} 
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                      <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">入住人邮箱</label>
                      </td>
                      <td className="py-3 px-4">
                        <EditableCell 
                          value={editingOrder.guest_email} 
                          field="guest_email" 
                          type="email"
                          isEditing={isEditMode} 
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
            )}

            {/* 入住信息 */}
            {!billingInfoOnly && (
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200">入住信息</h3>
              <div className="overflow-hidden border border-gray-300 rounded-lg">
                <table className="w-full">
                  <tbody>
                    <tr className="border-b border-gray-200">
                      <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">目的地 <span className="text-red-500">*</span></label>
                      </td>
                      <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                        <EditableCell 
                          value={editingOrder.destination} 
                          field="destination" 
                          isEditing={isEditMode} 
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                      <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">酒店ID <span className="text-red-500">*</span></label>
                      </td>
                      <td className="py-3 px-4 w-1/4">
                        <EditableCell 
                          value={editingOrder.hotel_id} 
                          field="hotel_id" 
                          isEditing={isEditMode} 
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                    </tr>
                    <tr className="border-b border-gray-200">
                      <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">预订房型 <span className="text-red-500">*</span></label>
                      </td>
                      <td className="py-3 px-4 border-r border-gray-200">
                        <EditableCell
                          value={editingOrder.room_type}
                          field="room_type"
                          type="select"
                          options={ROOM_TYPE_OPTIONS}
                          isEditing={isEditMode}
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                      <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">预订房间数量</label>
                      </td>
                      <td className="py-3 px-4">
                        <EditableCell 
                          value={editingOrder.room_count} 
                          field="room_count" 
                          type="number"
                          isEditing={isEditMode} 
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                    </tr>
                    <tr className="border-b border-gray-200">
                      <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">政策名称</label>
                      </td>
                      <td className="py-3 px-4 border-r border-gray-200">
                        <EditableCell 
                          value={editingOrder.policy_name} 
                          field="policy_name" 
                          isEditing={isEditMode} 
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                      <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">是否含早</label>
                      </td>
                      <td className="py-3 px-4">
                        <EditableCell
                          value={editingOrder.include_breakfast}
                          field="include_breakfast"
                          type="select"
                          options={BREAKFAST_OPTIONS}
                          isEditing={isEditMode}
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                    </tr>
                    <tr className="border-b border-gray-200">
                      <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">是否为半日房</label>
                      </td>
                      <td className="py-3 px-4 border-r border-gray-200">
                        <EditableCell
                          value={editingOrder.is_half_day_room}
                          field="is_half_day_room"
                          type="select"
                          options={HALF_DAY_OPTIONS}
                          isEditing={isEditMode}
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                      <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">入住时间 <span className="text-red-500">*</span></label>
                      </td>
                      <td className="py-3 px-4">
                        <EditableCell
                          value={formatDateForInput(editingOrder.check_in_time)}
                          field="check_in_time"
                          type="date"
                          isEditing={isEditMode}
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                    </tr>
                    <tr className="border-b border-gray-200">
                      <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">离店时间 <span className="text-red-500">*</span></label>
                      </td>
                      <td className="py-3 px-4 border-r border-gray-200">
                        <EditableCell
                          value={formatDateForInput(editingOrder.check_out_time)}
                          field="check_out_time"
                          type="date"
                          isEditing={isEditMode}
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                      <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">是否为团房</label>
                      </td>
                      <td className="py-3 px-4">
                        <EditableCell
                          value={editingOrder.is_group_booking}
                          field="is_group_booking"
                          type="select"
                          options={GROUP_BOOKING_OPTIONS}
                          isEditing={isEditMode}
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                    </tr>
                    <tr className="border-b border-gray-200">
                      <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">团房名称</label>
                      </td>
                      <td className="py-3 px-4 border-r border-gray-200">
                        <EditableCell 
                          value={editingOrder.group_booking_name} 
                          field="group_booking_name" 
                          isEditing={isEditMode} 
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                      <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">房间号</label>
                      </td>
                      <td className="py-3 px-4">
                        <EditableCell 
                          value={editingOrder.room_number} 
                          field="room_number" 
                          isEditing={isEditMode} 
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                    </tr>
                    <tr className="border-b border-gray-200">
                      <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">支付方式</label>
                      </td>
                      <td className="py-3 px-4 border-r border-gray-200">
                        <EditableCell
                          value={editingOrder.payment_method}
                          field="payment_method"
                          type="select"
                          options={PAYMENT_METHOD_OPTIONS}
                          isEditing={isEditMode}
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                      <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">发票类型</label>
                      </td>
                      <td className="py-3 px-4">
                        <EditableCell
                          value={editingOrder.invoice_type}
                          field="invoice_type"
                          type="select"
                          options={INVOICE_TYPE_OPTIONS}
                          isEditing={isEditMode}
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                    </tr>
                    <tr className="border-b border-gray-200">
                      <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">税率</label>
                      </td>
                      <td className="py-3 px-4 border-r border-gray-200">
                        <EditableCell
                          value={editingOrder.tax_rate}
                          field="tax_rate"
                          type="select"
                          options={TAX_RATE_OPTIONS}
                          isEditing={isEditMode}
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                      <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">协议类型</label>
                      </td>
                      <td className="py-3 px-4">
                        <EditableCell
                          value={editingOrder.agreement_type}
                          field="agreement_type"
                          type="select"
                          options={AGREEMENT_TYPE_OPTIONS}
                          isEditing={isEditMode}
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                    </tr>
                    <tr className="border-b border-gray-200">
                      <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">供应商名称</label>
                      </td>
                      <td className="py-3 px-4 border-r border-gray-200">
                        <EditableCell 
                          value={editingOrder.supplier_name} 
                          field="supplier_name" 
                          isEditing={isEditMode} 
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                      <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">支付渠道</label>
                      </td>
                      <td className="py-3 px-4">
                        <EditableCell 
                          value={editingOrder.payment_channel} 
                          field="payment_channel" 
                          isEditing={isEditMode} 
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                    </tr>
                    <tr className="border-b border-gray-200">
                      <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">支付流水号</label>
                      </td>
                      <td className="py-3 px-4" colSpan={3}>
                        <EditableCell
                          value={editingOrder.payment_transaction_id}
                          field="payment_transaction_id"
                          isEditing={isEditMode}
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                    </tr>
                    <tr className="border-b border-gray-200">
                      <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">对供成本（每间房成本）</label>
                      </td>
                      <td className="py-3 px-4 border-r border-gray-200">
                        <EditableCell
                          value={editingOrder.cost_per_room}
                          field="cost_per_room"
                          type="text"
                          isEditing={isEditMode}
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                      <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">隐藏手续费</label>
                      </td>
                      <td className="py-3 px-4">
                        <EditableCell
                          value={editingOrder.hidden_service_fee}
                          field="hidden_service_fee"
                          type="text"
                          isEditing={isEditMode}
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                    </tr>
                    <tr className="border-b border-gray-200">
                      <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">取消规则</label>
                      </td>
                      <td className="py-3 px-4 border-r border-gray-200">
                        <EditableCell
                          value={editingOrder.cancellation_policy}
                          field="cancellation_policy"
                          type="select"
                          options={CANCELLATION_POLICY_OPTIONS}
                          isEditing={isEditMode}
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                      <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">审批人 <span className="text-red-500">*</span></label>
                      </td>
                      <td className="py-3 px-4">
                        <EditableCell
                          value={editingOrder.approver}
                          field="approver"
                          isEditing={isEditMode}
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                    </tr>
                    <tr className="border-b border-gray-200">
                      <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">是否违规</label>
                      </td>
                      <td className="py-3 px-4 border-r border-gray-200">
                        <EditableCell
                          value={editingOrder.is_violation}
                          field="is_violation"
                          type="select"
                          options={VIOLATION_OPTIONS}
                          isEditing={isEditMode}
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                      <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">联系人姓名 <span className="text-red-500">*</span></label>
                      </td>
                      <td className="py-3 px-4">
                        <EditableCell 
                          value={editingOrder.contact_person} 
                          field="contact_person" 
                          isEditing={isEditMode} 
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                    </tr>
                    <tr className="border-b border-gray-200">
                      <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">联系人手机号国际区号</label>
                      </td>
                      <td className="py-3 px-4 border-r border-gray-200">
                        <EditableCell 
                          value={editingOrder.contact_mobile_country_code} 
                          field="contact_mobile_country_code" 
                          isEditing={isEditMode} 
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                      <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">联系人手机号 <span className="text-red-500">*</span></label>
                      </td>
                      <td className="py-3 px-4">
                        <EditableCell 
                          value={editingOrder.contact_mobile_phone} 
                          field="contact_mobile_phone" 
                          isEditing={isEditMode} 
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                    </tr>
                    <tr className="border-b border-gray-200">
                      <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">订单备注</label>
                      </td>
                      <td className="py-3 px-4" colSpan={3}>
                        <EditableCell 
                          value={editingOrder.order_remarks} 
                          field="order_remarks" 
                          type="textarea"
                          isEditing={isEditMode} 
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                    </tr>

                    <tr>
                      <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">行程提交项</label>
                      </td>
                      <td className="py-3 px-4" colSpan={3}>
                        <EditableCell
                          value={editingOrder.trip_submission_item}
                          field="trip_submission_item"
                          type="textarea"
                          isEditing={isEditMode}
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
            )}

            {/* 同住人信息 */}
            {!billingInfoOnly && (
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200">同住人信息</h3>
              <div className="overflow-hidden border border-gray-300 rounded-lg">
                <table className="w-full">
                  <tbody>
                    <tr className="border-b border-gray-200">
                      <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">同住人姓名</label>
                      </td>
                      <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                        <EditableCell 
                          value={editingOrder.roommate_name} 
                          field="roommate_name" 
                          isEditing={isEditMode} 
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                      <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">同住人姓</label>
                      </td>
                      <td className="py-3 px-4 w-1/4">
                        <EditableCell 
                          value={editingOrder.roommate_surname} 
                          field="roommate_surname" 
                          isEditing={isEditMode} 
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                    </tr>
                    <tr className="border-b border-gray-200">
                      <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">同住人名</label>
                      </td>
                      <td className="py-3 px-4 border-r border-gray-200">
                        <EditableCell 
                          value={editingOrder.roommate_given_name} 
                          field="roommate_given_name" 
                          isEditing={isEditMode} 
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                      <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">同住人国籍</label>
                      </td>
                      <td className="py-3 px-4">
                        <EditableCell 
                          value={editingOrder.roommate_nationality} 
                          field="roommate_nationality" 
                          isEditing={isEditMode} 
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                    </tr>
                    <tr className="border-b border-gray-200">
                      <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">同住人性别</label>
                      </td>
                      <td className="py-3 px-4 border-r border-gray-200">
                        <EditableCell 
                          value={editingOrder.roommate_gender} 
                          field="roommate_gender" 
                          isEditing={isEditMode} 
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                      <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">同住人出生日期</label>
                      </td>
                      <td className="py-3 px-4">
                        <EditableCell 
                          value={editingOrder.roommate_birth_date} 
                          field="roommate_birth_date" 
                          type="date" 
                          isEditing={isEditMode} 
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                    </tr>
                    <tr className="border-b border-gray-200">
                      <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">同住人证件类型</label>
                      </td>
                      <td className="py-3 px-4 border-r border-gray-200">
                        <EditableCell
                          value={editingOrder.roommate_id_type}
                          field="roommate_id_type"
                          type="select"
                          options={ID_TYPE_OPTIONS}
                          isEditing={isEditMode}
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                      <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">同住人证件号码</label>
                      </td>
                      <td className="py-3 px-4">
                        <EditableCell 
                          value={editingOrder.roommate_id_number} 
                          field="roommate_id_number" 
                          isEditing={isEditMode} 
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                    </tr>
                    <tr className="border-b border-gray-200">
                      <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">同住人证件有效期至</label>
                      </td>
                      <td className="py-3 px-4 border-r border-gray-200">
                        <EditableCell 
                          value={editingOrder.roommate_id_expiry_date} 
                          field="roommate_id_expiry_date" 
                          type="date" 
                          isEditing={isEditMode} 
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                      <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">同住人手机号国际区号</label>
                      </td>
                      <td className="py-3 px-4">
                        <EditableCell 
                          value={editingOrder.roommate_mobile_country_code} 
                          field="roommate_mobile_country_code" 
                          isEditing={isEditMode} 
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                    </tr>
                    <tr className="border-b border-gray-200">
                      <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">同住人手机号</label>
                      </td>
                      <td className="py-3 px-4 border-r border-gray-200">
                        <EditableCell 
                          value={editingOrder.roommate_mobile_phone} 
                          field="roommate_mobile_phone" 
                          isEditing={isEditMode} 
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                      <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">同住人邮箱</label>
                      </td>
                      <td className="py-3 px-4">
                        <EditableCell 
                          value={editingOrder.roommate_email} 
                          field="roommate_email" 
                          type="email"
                          isEditing={isEditMode} 
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
            )}

            {/* 对账单信息 */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200">对账单信息</h3>
              <div className="overflow-hidden border border-gray-300 rounded-lg">
                <table className="w-full">
                  <tbody>
                    <tr className="border-b border-gray-200">
                      <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">公司名称</label>
                      </td>
                      <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                        <EditableCell 
                          value={editingOrder.company_name} 
                          field="company_name" 
                          isEditing={isEditMode} 
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                      <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">酒店名称</label>
                      </td>
                      <td className="py-3 px-4 w-1/4">
                        <EditableCell 
                          value={editingOrder.hotel_name} 
                          field="hotel_name" 
                          isEditing={isEditMode} 
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                    </tr>
                    <tr className="border-b border-gray-200">
                      <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">对客金额</label>
                      </td>
                      <td className="py-3 px-4 border-r border-gray-200">
                        <EditableCell 
                          value={editingOrder.amount} 
                          field="amount" 
                          type="number"
                          isEditing={isEditMode} 
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                      <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">代订人</label>
                      </td>
                      <td className="py-3 px-4">
                        <EditableCell 
                          value={editingOrder.booking_agent} 
                          field="booking_agent" 
                          isEditing={isEditMode} 
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                    </tr>
                    <tr>
                      <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">订单号</label>
                      </td>
                      <td className="py-3 px-4 border-r border-gray-200">
                        <EditableCell 
                          value={editingOrder.order_number} 
                          field="order_number" 
                          isEditing={isEditMode} 
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                      <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                        <label className="text-sm font-medium text-gray-700">账单号</label>
                      </td>
                      <td className="py-3 px-4">
                        <EditableCell 
                          value={editingOrder.bill_number} 
                          field="bill_number" 
                          isEditing={isEditMode} 
                          onFieldChange={handleFieldChange}
                          validationErrors={validationErrors}
                        />
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
        
        {/* 弹窗底部 */}
        <div className="px-6 py-6 border-t border-gray-200 bg-gray-50 flex justify-between items-center">
          <div className="text-sm text-gray-500">
            {isEditMode
              ? '编辑模式：修改内容后点击保存按钮'
              : '查看模式：仅可查看订单信息'
            }
          </div>
          <div className="flex items-center gap-3">
            {isEditMode ? (
              <>
                <Button variant="outline" onClick={() => {
                  setIsEditMode(false);
                  setEditingOrder({ ...order });
                  setValidationErrors({});
                }} disabled={saving}>
                  取消
                </Button>
                <Button onClick={saveChanges} disabled={saving}>
                  {saving ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      保存中...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      保存
                    </>
                  )}
                </Button>
              </>
            ) : (
              <>
                {!billingInfoOnly && !hideEditButton && (
                  <Button variant="outline" onClick={() => setIsEditMode(true)}>
                    <Edit className="h-4 w-4 mr-2" />
                    编辑
                  </Button>
                )}
                <Button onClick={onClose}>
                  关闭
                </Button>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default HotelOrderDetailModal; 