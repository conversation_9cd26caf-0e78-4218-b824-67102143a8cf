import React, { useState, useRef } from 'react';
import { X, Upload, RefreshCw, FileSpreadsheet, AlertCircle, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import * as XLSX from 'xlsx-js-style';
import { saveAs } from 'file-saver';

interface FormatConverterModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const FormatConverterModal: React.FC<FormatConverterModalProps> = ({
  isOpen,
  onClose
}) => {
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [converting, setConverting] = useState(false);
  const [convertedData, setConvertedData] = useState<any[] | null>(null);
  const [conversionStatus, setConversionStatus] = useState<'idle' | 'success' | 'error'>('idle');

  if (!isOpen) return null;

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setUploadedFile(file);
      setConvertedData(null);
      setConversionStatus('idle');
    }
  };

  const handleSelectFile = () => {
    fileInputRef.current?.click();
  };

  // 从身份证号提取出生日期
  const extractBirthDateFromIdCard = (idCard: string): string => {
    if (!idCard || idCard.length !== 18) return '';
    const year = idCard.substring(6, 10);
    const month = idCard.substring(10, 12);
    const day = idCard.substring(12, 14);
    return `${year}-${month}-${day}`;
  };

  // 拆分姓名为姓和名
  const splitName = (fullName: string): { surname: string; givenName: string } => {
    if (!fullName) return { surname: '', givenName: '' };
    const surname = fullName.charAt(0);
    const givenName = fullName.substring(1);
    return { surname, givenName };
  };

  // 转换Excel日期序列号为标准日期
  const convertExcelDate = (value: any): string => {
    if (!value) return '';

    // 如果是数字，可能是Excel日期序列号
    if (typeof value === 'number') {
      try {
        // Excel日期序列号转换（从1900年1月1日开始计算）
        const excelEpoch = new Date(1899, 11, 30); // 1899年12月30日
        const convertedDate = new Date(excelEpoch.getTime() + value * 24 * 60 * 60 * 1000);
        return convertedDate.toISOString().split('T')[0]; // 返回YYYY-MM-DD格式
      } catch (e) {
        console.warn('Excel日期转换失败:', value, e);
        return String(value);
      }
    }

    // 如果已经是字符串，直接返回
    return String(value);
  };

  // 生成成本字符串（每间房成本）
  const generateCostString = (checkInDate: any, checkOutDate: any, dailyCost: string = 'xxx'): string => {
    if (!checkInDate || !checkOutDate) return '';

    // 转换Excel日期序列号
    const checkInStr = convertExcelDate(checkInDate);
    const checkOutStr = convertExcelDate(checkOutDate);

    if (!checkInStr || !checkOutStr) return '';

    const checkIn = new Date(checkInStr);
    const checkOut = new Date(checkOutStr);

    // 检查日期是否有效
    if (isNaN(checkIn.getTime()) || isNaN(checkOut.getTime())) {
      console.warn('日期格式无效:', checkInStr, checkOutStr);
      return '';
    }

    const costs: string[] = [];

    const currentDate = new Date(checkIn);
    while (currentDate < checkOut) {
      const dateStr = currentDate.toISOString().split('T')[0];
      costs.push(`${dateStr}/${dailyCost}`);
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return costs.join('\n');
  };

  // 检查指定单元格是否在合并单元格中
  const isCellInMerge = (row: number, col: number, merges: any[]): boolean => {
    return merges.some(merge => {
      return row >= merge.s.r && row <= merge.e.r && col >= merge.s.c && col <= merge.e.c;
    });
  };

  // 检查两行是否在同一个合并单元格中
  const areRowsInSameMerge = (row1: number, row2: number, col: number, merges: any[]): boolean => {
    const merge = merges.find(merge => {
      return row1 >= merge.s.r && row1 <= merge.e.r &&
             row2 >= merge.s.r && row2 <= merge.e.r &&
             col >= merge.s.c && col <= merge.e.c;
    });
    return !!merge;
  };



  // 计算时间段重叠和拆分
  const calculateTimeSegments = (person1: any, person2: any) => {
    const p1CheckIn = new Date(person1['入住时间']);
    const p1CheckOut = new Date(person1['离店时间']);
    const p2CheckIn = new Date(person2['入住时间']);
    const p2CheckOut = new Date(person2['离店时间']);

    // 找到重叠时间段
    const overlapStart = new Date(Math.max(p1CheckIn.getTime(), p2CheckIn.getTime()));
    const overlapEnd = new Date(Math.min(p1CheckOut.getTime(), p2CheckOut.getTime()));

    const segments = [];

    // 如果有重叠
    if (overlapStart < overlapEnd) {
      // Person1 单独的时间段（如果存在）
      if (p1CheckIn < overlapStart) {
        segments.push({
          type: 'single',
          person: person1,
          checkIn: p1CheckIn.toISOString().split('T')[0],
          checkOut: overlapStart.toISOString().split('T')[0],
          roommate: null
        });
      }

      // Person2 单独的时间段（如果存在）
      if (p2CheckIn < overlapStart) {
        segments.push({
          type: 'single',
          person: person2,
          checkIn: p2CheckIn.toISOString().split('T')[0],
          checkOut: overlapStart.toISOString().split('T')[0],
          roommate: null
        });
      }

      // 重叠时间段（同住）
      segments.push({
        type: 'shared',
        person: person1,
        checkIn: overlapStart.toISOString().split('T')[0],
        checkOut: overlapEnd.toISOString().split('T')[0],
        roommate: person2
      });

      // Person1 单独的后续时间段（如果存在）
      if (p1CheckOut > overlapEnd) {
        segments.push({
          type: 'single',
          person: person1,
          checkIn: overlapEnd.toISOString().split('T')[0],
          checkOut: p1CheckOut.toISOString().split('T')[0],
          roommate: null
        });
      }

      // Person2 单独的后续时间段（如果存在）
      if (p2CheckOut > overlapEnd) {
        segments.push({
          type: 'single',
          person: person2,
          checkIn: overlapEnd.toISOString().split('T')[0],
          checkOut: p2CheckOut.toISOString().split('T')[0],
          roommate: null
        });
      }
    } else {
      // 没有重叠，分别处理
      segments.push({
        type: 'single',
        person: person1,
        checkIn: person1['入住时间'],
        checkOut: person1['离店时间'],
        roommate: null
      });
      segments.push({
        type: 'single',
        person: person2,
        checkIn: person2['入住时间'],
        checkOut: person2['离店时间'],
        roommate: null
      });
    }

    return segments;
  };

  const handleConvert = async () => {
    if (!uploadedFile) {
      toast({
        title: "请选择文件",
        description: "请先选择要转换的Excel文件",
        variant: "destructive",
      });
      return;
    }

    setConverting(true);
    setConversionStatus('idle');

    try {
      // 调用后端API进行格式转换
      const formData = new FormData();
      formData.append('file', uploadedFile);

      console.log('📤 开始调用后端格式转换API...');

      // 使用统一的API请求方法
      const { api } = await import('@/api/request');

      const response = await api.post('/hotel-order/convert-format', formData, {
        isFormData: true,
        responseType: 'blob'
      });

      console.log('✅ 后端转换成功');

      // 获取转换后的文件（response.data 是 blob）
      const blob = response.data;
      const fileName = `酒店订单转换结果_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.xlsx`;

      // 直接下载文件
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      // 转换成功，设置状态为完成
      setConvertedData([]);
      setConversionStatus('completed');

      // 注释掉前端Excel处理，避免JavaScript错误
      /*
      // 为了预览，我们仍然读取原始文件数据
      const arrayBuffer = await uploadedFile.arrayBuffer();
      const workbook = XLSX.read(arrayBuffer, { type: 'array' });
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];

      // 读取原始数据用于预览
      const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1');
      const merges = worksheet['!merges'] || [];
      console.log('🔗 合并单元格信息:', merges);
      */

      /*
      // 从第4行开始读取数据（索引从0开始，所以是第3行）
      const rawData: any[] = [];
      console.log('📊 Excel文件范围:', range);
      console.log('📊 总行数:', range.e.r + 1);

      // 先读取列名（第2行）
      const headers: string[] = [];
      for (let col = range.s.c; col <= range.e.c; col++) {
        const headerAddress = XLSX.utils.encode_cell({ r: 1, c: col }); // 第2行为列名
        const header = worksheet[headerAddress]?.v || `Column${col}`;
        // 安全地确保header是字符串类型
        let headerStr = '';
        try {
          if (header !== null && header !== undefined) {
            headerStr = String(header);
          } else {
            headerStr = `Column${col}`;
          }
        } catch (e) {
          console.warn('Header转换失败:', header, e);
          headerStr = `Column${col}`;
        }
        headers.push(headerStr);
      }
      console.log('📊 列名:', headers);

      for (let row = 3; row <= range.e.r; row++) {
        const rowData: any = {};
        let hasData = false; // 检查行是否有数据

        for (let col = range.s.c; col <= range.e.c; col++) {
          const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
          const cell = worksheet[cellAddress];
          const header = headers[col - range.s.c] || `Column${col}`;
          const value = cell?.v || '';
          // 确保header是字符串类型
          const headerStr = String(header);
          rowData[headerStr] = value;

          if (value && value.toString().trim() !== '') {
            hasData = true;
          }
        }

        if (hasData) {
          console.log(`📊 第${row + 1}行数据:`, rowData);
          rawData.push(rowData);
        }
      }

      console.log('📊 读取到的原始数据总数:', rawData.length);

      // 转换数据
      const convertedResult: any[] = [];
      console.log('🔄 开始转换数据，原始数据条数:', rawData.length);

      for (let i = 0; i < rawData.length; i++) {
        const row = rawData[i];

        // 基础字段映射
        const { surname, givenName } = splitName(row['姓名'] || '');
        const idCard = row['身份证号'] || '';
        const birthDate = idCard.length === 18 ? extractBirthDateFromIdCard(idCard) : '';

        // 转换时间字段
        const checkInTime = convertExcelDate(row['入住时间']);
        const checkOutTime = convertExcelDate(row['离店时间']);

        const convertedRow = {
          '序号': row['编号'] || (i + 1),
          '入住人姓名': row['姓名'] || '',
          '入住人姓': surname,
          '入住人名': givenName,
          '入住人性别': row['性别'] || '',
          '入住人手机号': row['联系方式'] || '',
          '入住人证件号码': idCard,
          '入住人出生日期': birthDate,
          '入住人证件类型': idCard.length === 18 ? '身份证' : '',
          '预订房型': row['房间安排'] || row['房间安排（同住人请合并单元格）'] || '',
          '入住时间': checkInTime,
          '离店时间': checkOutTime,
          '订单备注': row['备注'] || '',
          '每间房成本': generateCostString(checkInTime, checkOutTime),
          '隐藏手续费': generateCostString(checkInTime, checkOutTime, '0'),
          '同住人姓名': '',
          '同住人姓': '',
          '同住人名': '',
          '同住人性别': '',
          '同住人证件号码': '',
          '同住人出生日期': '',
          '同住人证件类型': ''
        };

        // 检查是否有同住人（基于合并单元格检测）
        if (i + 1 < rawData.length) {
          const nextRow = rawData[i + 1];
          const currentRowIndex = 3 + i; // 当前行在Excel中的索引（第4行开始）
          const nextRowIndex = 3 + i + 1; // 下一行在Excel中的索引

          // 查找房间安排列的索引
          let roomColumnIndex = -1;
          for (let col = range.s.c; col <= range.e.c; col++) {
            const headerAddress = XLSX.utils.encode_cell({ r: 1, c: col });
            const header = worksheet[headerAddress]?.v || '';
            // 确保header是字符串类型，并且安全处理
            let headerStr = '';
            try {
              if (header !== null && header !== undefined) {
                headerStr = String(header);
              }
            } catch (e) {
              console.warn('Header转换失败:', header, e);
              headerStr = '';
            }

            if (headerStr && typeof headerStr === 'string' && headerStr.includes('房间安排')) {
              roomColumnIndex = col;
              break;
            }
          }

          console.log(`🔗 检查第${currentRowIndex + 1}行和第${nextRowIndex + 1}行是否为同住人`);
          console.log(`🔗 房间安排列索引: ${roomColumnIndex}`);

          // 检查房间安排列是否在合并单元格中，且两行在同一个合并单元格
          if (roomColumnIndex >= 0 && areRowsInSameMerge(currentRowIndex, nextRowIndex, roomColumnIndex, merges)) {
            console.log(`🔗 发现合并单元格: 第${currentRowIndex + 1}行和第${nextRowIndex + 1}行`);

            // 检查入住时间和离店时间（转换Excel日期序列号）
            const currentCheckIn = convertExcelDate(row['入住时间']);
            const nextCheckIn = convertExcelDate(nextRow['入住时间']);
            const currentCheckOut = convertExcelDate(row['离店时间']);
            const nextCheckOut = convertExcelDate(nextRow['离店时间']);

            console.log(`🔗 时间对比: 第一人 ${currentCheckIn}-${currentCheckOut}, 第二人 ${nextCheckIn}-${nextCheckOut}`);

            if (currentCheckIn === nextCheckIn && currentCheckOut === nextCheckOut) {
              // 完全相同时间，简单同住处理
              console.log(`🔗 时间完全相同，按同住人处理`);

              const { surname: roommateSurname, givenName: roommateGivenName } = splitName(nextRow['姓名'] || '');
              const roommateIdCard = nextRow['身份证号'] || '';
              const roommateBirthDate = roommateIdCard.length === 18 ? extractBirthDateFromIdCard(roommateIdCard) : '';

              convertedRow['同住人姓名'] = nextRow['姓名'] || '';
              convertedRow['同住人姓'] = roommateSurname;
              convertedRow['同住人名'] = roommateGivenName;
              convertedRow['同住人性别'] = nextRow['性别'] || '';
              convertedRow['同住人证件号码'] = roommateIdCard;
              convertedRow['同住人出生日期'] = roommateBirthDate;
              convertedRow['同住人证件类型'] = roommateIdCard.length === 18 ? '身份证' : '';

              console.log(`✅ 转换第${i + 1}条数据（含同住人）:`, convertedRow);
              convertedResult.push(convertedRow);

              // 跳过下一行，因为已经作为同住人处理了
              i++;
            } else {
              // 时间不同，需要拆分时间段
              console.log(`🔗 时间不同，进行时间段拆分处理`);

              const segments = calculateTimeSegments(row, nextRow);
              console.log(`🔗 拆分出 ${segments.length} 个时间段:`, segments);

              // 为每个时间段创建订单
              segments.forEach((segment, segmentIndex) => {
                const segmentRow = { ...convertedRow };

                // 更新时间
                segmentRow['入住时间'] = segment.checkIn;
                segmentRow['离店时间'] = segment.checkOut;
                segmentRow['每间房成本'] = generateCostString(segment.checkIn, segment.checkOut);
                segmentRow['隐藏手续费'] = generateCostString(segment.checkIn, segment.checkOut, '0');

                // 更新主要入住人信息
                const mainPerson = segment.person;
                const { surname: mainSurname, givenName: mainGivenName } = splitName(mainPerson['姓名'] || '');
                const mainIdCard = mainPerson['身份证号'] || '';
                const mainBirthDate = mainIdCard.length === 18 ? extractBirthDateFromIdCard(mainIdCard) : '';

                segmentRow['入住人姓名'] = mainPerson['姓名'] || '';
                segmentRow['入住人姓'] = mainSurname;
                segmentRow['入住人名'] = mainGivenName;
                segmentRow['入住人性别'] = mainPerson['性别'] || '';
                segmentRow['入住人手机号'] = mainPerson['联系方式'] || '';
                segmentRow['入住人证件号码'] = mainIdCard;
                segmentRow['入住人出生日期'] = mainBirthDate;
                segmentRow['入住人证件类型'] = mainIdCard.length === 18 ? '身份证' : '';

                // 处理同住人信息
                if (segment.type === 'shared' && segment.roommate) {
                  const roommate = segment.roommate;
                  const { surname: roommateSurname, givenName: roommateGivenName } = splitName(roommate['姓名'] || '');
                  const roommateIdCard = roommate['身份证号'] || '';
                  const roommateBirthDate = roommateIdCard.length === 18 ? extractBirthDateFromIdCard(roommateIdCard) : '';

                  segmentRow['同住人姓名'] = roommate['姓名'] || '';
                  segmentRow['同住人姓'] = roommateSurname;
                  segmentRow['同住人名'] = roommateGivenName;
                  segmentRow['同住人性别'] = roommate['性别'] || '';
                  segmentRow['同住人证件号码'] = roommateIdCard;
                  segmentRow['同住人出生日期'] = roommateBirthDate;
                  segmentRow['同住人证件类型'] = roommateIdCard.length === 18 ? '身份证' : '';
                } else {
                  // 清空同住人信息
                  segmentRow['同住人姓名'] = '';
                  segmentRow['同住人姓'] = '';
                  segmentRow['同住人名'] = '';
                  segmentRow['同住人性别'] = '';
                  segmentRow['同住人证件号码'] = '';
                  segmentRow['同住人出生日期'] = '';
                  segmentRow['同住人证件类型'] = '';
                }

                console.log(`✅ 转换时间段${segmentIndex + 1}:`, segmentRow);
                convertedResult.push(segmentRow);
              });

              // 跳过下一行，因为已经处理了
              i++;
            }
          } else {
            console.log(`🔗 房间安排列未合并，按单人处理`);
            console.log(`✅ 转换第${i + 1}条数据（单人）:`, convertedRow);
            convertedResult.push(convertedRow);
          }
        } else {
          console.log(`✅ 转换第${i + 1}条数据（最后一行）:`, convertedRow);
          convertedResult.push(convertedRow);
        }
      }

      console.log('🎉 转换完成，结果数据条数:', convertedResult.length);
      setConvertedData(convertedResult);
      setConversionStatus('success');

      toast({
        title: "转换成功",
        description: `成功转换数据，文件已自动下载`,
        variant: "default",
      });
      */

    } catch (error) {
      console.error('文件转换失败:', error);
      setConversionStatus('error');
      toast({
        title: "转换失败",
        description: error instanceof Error ? error.message : "转换过程中发生错误，请重试",
        variant: "destructive",
      });
    } finally {
      setConverting(false);
    }
  };

  const handleDownload = async () => {
    if (!convertedData) {
      console.error('❌ 没有转换数据可下载');
      return;
    }

    console.log('📥 开始下载，转换数据条数:', convertedData.length);

    try {
      // 第一步：加载模板文件
      console.log('📂 第一步：加载模板文件...');
      console.log('📂 尝试加载模板文件路径: /templates/hotel_order_template.xlsx');

      let templateResponse;
      try {
        templateResponse = await fetch('/templates/hotel_order_template.xlsx');
        console.log('📂 模板文件响应状态:', templateResponse.status, templateResponse.statusText);
      } catch (fetchError) {
        console.error('📂 模板文件加载失败 (fetch错误):', fetchError);
        throw new Error(`模板文件加载失败: ${fetchError}`);
      }

      if (!templateResponse.ok) {
        console.error('📂 模板文件HTTP错误:', templateResponse.status, templateResponse.statusText);
        throw new Error(`模板文件加载失败: HTTP ${templateResponse.status} ${templateResponse.statusText}`);
      }

      const templateArrayBuffer = await templateResponse.arrayBuffer();
      console.log('📂 模板文件大小:', templateArrayBuffer.byteLength, 'bytes');

      // 第二步：完整复制模板工作簿（保留所有样式）
      console.log('📂 第二步：复制模板工作簿...');
      const templateWorkbook = XLSX.read(templateArrayBuffer, {
        type: 'array',
        cellStyles: true,
        cellNF: true,
        cellHTML: false
      });
      const newWorkbook = XLSX.read(templateArrayBuffer, {
        type: 'array',
        cellStyles: true,
        cellNF: true,
        cellHTML: false
      }); // 完整复制，保留样式

      // 获取第一个工作表
      const templateSheetName = templateWorkbook.SheetNames[0];
      const templateWorksheet = templateWorkbook.Sheets[templateSheetName];
      const newWorksheet = newWorkbook.Sheets[templateSheetName];

      console.log('📂 模板工作表名称:', templateSheetName);
      console.log('� 模板工作表属性:', Object.keys(templateWorksheet).filter(k => k.startsWith('!')));

      // 检查模板的样式信息
      console.log('🎨 检查模板样式信息...');
      let stylesCount = 0;
      let cellsWithStyles: string[] = [];
      Object.keys(templateWorksheet).forEach(cellAddress => {
        if (!cellAddress.startsWith('!')) {
          const cell = templateWorksheet[cellAddress];
          if (cell && typeof cell === 'object' && cell.s) {
            stylesCount++;
            cellsWithStyles.push(cellAddress);
          }
        }
      });
      console.log('🎨 模板中有样式的单元格数量:', stylesCount);
      console.log('🎨 前几个有样式的单元格:', cellsWithStyles.slice(0, 10));

      // 第三步：读取模板的列名映射（第2行为列名）
      console.log('📋 第三步：读取模板列名...');
      const templateRange = XLSX.utils.decode_range(templateWorksheet['!ref'] || 'A1');
      const templateColumnMap: { [key: string]: number } = {}; // 列名到列索引的映射

      for (let col = templateRange.s.c; col <= templateRange.e.c; col++) {
        const headerAddress = XLSX.utils.encode_cell({ r: 1, c: col }); // 第2行为列名
        const header = templateWorksheet[headerAddress]?.v || '';
        // 确保header是字符串类型
        const headerStr = String(header || '');
        if (headerStr) {
          templateColumnMap[headerStr] = col;
        }
      }

      console.log('📋 模板列名映射:', templateColumnMap);
      console.log('📋 模板总列数:', Object.keys(templateColumnMap).length);
      console.log('📋 转换数据字段:', Object.keys(convertedData[0] || {}));

      // 验证模板完整性
      console.log('📋 验证模板完整性...');
      const allTemplateColumns: string[] = [];
      for (let col = templateRange.s.c; col <= templateRange.e.c; col++) {
        const headerAddress = XLSX.utils.encode_cell({ r: 1, c: col });
        const header = templateWorksheet[headerAddress]?.v || `空列${col}`;
        // 确保header是字符串类型
        allTemplateColumns.push(String(header));
      }
      console.log('📋 模板所有列名:', allTemplateColumns);

      // 第四步：从第4行开始写入转换数据（只更新有数据的单元格，保留模板的其他列）
      console.log('📋 第四步：写入转换数据...');
      console.log('📋 模板现有列数:', templateRange.e.c - templateRange.s.c + 1);
      console.log('📋 模板现有行数:', templateRange.e.r - templateRange.s.r + 1);

      convertedData.forEach((row: any, index: number) => {
        const targetRow = 3 + index; // 第4行开始（索引从0开始，第4行是索引3）

        // 遍历转换数据的每个字段
        Object.keys(row).forEach((fieldName: string) => {
          const value = row[fieldName];

          // 查找该字段在模板中对应的列
          if (templateColumnMap.hasOwnProperty(fieldName)) {
            const colIndex = templateColumnMap[fieldName];
            const cellAddress = XLSX.utils.encode_cell({ r: targetRow, c: colIndex });

            // 检查原单元格是否存在，如果存在则保留其完整格式
            const originalCell = newWorksheet[cellAddress];

            if (originalCell && typeof originalCell === 'object') {
              // 深度保留原有格式，只更新值
              newWorksheet[cellAddress] = {
                ...originalCell, // 保留所有原有属性
                v: value,        // 更新值
                t: typeof value === 'number' ? 'n' : 's', // 更新类型
                w: String(value) // 更新显示值
                // 保留 s (样式), z (数字格式), f (公式) 等所有其他属性
              };
              console.log(`📋 保留格式写入 ${cellAddress}:`, {
                value: value,
                hasStyle: !!originalCell.s,
                hasFormat: !!originalCell.z,
                originalKeys: Object.keys(originalCell)
              });
            } else {
              // 创建新单元格（通常不会到这里，因为我们是在模板基础上写入）
              newWorksheet[cellAddress] = {
                v: value,
                t: typeof value === 'number' ? 'n' : 's',
                w: String(value)
              };
              console.log(`📋 新建单元格 ${cellAddress}:`, value);
            }

            console.log(`📋 写入 ${cellAddress} (第${targetRow + 1}行,第${colIndex + 1}列): ${fieldName} = ${value}`);
          } else {
            console.warn(`⚠️ 字段 "${fieldName}" 在模板中未找到对应列`);
          }
        });
      });

      // 第五步：更新工作表范围
      console.log('📋 第五步：更新工作表范围...');
      const newRange = { ...templateRange };
      newRange.e.r = Math.max(templateRange.e.r, 3 + convertedData.length - 1);
      newWorksheet['!ref'] = XLSX.utils.encode_range(newRange);

      console.log('📋 原始模板范围:', templateWorksheet['!ref']);
      console.log('📋 更新后范围:', newWorksheet['!ref']);
      console.log('📋 数据写入完成，共写入', convertedData.length, '行数据');



      // 验证最终结果的列完整性
      console.log('📋 验证最终结果...');
      const finalRange = XLSX.utils.decode_range(newWorksheet['!ref'] || 'A1');
      const finalColumns: string[] = [];
      for (let col = finalRange.s.c; col <= finalRange.e.c; col++) {
        const headerAddress = XLSX.utils.encode_cell({ r: 1, c: col });
        const header = newWorksheet[headerAddress]?.v || `空列${col}`;
        // 确保header是字符串类型
        finalColumns.push(String(header));
      }
      console.log('📋 最终文件列名:', finalColumns);
      console.log('📋 最终文件列数:', finalColumns.length);
      console.log('📋 模板列数:', allTemplateColumns.length);
      console.log('📋 列数是否一致:', finalColumns.length === allTemplateColumns.length);

      // 生成Excel文件（保留所有样式和格式）
      console.log('💾 生成Excel文件...');
      const excelBuffer = XLSX.write(newWorkbook, {
        bookType: 'xlsx',
        type: 'array',
        cellStyles: true,    // 保留单元格样式
        compression: true    // 启用压缩
      });
      console.log('💾 Excel文件大小:', excelBuffer.byteLength, 'bytes');

      const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      console.log('💾 Blob大小:', blob.size, 'bytes');

      // 下载文件
      const fileName = `酒店订单转换结果_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.xlsx`;
      console.log('💾 下载文件名:', fileName);
      saveAs(blob, fileName);

      toast({
        title: "下载成功",
        description: "转换后的文件已下载到本地，格式与模板保持一致",
        variant: "default",
      });

    } catch (error) {
      console.error('❌ 使用模板下载失败:', error);

      // 备用方案：直接使用转换数据创建Excel
      try {
        console.log('🔄 使用备用方案下载...');
        const backupWorkbook = XLSX.utils.book_new();
        const backupWorksheet = XLSX.utils.json_to_sheet(convertedData);

        XLSX.utils.book_append_sheet(backupWorkbook, backupWorksheet, '酒店订单');

        const backupBuffer = XLSX.write(backupWorkbook, { bookType: 'xlsx', type: 'array' });
        const backupBlob = new Blob([backupBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

        const backupFileName = `酒店订单转换结果_备用_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.xlsx`;
        saveAs(backupBlob, backupFileName);

        toast({
          title: "下载成功（备用格式）",
          description: "使用备用格式下载成功，数据完整",
          variant: "default",
        });
      } catch (backupError) {
        console.error('❌ 备用下载也失败:', backupError);
        toast({
          title: "下载失败",
          description: "生成文件时发生错误，请重试",
          variant: "destructive",
        });
      }
    }
  };

  const handleClose = () => {
    setUploadedFile(null);
    setConvertedData(null);
    setConversionStatus('idle');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    onClose();
  };

  // 处理拖拽上传
  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
  };

  const handleDragEnter = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
  };

  const handleDragLeave = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();

    const files = event.dataTransfer.files;
    if (files && files.length > 0) {
      const file = files[0];

      // 检查文件类型
      const allowedTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
        'application/vnd.ms-excel', // .xls
      ];

      if (allowedTypes.includes(file.type) || file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
        setUploadedFile(file);
        // 重置转换状态
        setConvertedData(null);
        setConversionStatus('idle');

        toast({
          title: "文件已选择",
          description: `已选择文件: ${file.name}`,
          variant: "default",
        });
      } else {
        toast({
          title: "文件类型错误",
          description: "请选择 .xlsx 或 .xls 格式的Excel文件",
          variant: "destructive",
        });
      }
    }
  };



  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-orange-100 rounded-lg">
              <RefreshCw className="h-6 w-6 text-orange-600" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">格式转换工具</h2>
              <p className="text-sm text-gray-500">将您的Excel文件转换为系统标准模板格式</p>
            </div>
          </div>
          <Button
            onClick={handleClose}
            variant="ghost"
            size="sm"
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>

        {/* 内容区域 */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          {/* 注意事项 */}
          <div className="mb-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">注意事项</h3>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-blue-600 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-blue-800 font-medium">标题列需要确保字段正确</p>
                  <p className="text-sm text-blue-700 mt-1">
                    请确保Excel文件包含以下必要字段：<span className="font-medium">编号、姓名、性别、联系方式、身份证号、房间安排、入住时间、离店时间</span>
                  </p>
                  <div className="mt-2">
                    <button
                      onClick={() => {
                        const link = document.createElement('a');
                        link.href = '/templates/hotel_convert_template.xlsx';
                        link.download = 'hotel_convert_template.xlsx';
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                      }}
                      className="text-sm text-blue-600 hover:text-blue-800 underline"
                    >
                      下载标准模板
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 文件上传区域 */}
          <div className="mb-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">1. 选择要转换的文件</h3>
            <div
              className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-500 transition-colors cursor-pointer group"
              onClick={() => !uploadedFile && handleSelectFile()}
              onDragOver={handleDragOver}
              onDragEnter={handleDragEnter}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
            >
              {uploadedFile ? (
                <div className="flex items-center justify-center space-x-3">
                  <FileSpreadsheet className="h-8 w-8 text-green-600" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">{uploadedFile.name}</p>
                    <p className="text-xs text-gray-500">
                      {(uploadedFile.size / 1024 / 1024).toFixed(2)} MB
                    </p>
                  </div>
                  <Button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleSelectFile();
                    }}
                    variant="outline"
                    size="sm"
                  >
                    重新选择
                  </Button>
                </div>
              ) : (
                <div>
                  <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4 transition-colors group-hover:text-blue-500" />
                  <p className="text-sm text-gray-600 mb-2 transition-colors group-hover:text-blue-600">点击或拖拽Excel文件到此区域</p>
                  <p className="text-xs text-gray-400">支持 .xlsx 和 .xls 格式</p>
                  <div className="mt-4">
                    <Button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleSelectFile();
                      }}
                      variant="outline"
                    >
                      选择文件
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* 转换操作区域 */}
          <div className="mb-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">2. 执行格式转换</h3>
            <div className="flex items-center space-x-4">
              <Button
                onClick={handleConvert}
                disabled={!uploadedFile || converting}
                className="bg-orange-600 hover:bg-orange-700 text-white"
              >
                {converting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    转换中...
                  </>
                ) : (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2" />
                    开始转换
                  </>
                )}
              </Button>

              {conversionStatus === 'success' && (
                <div className="flex items-center text-green-600">
                  <CheckCircle className="h-5 w-5 mr-2" />
                  <span className="text-sm">转换成功</span>
                </div>
              )}

              {conversionStatus === 'error' && (
                <div className="flex items-center text-red-600">
                  <AlertCircle className="h-5 w-5 mr-2" />
                  <span className="text-sm">转换失败</span>
                </div>
              )}
            </div>
          </div>

          {/* 转换结果预览 */}
          {convertedData && convertedData.length > 0 && (
            <div className="mb-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">3. 转换结果预览</h3>
              <div className="border border-gray-200 rounded-lg overflow-hidden">
                <div className="bg-gray-50 px-4 py-2 border-b border-gray-200">
                  <p className="text-sm text-gray-600">
                    共转换 {convertedData.length} 条数据，将按照目标模板格式输出
                  </p>
                </div>
                <div className="max-h-60 overflow-y-auto">
                  <div className="overflow-x-auto">
                    <table className="w-full text-sm min-w-max">
                      <thead className="bg-gray-50 sticky top-0">
                        <tr>
                          {convertedData.length > 0 && Object.keys(convertedData[0]).map((key) => (
                            <th key={key} className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap min-w-[120px]">
                              {key}
                            </th>
                          ))}
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {convertedData.slice(0, 10).map((row, index) => (
                          <tr key={index}>
                            {Object.keys(convertedData[0]).map((key) => (
                              <td key={key} className="px-3 py-2 text-sm text-gray-900 whitespace-nowrap min-w-[120px]">
                                <div className="max-w-[200px] truncate" title={String(row[key])}>
                                  {String(row[key]).length > 30
                                    ? String(row[key]).substring(0, 30) + '...'
                                    : String(row[key])
                                  }
                                </div>
                              </td>
                            ))}
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                  {convertedData.length > 10 && (
                    <div className="px-4 py-2 text-center text-sm text-gray-500 bg-gray-50">
                      还有 {convertedData.length - 10} 条数据...
                    </div>
                  )}
                </div>
                <div className="bg-blue-50 px-4 py-2 border-t border-blue-200">
                  <p className="text-xs text-blue-600">
                    💡 提示：下载的文件将保持目标模板的完整格式，包括前三行的标题、列名和格式设置
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* 转换完成提示 */}
          {conversionStatus === 'completed' && (
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">3. 转换完成</h3>
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
                  <div>
                    <p className="text-sm font-medium text-green-800">转换成功完成！</p>
                    <p className="text-sm text-green-700 mt-1">
                      Excel文件已按照标准模板格式转换，文件已自动下载到您的设备
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* 隐藏的文件输入 */}
        <input
          ref={fileInputRef}
          type="file"
          accept=".xlsx,.xls"
          onChange={handleFileUpload}
          className="hidden"
        />
      </div>
    </div>
  );
};

export default FormatConverterModal;
