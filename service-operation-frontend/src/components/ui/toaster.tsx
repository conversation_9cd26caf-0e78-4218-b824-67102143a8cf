import React from 'react';
import { useToast } from '@/hooks/use-toast';
import { X } from 'lucide-react';

const Toaster: React.FC = () => {
  const { toasts, removeToast } = useToast();

  const getToastStyles = (variant?: string) => {
    switch (variant) {
      case 'destructive':
      case 'error':
        return 'bg-red-500 text-white border-red-600';
      case 'success':
        return 'bg-green-500 text-white border-green-600';
      case 'warning':
        return 'bg-yellow-500 text-white border-yellow-600';
      case 'info':
        return 'bg-blue-500 text-white border-blue-600';
      default:
        return 'bg-green-500 text-white border-green-600'; // 默认为成功样式
    }
  };

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2">
      {toasts.map((toast) => (
        <div
          key={toast.id}
          className={`
            max-w-sm rounded-lg p-4 shadow-lg transform transition-all duration-300 ease-in-out
            border ${getToastStyles(toast.variant)}
            animate-in slide-in-from-right-full
          `}
        >
          <div className="flex items-start justify-between">
            <div className="flex-1">
              {toast.title && (
                <div className="font-semibold text-sm mb-1">
                  {toast.title}
                </div>
              )}
              {toast.description && (
                <div className="text-sm opacity-90">
                  {toast.description}
                </div>
              )}
            </div>
            <button
              onClick={() => removeToast(toast.id)}
              className="ml-2 text-white/80 hover:text-white transition-colors"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </div>
      ))}
    </div>
  );
};

export { Toaster }; 