import React, { useState } from 'react';
import { passportService } from '@/services/train_order/passport-service';

export const FileUploadTest: React.FC = () => {
  const [isUploading, setIsUploading] = useState(false);
  const [result, setResult] = useState<string>('');

  const handleTestUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    setIsUploading(true);
    setResult('上传中...');

    try {
      const response = await passportService.uploadFiles(files);
      setResult(`✅ 上传成功！任务ID: ${response.task_id}, 文件数量: ${response.total_files}`);
    } catch (error) {
      setResult(`❌ 上传失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="bg-yellow-50 p-4 rounded-lg mb-4 border border-yellow-200">
      <h3 className="text-lg font-semibold mb-2 text-yellow-800">文件上传测试</h3>
      
      <div className="space-y-2">
        <div>
          <label htmlFor="test-upload" className="block text-sm font-medium mb-1">
            选择测试文件:
          </label>
          <input
            id="test-upload"
            type="file"
            accept="image/*"
            onChange={handleTestUpload}
            disabled={isUploading}
            className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
          />
        </div>
        
        {result && (
          <div className="mt-2 p-2 bg-white rounded border">
            <strong>测试结果:</strong> {result}
          </div>
        )}
        
        <div className="text-xs text-gray-600">
          <strong>Token状态:</strong> {localStorage.getItem('access_token') ? '✅ 存在' : '❌ 不存在'}
        </div>
      </div>
    </div>
  );
}; 