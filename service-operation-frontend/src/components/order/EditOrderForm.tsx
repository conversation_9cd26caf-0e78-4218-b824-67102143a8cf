import React, { useState } from 'react';
import { Button } from '../ui/button';

interface TrainOrder {
  id: number;
  project_id: number;
  sequence_number: number;
  traveler_full_name: string;
  traveler_surname?: string;
  traveler_given_name?: string;
  nationality?: string;
  gender?: string;
  birth_date?: string;
  id_type?: string;
  id_number?: string;
  id_expiry_date?: string;
  mobile_phone?: string;
  mobile_phone_country_code?: string;
  travel_date?: string;
  departure_station?: string;
  arrival_station?: string;
  train_number?: string;
  seat_type?: string;
  departure_time?: string;
  arrival_time?: string;
  cost_center?: string;
  trip_submission_item?: string;
  contact_person?: string;
  contact_phone?: string;
  contact_email?: string;
  approval_reference?: string;
  company_name?: string;
  booking_agent?: string;
  ticket_sms?: string;
  amount?: number | null;
  order_number?: string;
  bill_number?: string;
  order_status: string;
  fail_reason?: string;
  created_at: string;
  updated_at: string;
  is_deleted: boolean;
}

interface EditOrderFormProps {
  order: TrainOrder;
  onSave: (updatedData: Partial<TrainOrder>) => void;
  onCancel: () => void;
  loading: boolean;
}

export const EditOrderForm: React.FC<EditOrderFormProps> = ({
  order,
  onSave,
  onCancel,
  loading
}) => {
  const [formData, setFormData] = useState({
    // 出行人基础信息
    traveler_full_name: order.traveler_full_name,
    traveler_surname: order.traveler_surname || '',
    traveler_given_name: order.traveler_given_name || '',
    nationality: order.nationality || '',
    gender: order.gender || '',
    birth_date: order.birth_date || '',
    id_type: order.id_type || '',
    id_number: order.id_number || '',
    id_expiry_date: order.id_expiry_date || '',
    mobile_phone_country_code: order.mobile_phone_country_code || '+86',
    mobile_phone: order.mobile_phone || '',
    
    // 出行人行程信息
    sequence_number: order.sequence_number,
    travel_date: order.travel_date || '',
    departure_station: order.departure_station || '',
    arrival_station: order.arrival_station || '',
    train_number: order.train_number || '',
    seat_type: order.seat_type || '',
    departure_time: order.departure_time || '',
    arrival_time: order.arrival_time || '',
    cost_center: order.cost_center || '',
    trip_submission_item: order.trip_submission_item || '',
    
    // 对账单
    company_name: order.company_name || '',
    booking_agent: order.booking_agent || '',
    ticket_sms: order.ticket_sms || '',
    amount: order.amount || null,
    order_number: order.order_number || '',
    bill_number: order.bill_number || '',
  });

  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  // 验证规则定义 - 与火车票预订页面保持一致
  const getFieldValidationRule = (field: keyof TrainOrder) => {
    const rules: Record<string, {
      required?: boolean;
      pattern?: RegExp;
      message?: string;
      maxLength?: number;
      minLength?: number;
      enumValues?: string[];
    }> = {
      // 基础必填字段（所有情况下都必填）
      traveler_full_name: { required: true, message: '出行人姓名不能为空', maxLength: 50 },
      id_type: {
        required: true,
        message: '证件类型不能为空',
        maxLength: 20,
        enumValues: ['身份证', '公务护照', '普通护照', '港澳通行证', '台胞证', '回乡证', '军人证', '海员证', '台湾通行证', '外国永久居留证', '港澳台居民居住证', '其他']
      },
      id_number: {
        required: true,
        message: '证件号码不能为空',
        maxLength: 50
      },
      mobile_phone: {
        required: true,
        message: '手机号不能为空',
        maxLength: 20
      },
      travel_date: { required: true, message: '出行日期不能为空' },
      departure_station: { required: true, message: '出发站名不能为空', maxLength: 50 },
      arrival_station: { required: true, message: '到达站名不能为空', maxLength: 50 },
      train_number: { required: true, message: '车次不能为空', maxLength: 20 },
      seat_type: {
        required: true,
        message: '座位类型不能为空',
        maxLength: 20,
        enumValues: ['硬座', '硬卧', '软卧', '二等座', '一等座', '商务座', '特等座']
      },
      departure_time: { required: true, message: '出发时间不能为空' },
      arrival_time: { required: true, message: '到达时间不能为空' },
      contact_person: { required: true, message: '联系人不能为空', maxLength: 50 },
      contact_phone: {
        required: true,
        message: '联系人手机号不能为空',
        maxLength: 20
      },
      approval_reference: { required: true, message: '审批参考人不能为空', maxLength: 50 },

      // 证件类型不为身份证时的额外必填字段（动态必填）
      traveler_surname: { maxLength: 25 },
      traveler_given_name: { maxLength: 25 },
      nationality: { maxLength: 20 },
      gender: {
        maxLength: 10,
        enumValues: ['男', '女']
      },
      birth_date: {},
      id_expiry_date: {},

      // 手机号国际区号（自动补齐为86，非必填）
      mobile_phone_country_code: { maxLength: 10 },

      // 非必填字段
      contact_email: {
        pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        message: '联系人邮箱格式不正确',
        maxLength: 100
      },
      company_name: { maxLength: 100 },
      booking_agent: { maxLength: 50 },
      order_number: { maxLength: 50 },
      bill_number: { maxLength: 50 },
      amount: {
        pattern: /^\d+(\.\d{1,2})?$/,
        message: '金额格式不正确（最多两位小数）'
      },
      sequence_number: { maxLength: 50 },
      trip_submission_item: { maxLength: 200 },
      ticket_sms: { maxLength: 500 }
    };

    return rules[field] || {};
  };

  // 验证单个字段
  const validateField = (field: keyof TrainOrder, value: string | number | boolean | null | undefined, idType?: string): string | null => {
    const rule = getFieldValidationRule(field);

    // 处理boolean类型字段
    if (typeof value === 'boolean') {
      return null;
    }

    const stringValue = String(value || '').trim();

    // 动态必填字段：当证件类型不为身份证时，这些字段变为必填
    const dynamicRequiredFields = ['traveler_surname', 'traveler_given_name', 'nationality', 'gender', 'birth_date', 'id_expiry_date'];
    let isRequired = rule.required;

    if (dynamicRequiredFields.includes(field) && idType && idType !== '身份证') {
      isRequired = true;
    }

    // 必填验证
    if (isRequired && !stringValue) {
      const fieldNames: Record<string, string> = {
        // 基础必填字段
        traveler_full_name: '出行人姓名',
        id_type: '证件类型',
        id_number: '证件号码',
        mobile_phone: '手机号',
        travel_date: '出行日期',
        departure_station: '出发站名',
        arrival_station: '到达站名',
        train_number: '车次',
        seat_type: '座位类型',
        departure_time: '出发时间',
        arrival_time: '到达时间',
        contact_person: '联系人',
        contact_phone: '联系人手机号',
        approval_reference: '审批参考人',
        // 动态必填字段
        traveler_surname: '出行人姓',
        traveler_given_name: '出行人名',
        nationality: '国籍',
        gender: '性别',
        birth_date: '出生日期',
        id_expiry_date: '证件有效期'
      };
      return fieldNames[field] ? `${fieldNames[field]}不能为空` : (rule.message || `${field}不能为空`);
    }

    // 如果值为空且非必填，跳过其他验证
    if (!stringValue && !isRequired) {
      return null;
    }

    // 长度验证
    if (rule.maxLength && stringValue.length > rule.maxLength) {
      const fieldNames: Record<string, string> = {
        traveler_full_name: '出行人姓名',
        id_type: '证件类型',
        id_number: '证件号码',
        mobile_phone: '手机号',
        travel_date: '出行日期',
        departure_station: '出发站名',
        arrival_station: '到达站名',
        train_number: '车次',
        seat_type: '座位类型',
        departure_time: '出发时间',
        arrival_time: '到达时间',
        contact_person: '联系人',
        contact_phone: '联系人手机号',
        contact_email: '联系人邮箱',
        approval_reference: '审批参考人',
        company_name: '公司名称',
        booking_agent: '代订人',
        order_number: '订单号',
        bill_number: '账单号',
        sequence_number: '内部序列号',
        travel_order_number: '差旅单号',
        trip_submission_item: '行程提交项',
        ticket_sms: '出票短信'
      };
      const fieldDisplayName = fieldNames[field] || field;
      return `${fieldDisplayName}长度不能超过${rule.maxLength}个字符`;
    }

    // 格式验证
    if (rule.pattern && !rule.pattern.test(stringValue)) {
      return rule.message || `${field}格式不正确`;
    }

    // 身份证号码特殊验证：只在证件类型为身份证时验证格式
    if (field === 'id_number' && stringValue && idType === '身份证') {
      const idNumberPattern = /^[0-9Xx]{15}$|^[0-9Xx]{18}$/;
      if (!idNumberPattern.test(stringValue)) {
        return '身份证号码格式不正确（15位或18位数字或字母X）';
      }
    }

    // 枚举值验证
    if (rule.enumValues && rule.enumValues.length > 0 && !rule.enumValues.includes(stringValue)) {
      return `${field}值不在允许的范围内`;
    }

    return null;
  };

  // 验证所有字段
  const validateAllFields = (order: TrainOrder): Record<string, string> => {
    const errors: Record<string, string> = {};

    // 基础必填字段（所有情况下都必填）
    const basicRequiredFields: (keyof TrainOrder)[] = [
      'traveler_full_name', 'id_type', 'id_number', 'mobile_phone', 'travel_date',
      'departure_station', 'arrival_station', 'train_number', 'seat_type',
      'departure_time', 'arrival_time', 'contact_person',
      'contact_phone', 'approval_reference'
    ];

    // 证件类型不为身份证时的额外必填字段
    const dynamicRequiredFields: (keyof TrainOrder)[] = [
      'traveler_surname', 'traveler_given_name', 'nationality',
      'gender', 'birth_date', 'id_expiry_date'
    ];

    // 验证基础必填字段
    basicRequiredFields.forEach(field => {
      const error = validateField(field, order[field], order.id_type);
      if (error) {
        errors[field] = error;
      }
    });

    // 验证动态必填字段（仅当证件类型不为身份证时）
    if (order.id_type && order.id_type !== '身份证') {
      dynamicRequiredFields.forEach(field => {
        const error = validateField(field, order[field], order.id_type);
        if (error) {
          errors[field] = error;
        }
      });
    }

    // 验证非必填字段的格式（如果有值的话）
    const optionalFields: (keyof TrainOrder)[] = ['contact_email', 'amount'];
    optionalFields.forEach(field => {
      if (order[field]) {
        const error = validateField(field, order[field], order.id_type);
        if (error) {
          errors[field] = error;
        }
      }
    });

    return errors;
  };

  // 根据订单状态确定编辑权限
  const getEditPermissions = () => {
    const status = order.order_status;
    
    // 已提交和处理中：不能编辑任何字段
    if (status === 'submitted' || status === 'processing') {
      return {
        canEditBasicInfo: false,
        canEditTripInfo: false,
        canEditBillingInfo: false,
        canSave: false,
        statusMessage: '已提交和处理中的订单不可编辑'
      };
    }
    
    // 预定完成：只能编辑对账单信息
    if (status === 'completed') {
      return {
        canEditBasicInfo: false,
        canEditTripInfo: false,
        canEditBillingInfo: true,
        canSave: true,
        statusMessage: '预定完成的订单只能编辑对账单信息'
      };
    }
    
    // 其他状态：可以编辑所有字段
    return {
      canEditBasicInfo: true,
      canEditTripInfo: true,
      canEditBillingInfo: true,
      canSave: true,
      statusMessage: null
    };
  };

  const permissions = getEditPermissions();

  const handleInputChange = (field: string, value: string | number | null) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // 实时验证
    const error = validateField(field as keyof TrainOrder, value, formData.id_type);
    setValidationErrors(prev => ({
      ...prev,
      [field]: error || ''
    }));
  };

  // 获取输入字段的通用属性
  const getInputProps = (fieldType: 'basic' | 'trip' | 'billing', additionalClass = '') => {
    let disabled = false;
    
    if (fieldType === 'basic' && !permissions.canEditBasicInfo) disabled = true;
    if (fieldType === 'trip' && !permissions.canEditTripInfo) disabled = true;
    if (fieldType === 'billing' && !permissions.canEditBillingInfo) disabled = true;
    
    const baseClass = `w-full px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent`;
    const disabledClass = disabled ? 'bg-gray-100 cursor-not-allowed' : '';
    
    return {
      disabled,
      className: `${baseClass} ${disabledClass} ${additionalClass}`.trim()
    };
  };

  const handleSubmit = () => {
    if (!permissions.canSave) return;

    // 验证所有字段
    const errors = validateAllFields(formData as TrainOrder);
    setValidationErrors(errors);

    if (Object.keys(errors).length > 0) {
      return;
    }

    // 根据权限过滤可保存的字段
    let dataToSave: Partial<TrainOrder> = { ...formData };
    
    if (!permissions.canEditBasicInfo) {
      // 移除基本信息字段
      delete dataToSave.traveler_full_name;
      delete dataToSave.traveler_surname;
      delete dataToSave.traveler_given_name;
      delete dataToSave.nationality;
      delete dataToSave.gender;
      delete dataToSave.birth_date;
      delete dataToSave.id_type;
      delete dataToSave.id_number;
      delete dataToSave.id_expiry_date;
      delete dataToSave.mobile_phone;
      delete dataToSave.mobile_phone_country_code;
    }
    
    if (!permissions.canEditTripInfo) {
      // 移除行程信息字段
      delete dataToSave.travel_date;
      delete dataToSave.departure_station;
      delete dataToSave.arrival_station;
      delete dataToSave.train_number;
      delete dataToSave.seat_type;
      delete dataToSave.departure_time;
      delete dataToSave.arrival_time;
      delete dataToSave.cost_center;
      delete dataToSave.sequence_number;
      delete dataToSave.trip_submission_item;
    }
    
    if (!permissions.canEditBillingInfo) {
      // 移除对账单信息字段
      delete dataToSave.company_name;
      delete dataToSave.booking_agent;
      delete dataToSave.ticket_sms;
      delete dataToSave.amount;
      delete dataToSave.order_number;
      delete dataToSave.bill_number;
    }
    
    onSave(dataToSave);
  };

  const seatTypes = [
    '',
    '硬座',
    '硬卧',
    '软卧',
    '二等座',
    '一等座',
    '商务座',
    '特等座'
  ];

  const genderOptions = [
    '',
    '男',
    '女'
  ];

  const idTypes = [
    '',
    '身份证',
    '护照',
    '港澳通行证',
    '台湾通行证',
    '其他'
  ];

  const countryOptions = [
    '+86',
    '+1',
    '+44',
    '+81',
    '+82',
    '+852',
    '+853',
    '+886'
  ];

  return (
    <div className="p-6 space-y-8">
      {/* 状态提示信息 */}
      {permissions.statusMessage && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-800">
                {permissions.statusMessage}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* 出行人基础信息 */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200">
          出行人基础信息
          {!permissions.canEditBasicInfo && (
            <span className="ml-2 text-sm text-gray-500">(只读)</span>
          )}
        </h3>
        <div className="overflow-hidden border border-gray-300 rounded-lg">
          <table className="w-full">
            <tbody>
              <tr className="border-b border-gray-200">
                <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                  <label className="text-sm font-medium text-gray-700">出行人姓名 *</label>
                </td>
                <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                  <div>
                    <input
                      type="text"
                      value={formData.traveler_full_name}
                      onChange={(e) => handleInputChange('traveler_full_name', e.target.value)}
                      {...getInputProps('basic')}
                      required
                    />
                    {validationErrors.traveler_full_name && (
                      <div className="text-red-500 text-xs mt-1">{validationErrors.traveler_full_name}</div>
                    )}
                  </div>
                </td>
                <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                  <label className="text-sm font-medium text-gray-700">出行人姓</label>
                </td>
                <td className="py-3 px-4 w-1/4">
                  <input
                    type="text"
                    value={formData.traveler_surname}
                    onChange={(e) => handleInputChange('traveler_surname', e.target.value)}
                    {...getInputProps('basic')}
                  />
                </td>
              </tr>
              <tr className="border-b border-gray-200">
                <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                  <label className="text-sm font-medium text-gray-700">出行人名</label>
                </td>
                <td className="py-3 px-4 border-r border-gray-200">
                  <input
                    type="text"
                    value={formData.traveler_given_name}
                    onChange={(e) => handleInputChange('traveler_given_name', e.target.value)}
                    {...getInputProps('basic')}
                  />
                </td>
                <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                  <label className="text-sm font-medium text-gray-700">国籍</label>
                </td>
                <td className="py-3 px-4">
                  <input
                    type="text"
                    value={formData.nationality}
                    onChange={(e) => handleInputChange('nationality', e.target.value)}
                    {...getInputProps('basic')}
                    placeholder="中国"
                  />
                </td>
              </tr>
              <tr className="border-b border-gray-200">
                <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                  <label className="text-sm font-medium text-gray-700">性别</label>
                </td>
                <td className="py-3 px-4 border-r border-gray-200">
                  <select
                    value={formData.gender}
                    onChange={(e) => handleInputChange('gender', e.target.value)}
                    {...getInputProps('basic')}
                  >
                    {genderOptions.map(option => (
                      <option key={option} value={option}>
                        {option || '请选择'}
                      </option>
                    ))}
                  </select>
                </td>
                <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                  <label className="text-sm font-medium text-gray-700">出生日期</label>
                </td>
                <td className="py-3 px-4">
                  <input
                    type="date"
                    value={formData.birth_date}
                    onChange={(e) => handleInputChange('birth_date', e.target.value)}
                    {...getInputProps('basic')}
                  />
                </td>
              </tr>
              <tr className="border-b border-gray-200">
                <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                  <label className="text-sm font-medium text-gray-700">证件类型</label>
                </td>
                <td className="py-3 px-4 border-r border-gray-200">
                  <select
                    value={formData.id_type}
                    onChange={(e) => handleInputChange('id_type', e.target.value)}
                    {...getInputProps('basic')}
                  >
                    {idTypes.map(option => (
                      <option key={option} value={option}>
                        {option || '请选择'}
                      </option>
                    ))}
                  </select>
                </td>
                <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                  <label className="text-sm font-medium text-gray-700">证件号码</label>
                </td>
                <td className="py-3 px-4">
                  <input
                    type="text"
                    value={formData.id_number}
                    onChange={(e) => handleInputChange('id_number', e.target.value)}
                    {...getInputProps('basic', 'font-mono')}
                  />
                </td>
              </tr>
              <tr className="border-b border-gray-200">
                <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                  <label className="text-sm font-medium text-gray-700">证件有效期</label>
                </td>
                <td className="py-3 px-4 border-r border-gray-200">
                  <input
                    type="date"
                    value={formData.id_expiry_date}
                    onChange={(e) => handleInputChange('id_expiry_date', e.target.value)}
                    {...getInputProps('basic')}
                  />
                </td>
                <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                  <label className="text-sm font-medium text-gray-700">手机国际区号</label>
                </td>
                <td className="py-3 px-4">
                  <select
                    value={formData.mobile_phone_country_code}
                    onChange={(e) => handleInputChange('mobile_phone_country_code', e.target.value)}
                    {...getInputProps('basic')}
                  >
                    {countryOptions.map(code => (
                      <option key={code} value={code}>{code}</option>
                    ))}
                  </select>
                </td>
              </tr>
              <tr>
                <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                  <label className="text-sm font-medium text-gray-700">手机号</label>
                </td>
                <td className="py-3 px-4">
                  <input
                    type="text"
                    value={formData.mobile_phone}
                    onChange={(e) => handleInputChange('mobile_phone', e.target.value)}
                    {...getInputProps('basic')}
                  />
                </td>
                <td className="py-3 px-4 bg-gray-50 border-r border-gray-200"></td>
                <td className="py-3 px-4"></td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* 出行人行程信息 */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200">
          出行人行程信息
          {!permissions.canEditTripInfo && (
            <span className="ml-2 text-sm text-gray-500">(只读)</span>
          )}
        </h3>
        <div className="overflow-hidden border border-gray-300 rounded-lg">
          <table className="w-full">
            <tbody>
              <tr className="border-b border-gray-200">
                <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                  <label className="text-sm font-medium text-gray-700">差旅单号</label>
                </td>
                <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                  <input
                    type="number"
                    value={formData.sequence_number}
                    onChange={(e) => handleInputChange('sequence_number', parseInt(e.target.value) || 0)}
                    {...getInputProps('trip')}
                    placeholder="自动生成"
                  />
                </td>
                <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                  <label className="text-sm font-medium text-gray-700">出行日期</label>
                </td>
                <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                  <input
                    type="date"
                    value={formData.travel_date}
                    onChange={(e) => handleInputChange('travel_date', e.target.value)}
                    {...getInputProps('trip')}
                  />
                </td>
              </tr>
              <tr className="border-b border-gray-200">
                <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                  <label className="text-sm font-medium text-gray-700">出发站名</label>
                </td>
                <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                  <input
                    type="text"
                    value={formData.departure_station}
                    onChange={(e) => handleInputChange('departure_station', e.target.value)}
                    {...getInputProps('trip')}
                    placeholder="北京西"
                  />
                </td>
                <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                  <label className="text-sm font-medium text-gray-700">到达站名</label>
                </td>
                <td className="py-3 px-4 w-1/4">
                  <input
                    type="text"
                    value={formData.arrival_station}
                    onChange={(e) => handleInputChange('arrival_station', e.target.value)}
                    {...getInputProps('trip')}
                    placeholder="上海虹桥"
                  />
                </td>
              </tr>
              <tr className="border-b border-gray-200">
                <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                  <label className="text-sm font-medium text-gray-700">车次</label>
                </td>
                <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                  <input
                    type="text"
                    value={formData.train_number}
                    onChange={(e) => handleInputChange('train_number', e.target.value)}
                    {...getInputProps('trip', 'font-medium')}
                    placeholder="G1234"
                  />
                </td>
                <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                  <label className="text-sm font-medium text-gray-700">座位类型</label>
                </td>
                <td className="py-3 px-4 w-1/4">
                  <select
                    value={formData.seat_type}
                    onChange={(e) => handleInputChange('seat_type', e.target.value)}
                    {...getInputProps('trip')}
                  >
                    {seatTypes.map(type => (
                      <option key={type} value={type}>
                        {type || '请选择'}
                      </option>
                    ))}
                  </select>
                </td>
              </tr>
              <tr className="border-b border-gray-200">
                <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                  <label className="text-sm font-medium text-gray-700">出发时间</label>
                </td>
                <td className="py-3 px-4 border-r border-gray-200">
                  <input
                    type="time"
                    value={formData.departure_time}
                    onChange={(e) => handleInputChange('departure_time', e.target.value)}
                    {...getInputProps('trip')}
                  />
                </td>
                <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                  <label className="text-sm font-medium text-gray-700">到达时间</label>
                </td>
                <td className="py-3 px-4">
                  <input
                    type="time"
                    value={formData.arrival_time}
                    onChange={(e) => handleInputChange('arrival_time', e.target.value)}
                    {...getInputProps('trip')}
                  />
                </td>
              </tr>

              <tr className="border-b border-gray-200">
                <td className="py-3 px-4 bg-blue-50 border-r border-gray-200">
                  <label className="text-sm font-medium text-blue-800">行程提交项</label>
                </td>
                <td className="py-3 px-4" colSpan={3}>
                  <textarea
                    value={formData.trip_submission_item}
                    onChange={(e) => handleInputChange('trip_submission_item', e.target.value)}
                    rows={3}
                    {...getInputProps('trip', 'resize-vertical')}
                    placeholder="详细的行程安排和需求..."
                  />
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* 对账单 */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200">
          对账单
          {!permissions.canEditBillingInfo && (
            <span className="ml-2 text-sm text-gray-500">(只读)</span>
          )}
        </h3>
        <div className="overflow-hidden border border-gray-300 rounded-lg">
          <table className="w-full">
            <tbody>
              <tr className="border-b border-gray-200">
                <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                  <label className="text-sm font-medium text-gray-700">公司名称</label>
                </td>
                <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                  <input
                    type="text"
                    value={formData.company_name}
                    onChange={(e) => handleInputChange('company_name', e.target.value)}
                    {...getInputProps('billing')}
                  />
                </td>
                <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                  <label className="text-sm font-medium text-gray-700">代订人</label>
                </td>
                <td className="py-3 px-4 w-1/4">
                  <input
                    type="text"
                    value={formData.booking_agent}
                    onChange={(e) => handleInputChange('booking_agent', e.target.value)}
                    {...getInputProps('billing')}
                  />
                </td>
              </tr>
              <tr className="border-b border-gray-200">
                <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                  <label className="text-sm font-medium text-gray-700">订单号</label>
                </td>
                <td className="py-3 px-4 border-r border-gray-200">
                  <input
                    type="text"
                    value={formData.order_number}
                    onChange={(e) => handleInputChange('order_number', e.target.value)}
                    {...getInputProps('billing', 'font-mono')}
                  />
                </td>
                <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                  <label className="text-sm font-medium text-gray-700">账单号</label>
                </td>
                <td className="py-3 px-4">
                  <input
                    type="text"
                    value={formData.bill_number}
                    onChange={(e) => handleInputChange('bill_number', e.target.value)}
                    {...getInputProps('billing', 'font-mono')}
                  />
                </td>
              </tr>
              <tr className="border-b border-gray-200">
                <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                  <label className="text-sm font-medium text-gray-700">金额 (元)</label>
                </td>
                <td className="py-3 px-4 border-r border-gray-200">
                  <input
                    type="number"
                    step="0.01"
                    value={formData.amount || ''}
                    onChange={(e) => handleInputChange('amount', e.target.value ? parseFloat(e.target.value) : null)}
                    {...getInputProps('billing')}
                  />
                </td>
                <td className="py-3 px-4 bg-gray-50 border-r border-gray-200"></td>
                <td className="py-3 px-4"></td>
              </tr>
              <tr>
                <td className="py-3 px-4 bg-yellow-50 border-r border-gray-200">
                  <label className="text-sm font-medium text-yellow-800">出票短信</label>
                </td>
                <td className="py-3 px-4" colSpan={3}>
                  <textarea
                    value={formData.ticket_sms}
                    onChange={(e) => handleInputChange('ticket_sms', e.target.value)}
                    rows={3}
                    {...getInputProps('billing', 'resize-vertical')}
                    placeholder="购票成功短信内容..."
                  />
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* 操作按钮 */}
      <div className="flex justify-end gap-3 pt-6 border-t border-gray-200">
        <Button
          variant="outline"
          onClick={onCancel}
          disabled={loading}
        >
          取消
        </Button>
        <Button
          onClick={handleSubmit}
          disabled={loading || !permissions.canSave}
        >
          {loading ? '保存中...' : '保存'}
        </Button>
      </div>
    </div>
  );
}; 