import React, { useState, useEffect } from 'react';
import { rpaErrorLogApi, type RpaErrorLog } from '../../api/rpaErrorLog';

interface FailureDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  orderId: string;
  taskId?: string;
  failReason?: string;
}

const FailureDetailsModal: React.FC<FailureDetailsModalProps> = ({
  isOpen,
  onClose,
  orderId,
  taskId,
  failReason
}) => {
  const [errorLogs, setErrorLogs] = useState<RpaErrorLog[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (isOpen && orderId) {
      fetchErrorLogs();
    }
  }, [isOpen, orderId, taskId]);



  const fetchErrorLogs = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await rpaErrorLogApi.getByOrderId(orderId, 1, 50);
      setErrorLogs(response?.data?.items || []);
    } catch (err) {
      console.error('❌ 获取RPA错误日志失败:', err);
      setError('获取失败信息失败');
      console.log('❌ 设置错误状态，清空errorLogs');
      setErrorLogs([]); // 确保在错误时也设置为空数组
    } finally {
      setLoading(false);
    }
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[80vh] overflow-hidden">
        {/* 标题栏 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            预定失败详情 - 订单 #{orderId}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 内容区域 */}
        <div className="p-6 overflow-y-auto max-h-[calc(80vh-120px)]">
          {loading && (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-2 text-gray-600">加载中...</span>
            </div>
          )}

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-red-800">{error}</p>
                </div>
              </div>
            </div>
          )}

          {/* 基础失败原因 */}
          {failReason && (
            <div className="mb-6">
              <h3 className="text-lg font-medium text-gray-900 mb-3">基础失败原因</h3>
              <div className="bg-gray-50 rounded-md p-4">
                <p className="text-gray-700 whitespace-pre-wrap">{failReason}</p>
              </div>
            </div>
          )}

          {/* RPA错误日志 */}
          {!loading && !error && (
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-3">
                RPA执行错误日志 ({errorLogs?.length || 0} 条)
              </h3>



              {!errorLogs || errorLogs.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  暂无RPA错误日志记录
                </div>
              ) : (
                <div className="space-y-6">
                  {errorLogs.map((log) => (
                    <div key={log.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-center space-x-3">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            {log.error_type}
                          </span>
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            log.severity === 'high' ? 'bg-red-100 text-red-800' :
                            log.severity === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-green-100 text-green-800'
                          }`}>
                            {log.severity}
                          </span>
                        </div>
                        <span className="text-sm text-gray-500">
                          {formatDateTime(log.error_timestamp)}
                        </span>
                      </div>

                      {/* 详细信息列表 */}
                      <div className="grid grid-cols-1 gap-3">
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="font-medium text-gray-700">错误ID:</span>
                            <span className="ml-2 text-gray-600 font-mono text-xs">{log.error_id}</span>
                          </div>
                          <div>
                            <span className="font-medium text-gray-700">任务ID:</span>
                            <span className="ml-2 text-gray-600 font-mono text-xs">{log.task_id}</span>
                          </div>
                          <div>
                            <span className="font-medium text-gray-700">订单ID:</span>
                            <span className="ml-2 text-gray-600 font-mono text-xs">{log.order_id}</span>
                          </div>
                          <div>
                            <span className="font-medium text-gray-700">模块:</span>
                            <span className="ml-2 text-gray-600">{log.module}</span>
                          </div>
                          <div>
                            <span className="font-medium text-gray-700">函数:</span>
                            <span className="ml-2 text-gray-600">{log.function_name}</span>
                          </div>
                          <div>
                            <span className="font-medium text-gray-700">页面标题:</span>
                            <span className="ml-2 text-gray-600">{log.page_title || '-'}</span>
                          </div>
                        </div>

                        {/* 错误消息 */}
                        {log.error_message && (
                          <div className="mb-3">
                            <span className="font-medium text-gray-700">错误消息:</span>
                            <div className="mt-1 p-3 bg-red-50 border border-red-200 rounded">
                              <p className="text-sm text-red-800">{log.error_message}</p>
                            </div>
                          </div>
                        )}

                        {/* 页面URL */}
                        {log.page_url && (
                          <div className="mb-3">
                            <span className="font-medium text-gray-700">页面URL:</span>
                            <div className="mt-1 p-2 bg-gray-50 border border-gray-200 rounded">
                              <p className="text-xs text-gray-700 break-all font-mono">{log.page_url}</p>
                            </div>
                          </div>
                        )}

                        {/* 尝试的操作 */}
                        {log.action_attempted && (
                          <div className="mb-3">
                            <span className="font-medium text-gray-700">尝试的操作:</span>
                            <div className="mt-1 p-2 bg-blue-50 border border-blue-200 rounded">
                              <p className="text-sm text-blue-800">{log.action_attempted}</p>
                            </div>
                          </div>
                        )}

                        {/* 用户代理 */}
                        {log.user_agent && (
                          <div className="mb-3">
                            <span className="font-medium text-gray-700">用户代理:</span>
                            <div className="mt-1 p-2 bg-gray-50 border border-gray-200 rounded">
                              <p className="text-xs text-gray-700 font-mono break-all">{log.user_agent}</p>
                            </div>
                          </div>
                        )}

                        {/* 浏览器信息 */}
                        {log.browser_info && (
                          <div className="mb-3">
                            <span className="font-medium text-gray-700">浏览器信息:</span>
                            <div className="mt-1 p-2 bg-gray-50 border border-gray-200 rounded">
                              <pre className="text-xs text-gray-700 font-mono whitespace-pre-wrap">{log.browser_info}</pre>
                            </div>
                          </div>
                        )}

                        {/* 堆栈跟踪 */}
                        {log.stack_trace && (
                          <div className="mb-3">
                            <span className="font-medium text-gray-700">堆栈跟踪:</span>
                            <div className="mt-1 p-3 bg-red-50 border border-red-200 rounded">
                              <pre className="text-xs text-red-700 whitespace-pre-wrap font-mono overflow-x-auto">
                                {log.stack_trace}
                              </pre>
                            </div>
                          </div>
                        )}

                        {/* 额外上下文信息 */}
                        {log.additional_context && (
                          <div className="mb-3">
                            <span className="font-medium text-gray-700">额外上下文信息:</span>
                            <div className="mt-1 p-3 bg-yellow-50 border border-yellow-200 rounded">
                              <pre className="text-xs text-yellow-800 whitespace-pre-wrap font-mono overflow-x-auto">
                                {log.additional_context}
                              </pre>
                            </div>
                          </div>
                        )}

                        {/* 时间信息 */}
                        <div className="grid grid-cols-2 gap-4 text-xs text-gray-500 pt-3 border-t border-gray-200">
                          <div>
                            <span className="font-medium">创建时间:</span>
                            <span className="ml-2">{formatDateTime(log.created_at)}</span>
                          </div>
                          <div>
                            <span className="font-medium">更新时间:</span>
                            <span className="ml-2">{formatDateTime(log.updated_at)}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>

        {/* 底部按钮 */}
        <div className="flex justify-end p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            关闭
          </button>
        </div>
      </div>
    </div>
  );
};

export default FailureDetailsModal;
