import React, { useState, useEffect } from 'react';
import { Button } from '../ui/button';
import { X } from 'lucide-react';

interface TrainOrder {
  id: number;
  sequence_number: number;
  traveler_full_name: string;
  id_number: string;
  mobile_phone: string;
  travel_date: string;
  departure_station: string;
  arrival_station: string;
  train_number: string;
  seat_type: string;
  departure_time: string;
  arrival_time: string;
  cost_center: string;
  amount: number | null;
  order_number: string;
  order_status: string;
  created_at: string;
  updated_at: string;
}

interface EditOrderModalProps {
  order: TrainOrder;
  isOpen: boolean;
  onClose: () => void;
  onSave: (updatedData: Partial<TrainOrder>) => void;
  loading: boolean;
}

export const EditOrderModal: React.FC<EditOrderModalProps> = ({
  order,
  isOpen,
  onClose,
  onSave,
  loading
}) => {
  const [formData, setFormData] = useState<Partial<TrainOrder>>({});

  useEffect(() => {
    if (order) {
      setFormData({
        traveler_full_name: order.traveler_full_name,
        id_number: order.id_number,
        mobile_phone: order.mobile_phone,
        travel_date: order.travel_date,
        departure_station: order.departure_station,
        arrival_station: order.arrival_station,
        train_number: order.train_number,
        seat_type: order.seat_type,
        departure_time: order.departure_time,
        arrival_time: order.arrival_time,
        cost_center: order.cost_center,
        amount: order.amount,
      });
    }
  }, [order]);

  const handleInputChange = (field: keyof TrainOrder, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">编辑订单</h3>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            disabled={loading}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
        
        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-2 gap-4 mb-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">出行人姓名</label>
              <input
                type="text"
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={formData.traveler_full_name || ''}
                onChange={(e) => handleInputChange('traveler_full_name', e.target.value)}
                required
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">证件号</label>
              <input
                type="text"
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={formData.id_number || ''}
                onChange={(e) => handleInputChange('id_number', e.target.value)}
                required
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">手机号</label>
              <input
                type="text"
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={formData.mobile_phone || ''}
                onChange={(e) => handleInputChange('mobile_phone', e.target.value)}
                required
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">出行日期</label>
              <input
                type="date"
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={formData.travel_date || ''}
                onChange={(e) => handleInputChange('travel_date', e.target.value)}
                required
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">出发站</label>
              <input
                type="text"
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={formData.departure_station || ''}
                onChange={(e) => handleInputChange('departure_station', e.target.value)}
                required
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">到达站</label>
              <input
                type="text"
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={formData.arrival_station || ''}
                onChange={(e) => handleInputChange('arrival_station', e.target.value)}
                required
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">车次</label>
              <input
                type="text"
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={formData.train_number || ''}
                onChange={(e) => handleInputChange('train_number', e.target.value)}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">座位类型</label>
              <select
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={formData.seat_type || ''}
                onChange={(e) => handleInputChange('seat_type', e.target.value)}
              >
                <option value="">请选择座位类型</option>
                <option value="硬座">硬座</option>
                <option value="硬卧">硬卧</option>
                <option value="软卧">软卧</option>
                <option value="二等座">二等座</option>
                <option value="一等座">一等座</option>
                <option value="商务座">商务座</option>
                <option value="特等座">特等座</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">出发时间</label>
              <input
                type="time"
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={formData.departure_time || ''}
                onChange={(e) => handleInputChange('departure_time', e.target.value)}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">到达时间</label>
              <input
                type="time"
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={formData.arrival_time || ''}
                onChange={(e) => handleInputChange('arrival_time', e.target.value)}
              />
            </div>
            

            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">金额</label>
              <input
                type="number"
                step="0.01"
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={formData.amount || ''}
                onChange={(e) => handleInputChange('amount', parseFloat(e.target.value) || 0)}
              />
            </div>
          </div>
          
          <div className="flex justify-end gap-3">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={loading}
            >
              取消
            </Button>
            <Button
              type="submit"
              disabled={loading}
            >
              {loading ? '保存中...' : '保存'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}; 