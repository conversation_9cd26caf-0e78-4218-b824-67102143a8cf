import React, { useEffect, useRef } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/store/auth-context';

/**
 * 认证守卫组件
 * 监控认证状态变化，确保在认证失败时能够可靠地跳转到登录页面
 */
const AuthGuard: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const lastAuthState = useRef(isAuthenticated);
  const redirectTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    console.log('🛡️ [AuthGuard] 认证状态检查:', {
      isAuthenticated,
      hasUser: !!user,
      currentPath: location.pathname,
      lastAuthState: lastAuthState.current
    });

    // 检测认证状态从true变为false（认证失效）
    if (lastAuthState.current && !isAuthenticated) {
      console.warn('🔒 [AuthGuard] 检测到认证状态失效');
      
      // 如果当前不在登录页面，跳转到登录页面
      if (location.pathname !== '/login') {
        console.log('🔄 [AuthGuard] 准备跳转到登录页面...');
        
        // 清除之前的重定向定时器
        if (redirectTimeoutRef.current) {
          clearTimeout(redirectTimeoutRef.current);
        }
        
        // 设置重定向定时器，确保跳转执行
        redirectTimeoutRef.current = setTimeout(() => {
          try {
            console.log('🔄 [AuthGuard] 执行React Router导航到登录页面');
            navigate('/login', { replace: true });
            console.log('✅ [AuthGuard] React Router导航已执行');
          } catch (error) {
            console.error('❌ [AuthGuard] React Router导航失败:', error);
            
            // 如果React Router导航失败，使用原生跳转
            try {
              console.log('🔄 [AuthGuard] 使用原生跳转作为备用方案');
              window.location.replace('/login');
              console.log('✅ [AuthGuard] 原生跳转已执行');
            } catch (error2) {
              console.error('❌ [AuthGuard] 原生跳转也失败:', error2);
            }
          }
        }, 50); // 短暂延迟，确保状态更新完成
      }
    }

    // 更新上次认证状态
    lastAuthState.current = isAuthenticated;

    // 清理定时器
    return () => {
      if (redirectTimeoutRef.current) {
        clearTimeout(redirectTimeoutRef.current);
        redirectTimeoutRef.current = null;
      }
    };
  }, [isAuthenticated, user, location.pathname, navigate]);

  // 监听全局认证失败事件（作为额外保障）
  useEffect(() => {
    const handleGlobalAuthFailure = (event: CustomEvent) => {
      console.warn('🔒 [AuthGuard] 收到全局认证失败事件:', event.detail);
      
      if (location.pathname !== '/login') {
        console.log('🔄 [AuthGuard] 响应全局认证失败事件，执行跳转');
        
        // 清除之前的重定向定时器
        if (redirectTimeoutRef.current) {
          clearTimeout(redirectTimeoutRef.current);
        }
        
        redirectTimeoutRef.current = setTimeout(() => {
          try {
            navigate('/login', { replace: true });
            console.log('✅ [AuthGuard] 全局事件响应跳转完成');
          } catch (error) {
            console.error('❌ [AuthGuard] 全局事件响应跳转失败:', error);
            window.location.replace('/login');
          }
        }, 100);
      }
    };

    window.addEventListener('authenticationFailure', handleGlobalAuthFailure as EventListener);

    return () => {
      window.removeEventListener('authenticationFailure', handleGlobalAuthFailure as EventListener);
    };
  }, [location.pathname, navigate]);

  return <>{children}</>;
};

export default AuthGuard;
