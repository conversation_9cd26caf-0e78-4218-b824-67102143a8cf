import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { Search, AlertTriangle, RefreshCw, Eye, X, ChevronLeft, ChevronRight } from 'lucide-react';
import { 
  errorLogsApi, 
  type ErrorLog, 
  type ErrorLogListResponse, 
  type ErrorLogOptionsResponse,
  type ErrorLogQueryParams 
} from '@/api/errorLogs';

// 可搜索的下拉选择组件
interface SearchableSelectProps {
  options: Array<{ label: string; value: string }>;
  value: string;
  onChange: (value: string) => void;
  placeholder: string;
  className?: string;
}

const SearchableSelect: React.FC<SearchableSelectProps> = ({
  options,
  value,
  onChange,
  placeholder,
  className = ""
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  const filteredOptions = options.filter(option =>
    option.label.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const selectedOption = options.find(option => option.value === value);

  return (
    <div className={`relative ${className}`}>
      <div
        className="w-full px-3 py-2 border border-gray-300 rounded-md cursor-pointer bg-white flex justify-between items-center"
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className={selectedOption ? 'text-gray-900' : 'text-gray-500'}>
          {selectedOption ? selectedOption.label : placeholder}
        </span>
        <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </div>
      
      {isOpen && (
        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
          <div className="p-2">
            <Input
              type="text"
              placeholder="搜索..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full"
            />
          </div>
          <div className="py-1">
            <div
              className="px-3 py-2 cursor-pointer hover:bg-gray-100 text-gray-500"
              onClick={() => {
                onChange('');
                setIsOpen(false);
                setSearchTerm('');
              }}
            >
              {placeholder}
            </div>
            {filteredOptions.map((option) => (
              <div
                key={option.value}
                className="px-3 py-2 cursor-pointer hover:bg-gray-100"
                onClick={() => {
                  onChange(option.value);
                  setIsOpen(false);
                  setSearchTerm('');
                }}
              >
                {option.label}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

// 错误详情弹窗组件
interface ErrorDetailModalProps {
  errorLog: ErrorLog | null;
  isOpen: boolean;
  onClose: () => void;
  errorLogs: ErrorLog[];
  currentIndex: number;
  onPrevious: () => void;
  onNext: () => void;
}

const ErrorDetailModal: React.FC<ErrorDetailModalProps> = ({
  errorLog,
  isOpen,
  onClose,
  errorLogs,
  currentIndex,
  onPrevious,
  onNext
}) => {
  if (!isOpen || !errorLog) return null;

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  // JSON格式化辅助函数
  const formatJsonString = (jsonString: string) => {
    try {
      const parsed = JSON.parse(jsonString);
      return JSON.stringify(parsed, null, 2);
    } catch {
      return jsonString;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'high':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'medium':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'low':
        return 'text-green-600 bg-green-50 border-green-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <AlertTriangle className="h-6 w-6 text-red-500" />
            <div>
              <h2 className="text-xl font-semibold text-gray-900">错误详情</h2>
              <p className="text-sm text-gray-500 mt-1">
                第 {currentIndex + 1} 条，共 {errorLogs.length} 条
              </p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            {/* 导航按钮 */}
            <Button
              variant="outline"
              size="sm"
              onClick={onPrevious}
              disabled={currentIndex <= 0}
              className="h-8 w-8 p-0"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={onNext}
              disabled={currentIndex >= errorLogs.length - 1}
              className="h-8 w-8 p-0"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>

            {/* 关闭按钮 */}
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-8 w-8 p-0 ml-2"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* 内容 */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          <div className="space-y-6">
            {/* 基本信息 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label className="text-sm font-medium text-gray-700">错误ID</Label>
                <div className="mt-1 p-2 bg-gray-50 rounded border font-mono text-sm">
                  {errorLog.error_id}
                </div>
              </div>
              <div>
                <Label className="text-sm font-medium text-gray-700">任务ID</Label>
                <div className="mt-1 p-2 bg-gray-50 rounded border font-mono text-sm">
                  {errorLog.task_id}
                </div>
              </div>
              <div>
                <Label className="text-sm font-medium text-gray-700">订单ID</Label>
                <div className="mt-1 p-2 bg-gray-50 rounded border font-mono text-sm">
                  {errorLog.order_id}
                </div>
              </div>
              <div>
                <Label className="text-sm font-medium text-gray-700">发生时间</Label>
                <div className="mt-1 p-2 bg-gray-50 rounded border text-sm">
                  {formatDateTime(errorLog.error_timestamp)}
                </div>
              </div>
            </div>

            {/* 项目信息 */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label className="text-sm font-medium text-gray-700">项目名称</Label>
                <div className="mt-1 p-2 bg-gray-50 rounded border text-sm">
                  {errorLog.project_name || '-'}
                </div>
              </div>
              <div>
                <Label className="text-sm font-medium text-gray-700">创建人</Label>
                <div className="mt-1 p-2 bg-gray-50 rounded border text-sm">
                  {errorLog.creator_name || '-'}
                </div>
              </div>
              <div>
                <Label className="text-sm font-medium text-gray-700">任务类型</Label>
                <div className="mt-1 p-2 bg-gray-50 rounded border text-sm">
                  {errorLog.task_type || '-'}
                </div>
              </div>
            </div>

            {/* 错误信息 */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label className="text-sm font-medium text-gray-700">错误类型</Label>
                <div className="mt-1 p-2 bg-gray-50 rounded border text-sm">
                  {errorLog.error_type}
                </div>
              </div>
              <div>
                <Label className="text-sm font-medium text-gray-700">严重程度</Label>
                <div className="mt-1 p-2 rounded border text-sm font-medium" style={{
                  backgroundColor: errorLog.severity.toLowerCase() === 'high' ? '#fef2f2' :
                                   errorLog.severity.toLowerCase() === 'medium' ? '#fffbeb' :
                                   errorLog.severity.toLowerCase() === 'low' ? '#f0fdf4' : '#f9fafb',
                  borderColor: errorLog.severity.toLowerCase() === 'high' ? '#fecaca' :
                               errorLog.severity.toLowerCase() === 'medium' ? '#fed7aa' :
                               errorLog.severity.toLowerCase() === 'low' ? '#bbf7d0' : '#e5e7eb',
                  color: errorLog.severity.toLowerCase() === 'high' ? '#dc2626' :
                         errorLog.severity.toLowerCase() === 'medium' ? '#d97706' :
                         errorLog.severity.toLowerCase() === 'low' ? '#16a34a' : '#6b7280'
                }}>
                  {errorLog.severity}
                </div>
              </div>
              <div>
                <Label className="text-sm font-medium text-gray-700">模块</Label>
                <div className="mt-1 p-2 bg-gray-50 rounded border text-sm">
                  {errorLog.module}
                </div>
              </div>
            </div>

            {/* 详细错误信息 */}
            <div>
              <Label className="text-sm font-medium text-gray-700">错误信息</Label>
              <div className="mt-1 p-3 bg-red-50 rounded border border-red-200 text-sm">
                {errorLog.error_message || '-'}
              </div>
            </div>

            {/* 技术信息 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label className="text-sm font-medium text-gray-700">函数名称</Label>
                <div className="mt-1 p-2 bg-gray-50 rounded border font-mono text-sm">
                  {errorLog.function_name}
                </div>
              </div>
              <div>
                <Label className="text-sm font-medium text-gray-700">用户代理</Label>
                <div className="mt-1 p-2 bg-gray-50 rounded border text-sm">
                  {errorLog.user_agent}
                </div>
              </div>
            </div>

            {/* 页面信息 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label className="text-sm font-medium text-gray-700">页面URL</Label>
                <div className="mt-1 p-2 bg-gray-50 rounded border text-sm break-all">
                  {errorLog.page_url}
                </div>
              </div>
              <div>
                <Label className="text-sm font-medium text-gray-700">页面标题</Label>
                <div className="mt-1 p-2 bg-gray-50 rounded border text-sm">
                  {errorLog.page_title}
                </div>
              </div>
            </div>

            {/* 操作信息 */}
            <div>
              <Label className="text-sm font-medium text-gray-700">尝试的操作</Label>
              <div className="mt-1 p-2 bg-gray-50 rounded border text-sm">
                {errorLog.action_attempted || '-'}
              </div>
            </div>

            {/* 浏览器信息 */}
            <div>
              <Label className="text-sm font-medium text-gray-700">浏览器信息</Label>
              <div className="mt-1 p-3 bg-gray-50 rounded border text-sm font-mono">
                <pre className="whitespace-pre-wrap">{formatJsonString(errorLog.browser_info)}</pre>
              </div>
            </div>

            {/* 堆栈跟踪 */}
            {errorLog.stack_trace && (
              <div>
                <Label className="text-sm font-medium text-gray-700">堆栈跟踪</Label>
                <div className="mt-1 p-3 bg-gray-900 text-green-400 rounded border text-sm font-mono overflow-x-auto">
                  <pre className="whitespace-pre-wrap">{errorLog.stack_trace}</pre>
                </div>
              </div>
            )}

            {/* 附加上下文 */}
            {errorLog.additional_context && errorLog.additional_context.trim() !== '' && (
              <div>
                <Label className="text-sm font-medium text-gray-700">附加上下文</Label>
                <div className="mt-1 p-3 bg-blue-50 rounded border border-blue-200 text-sm font-mono">
                  <pre className="whitespace-pre-wrap">{formatJsonString(errorLog.additional_context)}</pre>
                </div>
              </div>
            )}

            {/* 时间信息 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label className="text-sm font-medium text-gray-700">创建时间</Label>
                <div className="mt-1 p-2 bg-gray-50 rounded border text-sm">
                  {formatDateTime(errorLog.created_at)}
                </div>
              </div>
              <div>
                <Label className="text-sm font-medium text-gray-700">更新时间</Label>
                <div className="mt-1 p-2 bg-gray-50 rounded border text-sm">
                  {formatDateTime(errorLog.updated_at)}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 底部 */}
        <div className="flex justify-end p-6 border-t border-gray-200">
          <Button onClick={onClose} variant="outline">
            关闭
          </Button>
        </div>
      </div>
    </div>
  );
};

const ErrorLogsTab: React.FC = () => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [errorLogs, setErrorLogs] = useState<ErrorLogListResponse | null>(null);
  const [options, setOptions] = useState<ErrorLogOptionsResponse | null>(null);

  // 详情弹窗状态
  const [selectedErrorLog, setSelectedErrorLog] = useState<ErrorLog | null>(null);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [currentDetailIndex, setCurrentDetailIndex] = useState(0);
  
  // 搜索参数
  const [searchParams, setSearchParams] = useState<ErrorLogQueryParams>({
    page: 1,
    page_size: 20
  });

  // 表单状态
  const [creatorName, setCreatorName] = useState('');
  const [projectName, setProjectName] = useState('');
  const [taskType, setTaskType] = useState('');
  const [orderId, setOrderId] = useState('');

  // 加载筛选选项
  const loadOptions = async () => {
    try {
      const result = await errorLogsApi.getOptions();
      // 提取 data 字段中的实际数据
      setOptions(result.data || result);
    } catch (error: any) {
      console.error('加载筛选选项失败:', error);
      toast({
        title: "加载失败",
        description: "无法加载筛选选项",
        variant: "destructive"
      });
    }
  };

  // 加载错误日志数据
  const loadErrorLogs = async (params: ErrorLogQueryParams = searchParams) => {
    setLoading(true);
    try {
      const result = await errorLogsApi.getErrorLogs(params);
      console.log('错误日志数据加载成功:', result);
      // 提取 data 字段中的实际数据
      setErrorLogs(result.data || result);
    } catch (error: any) {
      console.error('加载错误日志失败:', error);
      toast({
        title: "加载失败",
        description: error?.message || "无法加载错误日志数据",
        variant: "destructive"
      });
      // 设置空数据以避免渲染错误
      setErrorLogs({ items: [], total: 0, page: 1, page_size: 20, total_pages: 0 });
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    loadOptions();
    loadErrorLogs();
  }, []);

  // 搜索处理
  const handleSearch = () => {
    const newParams: ErrorLogQueryParams = {
      page: 1,
      page_size: 20,
      ...(creatorName && { creator_name: creatorName }),
      ...(projectName && { project_name: projectName }),
      ...(taskType && { task_type: taskType }),
      ...(orderId && { order_id: orderId })
    };

    setSearchParams(newParams);
    loadErrorLogs(newParams);
  };

  // 重置搜索
  const handleReset = () => {
    setCreatorName('');
    setProjectName('');
    setTaskType('');
    setOrderId('');
    const newParams = { page: 1, page_size: 20 };
    setSearchParams(newParams);
    loadErrorLogs(newParams);
  };

  // 分页处理
  const handlePageChange = (newPage: number) => {
    const newParams = { ...searchParams, page: newPage };
    setSearchParams(newParams);
    loadErrorLogs(newParams);
  };

  // 页数大小变更处理
  const handlePageSizeChange = (newPageSize: number) => {
    const newParams = { ...searchParams, page: 1, page_size: newPageSize };
    setSearchParams(newParams);
    loadErrorLogs(newParams);
  };

  // 查看详情处理
  const handleViewDetail = (errorLog: ErrorLog) => {
    const index = errorLogs?.items?.findIndex(log => log.id === errorLog.id) || 0;
    setCurrentDetailIndex(index);
    setSelectedErrorLog(errorLog);
    setIsDetailModalOpen(true);
  };

  // 关闭详情弹窗
  const handleCloseDetail = () => {
    setIsDetailModalOpen(false);
    setSelectedErrorLog(null);
    setCurrentDetailIndex(0);
  };

  // 上一个错误详情
  const handlePreviousDetail = () => {
    if (!errorLogs?.items || currentDetailIndex <= 0) return;
    const newIndex = currentDetailIndex - 1;
    setCurrentDetailIndex(newIndex);
    setSelectedErrorLog(errorLogs.items[newIndex]);
  };

  // 下一个错误详情
  const handleNextDetail = () => {
    if (!errorLogs?.items || currentDetailIndex >= errorLogs.items.length - 1) return;
    const newIndex = currentDetailIndex + 1;
    setCurrentDetailIndex(newIndex);
    setSelectedErrorLog(errorLogs.items[newIndex]);
  };

  // 准备下拉选项
  const projectOptions = React.useMemo(() => {
    if (!options?.projects) return [];
    return options.projects.map(p => ({
      label: p.project_name || '',
      value: p.project_name || ''
    })).filter(option => option.label && option.value);
  }, [options]);

  const creatorOptions = React.useMemo(() => {
    if (!options?.creators) return [];
    return options.creators.map(c => ({
      label: c.creator_name || '',
      value: c.creator_name || ''
    })).filter(option => option.label && option.value);
  }, [options]);

  const taskTypeOptions = React.useMemo(() => {
    if (!options?.task_types) return [];
    return options.task_types.map(t => ({
      label: t.task_type || '',
      value: t.task_type || ''
    })).filter(option => option.label && option.value);
  }, [options]);

  // 格式化时间
  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    // 调试信息
    console.log('原始时间字符串:', dateString);
    console.log('解析后的Date对象:', date);
    console.log('本地时间字符串:', date.toLocaleString('zh-CN'));
    console.log('UTC时间字符串:', date.toUTCString());

    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      timeZone: 'Asia/Shanghai'
    });
  };

  // 获取严重程度颜色
  const getSeverityColor = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'high':
        return 'text-red-600 bg-red-50';
      case 'medium':
        return 'text-yellow-600 bg-yellow-50';
      case 'low':
        return 'text-green-600 bg-green-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  // 调试信息
  console.log('ErrorLogsTab 渲染状态:', {
    loading,
    errorLogs,
    hasItems: errorLogs?.items?.length,
    options
  });

  return (
    <div className="space-y-6">
      {/* 搜索区域 */}
      <Card className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-4">
          <div>
            <Label htmlFor="creator-name">创建人</Label>
            <SearchableSelect
              options={creatorOptions}
              value={creatorName}
              onChange={setCreatorName}
              placeholder="选择创建人"
              className="mt-1"
            />
          </div>
          
          <div>
            <Label htmlFor="project-name">项目名称</Label>
            <SearchableSelect
              options={projectOptions}
              value={projectName}
              onChange={setProjectName}
              placeholder="选择项目"
              className="mt-1"
            />
          </div>

          <div>
            <Label htmlFor="task-type">任务类型</Label>
            <SearchableSelect
              options={taskTypeOptions}
              value={taskType}
              onChange={setTaskType}
              placeholder="选择任务类型"
              className="mt-1"
            />
          </div>

          <div>
            <Label htmlFor="order-id">订单ID</Label>
            <Input
              id="order-id"
              type="text"
              value={orderId}
              onChange={(e) => setOrderId(e.target.value)}
              placeholder="输入订单ID"
              className="mt-1"
            />
          </div>
          
          <div className="flex items-end gap-2">
            <Button
              onClick={handleSearch}
              disabled={loading}
              className="bg-purple-600 hover:bg-purple-700 flex-1"
            >
              <Search className="h-4 w-4 mr-2" />
              {loading ? '搜索中...' : '搜索'}
            </Button>
            <Button
              onClick={handleReset}
              variant="outline"
              disabled={loading}
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </Card>

      {/* 错误日志列表 */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-red-500" />
            错误日志列表
          </h3>
          {errorLogs && (
            <span className="text-sm text-gray-500">
              共 {errorLogs.total} 条记录
            </span>
          )}
        </div>



        {loading ? (
          <div className="text-center py-8">
            <div className="inline-flex items-center gap-2 text-gray-500">
              <RefreshCw className="h-4 w-4 animate-spin" />
              加载中...
            </div>
          </div>
        ) : errorLogs && errorLogs.items && errorLogs.items.length > 0 ? (
          <>
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="text-left p-3 font-medium text-gray-900">错误ID</th>
                    <th className="text-left p-3 font-medium text-gray-900">任务ID</th>
                    <th className="text-left p-3 font-medium text-gray-900">订单ID</th>
                    <th className="text-left p-3 font-medium text-gray-900">项目名称</th>
                    <th className="text-left p-3 font-medium text-gray-900">创建人</th>
                    <th className="text-left p-3 font-medium text-gray-900">任务类型</th>
                    <th className="text-left p-3 font-medium text-gray-900">错误类型</th>
                    <th className="text-left p-3 font-medium text-gray-900">严重程度</th>
                    <th className="text-left p-3 font-medium text-gray-900">错误信息</th>
                    <th className="text-left p-3 font-medium text-gray-900">发生时间</th>
                    <th className="text-left p-3 font-medium text-gray-900">操作</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {errorLogs.items.map((log) => (
                    <tr key={log.id} className="hover:bg-gray-50">
                      <td className="p-3 text-blue-600 font-mono text-xs">{log.error_id}</td>
                      <td className="p-3 font-mono text-xs">{log.task_id}</td>
                      <td className="p-3 font-mono text-xs">{log.order_id}</td>
                      <td className="p-3">{log.project_name || '-'}</td>
                      <td className="p-3">{log.creator_name || '-'}</td>
                      <td className="p-3">{log.task_type || '-'}</td>
                      <td className="p-3">{log.error_type}</td>
                      <td className="p-3">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getSeverityColor(log.severity)}`}>
                          {log.severity}
                        </span>
                      </td>
                      <td className="p-3 max-w-xs truncate" title={log.error_message}>
                        {log.error_message || '-'}
                      </td>
                      <td className="p-3 text-xs">{formatDateTime(log.error_timestamp)}</td>
                      <td className="p-3">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleViewDetail(log)}
                          className="h-8 px-3 text-xs"
                        >
                          <Eye className="h-3 w-3 mr-1" />
                          详情
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* 分页 */}
            {errorLogs && errorLogs.total > 0 && (
              <div className="flex items-center justify-between mt-4">
                <div className="flex items-center gap-4">
                  <div className="text-sm text-gray-500">
                    第 {errorLogs?.page || 1} 页，共 {errorLogs?.total_pages || 1} 页
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-500">每页显示</span>
                    <select
                      value={searchParams.page_size || 20}
                      onChange={(e) => handlePageSizeChange(Number(e.target.value))}
                      className="px-2 py-1 border border-gray-300 rounded text-sm"
                    >
                      <option value={10}>10</option>
                      <option value={20}>20</option>
                      <option value={50}>50</option>
                      <option value={100}>100</option>
                    </select>
                    <span className="text-sm text-gray-500">条</span>
                  </div>
                  <div className="text-sm text-gray-500">
                    共 {errorLogs?.total || 0} 条记录
                  </div>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange((errorLogs?.page || 1) - 1)}
                    disabled={(errorLogs?.page || 1) <= 1}
                  >
                    上一页
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange((errorLogs?.page || 1) + 1)}
                    disabled={(errorLogs?.page || 1) >= (errorLogs?.total_pages || 1)}
                  >
                    下一页
                  </Button>
                </div>
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-8 text-gray-500">
            暂无错误日志数据
          </div>
        )}
      </Card>

      {/* 错误详情弹窗 */}
      <ErrorDetailModal
        errorLog={selectedErrorLog}
        isOpen={isDetailModalOpen}
        onClose={handleCloseDetail}
        errorLogs={errorLogs?.items || []}
        currentIndex={currentDetailIndex}
        onPrevious={handlePreviousDetail}
        onNext={handleNextDetail}
      />
    </div>
  );
};

export default ErrorLogsTab;
