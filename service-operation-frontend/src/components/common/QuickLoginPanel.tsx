import React from 'react';
import { quickLogin, TEST_USERS } from '@/utils/mock-login';
import { Card } from '@/components/ui/card';
import { User, LogIn } from 'lucide-react';

/**
 * 快速登录面板
 * 仅在开发环境显示，用于快速切换测试用户
 */
const QuickLoginPanel: React.FC = () => {
  // 仅在开发环境显示
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  const users = [
    {
      key: 'guowei',
      info: TEST_USERS.guowei,
      description: '管理员 - 所有权限',
      color: 'bg-blue-50 border-blue-200 text-blue-800',
      action: quickLogin.guowei,
    },
    {
      key: 'yuanxuxu',
      info: TEST_USERS.yuanxuxu,
      description: '超级管理员 - 所有权限',
      color: 'bg-purple-50 border-purple-200 text-purple-800',
      action: quickLogin.yuanxuxu,
    },
    {
      key: 'chenjie',
      info: TEST_USERS.chenjie,
      description: '普通用户 - 3个权限',
      color: 'bg-green-50 border-green-200 text-green-800',
      action: quickLogin.chenjie,
    },
    {
      key: 'testUser',
      info: TEST_USERS.testUser,
      description: '测试用户 - 3个权限',
      color: 'bg-yellow-50 border-yellow-200 text-yellow-800',
      action: quickLogin.testUser,
    },
    {
      key: 'dashboardOnly',
      info: TEST_USERS.dashboardOnly,
      description: '受限用户 - 1个权限',
      color: 'bg-red-50 border-red-200 text-red-800',
      action: quickLogin.dashboardOnly,
    },
  ];

  return (
    <div className="fixed top-4 left-4 z-50">
      <Card className="p-4 bg-white shadow-lg border-2 border-orange-200">
        <div className="flex items-center gap-2 mb-3 pb-2 border-b border-gray-200">
          <LogIn className="h-4 w-4 text-orange-600" />
          <h3 className="font-semibold text-gray-900">快速登录 (开发)</h3>
        </div>
        
        <div className="space-y-2">
          {users.map((user) => (
            <button
              key={user.key}
              onClick={user.action}
              className={`w-full text-left p-2 rounded border transition-colors hover:opacity-80 ${user.color}`}
            >
              <div className="flex items-center gap-2">
                <User className="h-3 w-3" />
                <div className="flex-1 min-w-0">
                  <div className="font-medium text-sm">{user.info.username}</div>
                  <div className="text-xs opacity-75">{user.description}</div>
                  <div className="text-xs opacity-60">ID: {user.info.userId}</div>
                </div>
              </div>
            </button>
          ))}
        </div>
        
        <div className="mt-3 pt-2 border-t border-gray-200 text-xs text-gray-500">
          点击用户名快速登录并刷新页面
        </div>
      </Card>
    </div>
  );
};

export default QuickLoginPanel;
