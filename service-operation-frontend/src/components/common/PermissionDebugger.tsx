import React, { useState } from 'react';
import { useAuth } from '@/store/auth-context';
import { usePermissions } from '@/hooks/usePermissions';
import { Eye, EyeOff, Shield, User, Settings } from 'lucide-react';

/**
 * 权限调试组件
 * 仅在开发环境显示，用于调试权限系统
 */
const PermissionDebugger: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);
  const { user, isAuthenticated } = useAuth();
  const { permissions, loading, error } = usePermissions();

  // 仅在开发环境显示
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      {/* 切换按钮 */}
      <button
        onClick={() => setIsVisible(!isVisible)}
        className="bg-blue-600 text-white p-2 rounded-full shadow-lg hover:bg-blue-700 transition-colors"
        title="权限调试器"
      >
        {isVisible ? <EyeOff size={20} /> : <Eye size={20} />}
      </button>

      {/* 调试面板 */}
      {isVisible && (
        <div className="absolute bottom-12 right-0 w-80 bg-white border border-gray-200 rounded-lg shadow-xl p-4 max-h-96 overflow-y-auto">
          <div className="flex items-center gap-2 mb-3 pb-2 border-b border-gray-200">
            <Shield className="h-4 w-4 text-blue-600" />
            <h3 className="font-semibold text-gray-900">权限调试器</h3>
          </div>

          {/* 用户信息 */}
          <div className="mb-4">
            <div className="flex items-center gap-2 mb-2">
              <User className="h-4 w-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">用户信息</span>
            </div>
            <div className="bg-gray-50 rounded p-2 text-xs">
              <div><strong>ID:</strong> {user?.id}</div>
              <div><strong>用户名:</strong> {user?.username}</div>
              <div><strong>邮箱:</strong> {user?.email}</div>
              <div><strong>角色:</strong> {user?.role}</div>
            </div>
          </div>

          {/* 权限状态 */}
          <div className="mb-4">
            <div className="flex items-center gap-2 mb-2">
              <Settings className="h-4 w-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">权限状态</span>
            </div>
            <div className="bg-gray-50 rounded p-2 text-xs">
              <div><strong>加载中:</strong> {loading ? '是' : '否'}</div>
              <div><strong>权限数量:</strong> {permissions.length}</div>
              {error && (
                <div className="text-red-600"><strong>错误:</strong> {error}</div>
              )}
            </div>
          </div>

          {/* 权限列表 */}
          <div>
            <div className="text-sm font-medium text-gray-700 mb-2">
              有效权限 ({permissions.length})
            </div>
            <div className="space-y-1 max-h-40 overflow-y-auto">
              {loading ? (
                <div className="text-xs text-gray-500">加载中...</div>
              ) : permissions.length === 0 ? (
                <div className="text-xs text-gray-500">无权限</div>
              ) : (
                permissions.map((permission) => (
                  <div
                    key={permission.id}
                    className="bg-blue-50 border border-blue-200 rounded p-2 text-xs"
                  >
                    <div className="font-medium text-blue-800">
                      {permission.permission_name}
                    </div>
                    <div className="text-blue-600">
                      {permission.permission_code}
                    </div>
                    <div className="text-blue-500">
                      {permission.resource_path}
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>

          {/* 菜单权限映射 */}
          <div className="mt-4 pt-3 border-t border-gray-200">
            <div className="text-sm font-medium text-gray-700 mb-2">
              菜单权限映射
            </div>
            <div className="space-y-1 text-xs">
              {[
                { path: '/dashboard', code: 'dttrip:dashboard', name: '应用中心' },
                { path: '/projects', code: 'dttrip:projects', name: '团房团票' },
                { path: '/passport-recognition', code: 'dttrip:passport', name: '护照识别' },
                { path: '/user-management', code: 'user_mgmt:access', name: '用户管理' },
                { path: '/settings', code: 'dttrip:settings', name: '系统设置' },
              ].map((menu) => {
                const hasPermission = permissions.some(p => p.permission_code === menu.code);
                return (
                  <div
                    key={menu.path}
                    className={`flex items-center justify-between p-1 rounded ${
                      hasPermission ? 'bg-green-50 text-green-700' : 'bg-red-50 text-red-700'
                    }`}
                  >
                    <span>{menu.name}</span>
                    <span className={`text-xs ${hasPermission ? 'text-green-600' : 'text-red-600'}`}>
                      {hasPermission ? '✓' : '✗'}
                    </span>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PermissionDebugger;
