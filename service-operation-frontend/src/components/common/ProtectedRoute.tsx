import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/store/auth-context';
import { usePermissions, hasPathPermission } from '@/hooks/usePermissions';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredPermission?: string; // 权限代码
  requiredPath?: string; // 资源路径
  fallbackPath?: string; // 无权限时的跳转路径
}

/**
 * 路由权限保护组件
 * 检查用户是否有访问特定路由的权限
 */
const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredPermission,
  requiredPath,
  fallbackPath = '/dashboard'
}) => {
  const { isAuthenticated, isLoading } = useAuth();
  const { permissions, loading: permissionsLoading } = usePermissions();
  const location = useLocation();

  // 如果认证状态还在加载中，显示加载状态
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">加载中...</span>
      </div>
    );
  }

  // 如果用户未认证，重定向到登录页
  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // 如果权限还在加载中，显示加载状态
  if (permissionsLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">检查权限中...</span>
      </div>
    );
  }

  // 如果没有指定权限要求，直接渲染子组件
  if (!requiredPermission && !requiredPath) {
    return <>{children}</>;
  }

  // 检查权限
  let hasAccess = false;

  if (requiredPermission) {
    // 根据权限代码检查
    hasAccess = permissions.some(permission => permission.permission_code === requiredPermission);
  } else if (requiredPath) {
    // 根据资源路径检查
    hasAccess = hasPathPermission(requiredPath, permissions);
  }

  // 如果没有权限，显示无权限页面
  if (!hasAccess) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-center">
        <div className="text-6xl text-gray-300 mb-4">🔒</div>
        <h2 className="text-xl font-semibold text-gray-700 mb-2">访问受限</h2>
        <p className="text-gray-500 mb-4">
          您没有访问此页面的权限，请联系管理员获取相应权限。
        </p>
        <div className="space-x-3">
          <button
            onClick={() => window.history.back()}
            className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
          >
            返回上一页
          </button>
          <button
            onClick={() => {
              // 清空所有localStorage
              localStorage.clear();

              // 清空所有sessionStorage
              sessionStorage.clear();

              // 清空所有cookies
              document.cookie.split(";").forEach(function(c) {
                document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
              });

              // 重定向到登录页面
              window.location.href = '/login';
            }}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            重新登录
          </button>
        </div>
      </div>
    );
  }

  // 有权限，渲染子组件
  return <>{children}</>;
};

export default ProtectedRoute;
