import React, { useState } from 'react';
import { useAuth } from '../store/auth-context';
import { api } from '@/services/system';

interface PassportListResponse {
  total: number;
  items: any[];
  page: number;
  size: number;
}

export const AuthStatus: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const [apiTestResult, setApiTestResult] = useState<string>('');
  const [isTestingApi, setIsTestingApi] = useState(false);

  const testApi = async () => {
    setIsTestingApi(true);
    setApiTestResult('测试中...');
    
    try {
      const response = await api.get<PassportListResponse>('/passport/list');
      if (response.success) {
        setApiTestResult(`✅ API调用成功，返回 ${response.data?.total || 0} 条记录`);
      } else {
        setApiTestResult(`❌ API调用失败: ${response.error}`);
      }
    } catch (error) {
      setApiTestResult(`❌ API调用异常: ${error}`);
    } finally {
      setIsTestingApi(false);
    }
  };

  const updateToken = () => {
    const newToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NDg0ODg4NjUsInN1YiI6IjEyMzQ5NyJ9.3jO2GuEEPoxEqN3XJBcliC5itDvqQSchI1plsuCNUgo';
    localStorage.setItem('access_token', newToken);
    console.log('✅ Token已更新:', newToken);
    alert('Token已更新，请刷新页面');
  };

  return (
    <div className="bg-gray-100 p-4 rounded-lg mb-4">
      <h3 className="text-lg font-semibold mb-2">认证状态检查</h3>
      
      <div className="space-y-2">
        <div>
          <strong>认证状态:</strong> {isAuthenticated ? '✅ 已认证' : '❌ 未认证'}
        </div>
        
        {user && (
          <div>
            <strong>用户信息:</strong> {user.username} ({user.email})
          </div>
        )}
        
        <div>
          <strong>Token状态:</strong> {localStorage.getItem('access_token') ? '✅ 存在' : '❌ 不存在'}
        </div>
        
        <div className="flex gap-2">
          <button
            onClick={testApi}
            disabled={isTestingApi}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
          >
            {isTestingApi ? '测试中...' : '测试API'}
          </button>
          
          <button
            onClick={updateToken}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          >
            更新Token
          </button>
        </div>
        
        {apiTestResult && (
          <div className="mt-2 p-2 bg-white rounded border">
            <strong>API测试结果:</strong> {apiTestResult}
          </div>
        )}
      </div>
    </div>
  );
}; 