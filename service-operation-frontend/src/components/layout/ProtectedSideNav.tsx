import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { LayoutDashboard, Settings, GripVertical, FileText, FolderOpen, Users, BarChart3 } from 'lucide-react';
import { useAuth } from '@/store/auth-context';
import { usePermissions } from '@/hooks/usePermissions';

interface SideNavProps {
  isCollapsed: boolean;
  onCollapse: (collapsed: boolean) => void;
}

interface MenuItem {
  path: string;
  label: string;
  icon: React.ComponentType<{ size?: number }>;
  permissionCode: string;
}

const ProtectedSideNav: React.FC<SideNavProps> = ({ isCollapsed, onCollapse }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { isAuthenticated } = useAuth();
  const { permissions, loading } = usePermissions();

  // 菜单项配置 - 只包含在权限管理系统中定义的权限
  const allMenuItems: MenuItem[] = [
    {
      path: '/dashboard',
      label: '应用中心',
      icon: LayoutDashboard,
      permissionCode: 'dttrip:dashboard',
    },
    {
      path: '/projects',
      label: '团房团票',
      icon: FolderOpen,
      permissionCode: 'dttrip:projects',
    },
    {
      path: '/passport-recognition',
      label: '护照识别',
      icon: FileText,
      permissionCode: 'dttrip:passport',
    },
    {
      path: '/user-management',
      label: '用户管理',
      icon: Users,
      permissionCode: 'user_mgmt:access',
    },
    {
      path: '/data-analysis',
      label: '数据分析',
      icon: BarChart3,
      permissionCode: 'dttrip:data_analysis',
    },
    {
      path: '/settings',
      label: '系统设置',
      icon: Settings,
      permissionCode: 'dttrip:settings',
    },
  ];

  // 根据用户权限过滤菜单项（只检查权限管理系统中定义的权限）
  const authorizedMenuItems = React.useMemo(() => {
    if (!isAuthenticated) {
      return [];
    }

    // 如果权限还在加载中，返回空数组（避免显示无权限的菜单项）
    if (loading) {
      return [];
    }

    // 根据用户权限过滤菜单 - 只检查在权限管理系统中定义的权限
    return allMenuItems.filter(item => {
      return permissions.some(permission => permission.permission_code === item.permissionCode);
    });
  }, [isAuthenticated, loading, permissions, allMenuItems]);

  // 判断当前路由是否激活
  const isActiveRoute = (path: string) => {
    return location.pathname === path;
  };

  // 处理菜单点击
  const handleMenuClick = (path: string) => {
    navigate(path);
  };

  // 如果用户未认证，不显示菜单
  if (!isAuthenticated) {
    return (
      <div className="h-full flex flex-col">
        <div className="flex-1 py-4 px-2 space-y-1 overflow-y-auto">
          <div className="text-center text-gray-500 text-sm py-4">
            请先登录
          </div>
        </div>
        
        {/* 折叠按钮 */}
        <button 
          onClick={() => onCollapse(!isCollapsed)}
          className="flex-shrink-0 w-full h-10 flex items-center justify-center border-t border-gray-200 text-gray-500 hover:bg-gray-50"
        >
          <GripVertical size={16} />
        </button>
      </div>
    );
  }

  // 移除权限加载状态显示，直接显示菜单

  return (
    <div className="h-full flex flex-col">
      {/* 导航菜单 */}
      <nav className="flex-1 py-4 px-2 space-y-1 overflow-y-auto">
        {authorizedMenuItems.length === 0 ? (
          // 如果权限正在加载，不显示任何提示；否则显示无菜单提示
          loading ? null : (
            <div className="text-center text-gray-500 text-sm py-4">
              {isCollapsed ? '无菜单' : '暂无菜单'}
            </div>
          )
        ) : (
          authorizedMenuItems.map((item) => {
            const Icon = item.icon;
            const isActive = isActiveRoute(item.path);
            
            return (
              <button
                key={item.path}
                onClick={() => handleMenuClick(item.path)}
                className={`w-full flex items-center ${
                  isCollapsed ? 'justify-center px-2' : 'px-3'
                } py-2 rounded-md text-sm font-medium transition-colors ${
                  isActive
                    ? 'text-blue-600 bg-blue-50 border-r-2 border-blue-600'
                    : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                }`}
                title={isCollapsed ? item.label : undefined}
              >
                <Icon size={20} />
                {!isCollapsed && <span className="ml-3">{item.label}</span>}
              </button>
            );
          })
        )}
      </nav>

      {/* 菜单信息显示（仅在开发环境） */}
      {import.meta.env.DEV && !isCollapsed && (
        <div className="px-2 py-2 border-t border-gray-200 bg-gray-50">
          <div className="text-xs text-gray-500">
            菜单数: {authorizedMenuItems.length}
          </div>
        </div>
      )}

      {/* 折叠按钮 */}
      <button 
        onClick={() => onCollapse(!isCollapsed)}
        className="flex-shrink-0 w-full h-10 flex items-center justify-center border-t border-gray-200 text-gray-500 hover:bg-gray-50"
      >
        <GripVertical size={16} />
      </button>
    </div>
  );
};

export default ProtectedSideNav;
