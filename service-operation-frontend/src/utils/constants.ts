/**
 * 从环境变量中获取配置，如果环境变量不存在，则使用默认值
 */

/**
 * API基础URL
 * 开发环境使用代理路径，生产环境使用完整URL
 */
export const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 
  (import.meta.env.DEV ? '/api' : 'http://localhost:8000/api');

/**
 * 后端服务URL
 */
export const SERVER_URL = import.meta.env.VITE_SERVER_URL || 'http://localhost:8000';

/**
 * 前端服务URL
 */
export const FRONTEND_URL = import.meta.env.VITE_FRONTEND_URL || 'http://localhost:5173';

/**
 * 开发者API密钥配置
 */
export const DEVELOPER_KEY = import.meta.env.VITE_DEVELOPER_KEY;
export const DEVELOPER_SECRET = import.meta.env.VITE_DEVELOPER_SECRET;

/**
 * SSO API端点
 */
export const SSO_API = {
  // 获取SSO登录URL
  getLoginUrl: `${API_BASE_URL}${import.meta.env.VITE_SSO_LOGIN_URL_ENDPOINT || '/auth/sso/login-url'}`,
  // 处理SSO回调
  handleCallback: `${API_BASE_URL}${import.meta.env.VITE_SSO_CALLBACK_ENDPOINT || '/auth/sso/callback'}`,
  // 获取SSO登出URL
  getLogoutUrl: `${API_BASE_URL}${import.meta.env.VITE_SSO_LOGOUT_URL_ENDPOINT || '/auth/sso/logout-url'}`,
  // 执行SSO登出
  logout: `${API_BASE_URL}${import.meta.env.VITE_SSO_LOGOUT_ENDPOINT || '/auth/sso/logout'}`
};
