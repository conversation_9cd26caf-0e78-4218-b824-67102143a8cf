// 创建测试Excel文件的脚本
import * as XLSX from 'xlsx-js-style';

export const createTestExcel = () => {
  // 创建测试数据
  const testData = [
    [], // 第1行空行
    ['编号', '项目', '姓名', '性别', '联系方式', '身份证号', '房间安排（同住人请合并单元格）', '入住时间', '离店时间', '备注'], // 第2行列名
    [], // 第3行空行
    // 第4行开始数据
    [1, '测试项目', '张三', '男', '13800138001', '110101199001011234', '标准间', '2024-01-15', '2024-01-17', '无特殊要求'],
    [2, '测试项目', '李四', '女', '13800138002', '110101199002022345', '标准间', '2024-01-15', '2024-01-17', ''],
    [3, '测试项目', '王五', '男', '13800138003', '110101199003033456', '豪华间', '2024-01-16', '2024-01-18', '需要加床'],
    [4, '测试项目', '赵六', '女', '13800138004', '110101199004044567', '豪华间', '2024-01-16', '2024-01-18', '']
  ];

  // 创建工作簿
  const workbook = XLSX.utils.book_new();
  const worksheet = XLSX.utils.aoa_to_sheet(testData);

  // 设置合并单元格（模拟同住人情况）
  worksheet['!merges'] = [
    { s: { r: 3, c: 6 }, e: { r: 4, c: 6 } }, // 房间安排合并单元格（第4-5行）
    { s: { r: 3, c: 7 }, e: { r: 4, c: 7 } }, // 入住时间合并单元格
    { s: { r: 3, c: 8 }, e: { r: 4, c: 8 } }, // 离店时间合并单元格
    { s: { r: 5, c: 6 }, e: { r: 6, c: 6 } }, // 房间安排合并单元格（第6-7行）
    { s: { r: 5, c: 7 }, e: { r: 6, c: 7 } }, // 入住时间合并单元格
    { s: { r: 5, c: 8 }, e: { r: 6, c: 8 } }  // 离店时间合并单元格
  ];

  // 添加工作表到工作簿
  XLSX.utils.book_append_sheet(workbook, worksheet, '酒店预订');

  // 生成Excel文件
  const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
  const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
  
  // 创建下载链接
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = '测试酒店预订数据.xlsx';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};
