/**
 * 全局认证错误处理器
 * 用于统一处理所有认证失败的情况，确保在任何地方遇到认证错误时都能正确跳转到登录页面
 */

// 认证错误检测模式
const AUTH_ERROR_PATTERNS = [
  'Not authenticated',
  'not authenticated',
  'NOT AUTHENTICATED',
  'unauthorized',
  'Unauthorized',
  'UNAUTHORIZED',
  '认证失败',
  '认证已过期',
  '未授权',
  'token expired',
  'Token expired',
  'TOKEN EXPIRED',
  'invalid token',
  'Invalid token',
  'INVALID TOKEN'
];

/**
 * 检查错误信息是否包含认证失败的关键词
 */
function isAuthenticationError(error: any): boolean {
  console.log('🔍 全局认证错误检查:', error);
  
  // 检查HTTP状态码
  if (error?.status === 401 || error?.response?.status === 401) {
    console.log('✅ 检测到HTTP 401状态码');
    return true;
  }

  // 检查错误消息
  const errorMessage = error?.message || error?.detail || error?.error || '';
  if (typeof errorMessage === 'string') {
    const matched = AUTH_ERROR_PATTERNS.some(pattern => 
      errorMessage.toLowerCase().includes(pattern.toLowerCase())
    );
    if (matched) {
      console.log('✅ 检测到认证错误消息:', errorMessage);
      return true;
    }
  }

  // 检查响应数据中的错误信息
  const responseData = error?.response?.data;
  if (responseData) {
    const responseMessage = responseData.detail || responseData.message || responseData.error || '';
    if (typeof responseMessage === 'string') {
      const matched = AUTH_ERROR_PATTERNS.some(pattern => 
        responseMessage.toLowerCase().includes(pattern.toLowerCase())
      );
      if (matched) {
        console.log('✅ 检测到响应数据中的认证错误:', responseMessage);
        return true;
      }
    }
  }

  console.log('❌ 未检测到认证错误');
  return false;
}

/**
 * 处理认证失败
 */
function handleAuthenticationFailure(error?: any): void {
  console.warn('🔒 全局认证错误处理器：检测到认证失败', error);
  
  // 清理所有认证相关的本地存储
  const authKeys = [
    'access_token',
    'app_token', 
    'token',
    'user',
    'userInfo',
    'sso_state'
  ];
  
  console.log('🧹 清理本地存储数据...');
  authKeys.forEach(key => {
    const oldValue = localStorage.getItem(key);
    localStorage.removeItem(key);
    if (oldValue) {
      console.log(`  - 已清理 ${key}: ${oldValue.substring(0, 20)}...`);
    }
  });

  // 触发全局认证失败事件
  const authFailureEvent = new CustomEvent('authenticationFailure', {
    detail: {
      reason: 'global_auth_handler',
      message: '认证已过期，请重新登录',
      timestamp: new Date().toISOString(),
      originalError: error
    }
  });
  
  console.log('📢 触发全局认证失败事件:', authFailureEvent.detail);
  window.dispatchEvent(authFailureEvent);

  // 注意：跳转逻辑现在主要由AuthGuard组件和API请求处理器负责
  // 这里只作为最后的备用跳转机制，避免多重跳转冲突
  console.log('ℹ️ 全局认证错误处理器：事件已触发，主要跳转由AuthGuard组件处理');

  // 设置一个较长的延迟，只在其他机制都失效时才执行
  setTimeout(() => {
    const currentPath = window.location.pathname;
    console.log(`🔍 [备用跳转检查] 当前路径: ${currentPath}`);

    if (currentPath !== '/login') {
      console.log('🔄 [备用跳转] 其他跳转机制可能失效，执行备用跳转');
      try {
        window.location.replace('/login');
        console.log('✅ [备用跳转] 执行完成');
      } catch (error) {
        console.error('❌ [备用跳转] 失败:', error);
      }
    } else {
      console.log('ℹ️ [备用跳转] 已经在登录页面，无需跳转');
    }
  }, 3000); // 3秒延迟，确保其他机制有足够时间执行
}

/**
 * 全局错误监听器
 */
function setupGlobalErrorHandling(): void {
  console.log('🚀 开始设置全局错误监听器...');
  
  // 监听未捕获的Promise拒绝
  window.addEventListener('unhandledrejection', (event) => {
    console.log('🔍 捕获到未处理的Promise拒绝:', event.reason);
    if (isAuthenticationError(event.reason)) {
      console.warn('🔒 全局错误处理器：捕获到未处理的认证错误Promise');
      handleAuthenticationFailure(event.reason);
      event.preventDefault(); // 阻止默认的错误处理
    }
  });

  // 监听全局错误
  window.addEventListener('error', (event) => {
    console.log('🔍 捕获到全局错误:', event.error);
    if (isAuthenticationError(event.error)) {
      console.warn('🔒 全局错误处理器：捕获到全局认证错误');
      handleAuthenticationFailure(event.error);
    }
  });

  console.log('✅ 全局认证错误处理器已启动');
}

/**
 * 主动检查错误并处理认证失败
 */
export function checkAndHandleAuthError(error: any): boolean {
  console.log('🔍 checkAndHandleAuthError 被调用:', error);
  
  if (isAuthenticationError(error)) {
    console.log('✅ 确认为认证错误，开始处理...');
    handleAuthenticationFailure(error);
    return true;
  }
  
  console.log('❌ 不是认证错误，跳过处理');
  return false;
}

/**
 * 初始化全局认证错误处理
 */
export function initGlobalAuthHandler(): void {
  setupGlobalErrorHandling();
}

/**
 * 手动触发认证失败处理（用于测试或特殊情况）
 */
export function triggerAuthFailure(reason: string = 'manual_trigger'): void {
  console.log('🧪 手动触发认证失败测试:', reason);
  handleAuthenticationFailure({ reason });
}

/**
 * 测试认证错误检测（用于调试）
 */
export function testAuthErrorDetection(): void {
  console.log('🧪 开始测试认证错误检测...');
  
  const testCases = [
    { message: 'Not authenticated', status: 401 },
    { message: 'unauthorized' },
    { message: '认证失败' },
    { detail: 'Not authenticated' },
    { response: { status: 401, data: { detail: 'Not authenticated' } } },
    { status: 401 },
    { message: 'normal error' } // 这个不应该被检测为认证错误
  ];
  
  testCases.forEach((testCase, index) => {
    console.log(`🧪 测试用例 ${index + 1}:`, testCase);
    const isAuth = isAuthenticationError(testCase);
    console.log(`   结果: ${isAuth ? '✅ 认证错误' : '❌ 非认证错误'}`);
  });
  
  console.log('🧪 认证错误检测测试完成');
}

// 暴露调试函数到全局window对象（仅在开发环境）
if (typeof window !== 'undefined' && import.meta.env.DEV) {
  (window as any).authDebug = {
    triggerAuthFailure,
    testAuthErrorDetection,
    checkAndHandleAuthError,
    initGlobalAuthHandler
  };
  console.log('🔧 认证调试工具已暴露到 window.authDebug');
}

export default {
  init: initGlobalAuthHandler,
  checkAndHandle: checkAndHandleAuthError,
  trigger: triggerAuthFailure,
  testDetection: testAuthErrorDetection
}; 