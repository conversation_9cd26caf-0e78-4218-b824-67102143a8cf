/**
 * 模拟用户登录工具
 * 用于开发和测试环境快速登录用户
 */

export interface MockUserInfo {
  userId: string;
  username: string;
  email: string;
  department?: string;
  work_id?: string;
  avatar_url?: string;
}

/**
 * 模拟用户登录
 * @param userInfo 用户信息
 */
export const mockLogin = (userInfo: MockUserInfo) => {
  // 设置用户信息到本地存储
  localStorage.setItem('userInfo', JSON.stringify(userInfo));
  
  // 设置访问令牌
  localStorage.setItem('access_token', 'mock-token-' + Date.now());
  
  console.log('🔐 [MockLogin] 模拟登录成功:', userInfo);
  
  // 刷新页面以触发认证状态更新
  window.location.reload();
};

/**
 * 预定义的测试用户
 */
export const TEST_USERS = {
  // 郭伟 - 管理员 (数据库ID: 2, tc_user_id: 123497)
  guowei: {
    userId: '2',
    username: '郭伟',
    email: '<EMAIL>',
    department: '商旅解决方案组',
    work_id: '1227534',
  },

  // 袁栩栩 - 超级管理员 (数据库ID: 7, tc_user_id: 121577)
  yuanxuxu: {
    userId: '7',
    username: '袁栩栩',
    email: '<EMAIL>',
    department: '测试部门',
    work_id: '1227303',
  },

  // 陈洁 - 普通用户 (数据库ID: 11, tc_user_id: 103761)
  chenjie: {
    userId: '11',
    username: '陈洁',
    email: '<EMAIL>',
    department: '商旅服务支持小组',
    work_id: '1223409',
  },
  
  // 测试权限用户 - 普通用户
  testUser: {
    userId: 'U1751164870测试权',
    username: '测试权限用户',
    email: '<EMAIL>',
    department: '',
    work_id: '',
  },
  
  // 仅应用中心用户
  dashboardOnly: {
    userId: 'U1751165000仅应用',
    username: '仅应用中心用户',
    email: '<EMAIL>',
    department: '',
    work_id: '',
  },
};

/**
 * 快速登录预定义用户
 */
export const quickLogin = {
  guowei: () => mockLogin(TEST_USERS.guowei),
  yuanxuxu: () => mockLogin(TEST_USERS.yuanxuxu),
  chenjie: () => mockLogin(TEST_USERS.chenjie),
  testUser: () => mockLogin(TEST_USERS.testUser),
  dashboardOnly: () => mockLogin(TEST_USERS.dashboardOnly),
};

// 在开发环境下将快速登录函数暴露到全局
if (process.env.NODE_ENV === 'development') {
  (window as any).quickLogin = quickLogin;
  (window as any).mockLogin = mockLogin;
  (window as any).TEST_USERS = TEST_USERS;
  
  console.log('🚀 [MockLogin] 开发工具已加载，可使用以下命令:');
  console.log('  - quickLogin.guowei() // 登录郭伟');
  console.log('  - quickLogin.yuanxuxu() // 登录袁栩栩');
  console.log('  - quickLogin.chenjie() // 登录陈洁');
  console.log('  - quickLogin.testUser() // 登录测试用户');
  console.log('  - quickLogin.dashboardOnly() // 登录仅应用中心用户');
}
