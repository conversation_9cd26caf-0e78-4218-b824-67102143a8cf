/**
 * 前端错误处理工具
 * 处理后端全局异常处理器返回的标准化错误响应
 */

export interface ErrorResponse {
  success: false;
  error: {
    message: string;
    type: string;
    code: number;
    details?: any;
  };
  timestamp?: string;
}

export interface ErrorInfo {
  message: string;
  type: string;
  code: number;
  details?: any;
  isNetworkError?: boolean;
  isServerError?: boolean;
  isClientError?: boolean;
}

/**
 * 解析后端错误响应
 */
export function parseErrorResponse(error: any): ErrorInfo {
  // 默认错误信息
  const defaultError: ErrorInfo = {
    message: '网络连接异常，请稍后重试',
    type: 'NetworkError',
    code: 0,
    isNetworkError: true
  };

  try {
    // 检查是否是HTTP响应错误
    if (error?.response) {
      const { status, data } = error.response;
      
      // 检查是否是标准化的错误响应格式
      if (data && typeof data === 'object' && data.success === false && data.error) {
        const errorInfo: ErrorInfo = {
          message: data.error.message || '服务器错误',
          type: data.error.type || 'ServerError',
          code: data.error.code || status,
          details: data.error.details,
          isServerError: status >= 500,
          isClientError: status >= 400 && status < 500
        };
        
        return errorInfo;
      }
      
      // 处理非标准化的HTTP错误
      return {
        message: getHttpErrorMessage(status),
        type: 'HTTPError',
        code: status,
        isServerError: status >= 500,
        isClientError: status >= 400 && status < 500
      };
    }
    
    // 检查是否是网络错误
    if (error?.code === 'ECONNABORTED' || error?.message?.includes('timeout')) {
      return {
        message: '请求超时，请检查网络连接',
        type: 'TimeoutError',
        code: 0,
        isNetworkError: true
      };
    }
    
    if (error?.code === 'ERR_NETWORK' || error?.message?.includes('Network Error')) {
      return {
        message: '网络连接失败，请检查网络设置',
        type: 'NetworkError',
        code: 0,
        isNetworkError: true
      };
    }
    
    // 其他错误
    return {
      message: error?.message || '未知错误',
      type: 'UnknownError',
      code: 0,
      details: error
    };
    
  } catch (parseError) {
    console.error('解析错误响应失败:', parseError);
    return defaultError;
  }
}

/**
 * 获取HTTP状态码对应的错误消息
 */
function getHttpErrorMessage(status: number): string {
  const errorMessages: Record<number, string> = {
    400: '请求参数错误',
    401: '未授权，请重新登录',
    403: '没有权限访问此资源',
    404: '请求的资源不存在',
    405: '请求方法不被允许',
    422: '请求参数验证失败',
    429: '请求过于频繁，请稍后重试',
    500: '服务器内部错误',
    502: '网关错误',
    503: '服务暂时不可用',
    504: '网关超时'
  };
  
  return errorMessages[status] || `HTTP错误 ${status}`;
}

/**
 * 显示错误消息（可以集成到toast组件）
 */
export function showErrorMessage(error: ErrorInfo) {
  // 这里可以集成到你的toast组件
  console.error('错误信息:', {
    message: error.message,
    type: error.type,
    code: error.code,
    details: error.details
  });
  
  // 示例：显示用户友好的错误消息
  const userMessage = getUserFriendlyMessage(error);
  
  // 你可以在这里调用toast显示错误
  // toast.error(userMessage);
  
  return userMessage;
}

/**
 * 检查是否为认证错误并处理
 */
function handleAuthenticationError(error: ErrorInfo): boolean {
  // 检查是否为认证相关错误
  const isAuthError = 
    error.code === 401 || 
    error.type.includes('Authentication') ||
    error.message.includes('Not authenticated') ||
    error.message.includes('认证失败') ||
    error.message.includes('unauthorized') ||
    error.message.includes('Unauthorized');

  if (isAuthError) {
    console.warn('🔒 检测到认证错误，触发全局认证失败处理');
    
    // 触发全局认证失败事件
    const authFailureEvent = new CustomEvent('authenticationFailure', {
      detail: {
        reason: 'authentication_error',
        message: '认证已过期，请重新登录',
        originalError: error
      }
    });
    window.dispatchEvent(authFailureEvent);
    
    return true;
  }
  
  return false;
}

/**
 * 获取用户友好的错误消息
 */
function getUserFriendlyMessage(error: ErrorInfo): string {
  // 首先检查并处理认证错误
  if (handleAuthenticationError(error)) {
    return '登录已过期，请重新登录';
  }
  
  // SSO相关错误
  if (error.type.includes('SSO') || error.message.includes('SSO')) {
    return '登录验证失败，请重新登录';
  }
  
  // 权限相关错误
  if (error.code === 403 || error.type.includes('Permission')) {
    return '没有权限执行此操作';
  }
  
  // 验证错误
  if (error.code === 422 || error.type.includes('Validation')) {
    return '输入信息有误，请检查后重试';
  }
  
  // 网络错误
  if (error.isNetworkError) {
    return '网络连接异常，请检查网络后重试';
  }
  
  // 服务器错误
  if (error.isServerError) {
    return '服务器暂时不可用，请稍后重试';
  }
  
  // 返回原始错误消息
  return error.message;
}

/**
 * API请求错误处理装饰器
 */
export function withErrorHandling<T extends (...args: any[]) => Promise<any>>(
  apiFunction: T
): T {
  return (async (...args: any[]) => {
    try {
      return await apiFunction(...args);
    } catch (error) {
      const errorInfo = parseErrorResponse(error);
      showErrorMessage(errorInfo);
      throw errorInfo; // 重新抛出解析后的错误信息
    }
  }) as T;
}

/**
 * 示例：在API服务中使用错误处理
 */
export class ApiErrorHandler {
  /**
   * 处理API响应
   */
  static async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      let errorData;
      try {
        errorData = await response.json();
      } catch {
        errorData = { message: response.statusText };
      }
      
      const error = {
        response: {
          status: response.status,
          data: errorData
        }
      };
      
      throw error;
    }
    
    return await response.json();
  }
  
  /**
   * 发送API请求并处理错误
   */
  static async request<T>(
    url: string, 
    options: RequestInit = {}
  ): Promise<T> {
    try {
      const response = await fetch(url, options);
      return await this.handleResponse<T>(response);
    } catch (error) {
      const errorInfo = parseErrorResponse(error);
      showErrorMessage(errorInfo);
      throw errorInfo;
    }
  }
} 