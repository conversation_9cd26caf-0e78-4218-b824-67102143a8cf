/**
 * PDF转图片的配置选项
 */
export interface PdfToImageOptions {
  /** 图片质量 (0-1) */
  quality?: number;
  /** 图片格式 */
  format?: 'jpeg' | 'png';
  /** 缩放比例 */
  scale?: number;
  /** 最大页数限制 */
  maxPages?: number;
}

/**
 * 检查文件是否为PDF类型
 */
export function isPdfFile(file: File): boolean {
  return file.type === 'application/pdf' || file.name.toLowerCase().endsWith('.pdf');
}

/**
 * 将PDF文件转换为图片文件数组（后端处理版本）
 * 注意：实际转换在后端进行，这里只是返回原PDF文件
 */
export async function convertPdfToImages(
  pdfFile: File,
  options: PdfToImageOptions = {}
): Promise<File[]> {
  console.log('📄 PDF文件将由后端处理转换:', {
    fileName: pdfFile.name,
    fileSize: pdfFile.size,
    options
  });

  // 对于PDF文件，我们直接返回原文件
  // 实际的转换将在后端进行
  return [pdfFile];
}

/**
 * 处理混合文件（PDF + 图片），PDF文件保持原样，图片文件直接返回
 */
export async function processFilesWithPdfConversion(
  files: File[],
  options: PdfToImageOptions = {}
): Promise<File[]> {
  console.log('🔄 开始处理混合文件:', {
    totalFiles: files.length,
    fileTypes: files.map(f => ({ name: f.name, type: f.type, isPdf: isPdfFile(f) }))
  });

  const processedFiles: File[] = [];

  for (const file of files) {
    if (isPdfFile(file)) {
      console.log(`📄 检测到PDF文件: ${file.name}，将由后端处理`);
      // PDF文件直接添加，后端会处理转换
      processedFiles.push(file);
    } else {
      console.log(`🖼️ 检测到图片文件: ${file.name}，直接添加`);
      // 图片文件直接添加
      processedFiles.push(file);
    }
  }

  console.log(`✅ 文件处理完成，共 ${processedFiles.length} 个文件`);
  return processedFiles;
}

/**
 * 从剪贴板数据中提取PDF文件
 */
export function extractPdfFilesFromClipboard(items: DataTransferItemList): File[] {
  const pdfFiles: File[] = [];

  for (let i = 0; i < items.length; i++) {
    const item = items[i];
    if (item.type === 'application/pdf') {
      const file = item.getAsFile();
      if (file) {
        pdfFiles.push(file);
      }
    }
  }

  return pdfFiles;
}
