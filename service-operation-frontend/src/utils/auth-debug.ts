/**
 * 前端认证调试工具
 */

// JWT Token 解析函数
export function parseJWT(token: string): any {
  try {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map(function (c) {
          return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
        })
        .join('')
    );
    return JSON.parse(jsonPayload);
  } catch (error) {
    console.error('JWT解析失败:', error);
    return null;
  }
}

// 检查Token是否过期
export function isTokenExpired(token: string): boolean {
  const tokenData = parseJWT(token);
  if (!tokenData || !tokenData.exp) {
    return true;
  }
  
  const currentTime = Math.floor(Date.now() / 1000);
  return tokenData.exp < currentTime;
}

// 获取Token剩余时间（秒）
export function getTokenRemainingTime(token: string): number {
  const tokenData = parseJWT(token);
  if (!tokenData || !tokenData.exp) {
    return 0;
  }
  
  const currentTime = Math.floor(Date.now() / 1000);
  return Math.max(0, tokenData.exp - currentTime);
}

// 认证状态检查
export function checkAuthStatus(): {
  hasToken: boolean;
  isValid: boolean;
  isExpired: boolean;
  userInfo: any;
  remainingTime: number;
  errors: string[];
} {
  const errors: string[] = [];
  const token = localStorage.getItem('token');
  
  if (!token) {
    return {
      hasToken: false,
      isValid: false,
      isExpired: true,
      userInfo: null,
      remainingTime: 0,
      errors: ['没有找到认证Token']
    };
  }

  // 检查Token格式
  const tokenParts = token.split('.');
  if (tokenParts.length !== 3) {
    errors.push('Token格式不正确（应该包含3个部分）');
  }

  // 解析Token
  const tokenData = parseJWT(token);
  if (!tokenData) {
    errors.push('Token解析失败');
    return {
      hasToken: true,
      isValid: false,
      isExpired: true,
      userInfo: null,
      remainingTime: 0,
      errors
    };
  }

  // 检查是否过期
  const expired = isTokenExpired(token);
  if (expired) {
    errors.push('Token已过期');
  }

  // 检查必要字段
  if (!tokenData.sub) {
    errors.push('Token缺少用户ID（sub字段）');
  }

  const remainingTime = getTokenRemainingTime(token);

  return {
    hasToken: true,
    isValid: errors.length === 0,
    isExpired: expired,
    userInfo: tokenData,
    remainingTime,
    errors
  };
}

// 测试API认证
export async function testApiAuth(): Promise<{
  success: boolean;
  status: number;
  message: string;
  details?: any;
}> {
  const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';
  const token = localStorage.getItem('token');

  if (!token) {
    return {
      success: false,
      status: 0,
      message: '没有认证Token'
    };
  }

  try {
    const response = await fetch(`${API_BASE_URL}/api/passport/list`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    const data = await response.json();

    return {
      success: response.ok,
      status: response.status,
      message: response.ok ? '认证成功' : data.detail || '认证失败',
      details: data
    };
  } catch (error) {
    return {
      success: false,
      status: 0,
      message: `网络错误: ${error instanceof Error ? error.message : '未知错误'}`
    };
  }
}

// 清理认证数据
export function clearAuth(): void {
  localStorage.removeItem('token');
  localStorage.removeItem('user');
  console.log('认证数据已清理');
}

// 调试信息打印
export function printAuthDebugInfo(): void {
  console.group('🔍 认证调试信息');
  
  const authStatus = checkAuthStatus();
  console.log('认证状态:', authStatus);
  
  if (authStatus.hasToken) {
    const token = localStorage.getItem('token');
    console.log('Token (前50字符):', token?.substring(0, 50) + '...');
    
    if (authStatus.userInfo) {
      console.log('用户信息:', authStatus.userInfo);
      console.log('Token过期时间:', new Date(authStatus.userInfo.exp * 1000));
      console.log('当前时间:', new Date());
      console.log('剩余时间:', `${Math.floor(authStatus.remainingTime / 60)}分${authStatus.remainingTime % 60}秒`);
    }
  }
  
  if (authStatus.errors.length > 0) {
    console.error('认证错误:', authStatus.errors);
  }
  
  console.groupEnd();
}

// 自动认证检查（在开发环境下）
export function setupAuthDebug(): void {
  if (import.meta.env.DEV) {
    // 在控制台添加全局调试函数
    (window as any).authDebug = {
      check: checkAuthStatus,
      test: testApiAuth,
      clear: clearAuth,
      print: printAuthDebugInfo,
      parse: parseJWT
    };
    
    console.log('🔧 认证调试工具已加载，使用 authDebug.* 进行调试');
    
    // 自动检查认证状态
    const authStatus = checkAuthStatus();
    if (!authStatus.isValid) {
      console.warn('⚠️ 认证状态异常:', authStatus.errors);
    }
  }
}

// 认证错误处理
export function handleAuthError(error: any): void {
  console.error('认证错误:', error);
  
  // 如果是401错误或包含认证失败信息，清理认证数据
  const isAuthError = 
    error.status === 401 || 
    error.message?.includes('401') ||
    error.message?.includes('Not authenticated') ||
    error.message?.includes('认证失败') ||
    error.message?.includes('unauthorized');

  if (isAuthError) {
    console.log('检测到认证错误，清理认证数据并触发全局事件');
    clearAuth();
    
    // 触发全局认证失败事件
    const authFailureEvent = new CustomEvent('authenticationFailure', {
      detail: {
        reason: 'authentication_error_debug',
        message: '认证已过期，请重新登录',
        originalError: error
      }
    });
    window.dispatchEvent(authFailureEvent);
    
    // 重定向到登录页面
    if (window.location.pathname !== '/login') {
      window.location.href = '/login';
    }
  }
}

// 导出所有函数
export default {
  parseJWT,
  isTokenExpired,
  getTokenRemainingTime,
  checkAuthStatus,
  testApiAuth,
  clearAuth,
  printAuthDebugInfo,
  setupAuthDebug,
  handleAuthError
}; 