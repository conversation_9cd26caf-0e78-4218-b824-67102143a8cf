/**
 * 图片URL工具函数
 */

/**
 * 构建完整的图片URL
 * @param imagePath 图片路径（可能是相对路径或完整URL）
 * @returns 完整的图片URL
 */
export function buildImageUrl(imagePath: string): string {
  // 如果为空或undefined，返回空字符串
  if (!imagePath) {
    return '';
  }

  // 获取API基础URL
  const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';

  // 如果是S3 URL，使用后端代理接口
  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
    // 检查是否是S3 URL
    if (imagePath.includes('oss.') && imagePath.includes('/soap-files/')) {
      try {
        // 解析S3 URL获取bucket和path
        const url = new URL(imagePath);
        const pathParts = url.pathname.split('/');

        // 路径格式: /soap-files/tmc.ai.soap.server/passport/...
        if (pathParts.length >= 3 && pathParts[1] === 'soap-files') {
          const bucket = pathParts[1]; // soap-files
          const filePath = pathParts.slice(2).join('/'); // tmc.ai.soap.server/passport/...

          // 确保API基础URL正确处理，避免重复的/api路径
          const baseUrl = apiBaseUrl.endsWith('/api') ? apiBaseUrl : `${apiBaseUrl}/api`;
          const proxyUrl = `${baseUrl}/passport/s3-proxy/${bucket}/${filePath}`;
          console.log('🔗 使用S3代理URL:', proxyUrl);
          return proxyUrl;
        }
      } catch (error) {
        console.warn('⚠️ 解析S3 URL失败，使用原始URL:', error);
      }
    }

    // 对于其他外部URL，直接返回
    console.log('🔗 使用外部URL:', imagePath);
    return imagePath;
  }

  // 移除API路径中的/api后缀（如果存在）
  const baseUrl = apiBaseUrl.replace(/\/api$/, '');

  // 处理相对路径
  let path = imagePath;

  // 清理可能的重复路径
  if (path.includes('uploads/uploads/')) {
    console.warn('⚠️ 检测到重复路径，正在修复:', path);
    path = path.replace('uploads/uploads/', 'uploads/');
  }

  // 确保路径以/开头
  if (!path.startsWith('/')) {
    path = `/${path}`;
  }

  const finalUrl = `${baseUrl}${path}`;
  console.log('🔗 构建本地URL:', finalUrl);

  return finalUrl;
}



/**
 * 检查图片是否可以加载
 * @param imageUrl 图片URL
 * @returns Promise<boolean>
 */
export function checkImageLoad(imageUrl: string): Promise<boolean> {
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = () => resolve(true);
    img.onerror = () => resolve(false);
    img.src = imageUrl;
  });
} 