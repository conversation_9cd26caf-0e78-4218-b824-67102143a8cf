const renderAllOrdersTab = () => {
  // 计算各状态订单数量（前端计算，用于状态筛选显示）
  const statusCounts = allOrders.reduce((counts, order) => {
    counts[order.order_status] = (counts[order.order_status] || 0) + 1;
    return counts;
  }, {} as Record<string, number>);

  return (
    <div className="space-y-4">
      {/* 订单统计卡片 */}
      {allOrdersTotal > 0 && (
        <Card className="bg-white border border-gray-200">
          <div className="p-4">
            <h3 className="text-sm font-medium text-gray-900 mb-3">订单状态统计</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
              {/* 总订单数 */}
              <div className="text-center">
                <div className="p-2 bg-purple-100 rounded-lg mx-auto w-fit mb-1">
                  <CreditCard className="h-4 w-4 text-purple-600" />
                </div>
                <p className="text-lg font-medium text-gray-900">{projectStats?.total_orders || allOrdersTotal}</p>
                <p className="text-xs text-gray-500">总订单数</p>
              </div>
              {/* 验证失败 */}
              <div className="text-center">
                <div className="p-2 bg-amber-100 rounded-lg mx-auto w-fit mb-1">
                  <AlertCircle className="h-4 w-4 text-amber-600" />
                </div>
                <p className="text-lg font-medium text-gray-900">{statusCounts['check_failed'] || 0}</p>
                <p className="text-xs text-gray-500">验证失败</p>
              </div>
              {/* 待提交 */}
              <div className="text-center">
                <div className="p-2 bg-gray-100 rounded-lg mx-auto w-fit mb-1">
                  <Clock className="h-4 w-4 text-gray-600" />
                </div>
                <p className="text-lg font-medium text-gray-900">{statusCounts['initial'] || 0}</p>
                <p className="text-xs text-gray-500">待提交</p>
              </div>
              {/* 已提交 */}
              <div className="text-center">
                <div className="p-2 bg-blue-100 rounded-lg mx-auto w-fit mb-1">
                  <Send className="h-4 w-4 text-blue-600" />
                </div>
                <p className="text-lg font-medium text-gray-900">{statusCounts['submitted'] || 0}</p>
                <p className="text-xs text-gray-500">已提交</p>
              </div>
              {/* 处理中 */}
              <div className="text-center">
                <div className="p-2 bg-cyan-100 rounded-lg mx-auto w-fit mb-1">
                  <Loader className="h-4 w-4 text-cyan-600" />
                </div>
                <p className="text-lg font-medium text-gray-900">{statusCounts['processing'] || 0}</p>
                <p className="text-xs text-gray-500">处理中</p>
              </div>
              {/* 预定完成 */}
              <div className="text-center">
                <div className="p-2 bg-green-100 rounded-lg mx-auto w-fit mb-1">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                </div>
                <p className="text-lg font-medium text-gray-900">{statusCounts['completed'] || 0}</p>
                <p className="text-xs text-gray-500">预定完成</p>
              </div>
              {/* 预定失败 */}
              <div className="text-center">
                <div className="p-2 bg-red-100 rounded-lg mx-auto w-fit mb-1">
                  <XCircle className="h-4 w-4 text-red-600" />
                </div>
                <p className="text-lg font-medium text-gray-900">{statusCounts['failed'] || 0}</p>
                <p className="text-xs text-gray-500">预定失败</p>
              </div>
              {/* 完成金额 */}
              <div className="text-center">
                <div className="p-2 bg-purple-100 rounded-lg mx-auto w-fit mb-1">
                  <DollarSign className="h-4 w-4 text-purple-600" />
                </div>
                <p className="text-lg font-medium text-gray-900">¥{projectStats?.total_amount || '0'}</p>
                <p className="text-xs text-gray-500">完成金额</p>
              </div>
            </div>
          </div>
        </Card>
      )}

      <Card className="bg-white border border-gray-200 rounded-lg shadow-sm">
        <div className="border-b border-gray-200 p-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <Eye className="h-5 w-5 text-gray-600" />
              <h3 className="text-lg font-medium text-gray-900">所有订单</h3>
              {allOrdersTotal > 0 && (
                <span className="ml-2 bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full">
                  {allOrdersTotal}
                </span>
              )}
            </div>
          </div>
          
          {/* 搜索区域 */}
          <div className="flex flex-col gap-3">
            {/* 第一行：状态筛选 */}
            <div className="flex items-center gap-3">
              <span className="text-sm font-medium text-gray-700 whitespace-nowrap">状态筛选：</span>
              <div className="flex flex-wrap gap-2">
                {[
                  { value: 'check_failed', label: '验证失败', color: 'bg-amber-100 text-amber-700' },
                  { value: 'initial', label: '待提交', color: 'bg-gray-100 text-gray-600' },
                  { value: 'submitted', label: '已提交', color: 'bg-blue-100 text-blue-700' },
                  { value: 'processing', label: '处理中', color: 'bg-cyan-100 text-cyan-700' },
                  { value: 'completed', label: '预定完成', color: 'bg-green-100 text-green-700' },
                  { value: 'failed', label: '预定失败', color: 'bg-red-100 text-red-700' },
                ].map((status) => (
                  <label
                    key={status.value}
                    className="flex items-center gap-1 cursor-pointer hover:bg-gray-50 p-1 rounded"
                  >
                    <input
                      type="checkbox"
                      checked={selectedStatuses.includes(status.value)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedStatuses([...selectedStatuses, status.value]);
                        } else {
                          setSelectedStatuses(selectedStatuses.filter(s => s !== status.value));
                        }
                      }}
                      className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                    />
                    <span className={`px-2 py-1 rounded-full text-xs ${status.color}`}>
                      {status.label}
                    </span>
                  </label>
                ))}
              </div>
            </div>
            
            {/* 第二行：搜索输入框 */}
            <div className="flex flex-col md:flex-row md:items-center gap-3">
              <div className="flex items-center gap-2 flex-1 max-w-xs">
                <label className="text-sm text-gray-600 whitespace-nowrap min-w-fit">入住人姓名：</label>
                <input
                  type="text"
                  placeholder="请输入入住人姓名"
                  value={searchGuestName}
                  onChange={(e) => setSearchGuestName(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  className="flex-1 h-9 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                />
              </div>
              
              <div className="flex items-center gap-2 flex-1 max-w-xs">
                <label className="text-sm text-gray-600 whitespace-nowrap min-w-fit">手机号码：</label>
                <input
                  type="text"
                  placeholder="请输入手机号码"
                  value={searchMobilePhone}
                  onChange={(e) => setSearchMobilePhone(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  className="flex-1 h-9 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                />
              </div>
              
              <div className="flex items-center gap-2 flex-1 max-w-xs">
                <label className="text-sm text-gray-600 whitespace-nowrap min-w-fit">联系人手机：</label>
                <input
                  type="text"
                  placeholder="请输入联系人手机号"
                  value={searchContactPhone}
                  onChange={(e) => setSearchContactPhone(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  className="flex-1 h-9 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                />
              </div>

              <div className="flex items-center gap-2 ml-auto">
                <Button
                  size="sm"
                  onClick={handleSearch}
                  disabled={allOrdersLoading}
                  className="bg-blue-600 hover:bg-blue-700 text-white h-9"
                >
                  <Search className="h-4 w-4 mr-2" />
                  搜索
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleClearSearch}
                  disabled={allOrdersLoading}
                  className="h-9"
                >
                  <X className="h-4 w-4 mr-2" />
                  重置
                </Button>

                {allOrdersTotal > 0 && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={exportAllOrders}
                    disabled={allOrdersLoading}
                    className="h-9"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    导出
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>
        <div className="p-0">
          {allOrdersLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">加载中...</p>
            </div>
          ) : allOrders.length > 0 ? (
            <>
              <div className="overflow-x-auto custom-scrollbar" style={{ 
                scrollbarWidth: 'auto', 
                scrollbarColor: '#cbd5e1 #f1f5f9'
              }}>
                <style dangerouslySetInnerHTML={{
                  __html: `
                    .custom-scrollbar::-webkit-scrollbar {
                      height: 12px;
                      background-color: #f1f5f9;
                    }
                    .custom-scrollbar::-webkit-scrollbar-track {
                      background-color: #f1f5f9;
                      border-radius: 6px;
                    }
                    .custom-scrollbar::-webkit-scrollbar-thumb {
                      background-color: #cbd5e1;
                      border-radius: 6px;
                      border: 2px solid #f1f5f9;
                    }
                    .custom-scrollbar::-webkit-scrollbar-thumb:hover {
                      background-color: #94a3b8;
                    }
                  `
                }} />
                <table className="w-full text-xs" style={{ minWidth: '3000px' }}>
                  <thead className="bg-gray-50 border-b border-gray-200">
                    <tr>
                      <th className="text-center p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '60px' }}>序号</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>状态</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>入住人姓名</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>入住人姓</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>入住人名</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>国籍</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '60px' }}>性别</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>出生日期</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>证件类型</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '150px' }}>证件号码</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>手机号</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '150px' }}>邮箱</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>目的地</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>酒店ID</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '150px' }}>酒店名称</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>房型</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>房间数量</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>入住时间</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>离店时间</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>团队预订</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>团队预订名称</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>联系人</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>联系人手机</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>成本中心</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>审批人</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>金额</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>订单号</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>账单号</th>
                      <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '150px' }}>失败原因</th>
                      <th className="text-center p-2 font-medium text-gray-900 text-xs whitespace-nowrap sticky right-0 bg-gray-50 border-l border-gray-200" style={{ minWidth: '120px' }}>操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    {allOrders.map((order, index) => (
                      <tr key={order.id} className="border-b border-gray-100 hover:bg-gray-50">
                        <td className="p-2 text-xs text-gray-900 whitespace-nowrap text-center">{(allOrdersPage - 1) * allOrdersPageSize + index + 1}</td>
                        <td className="p-2 text-xs whitespace-nowrap">
                          {(() => {
                            const statusDisplay = getOrderStatusDisplay(order.order_status);
                            return (
                              <span className={`px-2 py-1 rounded-full text-xs ${statusDisplay.color} ${statusDisplay.bgColor}`}>
                                {statusDisplay.text}
                              </span>
                            );
                          })()}
                        </td>
                        <td className="p-2 text-xs font-medium text-gray-900 whitespace-nowrap">{order.guest_full_name || '-'}</td>
                        <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.guest_surname || '-'}</td>
                        <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.guest_given_name || '-'}</td>
                        <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.guest_nationality || '-'}</td>
                        <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.guest_gender || '-'}</td>
                        <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.guest_birth_date || '-'}</td>
                        <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.guest_id_type || '-'}</td>
                        <td className="p-2 text-xs text-gray-600 font-mono whitespace-nowrap">{order.guest_id_number || '-'}</td>
                        <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.guest_mobile_phone || '-'}</td>
                        <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.guest_email || '-'}</td>
                        <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.destination || '-'}</td>
                        <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.hotel_id || '-'}</td>
                        <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.hotel_name || '-'}</td>
                        <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.room_type || '-'}</td>
                        <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.room_count || '-'}</td>
                        <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.check_in_time || '-'}</td>
                        <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.check_out_time || '-'}</td>
                        <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.is_group_booking || '-'}</td>
                        <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.group_booking_name || '-'}</td>
                        <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.contact_person || '-'}</td>
                        <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.contact_mobile_phone || '-'}</td>
                        <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.cost_center || '-'}</td>
                        <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.approver || '-'}</td>
                        <td className="p-2 text-xs text-gray-900 font-medium whitespace-nowrap">
                          {formatAmount(order.amount)}
                        </td>
                        <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.order_number || '-'}</td>
                        <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.bill_number || '-'}</td>
                        <td className="p-2 text-xs text-gray-600 whitespace-nowrap relative group cursor-help">
                          {order.fail_reason ? (
                            <>
                              <span className={`${order.fail_reason.length > 15 ? 'border-b border-dotted border-gray-400' : ''}`}>
                                {order.fail_reason.length > 15 ? order.fail_reason.substring(0, 15) + '...' : order.fail_reason}
                              </span>
                              {order.fail_reason.length > 15 && (
                                <div className="invisible group-hover:visible absolute bottom-full mb-1 left-0 bg-gray-800 text-white text-xs rounded px-2 py-1 whitespace-normal max-w-xs z-50">
                                  {order.fail_reason}
                                </div>
                              )}
                            </>
                          ) : (
                            '-'
                          )}
                        </td>
                        <td className="p-2 text-xs whitespace-nowrap sticky right-0 bg-white border-l border-gray-200 z-20">
                          <div className="flex items-center gap-1">
                            <Button
                              variant="ghost"
                              size="icon"
                              title="查看详情"
                              className="h-6 w-6 p-0"
                              onClick={() => handleViewOrder(order)}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              title="编辑订单"
                              className="h-6 w-6 p-0 text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                              onClick={() => handleEditOrder(order)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            {/* 只有验证失败和待提交的订单可以删除 */}
                            {(order.order_status === 'check_failed' || order.order_status === 'initial') && (
                              <Button
                                variant="ghost"
                                size="icon"
                                title="删除订单"
                                className="h-6 w-6 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                                onClick={() => handleDeleteOrder(order)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              
              {/* 分页 - 始终显示 */}
              <div className="flex items-center justify-between p-4 border-t border-gray-200">
                <div className="text-sm text-gray-700">
                  {allOrdersTotal > 0 ? (
                    <>显示第 {(allOrdersPage - 1) * allOrdersPageSize + 1} 到 {Math.min(allOrdersPage * allOrdersPageSize, allOrdersTotal)} 条，共 {allOrdersTotal} 条记录</>
                  ) : (
                    <>共 0 条记录</>
                  )}
                </div>
                <div className="flex items-center space-x-4">
                  {/* 每页显示数量选择 */}
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-700">每页显示:</span>
                    <select
                      value={allOrdersPageSize}
                      onChange={(e) => {
                        const newPageSize = parseInt(e.target.value);
                        setAllOrdersPageSize(newPageSize);
                        // 页面重置和数据重载由useEffect自动处理
                      }}
                      className="text-sm border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      disabled={allOrdersLoading}
                    >
                      <option value={5}>5条</option>
                      <option value={10}>10条</option>
                      <option value={20}>20条</option>
                      <option value={50}>50条</option>
                    </select>
                  </div>
                  
                  {/* 分页导航 - 只在有多页时显示 */}
                  {Math.ceil(allOrdersTotal / allOrdersPageSize) > 1 && (
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => loadAllOrders(1)}
                        disabled={allOrdersPage <= 1 || allOrdersLoading}
                      >
                        首页
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => loadAllOrders(allOrdersPage - 1)}
                        disabled={allOrdersPage <= 1 || allOrdersLoading}
                      >
                        上一页
                      </Button>
                      
                      {/* 页码显示 */}
                      <div className="flex items-center space-x-1">
                        {(() => {
                          const totalPages = Math.ceil(allOrdersTotal / allOrdersPageSize);
                          const pages = [];
                          const maxVisiblePages = 5;
                          
                          let startPage = Math.max(1, allOrdersPage - Math.floor(maxVisiblePages / 2));
                          let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
                          
                          if (endPage - startPage + 1 < maxVisiblePages) {
                            startPage = Math.max(1, endPage - maxVisiblePages + 1);
                          }
                          
                          for (let i = startPage; i <= endPage; i++) {
                            pages.push(
                              <button
                                key={i}
                                onClick={() => loadAllOrders(i)}
                                disabled={allOrdersLoading}
                                className={`px-3 py-1 text-sm border rounded ${
                                  i === allOrdersPage
                                    ? 'bg-blue-600 text-white border-blue-600'
                                    : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                                }`}
                              >
                                {i}
                              </button>
                            );
                          }
                          
                          return pages;
                        })()}
                      </div>
                      
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => loadAllOrders(allOrdersPage + 1)}
                        disabled={allOrdersPage >= Math.ceil(allOrdersTotal / allOrdersPageSize) || allOrdersLoading}
                      >
                        下一页
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => loadAllOrders(Math.ceil(allOrdersTotal / allOrdersPageSize))}
                        disabled={allOrdersPage >= Math.ceil(allOrdersTotal / allOrdersPageSize) || allOrdersLoading}
                      >
                        末页
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </>
          ) : (
            <div className="text-center py-8">
              <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">暂无订单数据</p>
            </div>
          )}
        </div>
      </Card>
    </div>
  );
}; 