#!/bin/bash

# DTTrip 前端项目 Docker 构建脚本

set -e

# 配置变量
IMAGE_NAME="dttrip-frontend"
VERSION=${1:-latest}
REGISTRY=${2:-}  # 可选的镜像仓库地址
COMPANY_REGISTRY="hub.17usoft.com/dttrip_service_operation"

echo "================================"
echo "DTTrip 前端 Docker 构建"
echo "镜像名称: $IMAGE_NAME"
echo "版本标签: $VERSION"
if [ -n "$REGISTRY" ]; then
    echo "推送仓库: $REGISTRY"
else
    echo "推送仓库: 不推送（本地构建）"
fi
echo "================================"

# 清理旧的构建文件
echo "🧹 清理旧的构建文件..."
if [ -d "dist" ]; then
    rm -rf dist
    echo "✅ 已清理 dist 目录"
fi

# 构建 Docker 镜像
echo "🔨 开始构建 Docker 镜像..."
start_time=$(date +%s)

docker build \
    -t "$IMAGE_NAME:$VERSION" \
    -t "$IMAGE_NAME:latest" \
    --progress=plain \
    .

end_time=$(date +%s)
build_time=$((end_time - start_time))

echo "✅ 镜像构建完成！耗时: ${build_time}秒"

# 显示镜像信息
echo "📊 镜像信息:"
docker images | grep "$IMAGE_NAME" | head -2

# 检查镜像大小
image_size=$(docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}" | grep "$IMAGE_NAME" | grep "$VERSION" | awk '{print $3}')
echo "📦 镜像大小: $image_size"

# 如果提供了仓库地址，推送镜像
if [ -n "$REGISTRY" ]; then
    echo "🚀 推送镜像到仓库: $REGISTRY"
    
    # 处理公司内部仓库登录
    if [[ "$REGISTRY" == *"hub.17usoft.com"* ]]; then
        echo "🔐 登录公司镜像仓库..."
        docker login hub.17usoft.com
    fi
    
    # 标记镜像
    echo "🏷️  标记镜像..."
    docker tag "$IMAGE_NAME:$VERSION" "$REGISTRY/$IMAGE_NAME:$VERSION"
    docker tag "$IMAGE_NAME:latest" "$REGISTRY/$IMAGE_NAME:latest"
    
    # 推送镜像
    echo "📤 推送镜像..."
    docker push "$REGISTRY/$IMAGE_NAME:$VERSION"
    docker push "$REGISTRY/$IMAGE_NAME:latest"
    
    echo "✅ 镜像推送完成！"
    echo "📥 拉取命令: docker pull $REGISTRY/$IMAGE_NAME:$VERSION"
elif [ "$1" = "push" ] || [ "$2" = "push" ]; then
    # 如果参数中包含push，使用默认公司仓库
    echo "🚀 推送到默认公司仓库: $COMPANY_REGISTRY"
    
    echo "🔐 登录公司镜像仓库..."
    docker login hub.17usoft.com
    
    # 标记镜像
    echo "🏷️  标记镜像..."
    docker tag "$IMAGE_NAME:$VERSION" "$COMPANY_REGISTRY/$IMAGE_NAME:$VERSION"
    docker tag "$IMAGE_NAME:latest" "$COMPANY_REGISTRY/$IMAGE_NAME:latest"
    
    # 推送镜像
    echo "📤 推送镜像..."
    docker push "$COMPANY_REGISTRY/$IMAGE_NAME:$VERSION"
    docker push "$COMPANY_REGISTRY/$IMAGE_NAME:latest"
    
    echo "✅ 镜像推送完成！"
    echo "📥 拉取命令: docker pull $COMPANY_REGISTRY/$IMAGE_NAME:$VERSION"
fi

echo "================================"
echo "🎉 构建完成！"
echo ""
echo "📋 使用说明:"
echo "本地运行: docker run -d -p 80:80 --name dttrip-frontend $IMAGE_NAME:$VERSION"
if [ -n "$REGISTRY" ]; then
    echo "远程运行: docker run -d -p 80:80 --name dttrip-frontend $REGISTRY/$IMAGE_NAME:$VERSION"
elif [ "$1" = "push" ] || [ "$2" = "push" ]; then
    echo "远程运行: docker run -d -p 80:80 --name dttrip-frontend $COMPANY_REGISTRY/$IMAGE_NAME:$VERSION"
fi
echo ""
echo "💡 构建参数说明:"
echo "  ./build-docker.sh                    # 仅构建，不推送"
echo "  ./build-docker.sh v1.0.0            # 构建指定版本"
echo "  ./build-docker.sh push              # 构建并推送到公司仓库"
echo "  ./build-docker.sh v1.0.0 push       # 构建指定版本并推送"
echo "  ./build-docker.sh latest hub.17usoft.com/custom  # 推送到自定义仓库"
echo "================================" 