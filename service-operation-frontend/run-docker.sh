#!/bin/bash

# DTTrip 前端项目 Docker 运行管理脚本

set -e

# 配置变量
CONTAINER_NAME="dttrip-frontend"
IMAGE_NAME="dttrip-frontend:latest"
PORT=${1:-80}

# 函数定义
show_usage() {
    echo "用法: $0 [命令] [选项]"
    echo ""
    echo "命令:"
    echo "  start [端口]     启动容器 (默认端口: 80)"
    echo "  stop            停止容器"
    echo "  restart         重启容器"
    echo "  logs            查看容器日志"
    echo "  status          查看容器状态"
    echo "  shell           进入容器shell"
    echo "  clean           清理停止的容器"
    echo "  rebuild         重新构建并启动"
    echo ""
    echo "示例:"
    echo "  $0 start 8080   # 在8080端口启动"
    echo "  $0 logs         # 查看日志"
    echo "  $0 status       # 查看状态"
}

start_container() {
    local port=${1:-80}
    
    echo "🚀 启动 DTTrip 前端容器..."
    echo "端口映射: $port:80"
    
    # 停止并删除现有容器
    if docker ps -a --format 'table {{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
        echo "📦 发现现有容器，正在清理..."
        docker stop "$CONTAINER_NAME" 2>/dev/null || true
        docker rm "$CONTAINER_NAME" 2>/dev/null || true
    fi
    
    # 启动新容器
    docker run -d \
        --name "$CONTAINER_NAME" \
        -p "$port:80" \
        --restart unless-stopped \
        "$IMAGE_NAME"
    
    echo "✅ 容器启动成功！"
    echo "🌐 访问地址: http://localhost:$port"
    echo "📊 健康检查: http://localhost:$port/health"
    
    # 等待容器启动
    echo "⏳ 等待容器就绪..."
    sleep 3
    
    # 检查容器状态
    if docker ps --format 'table {{.Names}}\t{{.Status}}' | grep -q "$CONTAINER_NAME.*Up"; then
        echo "✅ 容器运行正常"
        
        # 测试健康检查
        if curl -s "http://localhost:$port/health" > /dev/null 2>&1; then
            echo "✅ 健康检查通过"
        else
            echo "⚠️  健康检查未通过，容器可能仍在启动中"
        fi
    else
        echo "❌ 容器启动失败"
        docker logs "$CONTAINER_NAME" --tail 20
        exit 1
    fi
}

stop_container() {
    echo "🛑 停止 DTTrip 前端容器..."
    if docker ps --format 'table {{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
        docker stop "$CONTAINER_NAME"
        echo "✅ 容器已停止"
    else
        echo "ℹ️  容器未运行"
    fi
}

restart_container() {
    echo "🔄 重启 DTTrip 前端容器..."
    stop_container
    sleep 2
    start_container "$PORT"
}

show_logs() {
    echo "📋 DTTrip 前端容器日志:"
    echo "================================"
    if docker ps -a --format 'table {{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
        docker logs "$CONTAINER_NAME" --tail 50 -f
    else
        echo "❌ 容器不存在"
        exit 1
    fi
}

show_status() {
    echo "📊 DTTrip 前端容器状态:"
    echo "================================"
    
    # 检查容器是否存在
    if docker ps -a --format 'table {{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
        # 显示容器状态
        docker ps -a --filter "name=$CONTAINER_NAME" --format 'table {{.Names}}\t{{.Status}}\t{{.Ports}}'
        
        # 显示资源使用情况
        echo ""
        echo "资源使用情况:"
        docker stats "$CONTAINER_NAME" --no-stream --format 'table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}'
        
        # 健康检查
        echo ""
        echo "健康检查:"
        if docker ps --format 'table {{.Names}}\t{{.Status}}' | grep -q "$CONTAINER_NAME.*Up"; then
            container_port=$(docker port "$CONTAINER_NAME" 80/tcp | cut -d: -f2)
            if [ -n "$container_port" ]; then
                if curl -s "http://localhost:$container_port/health" > /dev/null 2>&1; then
                    echo "✅ 健康检查通过 - http://localhost:$container_port/health"
                else
                    echo "❌ 健康检查失败"
                fi
            fi
        else
            echo "❌ 容器未运行"
        fi
    else
        echo "❌ 容器不存在"
    fi
}

enter_shell() {
    echo "🐚 进入 DTTrip 前端容器 shell..."
    if docker ps --format 'table {{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
        docker exec -it "$CONTAINER_NAME" /bin/sh
    else
        echo "❌ 容器未运行"
        exit 1
    fi
}

clean_container() {
    echo "🧹 清理 DTTrip 前端容器..."
    
    # 停止容器
    if docker ps --format 'table {{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
        docker stop "$CONTAINER_NAME"
        echo "✅ 容器已停止"
    fi
    
    # 删除容器
    if docker ps -a --format 'table {{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
        docker rm "$CONTAINER_NAME"
        echo "✅ 容器已删除"
    fi
    
    echo "✅ 清理完成"
}

rebuild_container() {
    echo "🔨 重新构建并启动 DTTrip 前端容器..."
    
    # 清理现有容器
    clean_container
    
    # 重新构建镜像
    echo "🔨 重新构建镜像..."
    ./build-docker.sh
    
    # 启动容器
    start_container "$PORT"
}

# 主逻辑
case "${1:-start}" in
    start)
        start_container "${2:-80}"
        ;;
    stop)
        stop_container
        ;;
    restart)
        restart_container
        ;;
    logs)
        show_logs
        ;;
    status)
        show_status
        ;;
    shell)
        enter_shell
        ;;
    clean)
        clean_container
        ;;
    rebuild)
        rebuild_container
        ;;
    help|--help|-h)
        show_usage
        ;;
    *)
        echo "❌ 未知命令: $1"
        echo ""
        show_usage
        exit 1
        ;;
esac 