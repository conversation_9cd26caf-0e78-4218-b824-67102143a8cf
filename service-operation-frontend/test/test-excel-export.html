<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excel导出测试</title>
    <script src="https://unpkg.com/xlsx-js-style@1.2.0/dist/xlsx.full.min.js"></script>
    <script src="https://unpkg.com/file-saver@2.0.5/dist/FileSaver.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 0;
        }
        button:hover {
            background-color: #0056b3;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>Excel导出功能测试</h1>
    <p>这个页面用于测试Excel导出功能，特别是单元格边框样式。</p>
    
    <button onclick="testExcelExport()">测试Excel导出（带边框）</button>
    
    <div id="status"></div>

    <script>
        function showStatus(message, isError = false) {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${isError ? 'error' : 'success'}`;
            statusDiv.textContent = message;
        }

        function testExcelExport() {
            try {
                showStatus('正在生成Excel文件...');
                
                // 测试数据
                const testData = [
                    {
                        '序号': 1,
                        '任务ID': 'AN_20241128_001',
                        '护照号码': 'P123456789',
                        '姓氏': 'ZHANG',
                        '名字': 'SAN',
                        '国籍': 'CHN',
                        '出生日期': '1990-01-01',
                        '性别': 'M',
                        '出生地': 'BEIJING',
                        '签发日期': '2020-01-01',
                        '有效期至': '2030-01-01',
                        '签发国': 'CHN',
                        '签发机关': 'BEIJING PSB',
                        '处理状态': '已完成',
                        '创建时间': '2024-11-28 10:00:00',
                        '更新时间': '2024-11-28 10:05:00'
                    },
                    {
                        '序号': 2,
                        '任务ID': 'AN_20241128_002',
                        '护照号码': 'P987654321',
                        '姓氏': 'LI',
                        '名字': 'SI',
                        '国籍': 'CHN',
                        '出生日期': '1985-05-15',
                        '性别': 'F',
                        '出生地': 'SHANGHAI',
                        '签发日期': '2019-03-10',
                        '有效期至': '2029-03-10',
                        '签发国': 'CHN',
                        '签发机关': 'SHANGHAI PSB',
                        '处理状态': '已完成',
                        '创建时间': '2024-11-28 11:00:00',
                        '更新时间': '2024-11-28 11:03:00'
                    }
                ];

                // 创建工作簿
                const ws = XLSX.utils.json_to_sheet(testData);
                const wb = XLSX.utils.book_new();
                
                // 设置列宽
                const colWidths = [
                    { wch: 6 },   // 序号
                    { wch: 20 },  // 任务ID
                    { wch: 15 },  // 护照号码
                    { wch: 12 },  // 姓氏
                    { wch: 12 },  // 名字
                    { wch: 10 },  // 国籍
                    { wch: 12 },  // 出生日期
                    { wch: 6 },   // 性别
                    { wch: 15 },  // 出生地
                    { wch: 12 },  // 签发日期
                    { wch: 12 },  // 有效期至
                    { wch: 10 },  // 签发国
                    { wch: 15 },  // 签发机关
                    { wch: 10 },  // 处理状态
                    { wch: 18 },  // 创建时间
                    { wch: 18 }   // 更新时间
                ];
                ws['!cols'] = colWidths;

                // 获取工作表范围
                const range = XLSX.utils.decode_range(ws['!ref'] || 'A1');
                
                // 定义完整的边框样式
                const borderAll = {
                    style: 'thin',
                    color: { rgb: '000000' }
                };
                
                // 为所有单元格添加边框和样式
                for (let row = range.s.r; row <= range.e.r; row++) {
                    for (let col = range.s.c; col <= range.e.c; col++) {
                        const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
                        if (!ws[cellAddress]) ws[cellAddress] = { t: 's', v: '' };
                        
                        // 设置单元格样式
                        ws[cellAddress].s = {
                            border: {
                                top: borderAll,
                                bottom: borderAll,
                                left: borderAll,
                                right: borderAll
                            },
                            alignment: { 
                                horizontal: 'left', 
                                vertical: 'center',
                                wrapText: true 
                            },
                            font: { 
                                name: '微软雅黑', 
                                sz: 10 
                            }
                        };
                        
                        // 为标题行添加特殊样式
                        if (row === 0) {
                            ws[cellAddress].s.fill = {
                                patternType: 'solid',
                                fgColor: { rgb: 'F3F4F6' }
                            };
                            ws[cellAddress].s.font = {
                                name: '微软雅黑',
                                sz: 10,
                                bold: true
                            };
                            ws[cellAddress].s.alignment = {
                                horizontal: 'center',
                                vertical: 'center',
                                wrapText: true
                            };
                        }
                    }
                }

                // 添加工作表
                XLSX.utils.book_append_sheet(wb, ws, '护照识别结果');
                
                // 生成Excel文件
                const excelBuffer = XLSX.write(wb, { 
                    bookType: 'xlsx', 
                    type: 'array',
                    cellStyles: true
                });
                const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
                
                // 下载文件
                const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
                const filename = `护照识别测试_${timestamp}.xlsx`;
                saveAs(blob, filename);
                
                showStatus('Excel文件导出成功！请检查下载的文件是否有边框。');
                
            } catch (error) {
                console.error('导出Excel失败:', error);
                showStatus(`导出失败: ${error.message}`, true);
            }
        }
    </script>
</body>
</html> 