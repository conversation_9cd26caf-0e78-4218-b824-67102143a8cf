<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>刷新功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        .button:hover {
            background-color: #0056b3;
        }
        .button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .refresh-icon {
            width: 16px;
            height: 16px;
            animation: spin 1s linear infinite;
        }
        .refresh-icon.stopped {
            animation: none;
        }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        .status-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
        }
        .status-completed { border-left: 4px solid #28a745; }
        .status-processing { border-left: 4px solid #007bff; }
        .status-pending { border-left: 4px solid #ffc107; }
        .status-failed { border-left: 4px solid #dc3545; }
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 5px;
            color: white;
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }
        .toast.success { background-color: #28a745; }
        .toast.error { background-color: #dc3545; }
        .toast.info { background-color: #007bff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 护照识别系统刷新功能测试</h1>
        
        <div class="status-card">
            <h3>任务详情页面按钮布局</h3>
            <p>任务详情页面的按钮布局已优化：</p>
            <ul>
                <li>✅ <strong>表格头部</strong>：导出Excel + 刷新按钮并排显示</li>
                <li>✅ <strong>错误状态</strong>：加载失败时显示"重试"按钮</li>
                <li>✅ <strong>无数据状态</strong>：没有护照记录时显示刷新按钮</li>
                <li>❌ <strong>页面头部</strong>：已移除刷新按钮，保持界面简洁</li>
            </ul>
            <p class="text-sm text-gray-600 mt-2">
                💡 优化后的布局：将相关操作按钮集中在表格头部，提供更好的用户体验
            </p>
        </div>

        <div class="status-card">
            <h3>功能说明</h3>
            <p>本页面用于测试护照识别系统的刷新功能，包括：</p>
            <ul>
                <li>✅ 识别历史列表整体刷新</li>
                <li>✅ 单个未完成任务的状态刷新</li>
                <li>✅ 任务详情页面数据刷新（多个位置）</li>
                <li>✅ 刷新状态指示和用户反馈</li>
            </ul>
        </div>

        <div class="status-card">
            <h3>测试场景</h3>
            <div id="test-scenarios">
                <div class="status-card status-pending">
                    <strong>场景1：待处理任务</strong>
                    <p>任务ID: AN_20241201_001</p>
                    <p>状态: 待处理 (pending)</p>
                    <p>应显示刷新按钮 ✅</p>
                </div>
                
                <div class="status-card status-processing">
                    <strong>场景2：处理中任务</strong>
                    <p>任务ID: AN_20241201_002</p>
                    <p>状态: 处理中 (processing)</p>
                    <p>应显示刷新按钮 ✅</p>
                </div>
                
                <div class="status-card status-completed">
                    <strong>场景3：已完成任务</strong>
                    <p>任务ID: AN_20241201_003</p>
                    <p>状态: 已完成 (completed)</p>
                    <p>不显示刷新按钮 ❌</p>
                </div>
                
                <div class="status-card status-failed">
                    <strong>场景4：失败任务</strong>
                    <p>任务ID: AN_20241201_004</p>
                    <p>状态: 失败 (failed)</p>
                    <p>不显示刷新按钮 ❌</p>
                </div>
            </div>
        </div>

        <div class="status-card">
            <h3>刷新功能测试</h3>
            <p>点击下面的按钮测试不同的刷新功能：</p>
            
            <button class="button" onclick="testHistoryRefresh()">
                <svg class="refresh-icon stopped" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M23 4v6h-6M1 20v-6h6M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4a9 9 0 0 1-14.85 3.36L13 14"/>
                </svg>
                刷新历史列表
            </button>
            
            <button class="button" onclick="testSingleTaskRefresh('AN_20241201_001')">
                <svg class="refresh-icon stopped" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M23 4v6h-6M1 20v-6h6M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4a9 9 0 0 1-14.85 3.36L13 14"/>
                </svg>
                刷新单个任务
            </button>
            
            <button class="button" onclick="testTaskDetailRefresh('AN_20241201_002')">
                <svg class="refresh-icon stopped" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M23 4v6h-6M1 20v-6h6M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4a9 9 0 0 1-14.85 3.36L13 14"/>
                </svg>
                刷新任务详情
            </button>
        </div>

        <div class="status-card">
            <h3>实际系统链接</h3>
            <p>在实际系统中测试刷新功能：</p>
            <a href="http://localhost:5173/passport-recognition" target="_blank" class="button">
                🔗 打开护照识别页面
            </a>
            <a href="http://localhost:5173/task-detail/AN_20241201_001" target="_blank" class="button">
                🔗 打开任务详情页面
            </a>
        </div>

        <div class="status-card">
            <h3>测试结果日志</h3>
            <div id="test-log" style="background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto;">
                <div>等待测试...</div>
            </div>
        </div>
    </div>

    <script>
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.innerHTML = `
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    ${type === 'success' ? '<path d="M5 13l4 4L19 7"/>' : 
                      type === 'error' ? '<path d="M6 18L18 6M6 6l12 12"/>' : 
                      '<circle cx="12" cy="12" r="10"/><path d="M12 6v6l4 2"/>'}
                </svg>
                <span>${message}</span>
            `;
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.style.transform = 'translateX(100%)';
                toast.style.opacity = '0';
                setTimeout(() => document.body.removeChild(toast), 300);
            }, 3000);
        }

        function logTest(message) {
            const log = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            log.appendChild(logEntry);
            log.scrollTop = log.scrollHeight;
        }

        function simulateLoading(button, duration = 2000) {
            const icon = button.querySelector('.refresh-icon');
            button.disabled = true;
            icon.classList.remove('stopped');
            
            setTimeout(() => {
                button.disabled = false;
                icon.classList.add('stopped');
            }, duration);
        }

        function testHistoryRefresh() {
            const button = event.target.closest('.button');
            logTest('开始测试历史列表刷新功能...');
            showToast('正在刷新历史列表...', 'info');
            simulateLoading(button);
            
            setTimeout(() => {
                logTest('✅ 历史列表刷新测试完成');
                showToast('历史列表刷新成功', 'success');
            }, 2000);
        }

        function testSingleTaskRefresh(taskId) {
            const button = event.target.closest('.button');
            logTest(`开始测试单个任务刷新功能 (${taskId})...`);
            showToast(`正在刷新任务 ${taskId}...`, 'info');
            simulateLoading(button);
            
            setTimeout(() => {
                logTest(`✅ 单个任务刷新测试完成 (${taskId})`);
                showToast('任务状态刷新成功', 'success');
            }, 2000);
        }

        function testTaskDetailRefresh(taskId) {
            const button = event.target.closest('.button');
            logTest(`开始测试任务详情刷新功能 (${taskId})...`);
            showToast(`正在刷新任务详情 ${taskId}...`, 'info');
            simulateLoading(button);
            
            setTimeout(() => {
                logTest(`✅ 任务详情刷新测试完成 (${taskId})`);
                showToast('任务详情刷新成功', 'success');
            }, 2000);
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            logTest('🚀 刷新功能测试页面已加载');
            logTest('📋 测试功能包括：历史列表刷新、单个任务刷新、任务详情刷新');
            logTest('💡 提示：只有未完成的任务才会显示刷新按钮');
        });
    </script>
</body>
</html> 