<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>复制功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        .button:hover {
            background-color: #0056b3;
        }
        .copy-button {
            background-color: #28a745;
            padding: 8px;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }
        .copy-button:hover {
            background-color: #218838;
        }
        .status-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
        }
        .table-container {
            overflow-x: auto;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            margin: 20px 0;
        }
        .demo-table {
            width: 100%;
            border-collapse: collapse;
            min-width: 1200px;
        }
        .demo-table th,
        .demo-table td {
            padding: 12px 8px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
            font-size: 12px;
        }
        .demo-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            position: sticky;
            top: 0;
        }
        .demo-table tr:hover {
            background-color: #f8f9fa;
        }
        .status-column,
        .action-column {
            position: sticky;
            background-color: white;
            border-left: 1px solid #dee2e6;
        }
        .status-column {
            right: 64px;
        }
        .action-column {
            right: 0;
            text-align: center;
        }
        .demo-table th.status-column,
        .demo-table th.action-column {
            background-color: #f8f9fa;
        }
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: bold;
        }
        .status-completed { background-color: #d4edda; color: #155724; }
        .status-processing { background-color: #d1ecf1; color: #0c5460; }
        .status-pending { background-color: #fff3cd; color: #856404; }
        .status-failed { background-color: #f8d7da; color: #721c24; }
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 5px;
            color: white;
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }
        .toast.success { background-color: #28a745; }
        .toast.error { background-color: #dc3545; }
        .copy-icon {
            width: 16px;
            height: 16px;
        }
        .copy-result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📋 护照数据复制功能测试</h1>
        
        <div class="status-card">
            <h3>功能说明</h3>
            <p>本页面用于测试护照识别系统的数据复制功能：</p>
            <ul>
                <li>✅ 状态列固定在表格最右边</li>
                <li>✅ 新增操作列，包含复制按钮</li>
                <li>✅ 一键复制行数据到剪贴板</li>
                <li>✅ 复制格式：字段名：值 换行格式</li>
                <li>✅ 复制成功/失败提示</li>
            </ul>
        </div>

        <div class="status-card">
            <h3>表格布局演示</h3>
            <p>下面的表格演示了新的列布局，状态列和操作列固定在右侧：</p>
        </div>

        <div class="table-container">
            <table class="demo-table">
                <thead>
                    <tr>
                        <th style="width: 50px;">序号</th>
                        <th style="width: 120px;">护照号码</th>
                        <th style="width: 80px;">姓氏</th>
                        <th style="width: 80px;">名字</th>
                        <th style="width: 60px;">国籍</th>
                        <th style="width: 100px;">出生日期</th>
                        <th style="width: 50px;">性别</th>
                        <th style="width: 100px;">出生地</th>
                        <th style="width: 100px;">签发日期</th>
                        <th style="width: 100px;">有效期至</th>
                        <th style="width: 60px;">签发国</th>
                        <th style="width: 120px;">签发机关</th>
                        <th class="status-column" style="width: 80px;">状态</th>
                        <th class="action-column" style="width: 64px;">操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1</td>
                        <td>P123456789</td>
                        <td>ZHANG</td>
                        <td>SAN</td>
                        <td>CHN</td>
                        <td>1990-01-01</td>
                        <td>M</td>
                        <td>BEIJING</td>
                        <td>2020-01-01</td>
                        <td>2030-01-01</td>
                        <td>CHN</td>
                        <td>公安部出入境管理局</td>
                        <td class="status-column">
                            <span class="status-badge status-completed">已完成</span>
                        </td>
                        <td class="action-column">
                            <button class="button copy-button" onclick="copyDemoData(1)" title="复制行数据">
                                <svg class="copy-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                </svg>
                            </button>
                        </td>
                    </tr>
                    <tr>
                        <td>2</td>
                        <td>P987654321</td>
                        <td>LI</td>
                        <td>SI</td>
                        <td>CHN</td>
                        <td>1985-05-15</td>
                        <td>F</td>
                        <td>SHANGHAI</td>
                        <td>2021-03-15</td>
                        <td>2031-03-15</td>
                        <td>CHN</td>
                        <td>公安部出入境管理局</td>
                        <td class="status-column">
                            <span class="status-badge status-processing">处理中</span>
                        </td>
                        <td class="action-column">
                            <button class="button copy-button" onclick="copyDemoData(2)" title="复制行数据">
                                <svg class="copy-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                </svg>
                            </button>
                        </td>
                    </tr>
                    <tr>
                        <td>3</td>
                        <td>P555666777</td>
                        <td>WANG</td>
                        <td>WU</td>
                        <td>CHN</td>
                        <td>1992-12-25</td>
                        <td>M</td>
                        <td>GUANGZHOU</td>
                        <td>2022-06-01</td>
                        <td>2032-06-01</td>
                        <td>CHN</td>
                        <td>公安部出入境管理局</td>
                        <td class="status-column">
                            <span class="status-badge status-pending">待处理</span>
                        </td>
                        <td class="action-column">
                            <button class="button copy-button" onclick="copyDemoData(3)" title="复制行数据">
                                <svg class="copy-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                </svg>
                            </button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="status-card">
            <h3>复制结果预览</h3>
            <p>点击上面表格中的复制按钮，复制的内容将显示在下面：</p>
            <div id="copy-result" class="copy-result">点击复制按钮查看复制内容...</div>
        </div>

        <div class="status-card">
            <h3>实际系统链接</h3>
            <p>在实际系统中测试复制功能：</p>
            <a href="http://localhost:5173/passport-recognition" target="_blank" class="button">
                🔗 打开护照识别页面
            </a>
            <a href="http://localhost:5173/task-detail/AN_20241201_001" target="_blank" class="button">
                🔗 打开任务详情页面
            </a>
        </div>
    </div>

    <script>
        const demoData = {
            1: {
                task_id: 'AN_20241201_001',
                passport_number: 'P123456789',
                surname: 'ZHANG',
                given_names: 'SAN',
                nationality: 'CHN',
                date_of_birth: '1990-01-01',
                sex: 'M',
                place_of_birth: 'BEIJING',
                date_of_issue: '2020-01-01',
                date_of_expiry: '2030-01-01',
                country_of_issue: 'CHN',
                authority: '公安部出入境管理局',
                processing_status: '已完成',
                created_at: '2024-12-01 10:00:00',
                updated_at: '2024-12-01 10:30:00'
            },
            2: {
                task_id: 'AN_20241201_002',
                passport_number: 'P987654321',
                surname: 'LI',
                given_names: 'SI',
                nationality: 'CHN',
                date_of_birth: '1985-05-15',
                sex: 'F',
                place_of_birth: 'SHANGHAI',
                date_of_issue: '2021-03-15',
                date_of_expiry: '2031-03-15',
                country_of_issue: 'CHN',
                authority: '公安部出入境管理局',
                processing_status: '处理中',
                created_at: '2024-12-01 11:00:00',
                updated_at: '2024-12-01 11:15:00'
            },
            3: {
                task_id: 'AN_20241201_003',
                passport_number: 'P555666777',
                surname: 'WANG',
                given_names: 'WU',
                nationality: 'CHN',
                date_of_birth: '1992-12-25',
                sex: 'M',
                place_of_birth: 'GUANGZHOU',
                date_of_issue: '2022-06-01',
                date_of_expiry: '2032-06-01',
                country_of_issue: 'CHN',
                authority: '公安部出入境管理局',
                processing_status: '待处理',
                created_at: '2024-12-01 12:00:00',
                updated_at: '2024-12-01 12:05:00'
            }
        };

        function showToast(message, type = 'success') {
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.innerHTML = `
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    ${type === 'success' ? '<path d="M5 13l4 4L19 7"/>' : '<path d="M6 18L18 6M6 6l12 12"/>'}
                </svg>
                <span>${message}</span>
            `;
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.style.transform = 'translateX(100%)';
                toast.style.opacity = '0';
                setTimeout(() => document.body.removeChild(toast), 300);
            }, 3000);
        }

        async function copyDemoData(id) {
            try {
                const data = demoData[id];
                if (!data) {
                    throw new Error('数据不存在');
                }

                const fieldNames = {
                    task_id: '任务ID',
                    passport_number: '护照号码',
                    surname: '姓氏',
                    given_names: '名字',
                    nationality: '国籍',
                    date_of_birth: '出生日期',
                    sex: '性别',
                    place_of_birth: '出生地',
                    date_of_issue: '签发日期',
                    date_of_expiry: '有效期至',
                    country_of_issue: '签发国',
                    authority: '签发机关',
                    processing_status: '处理状态',
                    created_at: '创建时间',
                    updated_at: '更新时间'
                };

                const copyText = Object.entries(fieldNames)
                    .map(([key, label]) => {
                        const value = data[key] || '-';
                        return `${label}：${value}`;
                    })
                    .join('\n');

                await navigator.clipboard.writeText(copyText);
                
                // 显示复制结果
                document.getElementById('copy-result').textContent = copyText;
                
                showToast('数据已复制到剪贴板', 'success');
                
            } catch (error) {
                console.error('复制失败:', error);
                showToast('复制失败，请重试', 'error');
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 复制功能测试页面已加载');
        });
    </script>
</body>
</html> 