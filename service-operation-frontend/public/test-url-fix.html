<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URL修复验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 URL构造修复验证</h1>
        
        <h2>📋 测试API URL构造</h2>
        <button class="test-button" onclick="testAPIConstruction()">测试API URL构造</button>
        <button class="test-button" onclick="testProjectAPI()">测试项目API调用</button>
        <button class="test-button" onclick="clearResults()">清除结果</button>
        
        <h2>📊 测试结果</h2>
        <div id="test-results"></div>
    </div>

    <script>
        // 模拟API_BASE_URL配置
        const API_BASE_URL = '/api'; // 开发环境相对路径
        
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = message;
            resultsDiv.appendChild(resultDiv);
        }
        
        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
        }
        
        // 测试URL构造逻辑
        function testAPIConstruction() {
            addResult('🔄 开始测试URL构造...', 'info');
            
            try {
                // 测试基本URL构造
                const endpoint = '/project/';
                const fullUrl = `${API_BASE_URL}${endpoint}`;
                addResult(`✅ 基本URL构造成功: ${fullUrl}`, 'success');
                
                // 测试带参数的URL构造
                const params = { page: '1', page_size: '20' };
                const searchParams = new URLSearchParams();
                Object.entries(params).forEach(([key, value]) => {
                    searchParams.append(key, value);
                });
                const queryString = searchParams.toString();
                const fullUrlWithParams = queryString ? `${fullUrl}?${queryString}` : fullUrl;
                addResult(`✅ 带参数URL构造成功: ${fullUrlWithParams}`, 'success');
                
                // 测试旧方法（会失败）
                try {
                    const oldUrl = new URL(`${API_BASE_URL}${endpoint}`);
                    addResult(`❌ 旧方法不应该成功: ${oldUrl.toString()}`, 'error');
                } catch (error) {
                    addResult(`✅ 旧方法正确失败: ${error.message}`, 'success');
                }
                
            } catch (error) {
                addResult(`❌ URL构造测试失败: ${error.message}`, 'error');
            }
        }
        
        // 测试实际API调用
        async function testProjectAPI() {
            addResult('🔄 开始测试项目API调用...', 'info');
            
            try {
                const endpoint = '/project/';
                const fullUrl = `${API_BASE_URL}${endpoint}`;
                
                addResult(`📡 发送请求到: ${fullUrl}`, 'info');
                
                const response = await fetch(fullUrl, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                addResult(`📥 响应状态: ${response.status} ${response.statusText}`, 'info');
                
                if (response.status === 401 || response.status === 403) {
                    addResult('✅ API调用成功（认证错误是预期的）', 'success');
                } else if (response.ok) {
                    addResult('✅ API调用成功', 'success');
                } else {
                    addResult(`⚠️ API调用返回: ${response.status}`, 'info');
                }
                
                const data = await response.text();
                addResult(`📄 响应内容: ${data.substring(0, 100)}...`, 'info');
                
            } catch (error) {
                if (error.message.includes('Invalid URL')) {
                    addResult(`❌ URL构造错误（修复前的问题）: ${error.message}`, 'error');
                } else {
                    addResult(`⚠️ 网络错误: ${error.message}`, 'info');
                }
            }
        }
        
        // 页面加载时显示当前配置
        window.onload = function() {
            addResult(`🔧 当前API_BASE_URL: ${API_BASE_URL}`, 'info');
            addResult(`🌍 当前页面URL: ${window.location.href}`, 'info');
            addResult(`📍 开发环境检测: ${window.location.hostname === 'localhost' ? '是' : '否'}`, 'info');
        };
    </script>
</body>
</html> 