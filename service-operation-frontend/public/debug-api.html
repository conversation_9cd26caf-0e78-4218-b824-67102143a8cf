<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API配置调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .config-item {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 API配置调试工具</h1>
        
        <h2>📋 当前配置</h2>
        <div id="config-info"></div>
        
        <h2>🧪 API测试</h2>
        <button class="test-button" onclick="testHealthAPI()">测试健康检查 (/api/health)</button>
        <button class="test-button" onclick="testSSOAPI()">测试SSO登录URL (/api/auth/sso/login-url)</button>
        <button class="test-button" onclick="testProjectAPI()">测试项目API (/api/project)</button>
        
        <h2>📊 测试结果</h2>
        <div id="test-results"></div>
    </div>

    <script>
        // 显示当前配置
        function showConfig() {
            const config = {
                'import.meta.env.DEV': typeof import !== 'undefined' && import.meta?.env?.DEV,
                'import.meta.env.VITE_API_BASE_URL': typeof import !== 'undefined' && import.meta?.env?.VITE_API_BASE_URL,
                'window.location.origin': window.location.origin,
                '推断的API_BASE_URL': getAPIBaseURL()
            };
            
            const configDiv = document.getElementById('config-info');
            configDiv.innerHTML = Object.entries(config).map(([key, value]) => 
                `<div class="config-item"><strong>${key}:</strong> ${JSON.stringify(value)}</div>`
            ).join('');
        }
        
        // 获取API基础URL（模拟constants.ts的逻辑）
        function getAPIBaseURL() {
            // 模拟 import.meta.env.VITE_API_BASE_URL || (import.meta.env.DEV ? '/api' : 'http://localhost:8000/api')
            const viteApiBaseUrl = typeof import !== 'undefined' && import.meta?.env?.VITE_API_BASE_URL;
            const isDev = typeof import !== 'undefined' && import.meta?.env?.DEV;
            
            if (viteApiBaseUrl) {
                return viteApiBaseUrl;
            }
            
            // 在浏览器环境中，我们假设开发环境
            return window.location.hostname === 'localhost' ? '/api' : 'http://localhost:8000/api';
        }
        
        // 测试API
        async function testAPI(endpoint, description) {
            const apiBaseUrl = getAPIBaseURL();
            const fullUrl = `${apiBaseUrl}${endpoint}`;
            
            const resultDiv = document.getElementById('test-results');
            const testId = `test-${Date.now()}`;
            
            // 添加测试开始信息
            resultDiv.innerHTML += `<div id="${testId}" class="result">🔄 测试 ${description}: ${fullUrl}</div>`;
            
            try {
                const response = await fetch(fullUrl);
                const status = response.status;
                const statusText = response.statusText;
                
                let result = `✅ ${description} - 状态: ${status} ${statusText}`;
                
                if (response.ok) {
                    try {
                        const data = await response.text();
                        result += `<br>响应: ${data.substring(0, 200)}${data.length > 200 ? '...' : ''}`;
                    } catch (e) {
                        result += `<br>响应解析失败: ${e.message}`;
                    }
                }
                
                document.getElementById(testId).className = 'result success';
                document.getElementById(testId).innerHTML = result;
            } catch (error) {
                document.getElementById(testId).className = 'result error';
                document.getElementById(testId).innerHTML = `❌ ${description} - 错误: ${error.message}`;
            }
        }
        
        function testHealthAPI() {
            testAPI('/health', '健康检查');
        }
        
        function testSSOAPI() {
            testAPI('/auth/sso/login-url', 'SSO登录URL');
        }
        
        function testProjectAPI() {
            testAPI('/project', '项目API');
        }
        
        // 页面加载时显示配置
        showConfig();
    </script>
</body>
</html> 