<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API路径测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 3px;
            font-size: 12px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin: 5px 0;
            padding: 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
            font-size: 12px;
        }
        th {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 API路径测试工具</h1>
        
        <h2>📋 测试所有API端点</h2>
        <button class="test-button" onclick="testAllAPIs()">测试所有API</button>
        <button class="test-button" onclick="clearResults()">清除结果</button>
        
        <h2>📊 测试结果</h2>
        <div id="test-results"></div>
        
        <h2>📈 结果统计</h2>
        <div id="test-summary"></div>
    </div>

    <script>
        // API端点列表
        const apiEndpoints = [
            // 认证相关
            { path: '/api/auth/sso/login-url', method: 'GET', name: 'SSO登录URL' },
            { path: '/api/auth/sso/callback', method: 'POST', name: 'SSO回调' },
            
            // 项目管理
            { path: '/api/project/', method: 'GET', name: '项目列表' },
            { path: '/api/project/1/', method: 'GET', name: '项目详情' },
            { path: '/api/project/stats/summary/', method: 'GET', name: '项目统计' },
            
            // 项目任务
            { path: '/api/project-task/', method: 'GET', name: '任务列表' },
            { path: '/api/project-task/1/', method: 'GET', name: '任务详情' },
            { path: '/api/project-task/stats/summary/', method: 'GET', name: '任务统计' },
            
            // 护照识别
            { path: '/api/passport/list/', method: 'GET', name: '护照列表' },
            { path: '/api/passport/1/', method: 'GET', name: '护照详情' },
            
            // 火车票订单
            { path: '/api/train-order/', method: 'GET', name: '订单列表' },
            { path: '/api/train-order/1/', method: 'GET', name: '订单详情' },
            
            // 健康检查
            { path: '/api/health/', method: 'GET', name: '健康检查' },
        ];

        let testResults = [];

        // 测试单个API
        async function testAPI(endpoint) {
            const { path, method, name } = endpoint;
            const startTime = Date.now();
            
            try {
                const response = await fetch(path, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                const endTime = Date.now();
                const duration = endTime - startTime;
                
                const result = {
                    name,
                    path,
                    method,
                    status: response.status,
                    statusText: response.statusText,
                    duration,
                    success: response.status < 500, // 认为4xx是正常的（认证错误等）
                    error: null
                };
                
                // 特殊处理：307重定向表示路径问题
                if (response.status === 307) {
                    result.success = false;
                    result.error = '路径重定向问题';
                }
                
                return result;
            } catch (error) {
                const endTime = Date.now();
                const duration = endTime - startTime;
                
                return {
                    name,
                    path,
                    method,
                    status: 0,
                    statusText: 'Network Error',
                    duration,
                    success: false,
                    error: error.message
                };
            }
        }

        // 测试所有API
        async function testAllAPIs() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<div class="result">🔄 正在测试所有API端点...</div>';
            
            testResults = [];
            
            for (const endpoint of apiEndpoints) {
                const result = await testAPI(endpoint);
                testResults.push(result);
                
                // 实时显示结果
                updateResults();
                updateSummary();
                
                // 短暂延迟避免过快请求
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            
            console.log('所有API测试完成:', testResults);
        }

        // 更新结果显示
        function updateResults() {
            const resultsDiv = document.getElementById('test-results');
            
            let html = '<table><thead><tr><th>API名称</th><th>路径</th><th>方法</th><th>状态</th><th>耗时</th><th>结果</th></tr></thead><tbody>';
            
            testResults.forEach(result => {
                const statusClass = result.success ? 'success' : 'error';
                const statusIcon = result.success ? '✅' : '❌';
                const errorInfo = result.error ? ` (${result.error})` : '';
                
                html += `
                    <tr class="${statusClass}">
                        <td>${result.name}</td>
                        <td>${result.path}</td>
                        <td>${result.method}</td>
                        <td>${result.status} ${result.statusText}</td>
                        <td>${result.duration}ms</td>
                        <td>${statusIcon}${errorInfo}</td>
                    </tr>
                `;
            });
            
            html += '</tbody></table>';
            resultsDiv.innerHTML = html;
        }

        // 更新统计信息
        function updateSummary() {
            const summaryDiv = document.getElementById('test-summary');
            
            const total = testResults.length;
            const success = testResults.filter(r => r.success).length;
            const failed = total - success;
            const avgDuration = total > 0 ? Math.round(testResults.reduce((sum, r) => sum + r.duration, 0) / total) : 0;
            
            const redirects = testResults.filter(r => r.status === 307).length;
            const authErrors = testResults.filter(r => r.status === 401 || r.status === 403).length;
            const networkErrors = testResults.filter(r => r.status === 0).length;
            
            summaryDiv.innerHTML = `
                <div class="result success">
                    📊 总计: ${total} | ✅ 成功: ${success} | ❌ 失败: ${failed} | ⏱️ 平均耗时: ${avgDuration}ms
                </div>
                <div class="result warning">
                    🔄 重定向问题: ${redirects} | 🔒 认证错误: ${authErrors} | 🌐 网络错误: ${networkErrors}
                </div>
            `;
        }

        // 清除结果
        function clearResults() {
            testResults = [];
            document.getElementById('test-results').innerHTML = '';
            document.getElementById('test-summary').innerHTML = '';
        }
    </script>
</body>
</html> 