<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>认证错误处理测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .error-button {
            background: #dc3545;
        }
        .error-button:hover {
            background: #c82333;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        .info {
            color: #17a2b8;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
            border-bottom: 1px solid #eee;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>认证错误处理测试</h1>
        <p>这个页面用于测试系统中的认证错误自动处理功能。当检测到"Not authenticated"等认证错误时，应该自动跳转到登录界面。</p>
    </div>

    <div class="container">
        <h2>测试用例</h2>
        
        <h3>1. 模拟不同类型的认证错误</h3>
        <button class="test-button error-button" onclick="testAuthError('Not authenticated')">
            测试 "Not authenticated"
        </button>
        <button class="test-button error-button" onclick="testAuthError('unauthorized')">
            测试 "unauthorized"
        </button>
        <button class="test-button error-button" onclick="testAuthError('认证失败')">
            测试 "认证失败"
        </button>
        <button class="test-button error-button" onclick="testAuthError('token expired')">
            测试 "token expired"
        </button>
        
        <h3>2. 模拟HTTP 401错误</h3>
        <button class="test-button error-button" onclick="testHttpError(401)">
            测试 HTTP 401 错误
        </button>
        
        <h3>3. 模拟API调用认证错误</h3>
        <button class="test-button error-button" onclick="testApiAuthError()">
            测试 API 认证错误
        </button>
        
        <h3>4. 手动触发认证失败</h3>
        <button class="test-button error-button" onclick="triggerManualAuthFailure()">
            手动触发认证失败
        </button>
        
        <h3>5. 清理测试</h3>
        <button class="test-button" onclick="clearLogs()">清理日志</button>
        <button class="test-button" onclick="checkAuthStatus()">检查认证状态</button>
    </div>

    <div class="container">
        <h2>测试日志</h2>
        <div id="testLog" class="log"></div>
    </div>

    <script>
        // 日志记录函数
        function log(message, type = 'info') {
            const logDiv = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[AUTH-TEST] ${message}`);
        }

        // 监听全局认证失败事件
        window.addEventListener('authenticationFailure', (event) => {
            log(`🔒 收到全局认证失败事件: ${JSON.stringify(event.detail)}`, 'error');
        });

        // 监听页面跳转
        let originalLocation = window.location.href;
        setInterval(() => {
            if (window.location.href !== originalLocation) {
                log(`🔄 页面跳转: ${originalLocation} -> ${window.location.href}`, 'info');
                originalLocation = window.location.href;
            }
        }, 100);

        // 测试不同类型的认证错误
        function testAuthError(errorMessage) {
            log(`开始测试认证错误: "${errorMessage}"`);
            
            const error = {
                message: errorMessage,
                status: 401,
                detail: errorMessage
            };
            
            // 模拟全局错误处理器检测
            const event = new Error(errorMessage);
            event.status = 401;
            
            // 触发未捕获的Promise拒绝事件
            const rejectedPromise = Promise.reject(error);
            
            setTimeout(() => {
                log(`模拟认证错误完成: "${errorMessage}"`, 'error');
            }, 100);
        }

        // 测试HTTP错误
        function testHttpError(statusCode) {
            log(`开始测试HTTP ${statusCode}错误`);
            
            const error = {
                status: statusCode,
                message: `HTTP ${statusCode} Error`,
                response: {
                    status: statusCode,
                    data: {
                        detail: statusCode === 401 ? 'Not authenticated' : `HTTP ${statusCode} Error`
                    }
                }
            };
            
            // 触发全局错误事件
            const errorEvent = new ErrorEvent('error', {
                error: error,
                message: error.message
            });
            
            window.dispatchEvent(errorEvent);
            
            log(`HTTP ${statusCode}错误测试完成`, 'error');
        }

        // 测试API调用认证错误
        async function testApiAuthError() {
            log('开始测试API认证错误');
            
            try {
                // 模拟一个会返回401错误的API调用
                const response = await fetch('/api/test-auth-error', {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer invalid-token'
                    }
                });
                
                if (response.status === 401) {
                    const errorData = await response.json().catch(() => ({ detail: 'Not authenticated' }));
                    
                    const error = {
                        status: 401,
                        message: errorData.detail || 'Not authenticated',
                        response: {
                            status: 401,
                            data: errorData
                        }
                    };
                    
                    // 手动触发认证错误处理
                    const authFailureEvent = new CustomEvent('authenticationFailure', {
                        detail: {
                            reason: 'api_test',
                            message: '认证已过期，请重新登录',
                            originalError: error
                        }
                    });
                    window.dispatchEvent(authFailureEvent);
                    
                    log('API认证错误测试完成 - 检测到401状态', 'error');
                } else {
                    log(`API调用返回状态: ${response.status}`, 'info');
                }
            } catch (error) {
                log(`API调用失败: ${error.message}`, 'error');
                
                // 如果是网络错误，也测试认证错误处理
                if (error.message.includes('Failed to fetch')) {
                    log('模拟网络错误为认证错误', 'info');
                    const authError = {
                        status: 401,
                        message: 'Not authenticated',
                        originalError: error
                    };
                    
                    const authFailureEvent = new CustomEvent('authenticationFailure', {
                        detail: {
                            reason: 'network_error_test',
                            message: '认证已过期，请重新登录',
                            originalError: authError
                        }
                    });
                    window.dispatchEvent(authFailureEvent);
                }
            }
        }

        // 手动触发认证失败
        function triggerManualAuthFailure() {
            log('手动触发认证失败事件');
            
            const authFailureEvent = new CustomEvent('authenticationFailure', {
                detail: {
                    reason: 'manual_test',
                    message: '手动测试认证失败',
                    timestamp: new Date().toISOString()
                }
            });
            
            window.dispatchEvent(authFailureEvent);
            log('手动认证失败事件已触发', 'error');
        }

        // 检查认证状态
        function checkAuthStatus() {
            log('检查当前认证状态');
            
            const tokens = {
                access_token: localStorage.getItem('access_token'),
                app_token: localStorage.getItem('app_token'),
                token: localStorage.getItem('token'),
                user: localStorage.getItem('user'),
                userInfo: localStorage.getItem('userInfo')
            };
            
            log(`本地存储的认证信息: ${JSON.stringify(tokens, null, 2)}`, 'info');
            
            // 检查当前页面URL
            log(`当前页面URL: ${window.location.href}`, 'info');
            
            // 检查是否在登录页面
            const isLoginPage = window.location.pathname === '/login' || window.location.pathname.includes('login');
            log(`是否在登录页面: ${isLoginPage}`, isLoginPage ? 'success' : 'info');
        }

        // 清理日志
        function clearLogs() {
            document.getElementById('testLog').innerHTML = '';
            log('日志已清理', 'success');
        }

        // 页面加载时的初始化
        window.addEventListener('load', () => {
            log('认证错误处理测试页面已加载', 'success');
            checkAuthStatus();
        });

        // 监听存储变化
        window.addEventListener('storage', (event) => {
            if (['access_token', 'app_token', 'token', 'user', 'userInfo'].includes(event.key)) {
                log(`本地存储变化: ${event.key} = ${event.newValue}`, 'info');
            }
        });
    </script>
</body>
</html> 