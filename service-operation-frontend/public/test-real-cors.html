<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>真实跨域测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; white-space: pre-wrap; }
        .status { font-weight: bold; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌐 真实跨域测试</h1>
        
        <div class="test-section info">
            <h3>📋 测试场景</h3>
            <p><strong>当前页面域名:</strong> <span id="currentOrigin"></span></p>
            <p><strong>目标API域名:</strong> https://soa-api.qa.dttrip.cn</p>
            <p><strong>跨域情况:</strong> <span id="corsStatus"></span></p>
            <p>这个测试模拟前端 (localhost:5173) 访问后端 (soa-api.qa.dttrip.cn) 的真实跨域场景</p>
        </div>

        <div class="test-section">
            <h3>🧪 跨域测试</h3>
            <button onclick="testLocalBackend()">测试本地后端 (localhost:8000)</button>
            <button onclick="testRemoteBackend()">测试远程后端 (soa-api.qa.dttrip.cn)</button>
            <button onclick="testProjectAPI()">测试项目API</button>
            <div id="testResult" class="status"></div>
        </div>

        <div class="test-section">
            <h3>📊 测试结果</h3>
            <div id="results"></div>
        </div>

        <div class="test-section">
            <h3>🔍 详细日志</h3>
            <pre id="logs"></pre>
        </div>
    </div>

    <script>
        // 显示当前页面信息
        const currentOrigin = window.location.origin;
        document.getElementById('currentOrigin').textContent = currentOrigin;
        
        // 判断是否跨域
        const targetOrigin = 'https://soa-api.qa.dttrip.cn';
        const isCrossOrigin = currentOrigin !== targetOrigin;
        document.getElementById('corsStatus').textContent = isCrossOrigin ? '是跨域请求' : '同域请求';
        document.getElementById('corsStatus').style.color = isCrossOrigin ? 'red' : 'green';

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            
            // 添加到日志区域
            const logsDiv = document.getElementById('logs');
            logsDiv.textContent += logEntry;
            
            // 添加到结果区域
            const resultsDiv = document.getElementById('results');
            const resultEntry = document.createElement('div');
            resultEntry.className = `test-section ${type}`;
            resultEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            resultsDiv.appendChild(resultEntry);
            
            console.log(logEntry);
        }

        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('testResult');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        async function testLocalBackend() {
            updateStatus('正在测试本地后端...', 'info');
            log('🧪 开始测试本地后端 (localhost:8000)');

            try {
                const response = await fetch('http://localhost:8000/health', {
                    method: 'GET',
                    credentials: 'include'
                });

                const data = await response.text();
                log(`✅ 本地后端响应: ${response.status} - ${data}`, 'success');
                updateStatus('✅ 本地后端访问成功', 'success');

            } catch (error) {
                log(`❌ 本地后端错误: ${error.message}`, 'error');
                updateStatus('❌ 本地后端访问失败', 'error');
            }
        }

        async function testRemoteBackend() {
            updateStatus('正在测试远程后端...', 'info');
            log('🌐 开始测试远程后端 (soa-api.qa.dttrip.cn)');

            try {
                const response = await fetch('https://soa-api.qa.dttrip.cn/health', {
                    method: 'GET',
                    credentials: 'include'
                });

                const data = await response.text();
                log(`✅ 远程后端响应: ${response.status} - ${data}`, 'success');
                updateStatus('✅ 远程后端访问成功', 'success');

            } catch (error) {
                log(`❌ 远程后端错误: ${error.message}`, 'error');
                updateStatus('❌ 远程后端访问失败', 'error');
                
                if (error.message.includes('CORS') || error.message.includes('cross-origin')) {
                    log('🚨 这是CORS跨域错误！说明后端CORS配置有问题', 'error');
                } else if (error.message.includes('Failed to fetch')) {
                    log('🚨 网络请求失败，可能是CORS阻止或网络问题', 'error');
                }
            }
        }

        async function testProjectAPI() {
            updateStatus('正在测试项目API...', 'info');
            log('📋 开始测试项目API (跨域请求)');

            try {
                // 模拟真实的API请求
                const response = await fetch('https://soa-api.qa.dttrip.cn/api/project/', {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer test-token',
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include'
                });

                const data = await response.text();
                log(`📊 项目API响应: ${response.status} - ${data.substring(0, 100)}...`, 'info');

                if (response.status === 401) {
                    log('✅ API跨域请求成功！(401认证错误是正常的)', 'success');
                    updateStatus('✅ 跨域问题已解决！', 'success');
                } else if (response.ok) {
                    log('✅ API请求完全成功！', 'success');
                    updateStatus('✅ API访问成功！', 'success');
                } else {
                    log(`⚠️ API返回错误状态: ${response.status}`, 'error');
                    updateStatus('⚠️ API返回错误', 'error');
                }

            } catch (error) {
                log(`❌ 项目API错误: ${error.message}`, 'error');
                updateStatus('❌ 跨域问题未解决', 'error');
                
                if (error.message.includes('CORS') || error.message.includes('cross-origin')) {
                    log('🚨 确认是CORS跨域错误！后端CORS配置需要修复', 'error');
                } else if (error.message.includes('Failed to fetch')) {
                    log('🚨 请求被浏览器阻止，很可能是CORS问题', 'error');
                }
            }
        }

        // 页面加载时显示环境信息
        window.onload = function() {
            log(`页面加载完成，当前环境: ${currentOrigin}`);
            log(`目标后端: ${targetOrigin}`);
            log(`跨域状态: ${isCrossOrigin ? '是跨域' : '同域'}`);
            log('准备进行真实跨域测试...');
        };
    </script>
</body>
</html> 