<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>酒店订单字段显示测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #e5e5e5;
            border-radius: 4px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: 600;
        }
        .field-row {
            display: flex;
            margin-bottom: 10px;
        }
        .field-label {
            width: 150px;
            font-weight: 600;
            color: #555;
        }
        .field-value {
            color: #333;
        }
        .test-data {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>酒店订单字段显示测试</h1>
        <p>测试"含早餐"、"半日房"、"团体预订"三个字段在各种场景下的显示效果</p>

        <div class="test-section">
            <div class="test-title">1. 订单列表显示测试</div>
            <table>
                <thead>
                    <tr>
                        <th>入住人姓名</th>
                        <th>酒店名称</th>
                        <th>含早餐</th>
                        <th>半日房</th>
                        <th>团体预订</th>
                        <th>金额</th>
                    </tr>
                </thead>
                <tbody id="order-list">
                    <!-- 将通过 JavaScript 填充 -->
                </tbody>
            </table>
        </div>

        <div class="test-section">
            <div class="test-title">2. 订单详情显示测试</div>
            <div id="order-details">
                <!-- 将通过 JavaScript 填充 -->
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">3. 字段值转换测试</div>
            <div class="test-data">
                <div><strong>测试数据：</strong></div>
                <div>字符串 "是" → 显示: <span id="test-yes"></span></div>
                <div>字符串 "否" → 显示: <span id="test-no"></span></div>
                <div>空值 null → 显示: <span id="test-null"></span></div>
                <div>空字符串 "" → 显示: <span id="test-empty"></span></div>
                <div>布尔值 true → 显示: <span id="test-true"></span></div>
                <div>布尔值 false → 显示: <span id="test-false"></span></div>
            </div>
        </div>
    </div>

    <script>
        // 模拟酒店订单数据
        const mockOrders = [
            {
                guest_full_name: "张三",
                hotel_name: "北京饭店",
                include_breakfast: "是",
                is_half_day_room: "否",
                is_group_booking: "是",
                amount: 500
            },
            {
                guest_full_name: "李四",
                hotel_name: "上海宾馆",
                include_breakfast: "否",
                is_half_day_room: "是",
                is_group_booking: "否",
                amount: 300
            },
            {
                guest_full_name: "王五",
                hotel_name: "广州酒店",
                include_breakfast: null,
                is_half_day_room: "",
                is_group_booking: "是",
                amount: 450
            }
        ];

        // 测试显示逻辑 - 使用修改后的逻辑
        function displayField(value) {
            return value || '否';
        }

        // 填充订单列表
        function fillOrderList() {
            const tbody = document.getElementById('order-list');
            tbody.innerHTML = '';
            
            mockOrders.forEach(order => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${order.guest_full_name}</td>
                    <td>${order.hotel_name}</td>
                    <td>${displayField(order.include_breakfast)}</td>
                    <td>${displayField(order.is_half_day_room)}</td>
                    <td>${displayField(order.is_group_booking)}</td>
                    <td>¥${order.amount}</td>
                `;
                tbody.appendChild(row);
            });
        }

        // 填充订单详情
        function fillOrderDetails() {
            const container = document.getElementById('order-details');
            container.innerHTML = '';
            
            mockOrders.forEach((order, index) => {
                const detailDiv = document.createElement('div');
                detailDiv.style.marginBottom = '20px';
                detailDiv.style.padding = '15px';
                detailDiv.style.backgroundColor = '#f8f9fa';
                detailDiv.style.borderRadius = '4px';
                
                detailDiv.innerHTML = `
                    <h4>订单 ${index + 1}: ${order.guest_full_name}</h4>
                    <div class="field-row">
                        <div class="field-label">含早餐:</div>
                        <div class="field-value">${displayField(order.include_breakfast)}</div>
                    </div>
                    <div class="field-row">
                        <div class="field-label">半日房:</div>
                        <div class="field-value">${displayField(order.is_half_day_room)}</div>
                    </div>
                    <div class="field-row">
                        <div class="field-label">团体预订:</div>
                        <div class="field-value">${displayField(order.is_group_booking)}</div>
                    </div>
                `;
                container.appendChild(detailDiv);
            });
        }

        // 测试字段值转换
        function testFieldConversion() {
            document.getElementById('test-yes').textContent = displayField('是');
            document.getElementById('test-no').textContent = displayField('否');
            document.getElementById('test-null').textContent = displayField(null);
            document.getElementById('test-empty').textContent = displayField('');
            document.getElementById('test-true').textContent = displayField(true);
            document.getElementById('test-false').textContent = displayField(false);
        }

        // 页面加载时执行
        document.addEventListener('DOMContentLoaded', function() {
            fillOrderList();
            fillOrderDetails();
            testFieldConversion();
        });
    </script>
</body>
</html> 