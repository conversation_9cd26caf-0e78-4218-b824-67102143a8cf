<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>认证错误跳转调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #dc3545;
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
            font-size: 16px;
            font-weight: bold;
        }
        .test-button:hover {
            background: #c82333;
        }
        .log-container {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #dee2e6;
            max-height: 300px;
            overflow-y: auto;
        }
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            font-family: monospace;
            font-size: 12px;
        }
        .log-error { color: #dc3545; }
        .log-success { color: #28a745; }
        .log-info { color: #17a2b8; }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.current-page {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔒 认证错误跳转调试</h1>
        
        <div class="status current-page">
            当前页面: <span id="currentUrl"></span>
        </div>
        
        <p>点击下面的按钮测试不同的认证错误场景，系统应该自动跳转到登录页面。</p>
        
        <button class="test-button" onclick="testNotAuthenticated()">
            测试 "Not authenticated" 错误
        </button>
        
        <button class="test-button" onclick="testUnauthorized()">
            测试 "unauthorized" 错误
        </button>
        
        <button class="test-button" onclick="testHttp401()">
            测试 HTTP 401 错误
        </button>
        
        <button class="test-button" onclick="testTokenExpired()">
            测试 "token expired" 错误
        </button>
        
        <button class="test-button" onclick="testManualTrigger()">
            手动触发认证失败
        </button>
        
        <button class="test-button" onclick="clearLogs()" style="background: #6c757d;">
            清理日志
        </button>
        
        <div class="log-container">
            <div id="logContainer"></div>
        </div>
    </div>

    <script>
        // 更新当前URL显示
        document.getElementById('currentUrl').textContent = window.location.href;
        
        // 日志函数
        function log(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(`[AUTH-DEBUG] ${message}`);
        }
        
        // 监听认证失败事件
        window.addEventListener('authenticationFailure', (event) => {
            log(`🔒 收到认证失败事件: ${JSON.stringify(event.detail)}`, 'error');
        });
        
        // 监听页面即将跳转
        const originalLocation = window.location.href;
        let redirectCheckInterval;
        
        function startRedirectMonitoring() {
            if (redirectCheckInterval) clearInterval(redirectCheckInterval);
            
            redirectCheckInterval = setInterval(() => {
                if (window.location.href !== originalLocation) {
                    log(`🔄 页面跳转检测: ${originalLocation} -> ${window.location.href}`, 'success');
                    clearInterval(redirectCheckInterval);
                }
            }, 100);
            
            // 5秒后停止监控
            setTimeout(() => {
                if (redirectCheckInterval) {
                    clearInterval(redirectCheckInterval);
                    log('⏰ 跳转监控超时（5秒），可能跳转失败', 'error');
                }
            }, 5000);
        }
        
        // 测试函数
        function testNotAuthenticated() {
            log('🧪 开始测试 "Not authenticated" 错误');
            startRedirectMonitoring();
            
            // 模拟API错误
            const error = {
                message: 'Not authenticated',
                status: 401,
                detail: 'Not authenticated'
            };
            
            // 触发Promise拒绝
            Promise.reject(error);
            
            // 同时手动触发全局事件
            setTimeout(() => {
                const authFailureEvent = new CustomEvent('authenticationFailure', {
                    detail: {
                        reason: 'test_not_authenticated',
                        message: '认证已过期，请重新登录',
                        originalError: error
                    }
                });
                window.dispatchEvent(authFailureEvent);
            }, 100);
        }
        
        function testUnauthorized() {
            log('🧪 开始测试 "unauthorized" 错误');
            startRedirectMonitoring();
            
            const error = {
                message: 'unauthorized',
                status: 401
            };
            
            const authFailureEvent = new CustomEvent('authenticationFailure', {
                detail: {
                    reason: 'test_unauthorized',
                    message: '认证已过期，请重新登录',
                    originalError: error
                }
            });
            window.dispatchEvent(authFailureEvent);
        }
        
        function testHttp401() {
            log('🧪 开始测试 HTTP 401 错误');
            startRedirectMonitoring();
            
            const error = {
                status: 401,
                response: {
                    status: 401,
                    data: {
                        detail: 'Unauthorized access'
                    }
                }
            };
            
            const authFailureEvent = new CustomEvent('authenticationFailure', {
                detail: {
                    reason: 'test_http_401',
                    message: '认证已过期，请重新登录',
                    originalError: error
                }
            });
            window.dispatchEvent(authFailureEvent);
        }
        
        function testTokenExpired() {
            log('🧪 开始测试 "token expired" 错误');
            startRedirectMonitoring();
            
            const error = {
                message: 'token expired',
                status: 401
            };
            
            const authFailureEvent = new CustomEvent('authenticationFailure', {
                detail: {
                    reason: 'test_token_expired',
                    message: '认证已过期，请重新登录',
                    originalError: error
                }
            });
            window.dispatchEvent(authFailureEvent);
        }
        
        function testManualTrigger() {
            log('🧪 开始手动触发认证失败');
            startRedirectMonitoring();
            
            // 直接触发认证失败事件
            const authFailureEvent = new CustomEvent('authenticationFailure', {
                detail: {
                    reason: 'manual_test_trigger',
                    message: '手动测试认证失败',
                    timestamp: new Date().toISOString()
                }
            });
            window.dispatchEvent(authFailureEvent);
            
            // 同时尝试直接跳转
            setTimeout(() => {
                log('🔄 尝试直接跳转到登录页面...', 'info');
                try {
                    window.location.href = '/login';
                    log('✅ 直接跳转命令已执行', 'success');
                } catch (error) {
                    log(`❌ 直接跳转失败: ${error.message}`, 'error');
                }
            }, 500);
        }
        
        function clearLogs() {
            document.getElementById('logContainer').innerHTML = '';
            log('日志已清理', 'success');
        }
        
        // 页面加载时的初始化
        window.addEventListener('load', () => {
            log('认证错误跳转调试页面已加载', 'success');
            log(`当前URL: ${window.location.href}`, 'info');
            
            // 检查本地存储中的认证信息
            const authTokens = {
                access_token: localStorage.getItem('access_token'),
                token: localStorage.getItem('token'),
                user: localStorage.getItem('user')
            };
            
            log(`本地认证信息: ${JSON.stringify(authTokens)}`, 'info');
        });
        
        // 监听存储变化
        window.addEventListener('storage', (event) => {
            if (['access_token', 'token', 'user', 'userInfo'].includes(event.key)) {
                log(`本地存储变化: ${event.key} = ${event.newValue}`, 'info');
            }
        });
    </script>
</body>
</html> 