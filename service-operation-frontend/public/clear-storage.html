<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>清理存储数据</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .success {
            color: #28a745;
            margin: 10px 0;
        }
        .info {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>清理存储数据</h1>
        <div class="info">
            <p>如果您遇到 "Request Header Or Cookie Too Large" 错误，可能是因为浏览器存储了过多数据。</p>
            <p>点击下面的按钮清理存储数据，然后重新登录。</p>
        </div>
        
        <button class="btn" onclick="clearStorage()">清理所有存储数据</button>
        <button class="btn" onclick="showStorageInfo()">查看存储信息</button>
        
        <div id="result"></div>
        <div id="storageInfo"></div>
        
        <div class="info" style="margin-top: 20px;">
            <p><strong>清理后请：</strong></p>
            <ol>
                <li>关闭所有浏览器标签页</li>
                <li>重新打开浏览器</li>
                <li>访问登录页面重新登录</li>
            </ol>
        </div>
    </div>

    <script>
        function clearStorage() {
            try {
                // 清理 localStorage
                localStorage.clear();
                
                // 清理 sessionStorage
                sessionStorage.clear();
                
                // 清理 cookies
                document.cookie.split(";").forEach(function(c) { 
                    document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
                });
                
                document.getElementById('result').innerHTML = 
                    '<div class="success">✅ 存储数据已清理完成！<br>请关闭浏览器重新打开，然后重新登录。</div>';
            } catch (error) {
                document.getElementById('result').innerHTML = 
                    '<div style="color: red;">❌ 清理失败: ' + error.message + '</div>';
            }
        }
        
        function showStorageInfo() {
            let info = '<h3>当前存储信息：</h3>';
            
            // localStorage 信息
            info += '<h4>localStorage:</h4><ul>';
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                const value = localStorage.getItem(key);
                const size = new Blob([value]).size;
                info += `<li><strong>${key}</strong>: ${size} bytes</li>`;
            }
            info += '</ul>';
            
            // sessionStorage 信息
            info += '<h4>sessionStorage:</h4><ul>';
            for (let i = 0; i < sessionStorage.length; i++) {
                const key = sessionStorage.key(i);
                const value = sessionStorage.getItem(key);
                const size = new Blob([value]).size;
                info += `<li><strong>${key}</strong>: ${size} bytes</li>`;
            }
            info += '</ul>';
            
            // cookies 信息
            info += '<h4>Cookies:</h4>';
            info += '<p>' + (document.cookie || '无 cookies') + '</p>';
            
            document.getElementById('storageInfo').innerHTML = info;
        }
        
        // 页面加载时显示存储信息
        window.onload = function() {
            showStorageInfo();
        };
    </script>
</body>
</html> 