<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSO测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 SSO登录测试</h1>
        
        <h2>📋 当前配置</h2>
        <div id="config-info"></div>
        
        <h2>🧪 SSO测试</h2>
        <button class="test-button" onclick="testSSO()">测试SSO登录URL</button>
        <button class="test-button" onclick="testDirectSSO()">测试直接访问后端</button>
        
        <h2>📊 测试结果</h2>
        <div id="test-results"></div>
    </div>

    <script>
        // 显示当前配置
        function showConfig() {
            const config = {
                'window.location.origin': window.location.origin,
                '推断的API_BASE_URL': getAPIBaseURL(),
                'SSO登录URL': getSSO_API().getLoginUrl
            };
            
            const configDiv = document.getElementById('config-info');
            configDiv.innerHTML = Object.entries(config).map(([key, value]) => 
                `<div style="margin: 5px 0; padding: 5px; background: #f8f9fa;"><strong>${key}:</strong> ${value}</div>`
            ).join('');
        }
        
        // 获取API基础URL（模拟constants.ts的逻辑）
        function getAPIBaseURL() {
            // 在开发环境中使用代理路径
            return window.location.hostname === 'localhost' ? '/api' : 'http://localhost:8000/api';
        }
        
        // 获取SSO API配置
        function getSSO_API() {
            const apiBaseUrl = getAPIBaseURL();
            return {
                getLoginUrl: `${apiBaseUrl}/auth/sso/login-url`
            };
        }
        
        // 测试SSO（通过代理）
        async function testSSO() {
            const ssoApi = getSSO_API();
            const url = ssoApi.getLoginUrl;
            
            const resultDiv = document.getElementById('test-results');
            const testId = `test-${Date.now()}`;
            
            resultDiv.innerHTML += `<div id="${testId}" class="result">🔄 测试SSO登录URL（代理）: ${url}</div>`;
            
            try {
                const response = await fetch(url);
                const data = await response.json();
                
                if (response.ok) {
                    document.getElementById(testId).className = 'result success';
                    document.getElementById(testId).innerHTML = `✅ SSO登录URL获取成功<br>URL: ${data.data.login_url}`;
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                document.getElementById(testId).className = 'result error';
                document.getElementById(testId).innerHTML = `❌ SSO登录URL获取失败: ${error.message}`;
            }
        }
        
        // 测试直接访问后端
        async function testDirectSSO() {
            const url = 'http://localhost:8000/api/auth/sso/login-url';
            
            const resultDiv = document.getElementById('test-results');
            const testId = `test-${Date.now()}`;
            
            resultDiv.innerHTML += `<div id="${testId}" class="result">🔄 测试直接访问后端: ${url}</div>`;
            
            try {
                const response = await fetch(url);
                const data = await response.json();
                
                if (response.ok) {
                    document.getElementById(testId).className = 'result success';
                    document.getElementById(testId).innerHTML = `✅ 直接访问成功<br>URL: ${data.data.login_url}`;
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                document.getElementById(testId).className = 'result error';
                document.getElementById(testId).innerHTML = `❌ 直接访问失败（预期的CORS错误）: ${error.message}`;
            }
        }
        
        // 页面加载时显示配置
        showConfig();
    </script>
</body>
</html> 