<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>认证调试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .section {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover { background: #0056b3; }
        .token-input {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>🔧 DTTrip 认证调试工具</h1>
    
    <div class="section">
        <h2>1. 设置认证Token</h2>
        <p>请输入最新生成的JWT Token：</p>
        <input type="text" id="tokenInput" class="token-input" placeholder="粘贴JWT Token到这里...">
        <br>
        <button class="btn" onclick="setToken()">设置Token</button>
        <button class="btn" onclick="clearToken()">清除Token</button>
        <div id="tokenStatus" class="section warning">
            当前Token状态：未设置
        </div>
    </div>

    <div class="section">
        <h2>2. Token信息</h2>
        <button class="btn" onclick="checkToken()">检查Token</button>
        <div id="tokenInfo"></div>
    </div>

    <div class="section">
        <h2>3. API测试</h2>
        <button class="btn" onclick="testProjectList()">测试项目列表API</button>
        <button class="btn" onclick="testUploadAPI()">测试上传API认证</button>
        <div id="apiResults"></div>
    </div>

    <div class="section">
        <h2>4. 网络调试</h2>
        <button class="btn" onclick="debugNetwork()">检查网络配置</button>
        <div id="networkInfo"></div>
    </div>

    <script>
        // API配置 - 优先从环境变量获取，否则使用默认值
        const API_BASE_URL = window.location.hostname === 'localhost' 
          ? 'http://localhost:8000/api'
          : (window.ENV?.VITE_API_BASE_URL || 'http://localhost:8000/api');

        // 最新的Token（从生成器复制）
        const LATEST_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NDg5MjM1OTEsInN1YiI6IjEyMzQ5NyJ9.pzmF3tW3XW3I9JhXB-k1hIHfy6315T7jNek-oxXJEsY';

        function setToken() {
            const token = document.getElementById('tokenInput').value.trim() || LATEST_TOKEN;
            localStorage.setItem('access_token', token);
            updateTokenStatus();
            log('✅ Token已设置');
        }

        function clearToken() {
            localStorage.removeItem('access_token');
            updateTokenStatus();
            log('🗑️ Token已清除');
        }

        function updateTokenStatus() {
            const token = localStorage.getItem('access_token');
            const statusDiv = document.getElementById('tokenStatus');
            
            if (token) {
                statusDiv.className = 'section success';
                statusDiv.innerHTML = `当前Token状态：✅ 已设置<br><small>Token长度: ${token.length} 字符</small>`;
            } else {
                statusDiv.className = 'section warning';
                statusDiv.innerHTML = '当前Token状态：❌ 未设置';
            }
        }

        function checkToken() {
            const token = localStorage.getItem('access_token');
            const infoDiv = document.getElementById('tokenInfo');
            
            if (!token) {
                infoDiv.innerHTML = '<div class="section error">❌ 没有找到Token</div>';
                return;
            }

            try {
                // 解析JWT Token
                const parts = token.split('.');
                if (parts.length !== 3) {
                    infoDiv.innerHTML = '<div class="section error">❌ Token格式不正确</div>';
                    return;
                }

                const payload = JSON.parse(atob(parts[1]));
                const currentTime = Math.floor(Date.now() / 1000);
                const isExpired = payload.exp < currentTime;
                const remainingTime = payload.exp - currentTime;

                infoDiv.innerHTML = `
                    <div class="section ${isExpired ? 'error' : 'success'}">
                        <h3>Token信息：</h3>
                        <pre>${JSON.stringify(payload, null, 2)}</pre>
                        <p><strong>过期状态:</strong> ${isExpired ? '❌ 已过期' : '✅ 有效'}</p>
                        <p><strong>剩余时间:</strong> ${isExpired ? '0秒' : remainingTime + '秒'}</p>
                        <p><strong>用户ID:</strong> ${payload.sub}</p>
                    </div>
                `;
            } catch (error) {
                infoDiv.innerHTML = `<div class="section error">❌ Token解析失败: ${error.message}</div>`;
            }
        }

        async function testProjectList() {
            const token = localStorage.getItem('access_token');
            const resultsDiv = document.getElementById('apiResults');
            
            if (!token) {
                resultsDiv.innerHTML = '<div class="section error">❌ 请先设置Token</div>';
                return;
            }

            try {
                log('🔄 测试项目列表API...');
                const response = await fetch(`${API_BASE_URL}/project/?page=1&page_size=5`, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();
                
                if (response.ok) {
                    resultsDiv.innerHTML = `
                        <div class="section success">
                            <h3>✅ 项目列表API测试成功</h3>
                            <p><strong>状态码:</strong> ${response.status}</p>
                            <p><strong>项目总数:</strong> ${result.total}</p>
                            <p><strong>返回项目数:</strong> ${result.items.length}</p>
                            <pre>${JSON.stringify(result, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultsDiv.innerHTML = `
                        <div class="section error">
                            <h3>❌ 项目列表API测试失败</h3>
                            <p><strong>状态码:</strong> ${response.status}</p>
                            <p><strong>错误信息:</strong> ${result.detail || '未知错误'}</p>
                            <pre>${JSON.stringify(result, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="section error">❌ 网络错误: ${error.message}</div>`;
            }
        }

        async function testUploadAPI() {
            const token = localStorage.getItem('access_token');
            const resultsDiv = document.getElementById('apiResults');
            
            if (!token) {
                resultsDiv.innerHTML = '<div class="section error">❌ 请先设置Token</div>';
                return;
            }

            try {
                log('🔄 测试上传API认证...');
                
                // 创建一个空的FormData来测试认证
                const formData = new FormData();
                formData.append('project_id', '10');
                
                // 创建一个空文件进行测试
                const emptyFile = new Blob(['test'], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
                formData.append('file', emptyFile, 'test.xlsx');

                const response = await fetch(`${API_BASE_URL}/train-order/upload-excel`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: formData
                });

                const result = await response.json();
                
                if (response.status === 401) {
                    resultsDiv.innerHTML = `
                        <div class="section error">
                            <h3>❌ 上传API认证失败</h3>
                            <p><strong>状态码:</strong> ${response.status}</p>
                            <p><strong>错误信息:</strong> ${result.detail}</p>
                            <p>这表明Token无效或过期</p>
                        </div>
                    `;
                } else if (response.status === 400) {
                    resultsDiv.innerHTML = `
                        <div class="section success">
                            <h3>✅ 上传API认证成功</h3>
                            <p><strong>状态码:</strong> ${response.status}</p>
                            <p><strong>说明:</strong> 认证通过，但文件格式错误（这是预期的）</p>
                            <p><strong>错误信息:</strong> ${result.detail}</p>
                        </div>
                    `;
                } else {
                    resultsDiv.innerHTML = `
                        <div class="section warning">
                            <h3>🤔 上传API测试结果</h3>
                            <p><strong>状态码:</strong> ${response.status}</p>
                            <pre>${JSON.stringify(result, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="section error">❌ 网络错误: ${error.message}</div>`;
            }
        }

        function debugNetwork() {
            const infoDiv = document.getElementById('networkInfo');
            
            infoDiv.innerHTML = `
                <div class="section">
                    <h3>网络配置信息：</h3>
                    <p><strong>当前页面URL:</strong> ${window.location.href}</p>
                    <p><strong>API基础URL:</strong> ${API_BASE_URL}</p>
                    <p><strong>用户代理:</strong> ${navigator.userAgent}</p>
                    <p><strong>localStorage支持:</strong> ${typeof Storage !== "undefined" ? '✅ 支持' : '❌ 不支持'}</p>
                    <p><strong>当前时间:</strong> ${new Date().toLocaleString()}</p>
                </div>
            `;
            
            // 测试CORS
            fetch(`${API_BASE_URL.replace('/api', '')}/docs`)
                .then(response => {
                    infoDiv.innerHTML += `<div class="section success">✅ CORS测试通过：可以访问后端服务</div>`;
                })
                .catch(error => {
                    infoDiv.innerHTML += `<div class="section error">❌ CORS测试失败：${error.message}</div>`;
                });
        }

        function log(message) {
            console.log(`[认证调试] ${message}`);
        }

        // 页面加载时初始化
        window.onload = function() {
            document.getElementById('tokenInput').value = LATEST_TOKEN;
            updateTokenStatus();
            log('🚀 认证调试工具已加载');
        };
    </script>
</body>
</html> 