# Nginx 反向代理问题修复 - 部署说明

## 问题确认

✅ **问题已确认**：
- 直接访问后端：`https://soa-api.qa.dttrip.cn/api/auth/sso/login-url` ✅ 正常工作
- 通过反向代理：`https://soa.qa.dttrip.cn/api/auth/sso/login-url` ❌ 返回 400 Bad Request
- 错误信息：`Request Header Or Cookie Too Large`

✅ **配置已修复**：
- 已更新 `nginx.conf` 文件，增加请求头大小限制配置
- 已优化代理缓冲设置

## 修复内容

### 1. nginx.conf 配置更新

```nginx
server {
    listen 80;
    server_name soa.qa.dttrip.cn;
    root /usr/share/nginx/html;
    index index.html index.htm;

    # 增加请求头和Cookie大小限制 - 修复SSO登录问题
    large_client_header_buffers 8 64k;
    client_header_buffer_size 64k;
    client_max_body_size 100m;
    client_body_buffer_size 128k;

    # API代理到后端服务
    location /api/ {
        proxy_pass https://soa-api.qa.dttrip.cn;
        
        # 代理缓冲设置 - 增大缓冲区处理大请求
        proxy_buffering on;
        proxy_buffer_size 16k;
        proxy_buffers 16 16k;
        proxy_busy_buffers_size 32k;
        proxy_temp_file_write_size 32k;
        proxy_max_temp_file_size 1024m;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # 其他代理设置...
    }
}
```

### 2. 关键修复点

1. **请求头大小限制**：
   - `large_client_header_buffers 8 64k` - 增加请求头缓冲区
   - `client_header_buffer_size 64k` - 设置单个请求头缓冲区大小

2. **代理缓冲优化**：
   - `proxy_buffer_size 16k` - 代理缓冲区大小
   - `proxy_buffers 16 16k` - 代理缓冲区数量和大小
   - `proxy_max_temp_file_size 1024m` - 临时文件最大大小

3. **超时设置**：
   - 从30秒增加到60秒，给SSO认证更多时间

## 部署步骤

### 方法1：Docker 重新构建（推荐）

```bash
# 1. 进入前端目录
cd service-operation-frontend

# 2. 重新构建镜像（当网络问题解决后）
docker build -t dttrip-frontend:latest . --no-cache

# 3. 停止现有容器
docker stop dttrip-frontend 2>/dev/null || true
docker rm dttrip-frontend 2>/dev/null || true

# 4. 启动新容器
docker run -d \
    --name dttrip-frontend \
    -p 80:80 \
    --restart unless-stopped \
    dttrip-frontend:latest

# 5. 验证部署
docker logs dttrip-frontend
curl -I https://soa.qa.dttrip.cn/health
```

### 方法2：使用部署脚本

```bash
cd service-operation-frontend
./deploy-fix.sh
```

### 方法3：生产环境 Docker Compose

如果使用 Docker Compose 部署：

```bash
# 1. 修复 docker-compose.prod.yml 中的配置问题
# 2. 设置环境变量
export MYSQL_ROOT_PASSWORD="your_root_password"
export DB_PASSWORD="your_db_password"

# 3. 重新构建和启动
docker-compose -f docker-compose.prod.yml down
docker-compose -f docker-compose.prod.yml build --no-cache
docker-compose -f docker-compose.prod.yml up -d
```

## 验证步骤

### 1. 基础验证

```bash
# 检查容器状态
docker ps | grep dttrip

# 检查nginx配置
docker exec dttrip-frontend nginx -t

# 检查健康状态
curl -I https://soa.qa.dttrip.cn/health
```

### 2. SSO功能验证

```bash
# 测试SSO登录URL接口
curl -X GET "https://soa.qa.dttrip.cn/api/auth/sso/login-url" \
     -H "Content-Type: application/json" \
     -v

# 预期结果：200 OK，返回登录URL
```

### 3. 前端功能验证

1. 访问 https://soa.qa.dttrip.cn
2. 点击"SSO登录"按钮
3. 检查是否能正常跳转到同程统一授权登录中心
4. 完成登录流程测试

## 故障排除

### 如果仍然出现400错误

1. **检查nginx配置是否生效**：
   ```bash
   docker exec dttrip-frontend cat /etc/nginx/conf.d/default.conf
   ```

2. **检查nginx错误日志**：
   ```bash
   docker logs dttrip-frontend
   ```

3. **重启nginx服务**：
   ```bash
   docker exec dttrip-frontend nginx -s reload
   ```

### 如果Docker构建失败

1. **网络问题**：
   - 检查Docker Hub连接
   - 尝试使用国内镜像源
   - 稍后重试

2. **缓存问题**：
   ```bash
   docker system prune -f
   docker build --no-cache
   ```

### 如果代理仍然不工作

1. **检查后端服务状态**：
   ```bash
   curl -v https://soa-api.qa.dttrip.cn/health
   ```

2. **检查DNS解析**：
   ```bash
   nslookup soa.qa.dttrip.cn
   nslookup soa-api.qa.dttrip.cn
   ```

3. **检查SSL证书**：
   ```bash
   openssl s_client -connect soa.qa.dttrip.cn:443 -servername soa.qa.dttrip.cn
   ```

## 监控和维护

### 1. 日志监控

```bash
# 实时查看nginx日志
docker logs -f dttrip-frontend

# 查看访问日志
docker exec dttrip-frontend tail -f /var/log/nginx/access.log

# 查看错误日志
docker exec dttrip-frontend tail -f /var/log/nginx/error.log
```

### 2. 性能监控

```bash
# 检查请求响应时间
curl -w "@curl-format.txt" -o /dev/null -s "https://soa.qa.dttrip.cn/api/auth/sso/login-url"

# 检查连接状态
netstat -an | grep :80
```

### 3. 定期检查

- 每周检查SSL证书有效期
- 每月检查nginx配置是否需要优化
- 监控SSO登录成功率

## 回滚方案

如果新配置出现问题，可以快速回滚：

```bash
# 1. 停止新容器
docker stop dttrip-frontend

# 2. 恢复旧配置（如果有备份）
git checkout HEAD~1 -- nginx.conf

# 3. 重新构建
docker build -t dttrip-frontend:rollback .

# 4. 启动回滚版本
docker run -d --name dttrip-frontend-rollback -p 80:80 dttrip-frontend:rollback
```

---

**修复状态**: ✅ 配置已修复，等待部署  
**部署时间**: 待网络问题解决后执行  
**验证负责人**: 开发团队  
**紧急联系**: 如有问题请及时反馈 