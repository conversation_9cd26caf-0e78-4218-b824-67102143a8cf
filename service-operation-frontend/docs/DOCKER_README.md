# DTTrip 前端 Docker 部署指南

## 📋 概述

本项目已完成 Docker 化，提供了完整的容器化部署方案。支持生产环境和开发环境的不同需求。

## 🏗️ 构建镜像

### 快速构建
```bash
# 使用构建脚本（推荐）
./build-docker.sh

# 或手动构建
docker build -t dttrip-frontend:latest .
```

### 构建选项
```bash
# 指定版本标签
./build-docker.sh v1.0.0

# 构建并推送到仓库
./build-docker.sh latest your-registry.com
```

## 🚀 运行容器

### 使用管理脚本（推荐）
```bash
# 启动容器（默认80端口）
./run-docker.sh start

# 在自定义端口启动
./run-docker.sh start 8080

# 查看状态
./run-docker.sh status

# 查看日志
./run-docker.sh logs

# 重启容器
./run-docker.sh restart

# 停止容器
./run-docker.sh stop

# 清理容器
./run-docker.sh clean
```

### 手动运行
```bash
# 启动容器
docker run -d \
  --name dttrip-frontend \
  -p 80:80 \
  --restart unless-stopped \
  dttrip-frontend:latest

# 查看容器状态
docker ps | grep dttrip-frontend

# 查看日志
docker logs dttrip-frontend -f
```

## 🐳 Docker Compose 部署

```bash
# 启动服务
docker-compose up -d

# 停止服务
docker-compose down

# 查看状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

## 📁 文件结构

```
service-operation-frontend/
├── Dockerfile                  # 主要的生产环境 Dockerfile
├── Dockerfile.standalone       # 独立部署版本（包含环境变量支持）
├── nginx.simple.conf           # 简化的 nginx 配置
├── nginx.standalone.conf       # 支持后端代理的 nginx 配置
├── docker-compose.yml          # Docker Compose 配置
├── build-docker.sh            # 构建脚本
├── run-docker.sh              # 运行管理脚本
├── .dockerignore              # Docker 忽略文件
└── DOCKER_README.md           # 本文档
```

## ⚙️ 配置说明

### 环境变量
- `BACKEND_URL`: 后端服务地址（仅 standalone 版本支持）

### 端口映射
- 容器内端口: `80`
- 默认外部端口: `80`
- 可自定义外部端口

### 健康检查
- 端点: `/health`
- 间隔: 30秒
- 超时: 3秒
- 重试: 3次

## 🔧 镜像特性

### 多阶段构建
1. **构建阶段**: 使用 Node.js 18 Alpine 进行项目构建
2. **运行阶段**: 使用 Nginx Alpine 作为 Web 服务器

### 优化特性
- **体积优化**: 生产镜像仅约 50MB
- **Gzip 压缩**: 自动压缩静态资源
- **缓存策略**: 静态资源一年缓存
- **SPA 支持**: 自动处理前端路由
- **安全头部**: 内置安全响应头

## 📊 镜像信息

```bash
# 查看镜像大小
docker images dttrip-frontend

# 查看镜像详情
docker inspect dttrip-frontend:latest
```

## 🛠️ 故障排除

### 常见问题

#### 1. 容器启动失败
```bash
# 查看容器日志
./run-docker.sh logs

# 或直接查看
docker logs dttrip-frontend --tail 50
```

#### 2. 端口被占用
```bash
# 检查端口使用情况
lsof -i :80

# 使用其他端口启动
./run-docker.sh start 8080
```

#### 3. 构建失败
```bash
# 清理 Docker 缓存
docker system prune -f

# 重新构建
./build-docker.sh
```

#### 4. 健康检查失败
```bash
# 手动测试健康检查
curl http://localhost:80/health

# 检查 nginx 配置
docker exec dttrip-frontend nginx -t
```

### 调试命令

```bash
# 进入容器 shell
./run-docker.sh shell

# 查看 nginx 配置
docker exec dttrip-frontend cat /etc/nginx/conf.d/default.conf

# 查看容器内的文件
docker exec dttrip-frontend ls -la /usr/share/nginx/html
```

## 🔄 更新部署

### 快速更新
```bash
# 重新构建并部署
./run-docker.sh rebuild
```

### 手动更新
```bash
# 1. 停止容器
./run-docker.sh stop

# 2. 重新构建镜像
./build-docker.sh

# 3. 启动新容器
./run-docker.sh start
```

## 📈 性能监控

### 资源使用
```bash
# 查看容器资源使用
docker stats dttrip-frontend

# 详细状态信息
./run-docker.sh status
```

### 日志分析
```bash
# 实时日志
./run-docker.sh logs

# 最近日志
docker logs dttrip-frontend --tail 100
```

## 🔒 安全考虑

### 镜像安全
- 使用官方基础镜像
- 定期更新依赖
- 最小权限运行

### 网络安全
- 内置安全响应头
- 仅暴露必要端口
- 支持 HTTPS（需配置反向代理）

## 📦 生产部署建议

### 1. 使用反向代理
```nginx
# 示例 nginx 反向代理配置
upstream dttrip_frontend {
    server 127.0.0.1:8080;
}

server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://dttrip_frontend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 2. 容器编排
- 使用 Docker Compose 进行多服务管理
- 考虑使用 Kubernetes 进行大规模部署
- 设置自动重启策略

### 3. 监控和日志
- 集成日志收集系统
- 设置性能监控
- 配置告警机制

## 📞 支持

如果遇到问题，请：
1. 查看容器日志: `./run-docker.sh logs`
2. 检查容器状态: `./run-docker.sh status`
3. 查看本文档的故障排除部分
4. 联系开发团队

---

## 快速开始

```bash
# 1. 构建镜像
./build-docker.sh

# 2. 启动容器
./run-docker.sh start

# 3. 访问应用
open http://localhost

# 4. 查看状态
./run-docker.sh status
```

🎉 **部署完成！** 您的 DTTrip 前端应用现在已在 Docker 容器中运行。 