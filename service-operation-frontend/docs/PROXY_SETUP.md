# 前端反向代理配置说明

## 概述

由于后端已移除CORS跨域支持，前端需要通过反向代理方式访问后端API。本项目提供了两种代理方案：

1. **开发环境**: Vite开发服务器代理
2. **生产环境**: Nginx反向代理

## 开发环境配置

### 1. Vite代理配置

已在 `vite.config.ts` 中配置了开发环境代理：

```typescript
server: {
  proxy: {
    '/api': {
      target: 'http://localhost:8000',
      changeOrigin: true,
      secure: false,
    },
  },
}
```

### 2. API基础URL配置

在 `src/utils/constants.ts` 中，API基础URL会根据环境自动选择：

```typescript
export const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 
  (import.meta.env.DEV ? '/api' : 'http://localhost:8000/api');
```

- **开发环境**: 使用相对路径 `/api`，由Vite代理转发
- **生产环境**: 使用完整URL或环境变量配置

### 3. 启动开发环境

使用提供的启动脚本：

```bash
# 方式1: 使用启动脚本
./start-dev.sh

# 方式2: 直接启动
npm run dev
```

启动后：
- 前端地址: `http://localhost:5173`
- API代理: `http://localhost:5173/api` → `http://localhost:8000/api`

## 生产环境配置

### 1. Nginx配置

使用 `nginx.simple.conf` 配置文件，已包含API代理配置：

```nginx
# API代理到后端服务（解决跨域）
location /api/ {
    proxy_pass https://soa-api.qa.dttrip.cn;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    
    # 其他代理配置...
}
```

### 2. 环境变量配置

在 `.qa.env` 中配置生产环境API地址：

```bash
# API基础URL - 生产环境使用代理路径
VITE_API_BASE_URL=https://soa.qa.dttrip.cn/api
```

## 代理工作原理

### 开发环境流程

1. 前端发起API请求: `fetch('/api/project')`
2. Vite代理拦截请求: `/api/project`
3. 转发到后端: `http://localhost:8000/api/project`
4. 后端处理请求并返回响应
5. Vite代理将响应返回给前端

### 生产环境流程

1. 前端发起API请求: `fetch('https://soa.qa.dttrip.cn/api/project')`
2. Nginx拦截 `/api/` 路径的请求
3. 转发到后端: `https://soa-api.qa.dttrip.cn/api/project`
4. 后端处理请求并返回响应
5. Nginx将响应返回给前端

## 优势

1. **无需CORS配置**: 通过代理实现同域访问
2. **开发体验一致**: 开发和生产环境使用相同的API调用方式
3. **安全性更高**: 生产环境无需开放跨域访问
4. **配置灵活**: 可以轻松切换不同的后端服务

## 故障排除

### 1. 代理不工作

检查后端服务是否启动：
```bash
curl http://localhost:8000/health
```

### 2. API请求失败

检查Vite代理日志，确认请求是否正确转发。

### 3. 生产环境问题

检查Nginx配置和后端服务地址是否正确。

## 注意事项

1. **开发环境**: 确保后端服务在 `http://localhost:8000` 启动
2. **生产环境**: 确保Nginx配置正确，后端服务可访问
3. **环境变量**: 不同环境使用对应的 `.env` 文件
4. **HTTPS**: 生产环境注意HTTP/HTTPS协议匹配 