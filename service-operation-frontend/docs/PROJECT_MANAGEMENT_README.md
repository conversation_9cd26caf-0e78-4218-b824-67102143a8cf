# 项目管理功能说明

## 🎯 功能概述

项目管理模块允许用户高效管理团队的住宿和交通预订项目，包含完整的CRUD操作、搜索筛选、分页功能，以及列表和卡片两种视图模式。

## 🌟 主要特性

### 1. 双视图模式
- **列表视图**: 表格形式展示所有项目信息，便于批量查看和比较
- **卡片视图**: 卡片式布局，信息展示更直观，包含创建新项目卡片

### 2. 搜索和筛选
- 支持按项目名称模糊搜索
- 支持按客户名称筛选
- 实时搜索，按Enter键或点击搜索按钮即可

### 3. 分页功能
- 支持自定义每页显示数量（10/20/50/100）
- 智能分页导航，大页数时显示省略号
- 显示当前页信息和总记录数

### 4. 项目操作
- **创建项目**: 填写项目基本信息，系统自动分配项目编号（从25001开始）
- **查看详情**: 查看项目完整信息
- **编辑项目**: 修改项目信息（待实现）
- **删除项目**: 删除不需要的项目

## 🚀 如何访问

### 1. 启动服务
```bash
# 后端服务
cd service-operation-server
source venv/bin/activate
python -m uvicorn src.main:app --reload --host 0.0.0.0 --port 8000

# 前端服务
cd service-operation-frontend
npm run dev
```

### 2. 访问页面
- 前端地址: `http://localhost:5173`
- 项目管理页面: `http://localhost:5173/projects`
- 需要先登录系统

### 3. API端点
- 后端API: `http://localhost:8000/api/project/`
- API文档: `http://localhost:8000/docs`

## 🎨 界面功能

### 页面头部
- 项目管理标题和描述
- "新建项目"按钮，点击打开创建模态框

### 搜索筛选栏
- 项目名称搜索框
- 客户名称筛选框
- 搜索按钮
- 视图模式切换（列表/卡片）

### 内容展示区
**列表视图** - 表格显示：
- 项目名称（包含描述）
- 状态标签
- 公司名称
- 成本中心
- 代订人
- 创建日期
- 预订类型标签
- 预订金额
- 操作按钮（查看/编辑/删除）

**卡片视图** - 卡片布局：
- 创建新项目卡片
- 项目信息卡片，包含：
  - 项目名称和状态
  - 创建人、客户、日期、成本中心
  - 预订类型标签
  - 预订金额
  - 项目详情按钮

### 分页控件
- 显示当前页范围和总数
- 每页条数选择器
- 页码导航按钮

### 项目说明区
- 功能介绍和使用说明
- 三个功能特性卡片：创建项目、预订服务、管理项目

## 💾 数据模型

### 项目字段
- `project_number`: 项目编号（自增，从25001开始）
- `project_name`: 项目名称
- `creator_user_id`: 创建用户ID
- `creator_name`: 创建人姓名
- `project_description`: 项目描述（可选）
- `client_name`: 客户名称
- `project_date`: 项目日期
- `cost_center`: 成本中心
- `created_at`: 创建时间
- `updated_at`: 更新时间

### 状态和标签
- **项目状态**: 进行中、已完成、计划中（基于项目ID生成）
- **预订类型**: 团队、机票、火车票、住宿（基于项目特征生成）
- **预订金额**: 模拟金额显示（28000、87500、24600、80000）

## 🔧 技术栈

### 前端
- **React 18** + **TypeScript**
- **Tailwind CSS** - 样式框架
- **Lucide React** - 图标库
- **React Router** - 路由管理

### 后端
- **FastAPI** - Web框架
- **Tortoise ORM** - 数据库ORM
- **MySQL** - 数据库
- **Pydantic** - 数据验证

### 组件结构
```
src/
├── types/project.ts              # 项目类型定义
├── services/project-service.ts   # 项目API服务
├── components/
│   ├── project/
│   │   ├── ProjectCard.tsx       # 项目卡片组件
│   │   ├── ProjectTable.tsx      # 项目表格组件
│   │   ├── CreateProjectCard.tsx # 创建项目卡片
│   │   └── CreateProjectModal.tsx# 创建项目模态框
│   └── common/
│       └── Pagination.tsx        # 分页组件
└── pages/
    └── ProjectManagementPage.tsx # 项目管理主页面
```

## 🎉 完成状态

✅ **已完成功能**:
- 项目列表展示（列表和卡片视图）
- 搜索和筛选
- 分页功能
- 创建新项目
- 查看项目详情
- 删除项目
- 响应式设计
- API集成

🚧 **待实现功能**:
- 编辑项目功能
- 项目详情页面
- 权限控制
- 数据导出
- 高级筛选器

## 📱 响应式设计

界面适配不同屏幕尺寸：
- **手机端**: 单列布局，搜索栏垂直排列
- **平板端**: 双列卡片布局
- **桌面端**: 三列卡片布局，水平搜索栏

## 🎨 视觉设计

- 现代化的卡片设计
- 清晰的视觉层次
- 一致的颜色系统
- 流畅的交互动画
- 直观的操作图标

项目管理功能已完整实现，可以立即投入使用！ 