# DTTrip 前端 Docker 快速开始指南

## 🚀 快速部署

### 1. 构建镜像
```bash
# 基础构建（仅本地）
./build-docker.sh

# 构建指定版本
./build-docker.sh v1.0.0

# 构建并推送到公司仓库
./build-docker.sh push
./build-docker.sh v1.0.0 push

# 推送到自定义仓库
./build-docker.sh latest hub.17usoft.com/custom
```

### 2. 运行容器
```bash
# 启动容器（80端口）
./run-docker.sh start

# 自定义端口启动
./run-docker.sh start 8080

# 查看状态
./run-docker.sh status

# 查看日志
./run-docker.sh logs
```

### 3. 容器管理
```bash
./run-docker.sh stop      # 停止容器
./run-docker.sh restart   # 重启容器
./run-docker.sh shell     # 进入容器
./run-docker.sh clean     # 清理容器
./run-docker.sh rebuild   # 重建并启动
```

## 📦 部署选项

### 本地部署
```bash
# 使用管理脚本
./run-docker.sh start

# 或手动运行
docker run -d -p 80:80 --name dttrip-frontend dttrip-frontend:latest
```

### Docker Compose部署
```bash
# 启动服务
docker-compose up -d

# 停止服务
docker-compose down

# 查看日志
docker-compose logs -f
```

### 从仓库部署
```bash
# 拉取镜像
docker pull hub.17usoft.com/dttrip_service_operation/dttrip-frontend:latest

# 运行
docker run -d -p 80:80 --name dttrip-frontend \
  hub.17usoft.com/dttrip_service_operation/dttrip-frontend:latest
```

## 🔧 Git 管理

### 推送到远程仓库
```bash
# 当前分支配置：本地main -> 远程master
git add .
git commit -m "your message"
git push origin HEAD:master  # 推送到master分支
```

### 日常使用
```bash
git status                   # 查看状态
git pull                     # 拉取更新
git push origin HEAD:master  # 推送更改
```

## 🌐 访问应用

部署成功后，访问：
- **本地**: http://localhost
- **自定义端口**: http://localhost:8080
- **健康检查**: http://localhost/health

## 📊 监控和调试

### 容器状态
```bash
./run-docker.sh status  # 详细状态信息
docker ps              # 简单状态
docker stats dttrip-frontend  # 资源使用
```

### 日志查看
```bash
./run-docker.sh logs    # 实时日志
docker logs dttrip-frontend --tail 50  # 最近日志
```

### 进入容器
```bash
./run-docker.sh shell   # 进入容器调试
docker exec -it dttrip-frontend /bin/sh
```

## 🆘 常见问题

### 网络连接问题
如果Docker Hub连接超时：
```bash
# 重试构建
./build-docker.sh

# 或使用已有镜像
docker images dttrip-frontend
```

### 端口占用
```bash
# 检查端口使用
lsof -i :80

# 使用其他端口
./run-docker.sh start 8080
```

### 容器启动失败
```bash
# 查看详细日志
./run-docker.sh logs

# 重建容器
./run-docker.sh rebuild
```

---

## 🎯 一键部署命令

```bash
# 完整部署流程
./build-docker.sh && ./run-docker.sh start
```

🎉 **部署完成！** 访问 http://localhost 查看应用 