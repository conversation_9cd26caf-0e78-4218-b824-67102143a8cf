// 清除浏览器存储脚本
// 在浏览器控制台中运行此脚本来清除所有认证相关的存储

console.log('🧹 开始清除浏览器存储...');

// 要清除的存储键
const keysToRemove = [
  'userInfo',
  'access_token', 
  'app_token',
  'sso_access_token',
  'sso_state',
  'user',
  'token',
  'train_booking_settings'
];

// 清除localStorage
keysToRemove.forEach(key => {
  const oldValue = localStorage.getItem(key);
  if (oldValue) {
    localStorage.removeItem(key);
    console.log(`✅ 已清除 localStorage.${key}`);
  }
});

// 清除sessionStorage
keysToRemove.forEach(key => {
  const oldValue = sessionStorage.getItem(key);
  if (oldValue) {
    sessionStorage.removeItem(key);
    console.log(`✅ 已清除 sessionStorage.${key}`);
  }
});

console.log('🎉 存储清除完成！请刷新页面重新登录。');
console.log('📝 现在用户ID将使用数据库ID而不是tc_user_id');

// 自动刷新页面
setTimeout(() => {
  console.log('🔄 正在刷新页面...');
  window.location.reload();
}, 2000);
