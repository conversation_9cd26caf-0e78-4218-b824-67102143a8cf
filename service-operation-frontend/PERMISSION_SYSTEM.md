# 权限系统实现文档

## 概述

本系统实现了基于权限表约束的权限控制：
- 只对在 permissions 表中定义的权限进行控制
- 不在 permissions 表中约束的页面只需要用户认证即可访问
- 权限管理页面内部根据用户权限进行精细化控制

## 核心组件

### 1. 权限管理Hook (`usePermissions.ts`)

```typescript
// 获取用户权限
const { permissions, loading, error } = usePermissions();

// 检查权限
const hasAccess = hasPermission('dttrip:dashboard', permissions);
const hasPathAccess = hasPathPermission('/dashboard', permissions);
```

**功能：**
- 获取当前用户的有效权限列表
- 提供权限检查工具函数
- 自动处理加载状态和错误

### 2. 侧边栏导航 (`ProtectedSideNav.tsx`)

**功能：**
- 对所有已认证用户显示所有菜单项
- 不再进行权限过滤
- 支持加载状态显示

**菜单配置：**
```typescript
const menuItems = [
  { path: '/dashboard', label: '应用中心' },
  { path: '/projects', label: '团房团票' },
  { path: '/passport-recognition', label: '护照识别' },
  { path: '/user-management', label: '用户管理' },
  { path: '/settings', label: '系统设置' },
];
```

### 3. 认证保护路由 (`AuthProtectedRoute`)

**功能：**
- 只检查用户认证状态
- 不再验证特定权限
- 未认证用户重定向到登录页

**使用方式：**
```typescript
<AuthProtectedRoute>
  <DashboardPage />
</AuthProtectedRoute>
```

### 4. 权限调试器 (`PermissionDebugger.tsx`)

**功能：**
- 仅在开发环境显示
- 实时显示用户权限状态
- 显示菜单权限映射关系
- 提供权限调试信息

## 权限数据结构

### 权限表 (permissions)
```sql
- id: 权限ID
- permission_name: 权限名称
- permission_code: 权限代码 (如: dttrip:dashboard)
- permission_type: 权限类型 (menu/button/api)
- resource_path: 资源路径 (如: /dashboard)
- sort_order: 排序
```

### 角色权限关联 (role_permissions)
```sql
- role_id: 角色ID
- permission_id: 权限ID
```

### 用户角色关联 (user_roles)
```sql
- user_id: 用户ID
- role_id: 角色ID
```

## 权限检查流程

1. **用户登录** → 获取用户信息
2. **菜单渲染** → 显示所有菜单项（已认证用户）
3. **路由访问** → 只检查认证状态，允许访问所有页面
4. **权限管理标签页** → 单独检查 `user_mgmt:access` 权限
5. **实时更新** → 权限变更时自动更新权限管理标签页

## 权限级别

### 系统角色
1. **超级管理员** (super_admin) - 所有权限
2. **管理员** (admin) - 管理权限
3. **普通用户** (user) - 基础权限

### 权限控制范围

#### 需要权限控制的页面（在 permissions 表中定义）
1. **应用中心** (`dttrip:dashboard`) - `/dashboard`
2. **团房团票** (`dttrip:projects`) - `/projects`
3. **护照识别** (`dttrip:passport`) - `/passport-recognition`
4. **用户管理** (`user_mgmt:access`) - `/user-management`
5. **系统设置** (`dttrip:settings`) - `/settings`

#### 只需要认证的页面（不在 permissions 表中约束）
- 项目详情页面 (`/project-detail/:projectId`)
- 项目任务详情页面 (`/project-task-detail/:projectId`)
- 任务详情页面 (`/task-detail/:taskId`)
- 任务订单页面 (`/task-orders/:taskId`)
- 火车票预订页面 (`/train-booking/:projectId`)
- 酒店预订页面 (`/hotel-booking/:projectId`)
- 酒店任务详情页面 (`/hotel-task-detail/:taskId`)

#### 权限管理标签页内部控制
- 根据用户拥有的权限过滤显示的权限列表
- 用户只能管理他们有权限的权限项

## 使用示例

### 1. 检查用户是否有特定权限
```typescript
import { usePermissions, hasPermission } from '@/hooks/usePermissions';

const { permissions } = usePermissions();
const canAccessDashboard = hasPermission('dttrip:dashboard', permissions);
```

### 2. 条件渲染组件
```typescript
{hasPermission('user_mgmt:access', permissions) && (
  <UserManagementButton />
)}
```

### 3. 保护路由
```typescript
<ProtectedRoute requiredPermission="dttrip:settings">
  <SystemSettingsPage />
</ProtectedRoute>
```

## 测试用户

### 1. 超级管理员用户
- **用户ID**: 121577
- **用户名**: 袁栩栩
- **权限**: 所有菜单权限

### 2. 普通用户
- **用户ID**: U1751164870测试权
- **用户名**: 测试权限用户
- **权限**: 应用中心、护照识别、系统设置

### 3. 仅应用中心用户
- **用户ID**: U1751165000仅应用
- **用户名**: 仅应用中心用户
- **权限**: 仅应用中心

## 调试工具

### 1. 权限测试页面
- **路径**: `/permission-test`
- **功能**: 显示用户权限详情和菜单权限测试

### 2. 权限调试器
- **位置**: 右下角浮动按钮（仅开发环境）
- **功能**: 实时显示权限状态

## 安全特性

1. **前后端双重验证** - 前端控制显示，后端控制访问
2. **权限实时检查** - 路由访问时验证权限
3. **优雅降级** - 无权限时显示友好提示
4. **错误处理** - 权限加载失败时的处理机制

## 扩展性

1. **新增菜单** - 在数据库添加权限记录，更新前端菜单配置
2. **新增角色** - 在角色表添加记录，配置角色权限
3. **细粒度权限** - 支持按钮级、API级权限控制
4. **动态权限** - 支持运行时权限变更

## 注意事项

1. 权限代码必须与数据库中的 `permission_code` 一致
2. 菜单路径必须与 `resource_path` 一致
3. 开发环境下可使用调试工具查看权限状态
4. 权限变更后需要重新登录或刷新页面
