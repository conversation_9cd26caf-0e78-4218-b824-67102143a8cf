#!/usr/bin/env python3
"""测试项目创建功能"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.db.models.user import User
from src.db.models.project import Project
from tortoise import Tortoise
from src.core.config import settings

async def test_project_creation():
    """测试项目创建功能"""
    await Tortoise.init(
        db_url=settings.db_url,
        modules={'models': ['src.db.models']}
    )
    
    try:
        # 获取一个测试用户
        user = await User.first()
        if not user:
            print("❌ 没有找到用户")
            return
            
        print(f"✅ 找到用户: ID={user.id}, 用户名={user.username}")
        
        # 测试项目创建
        project_data = {
            "project_name": "测试项目",
            "creator_user_id": user.id,  # 使用数据库ID
            "creator_name": user.username,
            "project_description": "这是一个测试项目",
            "client_name": "测试客户",
            "cost_center": "测试成本中心",
            "booking_agent_phone": "13800138000",
            "project_date": "2025-07-03 00:00:00"
        }
        
        project = await Project.create(**project_data)
        print(f"✅ 项目创建成功: ID={project.id}, 项目编号={project.project_number}")
        
        # 清理测试数据
        await project.delete()
        print("✅ 测试数据已清理")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(test_project_creation())
