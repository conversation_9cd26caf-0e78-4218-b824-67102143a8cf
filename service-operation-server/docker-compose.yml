services:
  api: &main_app
    build:
      context: .
      dockerfile: ./Dockerfile
    image: fastapi-base-framework:${APP_VERSION:-latest}
    restart: always
    env_file:
      - .env
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      HOST: 0.0.0.0
      DB_HOST: fastapi-base-db
      DB_PORT: 3306
      DB_USER: fastapi_user
      DB_PASSWORD: fastapi_password
      DB_BASE: fastapi_base
      REDIS_HOST: fastapi-base-redis

  db:
    image: mysql:8.4
    hostname: fastapi-base-db
    restart: always
    environment:
      MYSQL_ROOT_USER: "root"
      MYSQL_ROOT_PASSWORD: "fastapi_password"
      MYSQL_USER: "fastapi_user"
      MYSQL_PASSWORD: "fastapi_password"
      MYSQL_DATABASE: "fastapi_base"
      MYSQL_HOST: "0.0.0.0"
    healthcheck:
      test:
        - CMD
        - mysqladmin
        - ping
        - --user=fastapi_user
        - --password=fastapi_password
      interval: 10s
      timeout: 5s
      retries: 40
    volumes:
      - "fastapi-base-db-data:/var/lib/mysql"

  migrator:
    image: fastapi-base-framework:${APP_VERSION:-latest}
    restart: "no"
    command: aerich upgrade
    environment:
      DB_HOST: fastapi-base-db
      DB_PORT: 3306
      DB_USER: fastapi_user
      DB_PASSWORD: fastapi_password
      DB_BASE: fastapi_base
    depends_on:
      db:
        condition: service_healthy

  redis:
    image: bitnami/redis:6.2.5
    hostname: "fastapi-base-redis"
    restart: always
    environment:
      ALLOW_EMPTY_PASSWORD: "yes"
    healthcheck:
      test: redis-cli ping
      interval: 1s
      timeout: 3s
      retries: 50



volumes:
  fastapi-base-db-data:
    name: fastapi-base-db-data
