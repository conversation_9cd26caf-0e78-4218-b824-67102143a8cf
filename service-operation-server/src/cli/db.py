"""数据库管理CLI工具。

此模块提供了用于管理数据库迁移的命令行工具，基于Aerich。
它可以帮助开发人员创建、应用和回滚数据库迁移，以及查看迁移历史。
"""

import typer
import asyncio
from aerich import Command
from rich.console import Console
from rich.table import Table
from rich.style import Style

from src.db.config import get_tortoise_config

app = typer.Typer(name="数据库工具", help="用于管理数据库迁移的命令行工具")
console = Console()

async def get_command():
    """获取 Aerich 命令实例"""
    return Command(
        tortoise_config=get_tortoise_config(),
        app="models",
        location="./src/db/migrations"
    )

@app.command()
def init():
    """初始化数据库迁移环境"""
    async def run():
        command = await get_command()
        await command.init()
        console.print("[bold green]✓[/] 数据库迁移环境初始化完成")
    
    console.print("[bold]正在初始化数据库迁移环境...[/]")
    asyncio.run(run())

@app.command()
def migrate(name: str = "更新"):
    """创建新的迁移文件

    Args:
        name: 迁移的描述名称
    """
    async def run():
        command = await get_command()
        migrations = await command.migrate(name)
        if not migrations:
            console.print("[yellow]没有检测到模型更改，未创建新的迁移[/]")
            return
        
        console.print(f"[bold green]✓[/] 已创建迁移 '{name}'")
        for migration in migrations:
            console.print(f"  • {migration}")
    
    console.print(f"[bold]正在创建名为 '{name}' 的迁移...[/]")
    asyncio.run(run())

@app.command()
def upgrade(version: str = None):
    """应用迁移到数据库

    Args:
        version: 要升级到的特定版本（默认为最新）
    """
    async def run():
        command = await get_command()
        await command.upgrade(version)
        version_msg = f"版本 {version}" if version else "最新版本"
        console.print(f"[bold green]✓[/] 数据库已成功升级到{version_msg}")
    
    version_msg = f"版本 {version}" if version else "最新版本"
    console.print(f"[bold]正在升级数据库到{version_msg}...[/]")
    asyncio.run(run())

@app.command()
def downgrade(version: str = None):
    """回滚迁移

    Args:
        version: 要回滚到的特定版本（默认为前一个版本）
    """
    async def run():
        command = await get_command()
        await command.downgrade(version)
        version_msg = f"版本 {version}" if version else "上一个版本"
        console.print(f"[bold green]✓[/] 数据库已成功回滚到{version_msg}")
    
    version_msg = f"版本 {version}" if version else "上一个版本"
    console.print(f"[bold]正在回滚数据库到{version_msg}...[/]")
    asyncio.run(run())

@app.command()
def history():
    """显示迁移历史"""
    async def run():
        command = await get_command()
        migrations = await command.history()
        if not migrations:
            console.print("[yellow]没有迁移历史记录[/]")
            return
            
        table = Table(title="迁移历史")
        table.add_column("版本", style="cyan")
        table.add_column("时间戳", style="green")
        table.add_column("名称", style="blue")
        table.add_column("状态", style="magenta")
        
        for migration in migrations:
            status = "[green]已应用[/]" if migration.get("applied") else "[yellow]未应用[/]"
            table.add_row(
                migration.get("version", ""),
                migration.get("timestamp", ""),
                migration.get("name", ""),
                status
            )
            
        console.print(table)
    
    console.print("[bold]正在获取迁移历史...[/]")
    asyncio.run(run())

@app.command()
def show_models():
    """显示当前的数据库模型结构"""
    from tortoise import Tortoise
    from rich.tree import Tree
    
    async def run():
        # 初始化Tortoise ORM
        await Tortoise.init(config=get_tortoise_config())
        
        # 获取所有模型
        models = Tortoise.apps.get("models")
        if not models:
            console.print("[yellow]未找到模型[/]")
            await Tortoise.close_connections()
            return
            
        # 创建模型树
        model_tree = Tree("[bold]数据库模型结构[/]")
        
        for model_name, model in models.items():
            if model_name == "aerich":  # 跳过Aerich的模型
                continue
                
            model_node = model_tree.add(f"[bold blue]{model_name}[/]")
            
            # 添加字段
            fields_node = model_node.add("[bold green]字段[/]")
            for field_name, field in model._meta.fields_map.items():
                field_type = field.__class__.__name__
                required = "" if field.null else "[red]（必填）[/]"
                fields_node.add(f"{field_name}: [yellow]{field_type}[/] {required}")
            
            # 添加关系
            if model._meta.fetch_fields:
                relations_node = model_node.add("[bold green]关系[/]")
                for relation in model._meta.fetch_fields:
                    related_model = relation.related_model.__name__
                    relations_node.add(f"{relation.model_field_name} -> [blue]{related_model}[/]")
        
        console.print(model_tree)
        await Tortoise.close_connections()
    
    console.print("[bold]正在分析数据库模型结构...[/]")
    asyncio.run(run())

if __name__ == "__main__":
    app()
