import typer
import rich
from rich.console import Console
from rich.panel import Panel
from rich.table import Table

from src.core.config import settings
from .commands import developer, api_key
from .db import app as db_app

console = Console()

# 创建主应用
app = typer.Typer(
    name=f"{settings.app_name} CLI",
    help=f"命令行界面工具，用于管理 {settings.app_name} FastAPI 应用程序。",
    rich_markup_mode="rich",
    context_settings={"help_option_names": ["-h", "--help"]}
)

# 添加命令组/模块
app.add_typer(developer.app, name="developer", help="开发者管理命令，包括创建、查询、更新和删除开发者。")
app.add_typer(api_key.app, name="api-key", help="API密钥管理命令，包括创建、查询和撤销API密钥。")
app.add_typer(db_app, name="db", help="数据库迁移和管理命令，包括初始化、迁移和升级数据库。")


@app.callback(invoke_without_command=True)
def main(ctx: typer.Context):
    """FastAPI 基础框架命令行工具
    
    这个工具提供了管理开发者、API密钥和数据库的命令。
    使用 -h 或 --help 参数查看可用命令和选项。
    """
    # 如果没有子命令，显示帮助信息
    if ctx.invoked_subcommand is None:
        show_welcome_message()


def show_welcome_message():
    """显示欢迎信息和可用命令概览"""
    # 创建欢迎面板
    welcome_panel = Panel(
        f"[bold]欢迎使用 {settings.app_name} 命令行工具[/bold]\n\n"
        f"版本: [cyan]{settings.version}[/cyan]\n"
        f"环境: [green]{settings.environment}[/green]\n\n"
        "使用 [yellow]-h[/yellow] 或 [yellow]--help[/yellow] 查看详细帮助信息。",
        title="FastAPI 基础框架 CLI",
        border_style="blue"
    )
    console.print(welcome_panel)
    
    # 创建命令表格
    table = Table(title="可用命令组", show_header=True, header_style="bold magenta")
    table.add_column("命令", style="cyan")
    table.add_column("描述", style="green")
    table.add_column("用法示例", style="yellow")
    
    table.add_row(
        "developer", 
        "开发者管理命令", 
        "python -m src.cli developer list"
    )
    table.add_row(
        "api-key", 
        "API密钥管理命令", 
        "python -m src.cli api-key create 1"
    )
    table.add_row(
        "db", 
        "数据库迁移和管理命令", 
        "python -m src.cli db init"
    )
    
    console.print(table)
    
    # 显示使用提示
    console.print("\n[bold]详细使用方法:[/bold]")
    console.print("1. 查看命令组帮助: [yellow]python -m src.cli developer --help[/yellow]")
    console.print("2. 查看子命令帮助: [yellow]python -m src.cli developer add --help[/yellow]")


if __name__ == "__main__":
    app() 