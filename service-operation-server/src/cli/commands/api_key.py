import asyncio
import typer
from typing import Optional
from rich.console import Console
from rich.table import Table
from rich.panel import Panel

from tortoise import Tortoise

from src.core.config import settings
from src.services.api_key_service import create_api_key, get_developer_api_keys, revoke_api_key
from src.services.developer_service import get_developer_by_id
from src.db.models.base import AbstractBaseModel  # 确保Tortoise模型已导入

app = typer.Typer(help="API密钥管理命令")
console = Console()


async def setup_db():
    """初始化数据库连接"""
    await Tortoise.init(
        db_url=str(settings.db_url),
        modules={"models": ["src.db.models"]}
    )


async def close_db():
    """关闭数据库连接"""
    await Tortoise.close_connections()


@app.command("list")
def list_api_keys(
    developer_id: int = typer.Argument(..., help="开发者ID")
):
    """列出开发者的所有API密钥"""
    async def _list_api_keys():
        await setup_db()
        
        # 验证开发者存在
        developer = await get_developer_by_id(developer_id)
        if not developer:
            console.print(f"[bold red]错误:[/bold red] 找不到ID为 {developer_id} 的开发者")
            await close_db()
            return
        
        keys = await get_developer_api_keys(developer_id)
        await close_db()
        
        if not keys:
            console.print(f"[bold yellow]开发者 {developer.name} (ID: {developer_id}) 没有API密钥[/bold yellow]")
            return
        
        console.print(f"[bold green]开发者 {developer.name} (ID: {developer_id}) 的API密钥:[/bold green]")
        
        table = Table(show_header=True, header_style="bold blue")
        table.add_column("ID", style="dim")
        table.add_column("密钥")
        table.add_column("状态")
        table.add_column("创建时间")
        table.add_column("最后使用时间")
        
        for key in keys:
            table.add_row(
                str(key["id"]),
                key["api_key"],
                key["status"],
                key["created_at"].strftime("%Y-%m-%d %H:%M:%S") if key["created_at"] else "N/A",
                key["last_used_at"].strftime("%Y-%m-%d %H:%M:%S") if key["last_used_at"] else "从未使用"
            )
        
        console.print(table)
    
    asyncio.run(_list_api_keys())


@app.command("create")
def create_api_key_cmd(
    developer_id: int = typer.Argument(..., help="开发者ID")
):
    """为开发者创建新的API密钥"""
    async def _create_api_key():
        await setup_db()
        
        # 验证开发者存在
        developer = await get_developer_by_id(developer_id)
        if not developer:
            console.print(f"[bold red]错误:[/bold red] 找不到ID为 {developer_id} 的开发者")
            await close_db()
            return
        
        result = await create_api_key(developer_id)
        await close_db()
        
        if "error" in result:
            console.print(f"[bold red]错误:[/bold red] {result['error']}")
            return
        
        console.print(f"[bold green]已为开发者 {developer.name} (ID: {developer_id}) 创建新的API密钥:[/bold green]")
        
        # 使用Panel显示敏感信息
        api_key_panel = Panel(
            f"API密钥: [bold]{result['api_key']}[/bold]\n"
            f"API秘钥: [bold red]{result['secret']}[/bold red] [yellow](请保存这个秘钥，它只会显示一次！)[/yellow]",
            title="API密钥信息",
            border_style="green"
        )
        console.print(api_key_panel)
        
        # 显示其他信息
        console.print(f"API密钥ID: {result['id']}")
        console.print(f"状态: {result['status']}")
        console.print(f"创建时间: {result['created_at'].strftime('%Y-%m-%d %H:%M:%S')}")
    
    asyncio.run(_create_api_key())


@app.command("revoke")
def revoke_api_key_cmd(
    key_id: int = typer.Argument(..., help="API密钥ID"),
    confirm: bool = typer.Option(False, "--yes", "-y", help="确认撤销，不提示")
):
    """撤销API密钥"""
    if not confirm:
        confirm = typer.confirm(f"确定要撤销ID为 {key_id} 的API密钥吗？此操作将使密钥无法继续使用。")
        if not confirm:
            console.print("[bold yellow]操作已取消[/bold yellow]")
            return
    
    async def _revoke_api_key():
        await setup_db()
        success = await revoke_api_key(key_id)
        await close_db()
        
        if success:
            console.print(f"[bold green]成功撤销ID为 {key_id} 的API密钥[/bold green]")
        else:
            console.print(f"[bold red]错误:[/bold red] 无法撤销ID为 {key_id} 的API密钥，可能不存在")
    
    asyncio.run(_revoke_api_key())
