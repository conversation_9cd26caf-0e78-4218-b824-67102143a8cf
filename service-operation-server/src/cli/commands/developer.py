import asyncio
import typer
from typing import Optional
from rich.console import Console
from rich.table import Table

from tortoise import Tortoise

from src.core.config import settings
from src.services.developer_service import create_developer, get_all_developers, get_developer_by_id, update_developer, delete_developer
from src.db.models.base import AbstractBaseModel  # 确保Tortoise模型已导入

app = typer.Typer(help="开发者管理命令")
console = Console()


async def setup_db():
    """初始化数据库连接"""
    await Tortoise.init(
        db_url=str(settings.db_url),
        modules={"models": ["src.db.models"]}
    )


async def close_db():
    """关闭数据库连接"""
    await Tortoise.close_connections()


@app.command("list")
def list_developers():
    """列出所有开发者"""
    async def _list_developers():
        await setup_db()
        developers = await get_all_developers()
        await close_db()
        
        if not developers:
            console.print("[bold red]没有找到开发者记录[/bold red]")
            return
        
        table = Table(show_header=True, header_style="bold blue")
        table.add_column("ID", style="dim")
        table.add_column("名称")
        table.add_column("邮箱")
        table.add_column("创建时间")
        
        for dev in developers:
            table.add_row(
                str(dev["id"]),
                dev["name"],
                dev["email"],
                dev["created_at"].strftime("%Y-%m-%d %H:%M:%S") if dev["created_at"] else "N/A"
            )
        
        console.print(table)
    
    asyncio.run(_list_developers())


@app.command("add")
def add_developer(
    name: str = typer.Option(..., "--name", "-n", help="开发者名称"),
    email: str = typer.Option(..., "--email", "-e", help="开发者邮箱")
):
    """添加新开发者"""
    async def _add_developer():
        await setup_db()
        result = await create_developer(name, email)
        await close_db()
        
        if "error" in result:
            console.print(f"[bold red]错误:[/bold red] {result['error']}")
            return
        
        console.print(f"[bold green]成功创建开发者:[/bold green]")
        console.print(f"ID: {result['id']}")
        console.print(f"名称: {result['name']}")
        console.print(f"邮箱: {result['email']}")
        console.print(f"创建时间: {result['created_at'].strftime('%Y-%m-%d %H:%M:%S')}")
    
    asyncio.run(_add_developer())


@app.command("get")
def get_developer(
    developer_id: int = typer.Argument(..., help="开发者ID")
):
    """获取开发者详细信息"""
    async def _get_developer():
        await setup_db()
        developer = await get_developer_by_id(developer_id)
        await close_db()
        
        if not developer:
            console.print(f"[bold red]错误:[/bold red] 找不到ID为 {developer_id} 的开发者")
            return
        
        console.print(f"[bold green]开发者信息:[/bold green]")
        console.print(f"ID: {developer.id}")
        console.print(f"名称: {developer.name}")
        console.print(f"邮箱: {developer.email}")
        console.print(f"创建时间: {developer.created_at.strftime('%Y-%m-%d %H:%M:%S')}")
        console.print(f"更新时间: {developer.updated_at.strftime('%Y-%m-%d %H:%M:%S')}")
    
    asyncio.run(_get_developer())


@app.command("update")
def update_developer_cmd(
    developer_id: int = typer.Argument(..., help="开发者ID"),
    name: Optional[str] = typer.Option(None, "--name", "-n", help="新的开发者名称"),
    email: Optional[str] = typer.Option(None, "--email", "-e", help="新的开发者邮箱")
):
    """更新开发者信息"""
    if name is None and email is None:
        console.print("[bold yellow]警告:[/bold yellow] 没有提供要更新的字段")
        return
    
    async def _update_developer():
        await setup_db()
        result = await update_developer(developer_id, name, email)
        await close_db()
        
        if "error" in result:
            console.print(f"[bold red]错误:[/bold red] {result['error']}")
            return
        
        console.print(f"[bold green]成功更新开发者:[/bold green]")
        console.print(f"ID: {result['id']}")
        console.print(f"名称: {result['name']}")
        console.print(f"邮箱: {result['email']}")
        console.print(f"更新时间: {result['updated_at'].strftime('%Y-%m-%d %H:%M:%S')}")
    
    asyncio.run(_update_developer())


@app.command("delete")
def delete_developer_cmd(
    developer_id: int = typer.Argument(..., help="开发者ID"),
    confirm: bool = typer.Option(False, "--yes", "-y", help="确认删除，不提示")
):
    """删除开发者"""
    if not confirm:
        confirm = typer.confirm(f"确定要删除ID为 {developer_id} 的开发者吗？此操作不可撤销。")
        if not confirm:
            console.print("[bold yellow]操作已取消[/bold yellow]")
            return
    
    async def _delete_developer():
        await setup_db()
        success = await delete_developer(developer_id)
        await close_db()
        
        if success:
            console.print(f"[bold green]成功删除ID为 {developer_id} 的开发者[/bold green]")
        else:
            console.print(f"[bold red]错误:[/bold red] 无法删除ID为 {developer_id} 的开发者，可能不存在")
    
    asyncio.run(_delete_developer())
