"""
护照相关的Pydantic模式定义
"""

from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
from datetime import datetime
from enum import Enum

class ProcessingStatus(str, Enum):
    """处理状态枚举"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"

class FileUploadResponse(BaseModel):
    """文件上传响应"""
    filename: str = Field(..., description="原始文件名")
    file_path: str = Field(..., description="文件相对路径")
    file_url: str = Field(..., description="文件访问URL")
    file_size: int = Field(..., description="文件大小（字节）")
    content_type: str = Field(..., description="文件类型")

class PassportUploadResponse(BaseModel):
    """护照上传响应"""
    task_id: str = Field(..., description="任务ID")
    uploaded_files: List[FileUploadResponse] = Field(..., description="上传的文件列表")
    total_files: int = Field(..., description="总文件数量")
    message: str = Field(..., description="响应消息")

class PassportUploadRequest(BaseModel):
    """护照上传请求"""
    task_id: str = Field(..., description="任务ID")
    image_url: str = Field(..., description="护照图片URL")

class PassportRecognitionData(BaseModel):
    """护照识别数据"""
    certificate_type: Optional[str] = Field(None, description="证件类型")
    country_of_issue: Optional[str] = Field(None, description="签发国")
    certificate_number: Optional[str] = Field(None, description="证件号码")
    surname: Optional[str] = Field(None, description="姓氏")
    given_names: Optional[str] = Field(None, description="名字")
    nationality: Optional[str] = Field(None, description="国籍")
    date_of_birth: Optional[str] = Field(None, description="出生日期")
    sex: Optional[str] = Field(None, description="性别")
    date_of_issue: Optional[str] = Field(None, description="签发日期")
    date_of_expiry: Optional[str] = Field(None, description="有效期至")
    passenger_type: Optional[str] = Field(None, description="旅客类型（成人/儿童）")
    viz_mrz_consistency: Optional[str] = Field(None, description="VIZ与MRZ数据一致性检查结果")
    ssr_code: Optional[str] = Field(None, description="SSR码")
    mrz_line1: Optional[str] = Field(None, description="MRZ第一行")
    mrz_line2: Optional[str] = Field(None, description="MRZ第二行")
    additional_info: Optional[Dict[str, Any]] = Field(default_factory=dict, description="额外信息")

class PassportCreateRequest(BaseModel):
    """创建护照记录请求"""
    task_id: str = Field(..., description="任务ID")
    uploaded_image_url: str = Field(..., description="上传的护照图片地址")
    dify_image_url: Optional[str] = Field(None, description="Dify图片地址")
    dify_image_uuid: Optional[str] = Field(None, description="Dify图片UUID")
    dify_image_filename: Optional[str] = Field(None, description="Dify图片文件名")
    dify_filename: Optional[str] = Field(None, description="Dify文件名")
    recognition_data: PassportRecognitionData = Field(..., description="护照识别数据")

class PassportUpdateRequest(BaseModel):
    """更新护照记录请求"""
    dify_image_url: Optional[str] = Field(None, description="Dify图片地址")
    dify_image_uuid: Optional[str] = Field(None, description="Dify图片UUID")
    dify_image_filename: Optional[str] = Field(None, description="Dify图片文件名")
    dify_filename: Optional[str] = Field(None, description="Dify文件名")
    recognition_data: Optional[PassportRecognitionData] = Field(None, description="护照识别数据")
    processing_status: Optional[ProcessingStatus] = Field(None, description="处理状态")

class PassportResponse(BaseModel):
    """护照记录响应"""
    id: int = Field(..., description="记录ID")
    user_id: str = Field(..., description="用户ID")  # 通过外键关系获取
    task_id: str = Field(..., description="任务ID")
    uploaded_image_url: str = Field(..., description="上传的护照图片地址")
    dify_image_url: Optional[str] = Field(None, description="Dify图片地址")
    dify_image_uuid: Optional[str] = Field(None, description="Dify图片UUID")
    dify_image_filename: Optional[str] = Field(None, description="Dify图片文件名")
    dify_filename: Optional[str] = Field(None, description="Dify文件名")
    
    # 护照识别信息
    certificate_type: Optional[str] = Field(None, description="证件类型")
    country_of_issue: Optional[str] = Field(None, description="签发国")
    certificate_number: Optional[str] = Field(None, description="证件号码")
    surname: Optional[str] = Field(None, description="姓氏")
    given_names: Optional[str] = Field(None, description="名字")
    nationality: Optional[str] = Field(None, description="国籍")
    date_of_birth: Optional[str] = Field(None, description="出生日期")
    sex: Optional[str] = Field(None, description="性别")
    date_of_issue: Optional[str] = Field(None, description="签发日期")
    date_of_expiry: Optional[str] = Field(None, description="有效期至")
    passenger_type: Optional[str] = Field(None, description="旅客类型（成人/儿童）")
    viz_mrz_consistency: Optional[str] = Field(None, description="VIZ与MRZ数据一致性检查结果")
    ssr_code: Optional[str] = Field(None, description="SSR码")
    mrz_line1: Optional[str] = Field(None, description="MRZ第一行")
    mrz_line2: Optional[str] = Field(None, description="MRZ第二行")
    additional_info: Optional[Dict[str, Any]] = Field(None, description="额外信息")
    
    processing_status: ProcessingStatus = Field(..., description="处理状态")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True

class PassportListResponse(BaseModel):
    """护照列表响应"""
    total: int = Field(..., description="总数量")
    items: list[PassportResponse] = Field(..., description="护照记录列表")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页大小")

class PassportRecognitionResponse(BaseModel):
    """护照识别结果响应"""
    success: bool = Field(..., description="识别是否成功")
    passport_id: Optional[int] = Field(None, description="护照记录ID")
    recognition_data: Optional[PassportRecognitionData] = Field(None, description="识别数据")
    message: str = Field(..., description="响应消息")

class APIResponse(BaseModel):
    """通用API响应格式"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    data: Optional[Dict[str, Any]] = Field(None, description="响应数据")

# 新增任务相关的Schema
class TaskSummary(BaseModel):
    """任务摘要信息"""
    task_id: str = Field(..., description="任务ID")
    total_files: int = Field(..., description="总文件数")
    completed_files: int = Field(..., description="已完成文件数")
    processing_files: int = Field(..., description="处理中文件数")
    failed_files: int = Field(..., description="失败文件数")
    pending_files: int = Field(..., description="待处理文件数")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="最后更新时间")
    latest_upload_url: Optional[str] = Field(None, description="最新上传的图片URL")

class TaskListResponse(BaseModel):
    """任务列表响应"""
    total: int = Field(..., description="总数量")
    items: list[TaskSummary] = Field(..., description="任务列表")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页大小")

class TaskStatsResponse(BaseModel):
    """任务统计响应"""
    task_id: str = Field(..., description="任务ID")
    total_count: int = Field(..., description="总数量")
    pending_count: int = Field(..., description="待处理数量")
    processing_count: int = Field(..., description="处理中数量")
    completed_count: int = Field(..., description="已完成数量")
    failed_count: int = Field(..., description="失败数量") 