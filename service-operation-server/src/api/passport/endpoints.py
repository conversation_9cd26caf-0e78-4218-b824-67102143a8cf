"""
护照识别API端点

提供护照图片上传、识别和管理相关的API接口
"""

import os
import uuid
import zipfile
import tempfile
import rarfile
from typing import List, Optional
from datetime import datetime
import random

from fastapi import APIRouter, UploadFile, File, Form, HTTPException, Depends, Query
from fastapi.responses import JSONResponse, RedirectResponse
from loguru import logger

from src.api.dependencies import get_current_user
from src.api.passport.schemas import (
    PassportUploadResponse, 
    FileUploadResponse,
    PassportResponse,
    PassportListResponse,
    PassportCreateRequest,
    PassportUpdateRequest,
    APIResponse,
    ProcessingStatus,
    TaskSummary,
    TaskListResponse,
    TaskStatsResponse
)
from src.db.models import Passport, User
from tortoise.expressions import Q
from src.services.file_service import file_service
from src.services.s3_service import s3_service
from src.services.pdf_service import pdf_service

router = APIRouter()


async def _process_pdf_converted_image(
    image_data: bytes,
    filename: str,
    task_id: str,
    current_user: User
) -> FileUploadResponse:
    """
    处理PDF转换后的图片数据
    """
    try:
        logger.info(f"📸 处理PDF转换的图片: {filename}, 大小: {len(image_data)} bytes")

        # 尝试上传到S3
        s3_url = None
        try:
            # 生成S3文件路径
            from datetime import datetime
            date_prefix = datetime.now().strftime("%Y/%m/%d")
            s3_key = f"tmc.ai.soap.server/passport/{date_prefix}/{filename}"

            # 上传到S3
            s3_url = await s3_service.upload_file_data(
                file_data=image_data,
                filename=filename,
                folder="passport"
            )
            logger.info(f"✅ PDF转换图片S3上传成功: {s3_url}")

        except Exception as s3_error:
            logger.warning(f"⚠️ PDF转换图片S3上传失败，将使用本地存储: {s3_error}")

            # S3上传失败，保存到本地
            from pathlib import Path
            import tempfile
            import shutil

            # 创建临时文件
            with tempfile.NamedTemporaryFile(delete=False, suffix='.jpg') as temp_file:
                temp_file.write(image_data)
                temp_path = temp_file.name

            # 移动到正确的位置
            upload_dir = Path("uploads/passport") / datetime.now().strftime("%Y/%m/%d")
            upload_dir.mkdir(parents=True, exist_ok=True)
            final_path = upload_dir / filename
            shutil.move(temp_path, final_path)

            s3_url = str(final_path.relative_to(Path("uploads")))

        # 创建护照记录
        passport = await Passport.create(
            task_id=task_id,
            uploaded_image_url=s3_url,
            user_id=current_user.id,
            file_size=len(image_data),
            content_type="image/jpeg"
        )

        logger.info(f"✅ PDF转换图片护照记录创建成功: ID={passport.id}")

        return FileUploadResponse(
            filename=filename,
            file_path=s3_url or f"passport/{filename}",  # 使用S3 URL作为路径
            file_url=s3_url or f"/uploads/passport/{filename}",  # 文件访问URL
            file_size=len(image_data),
            content_type="image/jpeg"
        )

    except Exception as e:
        logger.error(f"❌ PDF转换图片处理失败: {e}")
        raise HTTPException(status_code=500, detail=f"PDF转换图片处理失败: {str(e)}")


async def _process_and_upload_file(file_path: str, filename: str, task_id: str, current_user: User) -> FileUploadResponse:
    """
    处理单个文件：上传到S3并创建护照记录
    
    Args:
        file_path: 本地文件路径
        filename: 文件名
        task_id: 任务ID
        current_user: 当前用户
        
    Returns:
        FileUploadResponse: 文件上传响应
    """
    from pathlib import Path
    
    file_path_obj = Path(file_path)
    file_size = file_path_obj.stat().st_size
    
    # 先生成本地URL作为备用
    relative_path = str(file_path_obj.relative_to(Path.cwd()))
    local_file_url = file_service.get_file_url(relative_path)
    
    # 尝试上传到S3
    s3_file_url = await s3_service.upload_file(file_path, "passport")
    
    # 选择最终的文件URL（优先使用S3 URL）
    final_file_url = s3_file_url if s3_file_url else local_file_url
    
    logger.info(f"文件处理完成: {filename}")
    logger.info(f"  - 本地路径: {file_path}")
    logger.info(f"  - 本地URL: {local_file_url}")
    logger.info(f"  - S3 URL: {s3_file_url or '上传失败'}")
    logger.info(f"  - 最终URL: {final_file_url}")
    
    # 创建护照记录
    await Passport.create(
        user=current_user,
        task_id=task_id,
        uploaded_image_url=final_file_url,
        processing_status="pending",
        # 为所有NOT NULL字段提供默认值
        dify_image_uuid="",
        dify_image_filename="",
        dify_filename="",
        certificate_type="",
        country_of_issue="",
        certificate_number="",
        surname="",
        given_names="",
        nationality="",
        date_of_birth="",  # 默认空字符串
        sex="",
        date_of_issue="",  # 默认空字符串
        date_of_expiry="",  # 默认空字符串
        passenger_type="",
        viz_mrz_consistency="",
        ssr_code="",
        mrz_line1="",
        mrz_line2="",
        additional_info={}
    )
    
    # 返回文件上传响应
    return FileUploadResponse(
        filename=filename,
        file_path=relative_path,
        file_url=final_file_url,
        file_size=file_size,
        content_type="image/jpeg"  # 假设提取的都是JPEG格式
    )


@router.post("/upload", response_model=PassportUploadResponse)
async def upload_passport_files(
    files: List[UploadFile] = File(..., description="护照图片文件、PDF文件或压缩包"),
    task_id: Optional[str] = Form(None, description="任务ID，如果不提供将自动生成"),
    current_user: User = Depends(get_current_user)
):
    """
    上传护照图片文件

    支持：
    - 单张或多张图片文件（JPG、PNG、GIF等）
    - PDF文件（自动转换为图片）
    - ZIP/RAR压缩包（自动解压提取图片）
    """
    if not files:
        raise HTTPException(status_code=400, detail="请选择要上传的文件")
    
    # 生成任务ID，格式：AN_当前时间_随机三位数
    if not task_id:
        # 获取当前时间戳
        current_time = datetime.now().strftime('%Y%m%d%H%M%S')
        # 生成随机三位数（100-999）
        random_suffix = random.randint(100, 999)
        # 生成任务ID
        task_id = f"AN_{current_time}_{random_suffix:03d}"
    
    uploaded_files = []

    try:
        logger.info(f"🚀 用户 {current_user.username} 开始上传护照文件，文件数量: {len(files)}")
        logger.info(f"📋 文件列表: {[f.filename for f in files]}")
        logger.info(f"🎯 任务ID: {task_id}")

        for i, file in enumerate(files):
            logger.info(f"📎 处理文件 {i+1}/{len(files)}: {file.filename}, 类型: {file.content_type}")

            # 检查是否为PDF文件
            if pdf_service.is_pdf_file(file.filename or '', file.content_type or ''):
                logger.info(f"📄 检测到PDF文件: {file.filename}，开始转换第一页为图片...")

                # 转换PDF为图片（只转换第一页）
                pdf_images = await pdf_service.convert_pdf_to_images(file)

                for image_data, image_filename in pdf_images:
                    logger.info(f"🖼️ 处理PDF转换的图片: {image_filename}")

                    # 直接处理图片数据，不需要保存到本地
                    file_response = await _process_pdf_converted_image(
                        image_data=image_data,
                        filename=image_filename,
                        task_id=task_id,
                        current_user=current_user
                    )
                    uploaded_files.append(file_response)

            elif file.content_type in file_service.allowed_archive_types:
                # 保存上传的文件
                relative_path, absolute_path = await file_service.save_uploaded_file(file, "passport")

                # 解压压缩包并提取图片
                extracted_files = await file_service.extract_images_from_archive(absolute_path)

                for extracted_relative_path, extracted_absolute_path in extracted_files:
                    # 使用新的处理函数
                    from pathlib import Path
                    file_response = await _process_and_upload_file(
                        file_path=extracted_absolute_path,
                        filename=Path(extracted_absolute_path).name,
                        task_id=task_id,
                        current_user=current_user
                    )
                    uploaded_files.append(file_response)
            else:
                # 保存上传的文件
                relative_path, absolute_path = await file_service.save_uploaded_file(file, "passport")

                # 普通图片文件，使用新的处理函数
                file_response = await _process_and_upload_file(
                    file_path=absolute_path,
                    filename=file.filename or "unknown",
                    task_id=task_id,
                    current_user=current_user
                )
                uploaded_files.append(file_response)
        
        logger.info(f"✅ 任务 {task_id} 文件上传完成，共 {len(uploaded_files)} 个文件")
        
        # 自动启动识别任务
        logger.info(f"=== 🚀 自动启动识别任务 {task_id} ===")
        try:
            from src.services.passport_task_service import passport_task_service
            logger.info(f"📋 启动任务处理器...")
            await passport_task_service.start_task_processor()
            
            logger.info(f"📤 提交识别任务到队列...")
            recognition_started = await passport_task_service.submit_recognition_task(
                task_id=task_id,
                user_id=str(current_user.id)
            )
            
            if recognition_started:
                logger.info(f"✅ 任务 {task_id} 的识别任务已自动启动")
            else:
                logger.warning(f"⚠️ 任务 {task_id} 的识别任务启动失败")
                
        except Exception as e:
            logger.error(f"💥 自动启动识别任务失败: {e}")
            import traceback
            logger.error(f"🔍 启动任务异常堆栈: {traceback.format_exc()}")
            # 不影响上传成功的返回
        
        return PassportUploadResponse(
            task_id=task_id,
            uploaded_files=uploaded_files,
            total_files=len(uploaded_files),
            message=f"成功上传 {len(uploaded_files)} 个文件，识别任务已启动"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文件上传失败: {e}")
        raise HTTPException(status_code=500, detail="文件上传失败")


@router.get("/list", response_model=PassportListResponse)
async def list_passports(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页大小"),
    task_id: Optional[str] = Query(None, description="任务ID筛选"),
    status: Optional[ProcessingStatus] = Query(None, description="处理状态筛选"),
    name: Optional[str] = Query(None, description="姓名搜索（模糊匹配姓氏或名字）"),
    cert_number: Optional[str] = Query(None, description="证件号码搜索（模糊匹配）"),
    current_user: User = Depends(get_current_user)
):
    """
    获取护照记录列表
    
    支持分页和筛选功能
    """
    try:
        # 构建查询条件（只查询未删除的记录）
        query = Passport.filter(user_id=current_user.id, is_deleted=False)

        if task_id:
            query = query.filter(task_id=task_id)

        if status:
            query = query.filter(processing_status=status)

        # 添加姓名搜索条件（模糊匹配姓氏或名字）
        if name:
            name_condition = Q(
                Q(surname__icontains=name) |
                Q(given_names__icontains=name)
            )
            query = query.filter(name_condition)

        # 添加证件号码搜索条件（模糊匹配）
        if cert_number:
            query = query.filter(certificate_number__icontains=cert_number)
        
        # 获取总数
        total = await query.count()
        
        # 分页查询
        offset = (page - 1) * size
        passports = await query.order_by('-created_at').offset(offset).limit(size)
        
        # 转换为响应格式
        items = []
        for passport in passports:
            items.append(PassportResponse(
                id=passport.id,
                user_id=passport.user_id,
                task_id=passport.task_id,
                uploaded_image_url=passport.uploaded_image_url,
                dify_image_url=passport.dify_image_url,
                dify_image_uuid=passport.dify_image_uuid,
                dify_image_filename=passport.dify_image_filename,
                dify_filename=passport.dify_filename,
                certificate_type=passport.certificate_type,
                country_of_issue=passport.country_of_issue,
                certificate_number=passport.certificate_number,
                surname=passport.surname,
                given_names=passport.given_names,
                nationality=passport.nationality,
                date_of_birth=passport.date_of_birth,
                sex=passport.sex,
                date_of_issue=passport.date_of_issue,
                date_of_expiry=passport.date_of_expiry,
                passenger_type=passport.passenger_type,
                viz_mrz_consistency=passport.viz_mrz_consistency,
                ssr_code=passport.ssr_code,
                mrz_line1=passport.mrz_line1,
                mrz_line2=passport.mrz_line2,
                additional_info=passport.additional_info,
                processing_status=passport.processing_status,
                created_at=passport.created_at,
                updated_at=passport.updated_at
            ))
        
        return PassportListResponse(
            total=total,
            items=items,
            page=page,
            size=size
        )
        
    except Exception as e:
        logger.error(f"获取护照列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取护照列表失败")


@router.get("/tasks", response_model=TaskListResponse)
async def list_tasks(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页大小"),
    current_user: User = Depends(get_current_user)
):
    """
    获取任务列表
    
    返回用户的所有任务的摘要信息，包括每个任务的文件统计和处理状态
    """
    try:
        # 获取该用户的所有不同任务ID（包括已删除的护照，以确保任务列表完整）
        task_ids = await Passport.filter(user_id=current_user.id)\
            .distinct()\
            .values_list('task_id', flat=True)
        
        # 获取总任务数
        total_tasks = len(task_ids)
        
        # 分页处理
        offset = (page - 1) * size
        paginated_task_ids = task_ids[offset:offset + size]
        
        # 为每个任务获取详细统计信息
        task_summaries = []
        for task_id in paginated_task_ids:
            # 获取该任务的所有护照记录（包括已删除的，用于获取时间信息）
            all_task_passports = await Passport.filter(
                user_id=current_user.id,
                task_id=task_id
            ).all()

            # 获取该任务的未删除护照记录（用于统计）
            active_task_passports = await Passport.filter(
                user_id=current_user.id,
                task_id=task_id,
                is_deleted=False
            ).all()

            # 如果任务没有任何护照记录，跳过该任务
            if not all_task_passports:
                continue

            # 统计各状态的数量（只统计未删除的）
            completed_files = 0
            processing_files = 0
            failed_files = 0
            pending_files = 0

            for passport in active_task_passports:
                if passport.processing_status == 'completed':
                    completed_files += 1
                elif passport.processing_status == 'processing':
                    processing_files += 1
                elif passport.processing_status == 'failed':
                    failed_files += 1
                elif passport.processing_status == 'pending':
                    pending_files += 1

            # 获取最新和最早的时间（基于所有护照记录）
            latest_passport = max(all_task_passports, key=lambda x: x.created_at)
            earliest_passport = min(all_task_passports, key=lambda x: x.created_at)

            latest_upload_url = latest_passport.uploaded_image_url if latest_passport else None

            task_summary = TaskSummary(
                task_id=task_id,
                total_files=len(active_task_passports),  # 只统计未删除的护照
                completed_files=completed_files,
                processing_files=processing_files,
                failed_files=failed_files,
                pending_files=pending_files,
                created_at=earliest_passport.created_at,
                updated_at=latest_passport.updated_at,
                latest_upload_url=latest_upload_url
            )
            
            task_summaries.append(task_summary)
        
        # 按创建时间倒序排列
        task_summaries.sort(key=lambda x: x.created_at if x.created_at else datetime.min, reverse=True)
        
        return TaskListResponse(
            total=total_tasks,
            items=task_summaries,
            page=page,
            size=size
        )
        
    except Exception as e:
        logger.error(f"获取任务列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取任务列表失败")


@router.get("/task/{task_id}", response_model=PassportListResponse)
async def get_passports_by_task(
    task_id: str,
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页大小"),
    current_user: User = Depends(get_current_user)
):
    """
    根据任务ID获取护照记录列表，支持分页
    """
    try:
        # 构建查询（只查询未删除的记录）
        query = Passport.filter(
            task_id=task_id,
            user_id=current_user.id,
            is_deleted=False
        )
        
        # 获取总数
        total = await query.count()
        
        # 分页查询
        offset = (page - 1) * size
        passports = await query.order_by('-created_at').offset(offset).limit(size)
        
        # 转换为响应格式
        items = []
        for passport in passports:
            items.append(PassportResponse(
                id=passport.id,
                user_id=passport.user_id,
                task_id=passport.task_id,
                uploaded_image_url=passport.uploaded_image_url,
                dify_image_url=passport.dify_image_url,
                dify_image_uuid=passport.dify_image_uuid,
                dify_image_filename=passport.dify_image_filename,
                dify_filename=passport.dify_filename,
                certificate_type=passport.certificate_type,
                country_of_issue=passport.country_of_issue,
                certificate_number=passport.certificate_number,
                surname=passport.surname,
                given_names=passport.given_names,
                nationality=passport.nationality,
                date_of_birth=passport.date_of_birth,
                sex=passport.sex,
                date_of_issue=passport.date_of_issue,
                date_of_expiry=passport.date_of_expiry,
                passenger_type=passport.passenger_type,
                viz_mrz_consistency=passport.viz_mrz_consistency,
                ssr_code=passport.ssr_code,
                mrz_line1=passport.mrz_line1,
                mrz_line2=passport.mrz_line2,
                additional_info=passport.additional_info,
                processing_status=passport.processing_status,
                created_at=passport.created_at,
                updated_at=passport.updated_at
            ))
        
        return PassportListResponse(
            total=total,
            items=items,
            page=page,
            size=size
        )
        
    except Exception as e:
        logger.error(f"获取任务护照列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取任务护照列表失败")


@router.get("/{passport_id}", response_model=PassportResponse)
async def get_passport(
    passport_id: int,
    current_user: User = Depends(get_current_user)
):
    """
    根据ID获取护照记录详情
    """
    try:
        passport = await Passport.get_or_none(
            id=passport_id,
            user_id=current_user.id,
            is_deleted=False
        )
        
        if not passport:
            raise HTTPException(status_code=404, detail="护照记录不存在")
        
        return PassportResponse(
            id=passport.id,
            user_id=passport.user_id,
            task_id=passport.task_id,
            uploaded_image_url=passport.uploaded_image_url,
            dify_image_url=passport.dify_image_url,
            dify_image_uuid=passport.dify_image_uuid,
            dify_image_filename=passport.dify_image_filename,
            dify_filename=passport.dify_filename,
            certificate_type=passport.certificate_type,
            country_of_issue=passport.country_of_issue,
            certificate_number=passport.certificate_number,
            surname=passport.surname,
            given_names=passport.given_names,
            nationality=passport.nationality,
            date_of_birth=passport.date_of_birth,
            sex=passport.sex,
            date_of_issue=passport.date_of_issue,
            date_of_expiry=passport.date_of_expiry,
            passenger_type=passport.passenger_type,
            viz_mrz_consistency=passport.viz_mrz_consistency,
            ssr_code=passport.ssr_code,
            mrz_line1=passport.mrz_line1,
            mrz_line2=passport.mrz_line2,
            additional_info=passport.additional_info,
            processing_status=passport.processing_status,
            created_at=passport.created_at,
            updated_at=passport.updated_at
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取护照记录失败: {e}")
        raise HTTPException(status_code=500, detail="获取护照记录失败")


@router.put("/{passport_id}", response_model=PassportResponse)
async def update_passport(
    passport_id: int,
    update_data: PassportUpdateRequest,
    current_user: User = Depends(get_current_user)
):
    """
    更新护照记录
    """
    try:
        # 检查护照记录是否存在且属于当前用户（只查询未删除的记录）
        passport = await Passport.get_or_none(
            id=passport_id,
            user_id=current_user.id,
            is_deleted=False
        )
        
        if not passport:
            raise HTTPException(status_code=404, detail="护照记录不存在")
        
        # 构建更新字典
        update_dict = {}
        
        # 更新Dify相关字段
        if update_data.dify_image_url is not None:
            update_dict['dify_image_url'] = update_data.dify_image_url
        
        if update_data.dify_image_uuid is not None:
            update_dict['dify_image_uuid'] = update_data.dify_image_uuid
            
        if update_data.dify_image_filename is not None:
            update_dict['dify_image_filename'] = update_data.dify_image_filename
            
        if update_data.dify_filename is not None:
            update_dict['dify_filename'] = update_data.dify_filename
        
        # 更新识别数据
        if update_data.recognition_data:
            recognition_data = update_data.recognition_data
            
            if recognition_data.certificate_type is not None:
                update_dict['certificate_type'] = recognition_data.certificate_type
                
            if recognition_data.country_of_issue is not None:
                update_dict['country_of_issue'] = recognition_data.country_of_issue
                
            if recognition_data.certificate_number is not None:
                update_dict['certificate_number'] = recognition_data.certificate_number
                
            if recognition_data.surname is not None:
                update_dict['surname'] = recognition_data.surname
                
            if recognition_data.given_names is not None:
                update_dict['given_names'] = recognition_data.given_names
                
            if recognition_data.nationality is not None:
                update_dict['nationality'] = recognition_data.nationality
                
            if recognition_data.date_of_birth is not None:
                update_dict['date_of_birth'] = recognition_data.date_of_birth
                
            if recognition_data.sex is not None:
                update_dict['sex'] = recognition_data.sex
                
            if recognition_data.date_of_issue is not None:
                update_dict['date_of_issue'] = recognition_data.date_of_issue
                
            if recognition_data.date_of_expiry is not None:
                update_dict['date_of_expiry'] = recognition_data.date_of_expiry
                
            if recognition_data.passenger_type is not None:
                update_dict['passenger_type'] = recognition_data.passenger_type
                
            if recognition_data.viz_mrz_consistency is not None:
                update_dict['viz_mrz_consistency'] = recognition_data.viz_mrz_consistency
                
            if recognition_data.ssr_code is not None:
                update_dict['ssr_code'] = recognition_data.ssr_code
                
            if recognition_data.mrz_line1 is not None:
                update_dict['mrz_line1'] = recognition_data.mrz_line1
                
            if recognition_data.mrz_line2 is not None:
                update_dict['mrz_line2'] = recognition_data.mrz_line2
                
            if recognition_data.additional_info is not None:
                update_dict['additional_info'] = recognition_data.additional_info
        
        # 更新处理状态
        if update_data.processing_status is not None:
            update_dict['processing_status'] = update_data.processing_status
        
        # 执行更新
        if update_dict:
            await passport.update_from_dict(update_dict)
            await passport.save()
        
        # 重新获取更新后的记录
        updated_passport = await Passport.get(id=passport_id)
        
        return PassportResponse(
            id=updated_passport.id,
            user_id=updated_passport.user_id,
            task_id=updated_passport.task_id,
            uploaded_image_url=updated_passport.uploaded_image_url,
            dify_image_url=updated_passport.dify_image_url,
            dify_image_uuid=updated_passport.dify_image_uuid,
            dify_image_filename=updated_passport.dify_image_filename,
            dify_filename=updated_passport.dify_filename,
            certificate_type=updated_passport.certificate_type,
            country_of_issue=updated_passport.country_of_issue,
            certificate_number=updated_passport.certificate_number,
            surname=updated_passport.surname,
            given_names=updated_passport.given_names,
            nationality=updated_passport.nationality,
            date_of_birth=updated_passport.date_of_birth,
            sex=updated_passport.sex,
            date_of_issue=updated_passport.date_of_issue,
            date_of_expiry=updated_passport.date_of_expiry,
            passenger_type=updated_passport.passenger_type,
            viz_mrz_consistency=updated_passport.viz_mrz_consistency,
            ssr_code=updated_passport.ssr_code,
            mrz_line1=updated_passport.mrz_line1,
            mrz_line2=updated_passport.mrz_line2,
            additional_info=updated_passport.additional_info,
            processing_status=updated_passport.processing_status,
            created_at=updated_passport.created_at,
            updated_at=updated_passport.updated_at
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新护照记录失败: {e}")
        raise HTTPException(status_code=500, detail="更新护照记录失败")


@router.delete("/clear-all", response_model=APIResponse)
async def delete_all_passports(
    current_user: User = Depends(get_current_user)
):
    """
    删除当前用户的所有护照记录
    """
    try:
        # 获取当前用户的所有未删除护照记录
        passports = await Passport.filter(user_id=current_user.id, is_deleted=False).all()

        if not passports:
            return APIResponse(
                success=True,
                message="没有护照记录需要删除"
            )

        # 统计删除数量
        deleted_count = len(passports)

        # 软删除所有护照记录
        await Passport.filter(user_id=current_user.id, is_deleted=False).update(is_deleted=True)

        return APIResponse(
            success=True,
            message=f"已删除 {deleted_count} 条护照记录"
        )

    except Exception as e:
        logger.error(f"删除所有护照记录失败: {e}")
        raise HTTPException(status_code=500, detail="删除所有护照记录失败")


@router.delete("/{passport_id}", response_model=APIResponse)
async def delete_passport(
    passport_id: int,
    current_user: User = Depends(get_current_user)
):
    """
    删除护照记录
    """
    try:
        # 检查护照记录是否存在且属于当前用户（只查询未删除的记录）
        passport = await Passport.get_or_none(
            id=passport_id,
            user_id=current_user.id,
            is_deleted=False
        )

        if not passport:
            raise HTTPException(status_code=404, detail="护照记录不存在")

        task_id = passport.task_id

        # 软删除记录
        passport.is_deleted = True
        await passport.save()

        # 检查该任务是否还有其他未删除的护照记录
        remaining_passports = await Passport.filter(
            task_id=task_id,
            user_id=current_user.id,
            is_deleted=False
        ).count()

        # 如果任务中没有剩余护照记录，任务也会被自动清理
        message = "护照记录删除成功"
        if remaining_passports == 0:
            message += "，任务已自动清理"

        return APIResponse(
            success=True,
            message=message
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除护照记录失败: {e}")
        raise HTTPException(status_code=500, detail="删除护照记录失败")


@router.post("/task/{task_id}/recognize", response_model=APIResponse)
async def start_recognition_task(
    task_id: str,
    current_user: User = Depends(get_current_user)
):
    """
    启动护照识别任务
    """
    try:
        # 检查任务是否存在
        passports = await Passport.filter(
            task_id=task_id,
            user_id=current_user.id
        ).all()
        
        if not passports:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        # 启动识别任务
        from src.services.passport_task_service import passport_task_service
        
        await passport_task_service.start_task_processor()
        
        success = await passport_task_service.submit_recognition_task(
            task_id=task_id,
            user_id=str(current_user.id)
        )
        
        if success:
            return APIResponse(
                success=True,
                message=f"识别任务 {task_id} 启动成功"
            )
        else:
            raise HTTPException(status_code=500, detail="识别任务启动失败")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"启动识别任务失败: {e}")
        raise HTTPException(status_code=500, detail="启动识别任务失败")


@router.get("/task/{task_id}/status")
async def get_recognition_task_status(
    task_id: str,
    current_user: User = Depends(get_current_user)
):
    """
    获取识别任务状态
    """
    try:
        # 获取任务的所有护照记录
        passports = await Passport.filter(
            task_id=task_id,
            user_id=current_user.id
        ).all()
        
        if not passports:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        # 统计各状态数量
        status_counts = {
            'pending': 0,
            'processing': 0,
            'completed': 0,
            'failed': 0
        }
        
        for passport in passports:
            status_counts[passport.processing_status] += 1
        
        return {
            "task_id": task_id,
            "total_files": len(passports),
            "status_counts": status_counts,
            "is_completed": status_counts['pending'] == 0 and status_counts['processing'] == 0
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务状态失败: {e}")
        raise HTTPException(status_code=500, detail="获取任务状态失败")


@router.post("/task/{task_id}/stop", response_model=APIResponse)
async def stop_recognition_task(
    task_id: str,
    current_user: User = Depends(get_current_user)
):
    """
    停止护照识别任务
    """
    try:
        # 检查任务是否存在
        passports = await Passport.filter(
            task_id=task_id,
            user_id=current_user.id
        ).all()
        
        if not passports:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        # 停止任务处理
        from src.services.passport_task_service import passport_task_service
        
        success = await passport_task_service.stop_task(task_id)
        
        if success:
            # 将所有处理中的记录状态改为失败
            for passport in passports:
                if passport.processing_status in ['pending', 'processing']:
                    passport.processing_status = 'failed'
                    await passport.save()
            
            return APIResponse(
                success=True,
                message=f"识别任务 {task_id} 已停止"
            )
        else:
            raise HTTPException(status_code=500, detail="停止任务失败")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"停止识别任务失败: {e}")
        raise HTTPException(status_code=500, detail="停止识别任务失败")


@router.post("/task/{task_id}/delete", response_model=APIResponse)
async def delete_task(
    task_id: str,
    current_user: User = Depends(get_current_user)
):
    """
    删除整个任务及其所有护照记录
    """
    try:
        # 获取任务的所有未删除护照记录
        passports = await Passport.filter(
            task_id=task_id,
            user_id=current_user.id,
            is_deleted=False
        ).all()

        if not passports:
            raise HTTPException(status_code=404, detail="任务不存在")

        # 软删除所有护照记录
        await Passport.filter(
            task_id=task_id,
            user_id=current_user.id,
            is_deleted=False
        ).update(is_deleted=True)
        
        return APIResponse(
            success=True,
            message=f"任务 {task_id} 及其所有记录已删除"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除任务失败: {e}")
        raise HTTPException(status_code=500, detail="删除任务失败")


@router.post("/task/{task_id}/restart", response_model=APIResponse)
async def restart_recognition_task(
    task_id: str,
    current_user: User = Depends(get_current_user)
):
    """
    重启护照识别任务
    """
    try:
        # 检查任务是否存在
        passports = await Passport.filter(
            task_id=task_id,
            user_id=current_user.id
        ).all()
        
        if not passports:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        # 重置所有失败的记录状态为待处理
        reset_count = 0
        for passport in passports:
            if passport.processing_status == 'failed':
                passport.processing_status = 'pending'
                await passport.save()
                reset_count += 1
        
        # 重新启动识别任务
        from src.services.passport_task_service import passport_task_service
        
        await passport_task_service.start_task_processor()
        
        success = await passport_task_service.submit_recognition_task(
            task_id=task_id,
            user_id=str(current_user.id)
        )
        
        if success:
            return APIResponse(
                success=True,
                message=f"识别任务 {task_id} 重启成功，重置了 {reset_count} 个失败记录"
            )
        else:
            raise HTTPException(status_code=500, detail="重启识别任务失败")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"重启识别任务失败: {e}")
        raise HTTPException(status_code=500, detail="重启识别任务失败")


@router.get("/task/{task_id}/stats", response_model=TaskStatsResponse)
async def get_task_stats(
    task_id: str,
    current_user: User = Depends(get_current_user)
):
    """
    获取任务统计信息
    """
    try:
        # 获取任务的所有护照记录
        passports = await Passport.filter(
            task_id=task_id,
            user_id=current_user.id
        ).all()
        
        if not passports:
            # 如果任务不存在，返回空统计
            return TaskStatsResponse(
                task_id=task_id,
                total_count=0,
                pending_count=0,
                processing_count=0,
                completed_count=0,
                failed_count=0
            )
        
        # 统计各状态数量
        pending_count = sum(1 for p in passports if p.processing_status == 'pending')
        processing_count = sum(1 for p in passports if p.processing_status == 'processing')
        completed_count = sum(1 for p in passports if p.processing_status == 'completed')
        failed_count = sum(1 for p in passports if p.processing_status == 'failed')
        
        return TaskStatsResponse(
            task_id=task_id,
            total_count=len(passports),
            pending_count=pending_count,
            processing_count=processing_count,
            completed_count=completed_count,
            failed_count=failed_count
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务统计失败: {e}")
        raise HTTPException(status_code=500, detail="获取任务统计失败")


@router.get("/s3/test", response_model=APIResponse)
async def test_s3_connection(
    current_user: User = Depends(get_current_user)
):
    """
    测试S3连接
    """
    try:
        success, message = await s3_service.test_connection()
        
        if success:
            logger.info(f"S3连接测试成功: {message}")
            return APIResponse(
                success=True,
                message=message,
                data={
                    "s3_available": True,
                    "bucket": s3_service.bucket_name,
                    "key_prefix": s3_service.key_prefix
                }
            )
        else:
            logger.warning(f"S3连接测试失败: {message}")
            return APIResponse(
                success=False,
                message=message,
                data={
                    "s3_available": False,
                    "bucket": s3_service.bucket_name,
                    "key_prefix": s3_service.key_prefix
                }
            )
            
    except Exception as e:
        logger.error(f"S3连接测试异常: {e}")
        raise HTTPException(status_code=500, detail=f"S3连接测试失败: {e}")


@router.get("/file/{passport_id}")
async def get_passport_file(
    passport_id: int,
    current_user: User = Depends(get_current_user)
):
    """
    获取护照文件（通过护照ID）

    Args:
        passport_id: 护照记录ID
        current_user: 当前用户

    Returns:
        重定向到预签名URL或返回错误
    """
    try:
        # 查找护照记录（只查询未删除的记录）
        passport = await Passport.get_or_none(id=passport_id, user_id=current_user.id, is_deleted=False)
        if not passport:
            raise HTTPException(status_code=404, detail="护照记录不存在")

        file_url = passport.uploaded_image_url

        # 如果是S3 URL，生成预签名URL
        if file_url and file_url.startswith('http') and 'oss.' in file_url:
            # 提取S3键
            try:
                # 解析S3 URL获取键
                from urllib.parse import urlparse
                parsed_url = urlparse(file_url)
                # 移除开头的斜杠和存储桶名称
                path_parts = parsed_url.path.strip('/').split('/', 1)
                if len(path_parts) > 1:
                    s3_key = path_parts[1]  # 跳过存储桶名称
                else:
                    s3_key = path_parts[0]

                # 生成预签名URL
                presigned_url = await s3_service.generate_presigned_url(s3_key)
                if presigned_url:
                    logger.info(f"为护照 {passport_id} 生成预签名URL成功")
                    return RedirectResponse(url=presigned_url, status_code=302)
                else:
                    logger.warning(f"为护照 {passport_id} 生成预签名URL失败")
                    # 如果预签名失败，返回原始URL
                    return RedirectResponse(url=file_url, status_code=302)

            except Exception as e:
                logger.error(f"处理S3 URL失败: {e}")
                # 如果处理失败，返回原始URL
                return RedirectResponse(url=file_url, status_code=302)

        # 如果是本地文件URL，直接返回
        elif file_url:
            return RedirectResponse(url=file_url, status_code=302)

        else:
            raise HTTPException(status_code=404, detail="文件URL不存在")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取护照文件失败: {e}")
        raise HTTPException(status_code=500, detail="获取文件失败")


@router.get("/s3-proxy/{bucket}/{path:path}")
async def s3_file_proxy(
    bucket: str,
    path: str
):
    """
    S3文件访问代理（公共接口，无需认证）

    Args:
        bucket: S3存储桶名称
        path: 文件路径

    Returns:
        重定向到预签名URL
    """
    try:
        # 验证存储桶名称
        if bucket != s3_service.bucket_name:
            raise HTTPException(status_code=400, detail="无效的存储桶名称")

        # 生成预签名URL
        presigned_url = await s3_service.generate_presigned_url(path)
        if presigned_url:
            logger.info(f"为路径 {path} 生成预签名URL成功")
            return RedirectResponse(url=presigned_url, status_code=302)
        else:
            logger.warning(f"为路径 {path} 生成预签名URL失败")
            raise HTTPException(status_code=500, detail="生成文件访问链接失败")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"S3文件代理失败: {e}")
        raise HTTPException(status_code=500, detail="文件访问失败")


@router.get("/concurrent-config", summary="获取护照识别并发配置")
async def get_concurrent_config(
    current_user: User = Depends(get_current_user)
):
    """获取当前护照识别并发处理配置"""
    from src.services.passport_task_service import passport_task_service

    return {
        "max_concurrent_passports": passport_task_service.get_max_concurrent_passports(),
        "current_running_tasks": len(passport_task_service.running_tasks),
        "available_slots": passport_task_service.semaphore._value
    }


@router.post("/concurrent-config", summary="设置护照识别并发配置")
async def set_concurrent_config(
    max_concurrent: int = Form(..., description="最大并发处理护照数量（1-20）"),
    current_user: User = Depends(get_current_user)
):
    """设置护照识别并发处理数量"""
    try:
        from src.services.passport_task_service import passport_task_service

        # 验证用户权限（可以根据需要添加管理员权限检查）
        # if not current_user.is_admin:
        #     raise HTTPException(status_code=403, detail="需要管理员权限")

        # 设置并发数
        passport_task_service.set_max_concurrent_passports(max_concurrent)

        logger.info(f"用户 {current_user.username} 设置护照识别并发数为: {max_concurrent}")

        return {
            "message": f"护照识别并发数已设置为 {max_concurrent}",
            "max_concurrent_passports": max_concurrent,
            "previous_value": passport_task_service.get_max_concurrent_passports()
        }

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"设置并发配置失败: {e}")
        raise HTTPException(status_code=500, detail="设置失败")