"""
用户密码管理服务
使用与系统设置相同的加密方式
"""
import hashlib
import secrets
from typing import Optional
from src.core.encryption import encryption_service


class PasswordService:
    """密码管理服务类"""
    
    @staticmethod
    def hash_password(password: str, salt: Optional[str] = None) -> tuple[str, str]:
        """
        对密码进行哈希处理
        
        Args:
            password: 明文密码
            salt: 可选的盐值，如果不提供则生成新的
            
        Returns:
            tuple: (加密后的密码, 盐值)
        """
        if not salt:
            salt = secrets.token_hex(16)  # 生成32字符的随机盐值
        
        # 使用SHA256进行哈希
        password_hash = hashlib.sha256((password + salt).encode()).hexdigest()
        
        # 使用系统设置的加密服务进行二次加密
        encrypted_password = encryption_service.encrypt(password_hash)
        
        return encrypted_password, salt
    
    @staticmethod
    def verify_password(password: str, encrypted_password: str, salt: str) -> bool:
        """
        验证密码
        
        Args:
            password: 明文密码
            encrypted_password: 加密后的密码
            salt: 盐值
            
        Returns:
            bool: 密码是否正确
        """
        try:
            # 解密存储的密码哈希
            stored_hash = encryption_service.decrypt(encrypted_password)
            
            # 计算输入密码的哈希
            input_hash = hashlib.sha256((password + salt).encode()).hexdigest()
            
            # 比较哈希值
            return stored_hash == input_hash
        except Exception:
            return False
    
    @staticmethod
    def generate_random_password(length: int = 12) -> str:
        """
        生成随机密码
        
        Args:
            length: 密码长度，默认12位
            
        Returns:
            str: 随机密码
        """
        # 包含大小写字母、数字和特殊字符
        characters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*"
        return ''.join(secrets.choice(characters) for _ in range(length))
    
    @staticmethod
    def validate_password_strength(password: str) -> tuple[bool, str]:
        """
        验证密码强度
        
        Args:
            password: 密码
            
        Returns:
            tuple: (是否符合要求, 错误信息)
        """
        if len(password) < 8:
            return False, "密码长度至少8位"
        
        if len(password) > 50:
            return False, "密码长度不能超过50位"
        
        has_upper = any(c.isupper() for c in password)
        has_lower = any(c.islower() for c in password)
        has_digit = any(c.isdigit() for c in password)
        has_special = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password)
        
        if not (has_upper and has_lower and has_digit):
            return False, "密码必须包含大写字母、小写字母和数字"
        
        return True, ""


# 全局密码服务实例
password_service = PasswordService()
