"""酒店订单管理API端点"""

import logging
import pandas as pd
from io import BytesIO, StringIO
from fastapi import APIRouter, Depends, HTTPException, Query, UploadFile, File, Form
from fastapi.responses import J<PERSON><PERSON>esponse
from typing import Optional, List
from tortoise.exceptions import DoesNotExist
from tortoise.transactions import in_transaction
from tortoise.queryset import Q
from datetime import datetime, date, time, timedelta
from decimal import Decimal, InvalidOperation
import random
import re

from src.db.models.hotel_order import HotelOrder
from src.db.models.task_to_hotel_order import TaskToHotelOrder
from src.db.models.project_task import ProjectTask
from src.db.models.project import Project
from src.db.models.user import User
from src.api.dependencies import get_current_user
from .schemas import (
    HotelOrderCreate,
    HotelOrderUpdate,
    HotelOrderResponse,
    HotelOrderListResponse,
    ExcelUploadResponse,
    ValidationError,
    ExcelValidationResponse,
    BookingRequest,
    BookingResponse,
    ClearOrdersResponse,
    CreateBookingTaskRequest,
    CreateBookingTaskResponse,
    ProjectOrderStatsResponse,
    ProjectOrderDetailStatsResponse,
    TaskOrderStatsResponse,
    PauseOrdersRequest,
    PauseOrdersResponse,
    ResetPausedOrdersRequest,
    ResetPausedOrdersResponse,
    ResetFailedOrdersRequest,
    ResetFailedOrdersResponse
)

router = APIRouter()
logger = logging.getLogger(__name__)

# 数据库字段名到中文字段名的映射
DB_FIELD_TO_CHINESE = {
    'sequence_number': '序号',
    'guest_full_name': '入住人姓名',
    'guest_surname': '入住人姓',
    'guest_given_name': '入住人名',
    'guest_nationality': '入住人国籍',
    'guest_gender': '入住人性别',
    'guest_birth_date': '入住人出生日期',
    'guest_id_type': '入住人证件类型',
    'guest_id_number': '入住人证件号码',
    'guest_id_expiry_date': '入住人证件有效期至',
    'guest_mobile_country_code': '入住人手机号国际区号',
    'guest_mobile_phone': '入住人手机号',
    'guest_email': '入住人邮箱',
    'destination': '目的地',
    'hotel_id': '酒店ID',
    'hotel_name': '酒店名称',
    'room_type': '预订房型',
    'room_count': '预订房间数量',
    'policy_name': '政策名称',
    'include_breakfast': '是否含早',
    'is_half_day_room': '是否为半日房',
    'check_in_time': '入住时间',
    'check_out_time': '离店时间',
    'is_group_booking': '是否为团房',
    'group_booking_name': '团房名称',
    'room_number': '房间号',
    'payment_method': '支付方式',
    'invoice_type': '发票类型',
    'tax_rate': '税率',
    'agreement_type': '协议类型',
    'supplier_name': '供应商名称',
    'payment_channel': '支付渠道',
    'payment_transaction_id': '支付流水号',
    'cost_per_room': '对供成本（每间房成本）',
    'hidden_service_fee': '隐藏手续费',
    'cancellation_policy': '取消规则',
    'is_violation': '是否违规',
    'contact_person': '联系人姓名',
    'contact_mobile_country_code': '联系人手机号国际区号',
    'contact_mobile_phone': '联系人手机号',
    'order_remarks': '订单备注',
    'trip_submission_item': '行程提交项',
    'approver': '审批人',
    'roommate_name': '同住人姓名',
    'roommate_surname': '同住人姓',
    'roommate_given_name': '同住人名',
    'roommate_nationality': '同住人国籍',
    'roommate_gender': '同住人性别',
    'roommate_birth_date': '同住人出生日期',
    'roommate_id_type': '同住人证件类型',
    'roommate_id_number': '同住人证件号码',
    'roommate_id_expiry_date': '同住人证件有效期至',
    'roommate_mobile_country_code': '同住人手机号国际区号',
    'roommate_mobile_phone': '同住人手机号',
    'roommate_email': '同住人邮箱',
    'company_name': '公司名称',
    'amount': '对客金额',
    'booking_agent': '代订人',
    'order_number': '订单号',
    'bill_number': '账单号'
}

# 完整的酒店订单字段映射 - 基于61个字段的Excel模板
EXCEL_FIELD_MAPPING = {
    # Excel列名 -> 数据库字段名
    '序号': 'sequence_number',
    '入住人姓名': 'guest_full_name',
    '入住人姓': 'guest_surname', 
    '入住人名': 'guest_given_name',
    '入住人国籍': 'guest_nationality',
    '入住人性别': 'guest_gender',
    '入住人出生日期': 'guest_birth_date',
    '入住人证件类型': 'guest_id_type',
    '入住人证件号码': 'guest_id_number',
    '入住人证件有效期至': 'guest_id_expiry_date',
    '入住人手机号国际区号': 'guest_mobile_country_code',
    '入住人手机号': 'guest_mobile_phone',
    '入住人邮箱': 'guest_email',
    '目的地': 'destination',
    '酒店ID': 'hotel_id',
    '预订房型': 'room_type',
    '预订房间数量': 'room_count',
    '政策名称': 'policy_name',
    '是否含早': 'include_breakfast',
    '是否为半日房': 'is_half_day_room',
    '入住时间': 'check_in_time',
    '离店时间': 'check_out_time',
    '是否为团房': 'is_group_booking',
    '团房名称': 'group_booking_name',
    '房间号': 'room_number',
    '支付方式': 'payment_method',
    '发票类型': 'invoice_type',
    '税率': 'tax_rate',
    '协议类型': 'agreement_type',
    '供应商名称': 'supplier_name',
    '支付渠道': 'payment_channel',
    '支付流水号': 'payment_transaction_id',
    '对供成本（每间房成本）': 'cost_per_room',
    '隐藏手续费': 'hidden_service_fee',
    '取消规则': 'cancellation_policy',
    '是否违规': 'is_violation',
    '联系人姓名': 'contact_person',
    '联系人手机号国际区号': 'contact_mobile_country_code',
    '联系人手机号': 'contact_mobile_phone',
    '订单备注': 'order_remarks',
    '行程提交项': 'trip_submission_item',
    '审批人': 'approver',
    '同住人姓名': 'roommate_name',
    '同住人姓': 'roommate_surname',
    '同住人名': 'roommate_given_name',
    '同住人国籍': 'roommate_nationality',
    '同住人性别': 'roommate_gender',
    '同住人出生日期': 'roommate_birth_date',
    '同住人证件类型': 'roommate_id_type',
    '同住人证件号码': 'roommate_id_number',
    '同住人证件有效期至': 'roommate_id_expiry_date',
    '同住人手机号国际区号': 'roommate_mobile_country_code',
    '同住人手机号': 'roommate_mobile_phone',
    '同住人邮箱': 'roommate_email',
    '公司名称': 'company_name',
    '酒店名称': 'hotel_name',
    '对客金额': 'amount',
    '代订人': 'booking_agent',
    '订单号': 'order_number',
    '账单号': 'bill_number'
}

def validate_id_number(id_number: str, id_type: str) -> bool:
    """验证证件号码格式"""
    if not id_number:
        return False
        
    id_number = id_number.strip().upper()
    
    # 身份证号码验证
    if id_type == '身份证':
        if len(id_number) != 18:
            return False
        # 简单的格式验证
        pattern = r'^\d{17}[\dX]$'
        return bool(re.match(pattern, id_number))
    
    # 护照号码验证（公务护照和普通护照）
    elif id_type in ['公务护照', '普通护照']:
        # 护照号码格式：字母开头，后跟8-9位数字或字母
        pattern = r'^[A-Z][A-Z0-9]{7,8}$'
        return bool(re.match(pattern, id_number))
    
    # 港澳通行证验证
    elif id_type == '港澳通行证':
        # 格式：H/M + 8位或11位数字
        pattern = r'^[HM]\d{8,11}$'
        return bool(re.match(pattern, id_number))
    
    # 台胞证验证
    elif id_type == '台胞证':
        # 新版台胞证格式：8位数字
        pattern = r'^\d{8}$'
        return bool(re.match(pattern, id_number))
    
    # 其他证件类型
    else:
        # 至少包含3个字符
        return len(id_number) >= 3

def validate_phone(phone: str) -> bool:
    """验证手机号格式"""
    if not phone:
        return False
    
    phone = phone.strip()
    # 支持国内手机号（11位）和国际号码（带国际区号，7-15位数字）
    pattern = r'^(\+?\d{1,3}[-\s]?)?\d{7,15}$'
    return bool(re.match(pattern, phone))

def validate_email(email: str) -> bool:
    """验证邮箱格式"""
    if not email:
        return False
    
    email = email.strip()
    # 基本的邮箱格式验证
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))

def is_id_expired(expiry_date: date) -> bool:
    """检查证件是否过期"""
    if not expiry_date:
        return True
    return expiry_date < date.today()

async def check_duplicate_order(project_id: int, id_number: str, check_in_time: str, hotel_id: str, guest_name: str = None, mobile_phone: str = None) -> bool:
    """检查是否存在重复订单"""
    try:
        # 如果有证件号码，使用证件号码+入住时间+酒店ID检查
        if id_number and id_number.strip():
            existing_order = await HotelOrder.filter(
                project_id=project_id,
                guest_id_number=id_number,
                check_in_time=check_in_time,
                hotel_id=hotel_id,
                is_deleted=False
            ).first()
            return existing_order is not None

        # 如果没有证件号码，使用姓名+手机号+入住时间+酒店ID检查
        elif guest_name and mobile_phone:
            existing_order = await HotelOrder.filter(
                project_id=project_id,
                guest_full_name=guest_name,
                guest_mobile_phone=mobile_phone,
                check_in_time=check_in_time,
                hotel_id=hotel_id,
                is_deleted=False
            ).first()
            return existing_order is not None

        # 如果既没有证件号码也没有姓名和手机号，不检查重复
        return False
    except Exception as e:
        logger.error(f"检查重复订单时发生错误: {str(e)}")
        return False

def validate_hotel_order_data(row_data: dict, row_index: int) -> list:
    """验证单行酒店订单数据"""
    errors = []
    
    # 基础必填字段验证（所有情况下都必填）
    basic_required_fields = {
        'guest_full_name': '入住人姓名',
        'guest_mobile_phone': '入住人手机号',
        'check_in_time': '入住时间',
        'check_out_time': '离店时间',
        'hotel_id': '酒店ID',
        'room_type': '预订房型',
        'contact_person': '联系人姓名',
        'contact_mobile_phone': '联系人手机号',
        'approver': '审批人'
    }

    # 证件类型相关的条件必填字段（只有在证件类型不为空且不为身份证时才必填）
    id_type = row_data.get('guest_id_type')
    if id_type and id_type.strip() and id_type != '身份证':
        # 当证件类型不为空且不为身份证时，这些字段才必填
        basic_required_fields.update({
            'guest_surname': '入住人姓',
            'guest_given_name': '入住人名',
            'guest_nationality': '入住人国籍',
            'guest_gender': '入住人性别',
            'guest_birth_date': '入住人出生日期'
        })
    
    for field_key, field_name in basic_required_fields.items():
        value = row_data.get(field_key)
        if not value or (isinstance(value, str) and value.strip() == ''):
            errors.append(ValidationError(
                row=row_index,
                message=f"{field_name}为必填项",
                field=field_name,
                error_type="required",
                value=str(value) if value else ""
            ))
    
    # 证件类型枚举验证（只在证件类型不为空时验证）
    id_type = row_data.get('guest_id_type')
    valid_id_types = [
        '身份证', '公务护照', '普通护照', '港澳通行证', '台胞证', '回乡证',
        '军人证', '海员证', '台湾通行证', '外国永久居留证', '港澳台居民居住证', '其他'
    ]

    if id_type and id_type.strip() and id_type not in valid_id_types:
        errors.append(ValidationError(
            row=row_index,
            message=f"证件类型必须是以下值之一：{', '.join(valid_id_types)}",
            field="入住人证件类型",
            error_type="invalid_value",
            value=id_type
        ))
    
    # 证件有效期条件必填验证（只在证件类型不为空且不为身份证时验证）
    if id_type and id_type.strip() and id_type != '身份证':
        # 只验证证件有效期
        expiry_date = row_data.get('guest_id_expiry_date')
        if not expiry_date or (isinstance(expiry_date, str) and expiry_date.strip() == ''):
            errors.append(ValidationError(
                row=row_index,
                message="证件类型不为身份证时，入住人证件有效期至为必填项",
                field="入住人证件有效期至",
                error_type="required",
                value=str(expiry_date) if expiry_date else ""
            ))
    
    # 证件号码格式验证（只在证件号码和证件类型都不为空时验证）
    id_number = row_data.get('guest_id_number')
    if id_number and id_type and not validate_id_number(id_number, id_type):
        errors.append(ValidationError(
            row=row_index,
            message="证件号码格式不正确",
            field="入住人证件号码",
            error_type="invalid_format",
            value=id_number
        ))
    
    # 手机号格式验证
    mobile_phone = row_data.get('guest_mobile_phone')
    if mobile_phone and not validate_phone(mobile_phone):
        errors.append(ValidationError(
            row=row_index,
            message="手机号码格式不正确",
            field="入住人手机号",
            error_type="invalid_format",
            value=mobile_phone
        ))
    
    # 联系人手机号格式验证
    contact_phone = row_data.get('contact_mobile_phone')
    if contact_phone and not validate_phone(contact_phone):
        errors.append(ValidationError(
            row=row_index,
            message="联系人手机号码格式不正确",
            field="联系人手机号",
            error_type="invalid_format",
            value=contact_phone
        ))
    
    # 邮箱格式验证
    guest_email = row_data.get('guest_email')
    if guest_email and not validate_email(guest_email):
        errors.append(ValidationError(
            row=row_index,
            message="邮箱格式不正确",
            field="入住人邮箱",
            error_type="invalid_format",
            value=guest_email
        ))
    
    roommate_email = row_data.get('roommate_email')
    if roommate_email and not validate_email(roommate_email):
        errors.append(ValidationError(
            row=row_index,
            message="同住人邮箱格式不正确",
            field="同住人邮箱",
            error_type="invalid_format",
            value=roommate_email
        ))

    # 同住人字段条件必填验证：同住人姓名填写后，根据证件类型决定其他字段是否必填
    roommate_name = row_data.get('roommate_name')
    roommate_id_type = row_data.get('roommate_id_type')

    if roommate_name and roommate_name.strip():
        # 当同住人姓名填写后，基础字段必填（但证件相关字段除外）
        roommate_basic_required_fields = {
            'roommate_mobile_phone': '同住人手机号'
        }

        # 如果同住人证件类型不为空且不为身份证，则证件相关字段必填
        if roommate_id_type and roommate_id_type.strip() and roommate_id_type != '身份证':
            roommate_basic_required_fields.update({
                'roommate_surname': '同住人姓',
                'roommate_given_name': '同住人名',
                'roommate_nationality': '同住人国籍',
                'roommate_gender': '同住人性别',
                'roommate_birth_date': '同住人出生日期',
                'roommate_id_number': '同住人证件号码',
                'roommate_id_expiry_date': '同住人证件有效期至'
            })

        # 验证必填字段
        for field_key, field_name in roommate_basic_required_fields.items():
            value = row_data.get(field_key)
            if not value or (isinstance(value, str) and value.strip() == ''):
                if roommate_id_type and roommate_id_type.strip() and roommate_id_type != '身份证':
                    message = f"同住人证件类型不为空且不为身份证时，{field_name}为必填项"
                else:
                    message = f"同住人姓名填写后，{field_name}为必填项"

                errors.append(ValidationError(
                    row=row_index,
                    message=message,
                    field=field_name,
                    error_type="required",
                    value=str(value) if value else ""
                ))


    return errors

async def validate_excel_data(df: pd.DataFrame, project_id: int) -> ExcelValidationResponse:
    """验证Excel数据"""
    total_rows = len(df)
    errors = []
    valid_rows = 0
    error_rows = 0
    duplicate_rows = []
    duplicate_combinations_in_file = set()  # 存储文件内的重复组合
    
    # 检查必需的列是否存在
    required_columns = ['入住人姓名', '酒店ID']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        raise HTTPException(status_code=400, detail=f"Excel文件缺少必需的列: {missing_columns}")
    
    for index, row in df.iterrows():
        row_index = index + 1
        row_errors = []
        
        # 准备数据进行验证
        row_data = {}
        for excel_field, db_field in EXCEL_FIELD_MAPPING.items():
            if excel_field in row:
                value = row[excel_field]
                # 处理空值
                if pd.isna(value):
                    value = ''
                # 处理出生日期字段
                elif db_field in ['guest_birth_date', 'roommate_birth_date']:
                    value = parse_birth_date(value)
                # 处理数值类型
                elif isinstance(value, (int, float)):
                    value = str(value)
                # 处理其他类型
                else:
                    value = str(value)
                row_data[db_field] = value.strip() if isinstance(value, str) else value

        # 自动补充入住人信息
        row_data = auto_complete_guest_info(row_data, 'guest')

        # 自动补充同住人信息
        row_data = auto_complete_guest_info(row_data, 'roommate')

        # 自动补充公司名称
        row_data = await auto_complete_company_name(row_data, project_id)

        # 检查重复订单
        id_number = row_data.get('guest_id_number', '')
        hotel_id = row_data.get('hotel_id', '')
        check_in_time = parse_excel_date(row.get('入住时间'))
        guest_name = row_data.get('guest_full_name', '')
        mobile_phone = row_data.get('guest_mobile_phone', '')

        # 只有在有足够信息时才检查重复
        should_check_duplicate = (id_number and hotel_id and check_in_time) or (guest_name and mobile_phone and hotel_id and check_in_time)

        if should_check_duplicate:
            # 创建组合标识符
            if id_number:
                combination_key = f"id:{id_number}|{hotel_id}|{check_in_time}"
                duplicate_field = "入住人证件号码"
                duplicate_value = f"{id_number} | {hotel_id} | {check_in_time}"
            else:
                combination_key = f"name:{guest_name}|{mobile_phone}|{hotel_id}|{check_in_time}"
                duplicate_field = "入住人姓名"
                duplicate_value = f"{guest_name} | {mobile_phone} | {hotel_id} | {check_in_time}"

            # 检查文件内重复
            if combination_key in duplicate_combinations_in_file:
                row_errors.append(ValidationError(
                    row=row_index,
                    message="文件内存在重复的订单，导入时将忽略",
                    field=duplicate_field,
                    error_type="duplicate_in_file",
                    value=duplicate_value
                ))
                duplicate_rows.append(row_index)
            else:
                duplicate_combinations_in_file.add(combination_key)

                # 检查数据库内重复订单
                is_duplicate = await check_duplicate_order(project_id, id_number, check_in_time, hotel_id, guest_name, mobile_phone)
                if is_duplicate:
                    row_errors.append(ValidationError(
                        row=row_index,
                        message="该订单已存在于项目中，导入时将忽略",
                        field=duplicate_field,
                        error_type="duplicate_in_db",
                        value=duplicate_value
                    ))
                    duplicate_rows.append(row_index)
        
        # 进行详细的字段验证
        if row_index not in duplicate_rows:  # 只对非重复数据进行验证
            field_validation_errors = validate_hotel_order_data(row_data, row_index)
            if field_validation_errors:
                row_errors.extend(field_validation_errors)
                error_rows += 1  # 只有非重复数据的验证错误才计入错误行数
            else:
                valid_rows += 1
        
        # 将所有错误添加到错误列表
        if row_errors:
            errors.extend(row_errors)
    
    # 所有数据都可以继续导入，即使有错误
    should_continue = True
    
    # 计算实际可导入的行数（总行数减去重复行数）
    importable_rows = total_rows - len(duplicate_rows)
    
    return ExcelValidationResponse(
        total_rows=total_rows,
        valid_rows=valid_rows,
        error_rows=error_rows,
        errors=errors,
        should_continue=should_continue,
        duplicate_rows=duplicate_rows
    )

def parse_excel_date(value):
    """解析Excel日期值"""
    if pd.isna(value) or value == "" or value is None:
        return ""
    
    # 如果已经是字符串格式，直接返回
    if isinstance(value, str):
        return value.strip()
    
    # 如果是datetime类型，格式化为字符串（保留时间部分）
    if isinstance(value, (datetime, pd.Timestamp)):
        return value.strftime('%Y-%m-%d %H:%M:%S')
    
    # 如果是date类型
    if isinstance(value, date):
        return value.strftime('%Y-%m-%d')
    
    # 尝试转换为字符串
    try:
        return str(value).strip()
    except:
        return ""

def parse_birth_date(value):
    """解析出生日期，格式化为YYYY-MM-DD格式"""
    if pd.isna(value) or value == "" or value is None:
        return ""
    
    # 如果已经是字符串格式，检查是否已经是目标格式
    if isinstance(value, str):
        value = value.strip()
        # 如果已经是YYYY-MM-DD格式，直接返回
        if len(value) == 10 and value.count('-') == 2:
            return value
        # 如果是YYYY_MM_DD格式，转换为YYYY-MM-DD
        if len(value) == 10 and value.count('_') == 2:
            return value.replace('_', '-')
        # 尝试解析其他格式的日期字符串
        try:
            # 尝试解析常见日期格式
            import re
            # 替换常见分隔符为标准分隔符
            date_str = re.sub(r'[/.]', '-', value)
            if re.match(r'^\d{4}-\d{1,2}-\d{1,2}$', date_str):
                # 标准化为两位数月日
                parts = date_str.split('-')
                if len(parts) == 3:
                    year, month, day = parts
                    return f"{year}-{month.zfill(2)}-{day.zfill(2)}"
            return value
        except:
            return value
    
    # 如果是datetime类型，格式化为YYYY-MM-DD格式
    if isinstance(value, (datetime, pd.Timestamp)):
        return value.strftime('%Y-%m-%d')
    
    # 如果是date类型
    if isinstance(value, date):
        return value.strftime('%Y-%m-%d')
    
    # 尝试转换为字符串
    try:
        return str(value).strip()
    except:
        return ""

def parse_id_expiry_date(value):
    """解析证件有效期至日期，格式化为YYYY-MM-DD格式"""
    if pd.isna(value) or value == "" or value is None:
        return ""
    
    # 如果已经是字符串格式，检查是否已经是目标格式
    if isinstance(value, str):
        value = value.strip()
        # 如果已经是YYYY-MM-DD格式，直接返回
        if len(value) == 10 and value.count('-') == 2:
            return value
        # 如果是YYYY_MM_DD格式，转换为YYYY-MM-DD
        if len(value) == 10 and value.count('_') == 2:
            return value.replace('_', '-')
        # 尝试解析其他格式的日期字符串
        try:
            # 尝试解析常见日期格式
            import re
            # 替换常见分隔符为标准分隔符
            date_str = re.sub(r'[/.]', '-', value)
            if re.match(r'^\d{4}-\d{1,2}-\d{1,2}$', date_str):
                # 标准化为两位数月日
                parts = date_str.split('-')
                if len(parts) == 3:
                    year, month, day = parts
                    return f"{year}-{month.zfill(2)}-{day.zfill(2)}"
            return value
        except:
            return value
    
    # 如果是datetime类型，格式化为YYYY-MM-DD格式
    if isinstance(value, (datetime, pd.Timestamp)):
        return value.strftime('%Y-%m-%d')
    
    # 如果是date类型
    if isinstance(value, date):
        return value.strftime('%Y-%m-%d')
    
    # 尝试转换为字符串
    try:
        return str(value).strip()
    except:
        return ""

def parse_excel_time(value):
    """解析Excel时间值"""
    if pd.isna(value) or value == "" or value is None:
        return ""
    
    # 如果已经是字符串格式，直接返回
    if isinstance(value, str):
        return value.strip()
    
    # 如果是datetime类型，格式化为字符串
    if isinstance(value, (datetime, pd.Timestamp)):
        return value.strftime('%Y-%m-%d %H:%M')
    
    # 如果是time类型
    if isinstance(value, time):
        return value.strftime('%H:%M')
    
    # 尝试转换为字符串
    try:
        return str(value).strip()
    except:
        return ""

def safe_str(value):
    """安全转换为字符串"""
    if pd.isna(value) or value == "" or value is None:
        return ""
    return str(value).strip()

class SafeDecimalError(Exception):
    def __init__(self, field_name, row_number, value, original_exception):
        self.field_name = field_name
        self.row_number = row_number
        self.value = value
        self.original_exception = original_exception
        super().__init__(f"字段: {field_name}, 行: {row_number}, 原始值: {repr(value)}, 错误: {original_exception}")

def safe_decimal(value, field_name=None, row_number=None) -> Decimal:
    """安全转换为Decimal类型，异常时抛出详细信息"""
    if pd.isna(value) or value == "" or value is None:
        return Decimal('0')
    try:
        if isinstance(value, str):
            value = value.strip().replace('¥', '').replace('$', '').replace(',', '')

            # 特殊处理税率字段的百分号格式
            if field_name == 'tax_rate' and value.endswith('%'):
                # 移除百分号，将百分比转换为数值
                # 例如：6% -> 6, 3% -> 3
                value = value.replace('%', '')

        return Decimal(str(float(value)))
    except Exception as e:
        raise SafeDecimalError(field_name, row_number, value, e)

def safe_int(value):
    """安全转换为整数"""
    if pd.isna(value) or value == "" or value is None:
        return 0
    try:
        return int(float(value))
    except:
        return 0

def parse_boolean(value):
    """解析布尔值"""
    if pd.isna(value):
        return False
    if isinstance(value, bool):
        return value
    if isinstance(value, (int, float)):
        return bool(value)
    if isinstance(value, str):
        value = value.lower().strip()
        return value in ['true', '1', 'yes', 'y', '是', '√', '✓']
    return False

def parse_yes_no(value):
    """解析是否字段，返回'是'或'否'字符串"""
    if pd.isna(value):
        return "否"
    if isinstance(value, bool):
        return "是" if value else "否"
    if isinstance(value, (int, float)):
        return "是" if value else "否"
    if isinstance(value, str):
        value = value.strip()
        if value in ['true', '1', 'yes', 'y', '是', '√', '✓', 'True', 'TRUE', 'YES', 'Y']:
            return "是"
        elif value in ['false', '0', 'no', 'n', '否', '×', '✗', 'False', 'FALSE', 'NO', 'N']:
            return "否"
        else:
            # 如果不是明确的是/否值，则保持原值（如果是"是"或"否"）
            if value in ['是', '否']:
                return value
            else:
                return "否"  # 默认返回"否"
    return "否"


def is_id_card_number(id_number: str) -> bool:
    """判断是否为身份证号码格式"""
    if not id_number:
        return False

    id_number = str(id_number).strip()
    # 18位身份证号码格式验证
    pattern = r'^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$'
    return bool(re.match(pattern, id_number))


def extract_birth_date_from_id_card(id_number: str) -> str:
    """从身份证号码提取出生日期"""
    if not id_number or len(id_number) != 18:
        return ""

    try:
        year = id_number[6:10]
        month = id_number[10:12]
        day = id_number[12:14]
        return f"{year}-{month}-{day}"
    except:
        return ""


def extract_gender_from_id_card(id_number: str) -> str:
    """从身份证号码提取性别"""
    if not id_number or len(id_number) != 18:
        return ""

    try:
        # 第17位数字，奇数为男，偶数为女
        gender_digit = int(id_number[16])
        return "男" if gender_digit % 2 == 1 else "女"
    except:
        return ""


def split_full_name(full_name: str) -> tuple:
    """拆分姓名为姓和名"""
    if not full_name:
        return "", ""

    full_name = str(full_name).strip()
    if len(full_name) == 0:
        return "", ""

    surname = full_name[0]
    given_name = full_name[1:] if len(full_name) > 1 else ""
    return surname, given_name


def generate_random_expiry_date() -> str:
    """生成六个月后的随机证件有效期"""
    # 六个月后的基准日期
    base_date = datetime.now() + timedelta(days=180)

    # 在基准日期前后30天内随机选择一天
    random_days = random.randint(-30, 30)
    expiry_date = base_date + timedelta(days=random_days)

    return expiry_date.strftime("%Y-%m-%d")


def auto_complete_guest_info(row_data: dict, prefix: str = 'guest') -> dict:
    """
    自动补充入住人或同住人信息
    prefix: 'guest' 或 'roommate'
    """
    completed_data = row_data.copy()

    # 获取相关字段 - 注意同住人的姓名字段是 roommate_name，不是 roommate_full_name
    id_number = completed_data.get(f'{prefix}_id_number', '').strip()
    id_type = completed_data.get(f'{prefix}_id_type', '').strip()

    if prefix == 'guest':
        full_name = completed_data.get('guest_full_name', '').strip()
    else:  # roommate
        full_name = completed_data.get('roommate_name', '').strip()

    logger.info(f"开始自动补充{prefix}信息: id_number={id_number}, id_type={id_type}, full_name={full_name}")

    # 如果证件号码有内容并且是身份证格式
    if id_number and is_id_card_number(id_number):
        logger.info(f"检测到身份证号码: {id_number}，开始自动补齐信息")

        # 补齐证件类型
        if not id_type:
            completed_data[f'{prefix}_id_type'] = '身份证'
            logger.info(f"自动补齐{prefix}证件类型: 身份证")

        # 补齐国籍
        if not completed_data.get(f'{prefix}_nationality', '').strip():
            completed_data[f'{prefix}_nationality'] = '中国'
            logger.info(f"自动补齐{prefix}国籍: 中国")

        # 补齐性别
        if not completed_data.get(f'{prefix}_gender', '').strip():
            gender = extract_gender_from_id_card(id_number)
            if gender:
                completed_data[f'{prefix}_gender'] = gender
                logger.info(f"自动补齐{prefix}性别: {gender}")

        # 补齐出生日期
        if not completed_data.get(f'{prefix}_birth_date', '').strip():
            birth_date = extract_birth_date_from_id_card(id_number)
            if birth_date:
                completed_data[f'{prefix}_birth_date'] = birth_date
                logger.info(f"自动补齐{prefix}出生日期: {birth_date}")

        # 补齐证件有效期
        if not completed_data.get(f'{prefix}_id_expiry_date', '').strip():
            expiry_date = generate_random_expiry_date()
            completed_data[f'{prefix}_id_expiry_date'] = expiry_date
            logger.info(f"自动补齐{prefix}证件有效期: {expiry_date}")

    # 如果姓名有内容，补齐姓和名
    if full_name:
        surname = completed_data.get(f'{prefix}_surname', '').strip()
        given_name = completed_data.get(f'{prefix}_given_name', '').strip()

        if not surname or not given_name:
            auto_surname, auto_given_name = split_full_name(full_name)
            if not surname and auto_surname:
                completed_data[f'{prefix}_surname'] = auto_surname
                logger.info(f"自动补齐{prefix}姓: {auto_surname}")
            if not given_name and auto_given_name:
                completed_data[f'{prefix}_given_name'] = auto_given_name
                logger.info(f"自动补齐{prefix}名: {auto_given_name}")

    return completed_data


async def auto_complete_company_name(row_data: dict, project_id: int) -> dict:
    """
    自动补充公司名称
    如果公司名称为空，则从项目的client_name补充
    """
    completed_data = row_data.copy()

    # 检查公司名称是否为空
    company_name = completed_data.get('company_name', '').strip()
    if not company_name:
        try:
            # 从数据库获取项目信息
            from src.db.models.project import Project
            project = await Project.get(id=project_id)
            if project and project.client_name:
                completed_data['company_name'] = project.client_name.strip()
                logger.info(f"自动补齐公司名称: {project.client_name}")
        except Exception as e:
            logger.warning(f"获取项目信息失败: {e}")

    return completed_data


def parse_phone_with_country_code(phone_value, country_code_value=None):
    """解析手机号和国际区号（返回不带+号的区号）"""
    phone = safe_str(phone_value)
    country_code = safe_str(country_code_value) if country_code_value else "86"

    # 如果手机号为空，返回空值
    if not phone:
        return "", ""

    # 如果手机号包含国际区号，分离出来
    if phone.startswith('+'):
        # 格式如: +86 13800138000 或 +8613800138000
        parts = phone.split(' ', 1)
        if len(parts) == 2:
            country_code = parts[0].lstrip('+')  # 移除+号
            phone = parts[1]
        else:
            # 没有空格分隔，尝试提取前几位作为国际区号
            if len(phone) > 3:
                country_code = phone[1:3]  # 移除+号，提取86
                phone = phone[3:]
    elif phone.startswith('86') and len(phone) > 12:
        # 格式如: 8613800138000
        country_code = "86"
        phone = phone[2:]

    # 确保国际区号不包含+号
    if country_code and country_code.startswith('+'):
        country_code = country_code[1:]

    # 如果没有提供国际区号，默认使用86
    if not country_code:
        country_code = "86"
    
    return phone, country_code

@router.post("/validate-excel", response_model=ExcelValidationResponse, summary="验证Excel文件数据")
async def validate_excel(
    project_id: int = Form(...),
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user)
):
    """
    验证Excel文件并返回验证结果，不进行实际导入
    
    功能特性：
    - 验证文件格式和项目存在性
    - 必填字段验证
    - 身份证号码格式和有效期验证
    - 重复身份证号检查
    - 邮箱和手机号格式验证
    - 返回详细的验证错误列表
    """
    logger.info(f"开始验证Excel文件，用户: {current_user.username}, 项目ID: {project_id}, 文件: {file.filename}")
    
    try:
        # 验证项目是否存在
        try:
            project = await Project.get(id=project_id)
            logger.info(f"项目验证成功: {project.project_name}")
        except DoesNotExist:
            logger.error(f"项目不存在: {project_id}")
            raise HTTPException(status_code=404, detail="项目不存在")
        
        # 验证文件类型
        if not file.filename or not file.filename.lower().endswith(('.xlsx', '.xls')):
            logger.error(f"无效的文件类型: {file.filename}")
            raise HTTPException(status_code=400, detail="请上传Excel文件(.xlsx或.xls)")
        
        # 验证文件大小
        file_content = await file.read()
        file_size = len(file_content)
        max_size = 10 * 1024 * 1024  # 10MB
        if file_size > max_size:
            logger.error(f"文件过大: {file_size} bytes")
            raise HTTPException(status_code=400, detail="文件大小不能超过10MB")
        
        logger.info(f"文件验证成功: {file.filename}, 大小: {file_size} bytes")
        
        # 读取Excel文件
        try:
            # 首先读取前5行用于格式检测
            df_preview = pd.read_excel(BytesIO(file_content), header=None, nrows=5, engine='openpyxl')
            logger.info(f"Excel文件预览成功，检测到 {len(df_preview)} 行数据")
            
            # 检测是否符合用户要求的格式（第二行为列名，第四行开始为数据）
            header_row = 1  # 第二行是header（索引从0开始，所以是1）
            skip_rows = [0, 2]  # 跳过第1行和第3行
            
            # 检查第二行是否包含预期的酒店订单列名
            if len(df_preview) >= 2:
                potential_header = df_preview.iloc[1].dropna().astype(str).str.strip()
                expected_columns = ['序号', '入住人姓名', '入住人姓', '入住人名', '入住人国籍', '入住人性别']
                
                if any(col.strip() in potential_header.values for col in expected_columns[:3]):
                    logger.info("检测到用户要求的格式：第二行为列名，第四行开始为数据")
                else:
                    # 如果第二行不是列名，则尝试检测第一行（兼容性）
                    first_row_header = df_preview.iloc[0].dropna().astype(str).str.strip()
                    if any(col.strip() in first_row_header.values for col in expected_columns[:3]):
                        header_row = 0  # 第一行是header
                        skip_rows = []  # 不跳过任何行
                        logger.info("检测到兼容格式：第一行为列名")
                    else:
                        logger.warning("未检测到标准格式，使用默认设置")
            
            # 根据检测结果读取Excel文件
            if header_row == 1:
                # 用户要求格式：第二行为列名，跳过第一行和第三行，从第四行开始读取数据
                df = pd.read_excel(BytesIO(file_content), header=1, skiprows=[2], engine='openpyxl', dtype={'税率': str})
                logger.info(f"Excel文件读取成功，使用第二行作为列名，跳过第3行，共 {len(df)} 行数据")
            else:
                # 兼容格式：第一行是header，第二行开始是数据
                df = pd.read_excel(BytesIO(file_content), header=header_row, engine='openpyxl', dtype={'税率': str})
                logger.info(f"Excel文件读取成功，header在第{header_row+1}行，共 {len(df)} 行数据")

            logger.info(f"强制税率列为字符串类型读取")
            
            # 清理列名（移除空格和特殊字符）
            original_columns = df.columns.tolist()
            df.columns = [col.strip() if isinstance(col, str) else str(col).strip() for col in df.columns]

            # 调试：检查税率列是否存在以及内容
            logger.info(f"Excel列名: {df.columns.tolist()}")
            logger.info(f"Excel列名(repr): {[repr(col) for col in df.columns.tolist()]}")

            # 检查是否有包含"税率"的列名
            tax_rate_columns = [col for col in df.columns if '税率' in str(col)]
            logger.info(f"包含'税率'的列名: {tax_rate_columns}")

            if '税率' in df.columns:
                tax_rate_values = df['税率'].dropna().head(5).tolist()
                logger.info(f"税率列前5个非空值: {tax_rate_values}")
                logger.info(f"税率列数据类型: {df['税率'].dtype}")
            else:
                logger.warning("Excel中未找到'税率'列")
                # 检查字段映射中的所有列
                for excel_field in EXCEL_FIELD_MAPPING.keys():
                    if excel_field in df.columns:
                        logger.info(f"找到映射字段: {excel_field}")
                    else:
                        logger.warning(f"未找到映射字段: {excel_field}")
            cleaned_columns = df.columns.tolist()
            
            logger.info(f"原始列名: {original_columns}")
            logger.info(f"清理后列名: {cleaned_columns}")
            
            # 检查关键列是否存在
            required_columns = ['入住人姓名']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                logger.error(f"缺少必需的列: {missing_columns}")
                logger.error(f"可用列名: {list(df.columns)}")
                raise HTTPException(status_code=400, detail=f"Excel文件缺少必需的列: {missing_columns}")
                
        except Exception as e:
            logger.error(f"Excel文件读取失败: {str(e)}")
            raise HTTPException(status_code=400, detail=f"Excel文件格式错误: {str(e)}")
        
        if df.empty:
            logger.error("Excel文件为空或无有效数据")
            raise HTTPException(status_code=400, detail="Excel文件为空或无有效数据")
        
        # 验证Excel数据
        validation_result = await validate_excel_data(df, project_id)
        
        logger.info(f"Excel验证完成: 总行数={validation_result.total_rows}, "
                   f"有效行数={validation_result.valid_rows}, "
                   f"错误行数={validation_result.error_rows}, "
                   f"错误数量={len(validation_result.errors)}")
        
        return validation_result
        
    except HTTPException:
        raise
    except Exception as e:
        error_msg = f"Excel验证处理异常: {str(e)}"
        logger.error(error_msg)
        raise HTTPException(status_code=500, detail=error_msg)

@router.post("/upload-excel", response_model=ExcelUploadResponse, summary="上传Excel文件")
async def upload_excel(
    project_id: int = Form(...),
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user)
):
    """上传Excel文件"""
    try:
        # 读取Excel文件
        file_content = await file.read()
        # 第二行为列名，跳过第三行，从第四行开始读取数据
        # 强制税率列为字符串类型读取，确保百分号格式正确处理
        df = pd.read_excel(BytesIO(file_content), header=1, skiprows=[2], engine='openpyxl', dtype={'税率': str})

        logger.info(f"Excel文件读取成功，强制税率列为字符串类型，共 {len(df)} 行数据")

        # 调试：检查税率列是否存在以及内容
        if '税率' in df.columns:
            tax_rate_values = df['税率'].dropna().head(5).tolist()
            logger.info(f"税率列前5个非空值: {tax_rate_values}")
            logger.info(f"税率列数据类型: {df['税率'].dtype}")
        else:
            logger.warning("Excel中未找到'税率'列")
        
        # 初始化计数器
        success_count = 0
        validation_failed_count = 0
        duplicate_count = 0
        validation_errors = []
        
        try:
            # 开启事务
            async with in_transaction() as connection:
                logger.info("开始事务处理")
                
                # 处理每一行数据
                for index, row in df.iterrows():
                    sequence_number = index + 1
                    logger.info(f"处理第 {sequence_number} 行数据")
                    
                    try:
                        # 准备数据进行验证
                        row_data = {}
                        for excel_field, db_field in EXCEL_FIELD_MAPPING.items():
                            if excel_field in row:
                                value = row[excel_field]
                                # 处理空值
                                if pd.isna(value):
                                    value = ''
                                # 处理数值类型
                                elif isinstance(value, (int, float)):
                                    value = str(value)
                                # 处理其他类型
                                else:
                                    value = str(value)
                                row_data[db_field] = value.strip() if isinstance(value, str) else value

                        # 检查重复订单
                        id_number = row_data.get('guest_id_number', '')
                        hotel_id = row_data.get('hotel_id', '')
                        check_in_time = parse_excel_date(row.get('入住时间'))
                        guest_name = row_data.get('guest_full_name', '')
                        mobile_phone = row_data.get('guest_mobile_phone', '')

                        # 只有在有足够信息时才检查重复
                        should_check_duplicate = (id_number and hotel_id and check_in_time) or (guest_name and mobile_phone and hotel_id and check_in_time)

                        if should_check_duplicate:
                            # 检查数据库内重复订单
                            is_duplicate = await check_duplicate_order(project_id, id_number, check_in_time, hotel_id, guest_name, mobile_phone)
                            if is_duplicate:
                                duplicate_count += 1
                                logger.warning(f"第 {sequence_number} 行数据重复")

                                if id_number:
                                    duplicate_value = f"{id_number} | {hotel_id} | {check_in_time}"
                                    duplicate_field = "入住人证件号码"
                                else:
                                    duplicate_value = f"{guest_name} | {mobile_phone} | {hotel_id} | {check_in_time}"
                                    duplicate_field = "入住人姓名"

                                validation_errors.append(ValidationError(
                                    row=sequence_number,
                                    message="该订单已存在于项目中，导入时将忽略",
                                    field=duplicate_field,
                                    error_type="duplicate_in_db",
                                    value=duplicate_value
                                ))
                                continue

                        # 构建订单数据
                        order_data = {}
                        for excel_field, db_field in EXCEL_FIELD_MAPPING.items():
                            value = row.get(excel_field)

                            # 特别记录税率字段的处理过程
                            if db_field == 'tax_rate':
                                logger.info(f"第 {sequence_number} 行税率字段处理开始 - Excel列名: {excel_field}, 原始值: {repr(value)}, 类型: {type(value)}")

                            if pd.isna(value) or (isinstance(value, str) and value.strip() == ''):
                                # 处理空值 → 使用业务默认值（若Excel未提供）
                                if db_field in ['room_count', 'sequence_number']:
                                    value = 1  # 默认房间数量/序号
                                elif db_field == 'tax_rate':
                                    value = 0  # 税率字段使用整数0
                                elif db_field == 'amount':
                                    value = Decimal('0.00')  # 金额默认0.00
                                elif db_field == 'cost_per_room':
                                    value = ""  # 对供成本（文本字段）
                                elif db_field == 'hidden_service_fee':
                                    value = ""  # 隐藏手续费（文本字段）
                                elif db_field == 'policy_name':
                                    value = '手工单政策'  # 默认政策名称
                                elif db_field == 'is_group_booking':
                                    value = '非团房'  # 默认是否为团房
                                elif db_field == 'payment_method':
                                    value = '预付'  # 默认支付方式
                                elif db_field == 'cancellation_policy':
                                    value = '不可取消'  # 默认取消规则
                                elif db_field in ['is_half_day_room', 'is_violation']:
                                    value = '否'  # 其他是否字段默认否
                                elif db_field in ['include_breakfast']:
                                    value = ''  # 保持为空字符串（按现有规则）
                                elif db_field in ['guest_birth_date', 'roommate_birth_date']:
                                    value = ''  # 出生日期字段空值使用空字符串
                                else:
                                    value = ''  # 其他字符串字段
                            else:
                                # 非空值处理
                                if db_field == 'tax_rate':
                                    # 税率字段特殊处理：转换为整数
                                    try:
                                        logger.info(f"第 {sequence_number} 行 {db_field} 字段原始值: {repr(value)}, 类型: {type(value)}")
                                        str_value = str(value).strip()

                                        # 如果是百分号格式，先去除百分号
                                        if str_value.endswith('%'):
                                            str_value = str_value.replace('%', '')
                                            logger.info(f"第 {sequence_number} 行 {db_field} 字段去除百分号后: {str_value}")
                                            value = int(float(str_value))
                                        else:
                                            # 如果是小数格式（如0.06），需要转换为百分比整数
                                            float_value = float(str_value)
                                            if 0 < float_value < 1:
                                                # 小数格式，转换为百分比
                                                value = int(float_value * 100)
                                                logger.info(f"第 {sequence_number} 行 {db_field} 字段小数格式转换: {float_value} -> {value}%")
                                            else:
                                                # 已经是整数格式
                                                value = int(float_value)
                                                logger.info(f"第 {sequence_number} 行 {db_field} 字段整数格式: {value}")

                                        logger.info(f"第 {sequence_number} 行 {db_field} 字段转换为整数: {value}")
                                    except Exception as e:
                                        logger.warning(f"第 {sequence_number} 行 {db_field} 字段转换失败: {value}, 错误: {e}, 使用默认值0")
                                        value = 0
                                elif isinstance(value, (int, float)):
                                    if db_field == 'amount':
                                        # 金额字段使用Decimal
                                        try:
                                            value = safe_decimal(value, db_field, sequence_number)
                                            value = value.quantize(Decimal('0.00'))
                                        except Exception as e:
                                            logger.warning(f"第 {sequence_number} 行 {db_field} 字段数值转换失败: {value}, 使用默认值0")
                                            value = Decimal('0.00')
                                    elif db_field in ['hidden_service_fee', 'cost_per_room']:
                                        # 这两个字段现在是文本字段，直接转为字符串
                                        value = str(value)
                                        logger.info(f"第 {sequence_number} 行 {db_field} 字段保持原值: {value}")
                                    elif db_field in ['room_count', 'sequence_number']:
                                        value = safe_int(value)  # 使用safe_int函数
                                    elif db_field in ['guest_birth_date', 'roommate_birth_date']:
                                        # 出生日期字段使用专门的解析函数，格式化为YYYY-MM-DD
                                        value = parse_birth_date(value)
                                        logger.info(f"第 {sequence_number} 行 {db_field} 字段解析为: {value}")
                                    elif db_field in ['guest_id_expiry_date', 'roommate_id_expiry_date']:
                                        # 证件有效期字段使用专门的解析函数，格式化为YYYY-MM-DD
                                        value = parse_id_expiry_date(value)
                                        logger.info(f"第 {sequence_number} 行 {db_field} 字段解析为: {value}")
                                    elif db_field in ['guest_mobile_country_code', 'roommate_mobile_country_code', 'contact_mobile_country_code']:
                                        # 手机号国际区号字段特殊处理，确保为整数字符串，去除+号
                                        try:
                                            # 如果是数字，转换为整数再转为字符串，避免小数点
                                            if isinstance(value, (int, float)):
                                                value = str(int(value))
                                            else:
                                                # 如果是字符串，去除可能的小数点和+号
                                                str_value = str(value).strip()
                                                # 去除+号
                                                if str_value.startswith('+'):
                                                    str_value = str_value[1:]
                                                # 去除小数点
                                                if '.' in str_value:
                                                    value = str(int(float(str_value)))
                                                else:
                                                    value = str_value
                                            logger.info(f"第 {sequence_number} 行 {db_field} 字段处理为: {value}")
                                        except Exception as e:
                                            logger.warning(f"第 {sequence_number} 行 {db_field} 字段处理失败: {value}, 错误: {e}, 使用空字符串")
                                            value = ''
                                    elif db_field in ['guest_mobile_phone', 'roommate_mobile_phone', 'contact_mobile_phone']:
                                        # 手机号字段特殊处理，保持为字符串格式，不转换为小数
                                        try:
                                            # 直接转为字符串，保持原格式
                                            if isinstance(value, (int, float)):
                                                # 如果是数字，转为字符串但保持完整格式（避免科学计数法）
                                                if isinstance(value, float) and value.is_integer():
                                                    value = str(int(value))  # 去除.0
                                                else:
                                                    value = str(value)
                                            else:
                                                value = str(value).strip()
                                            logger.info(f"第 {sequence_number} 行 {db_field} 字段处理为: {value}")
                                        except Exception as e:
                                            logger.warning(f"第 {sequence_number} 行 {db_field} 字段处理失败: {value}, 错误: {e}, 使用空字符串")
                                            value = ''
                                    elif db_field in ['include_breakfast', 'is_group_booking']:
                                        # 这两个字段直接转为字符串，不进行是否转换
                                        value = str(value)
                                        logger.info(f"第 {sequence_number} 行 {db_field} 字段保持原值: {value}")
                                    elif db_field in ['is_half_day_room', 'is_violation']:
                                        # 其他是否字段使用专门的解析函数，返回"是"或"否"
                                        value = parse_yes_no(value)
                                        logger.info(f"第 {sequence_number} 行 {db_field} 字段解析为: {value}")
                                    else:
                                        value = str(value)
                                elif isinstance(value, bool):
                                    # 对于特殊的是否字段处理
                                    if db_field in ['include_breakfast', 'is_group_booking']:
                                        # 这两个字段直接转为字符串，不进行是否转换
                                        value = str(value)
                                        logger.info(f"第 {sequence_number} 行 {db_field} 字段保持原值: {value}")
                                    elif db_field in ['is_half_day_room', 'is_violation']:
                                        # 其他是否字段转换为"是"或"否"
                                        value = parse_yes_no(value)
                                        logger.info(f"第 {sequence_number} 行 {db_field} 字段解析为: {value}")
                                    else:
                                        # 其他布尔字段直接使用布尔值
                                        pass
                                else:
                                    # 其他类型都转换为字符串，但对于数值字段需要特殊处理
                                    if db_field == 'amount':
                                        # 金额字段使用Decimal
                                        try:
                                            value = safe_decimal(str(value).strip(), db_field, sequence_number)
                                            value = value.quantize(Decimal('0.00'))
                                        except Exception as e:
                                            logger.warning(f"第 {sequence_number} 行 {db_field} 字段字符串转数值失败: {value}, 使用默认值0")
                                            value = Decimal('0.00')
                                    elif db_field in ['hidden_service_fee', 'cost_per_room']:
                                        # 这两个字段现在是文本字段，直接保持原字符串值
                                        value = str(value).strip()
                                        logger.info(f"第 {sequence_number} 行 {db_field} 字段保持原值: {value}")
                                    elif db_field in ['room_count', 'sequence_number']:
                                        # 字符串形式的整数需要转换
                                        try:
                                            value = safe_int(str(value).strip())
                                        except Exception as e:
                                            logger.warning(f"第 {sequence_number} 行 {db_field} 字段字符串转整数失败: {value}, 使用默认值1")
                                            value = 1
                                    elif db_field in ['guest_birth_date', 'roommate_birth_date']:
                                        # 出生日期字段使用专门的解析函数，格式化为YYYY-MM-DD
                                        value = parse_birth_date(value)
                                        logger.info(f"第 {sequence_number} 行 {db_field} 字段解析为: {value}")
                                    elif db_field in ['guest_id_expiry_date', 'roommate_id_expiry_date']:
                                        # 证件有效期字段使用专门的解析函数，格式化为YYYY-MM-DD
                                        value = parse_id_expiry_date(value)
                                        logger.info(f"第 {sequence_number} 行 {db_field} 字段解析为: {value}")
                                    elif db_field in ['guest_mobile_country_code', 'roommate_mobile_country_code', 'contact_mobile_country_code']:
                                        # 手机号国际区号字段特殊处理，确保为整数字符串，去除+号
                                        try:
                                            str_value = str(value).strip()
                                            # 去除+号
                                            if str_value.startswith('+'):
                                                str_value = str_value[1:]
                                            # 去除小数点
                                            if str_value and '.' in str_value:
                                                value = str(int(float(str_value)))
                                            else:
                                                value = str_value
                                            logger.info(f"第 {sequence_number} 行 {db_field} 字段处理为: {value}")
                                        except Exception as e:
                                            logger.warning(f"第 {sequence_number} 行 {db_field} 字段处理失败: {value}, 错误: {e}, 使用空字符串")
                                            value = ''
                                    elif db_field in ['guest_mobile_phone', 'roommate_mobile_phone', 'contact_mobile_phone']:
                                        # 手机号字段特殊处理，保持为字符串格式，不转换为小数
                                        try:
                                            str_value = str(value).strip()
                                            # 保持原格式，不进行数字转换
                                            value = str_value
                                            logger.info(f"第 {sequence_number} 行 {db_field} 字段处理为: {value}")
                                        except Exception as e:
                                            logger.warning(f"第 {sequence_number} 行 {db_field} 字段处理失败: {value}, 错误: {e}, 使用空字符串")
                                            value = ''
                                    elif db_field in ['include_breakfast', 'is_group_booking']:
                                        # 这两个字段直接保持原字符串值，不进行是否转换
                                        value = str(value).strip()
                                        logger.info(f"第 {sequence_number} 行 {db_field} 字段保持原值: {value}")
                                    elif db_field in ['is_half_day_room', 'is_violation']:
                                        # 其他是否字段使用专门的解析函数，返回"是"或"否"
                                        value = parse_yes_no(value)
                                        logger.info(f"第 {sequence_number} 行 {db_field} 字段解析为: {value}")
                                    else:
                                        # 其他字段转换为字符串
                                        value = str(value).strip()

                            order_data[db_field] = value

                            # 特别记录税率字段的最终值
                            if db_field == 'tax_rate':
                                logger.info(f"第 {sequence_number} 行税率字段最终值: {repr(value)}, 类型: {type(value)}")

                        # 自动补充入住人信息
                        order_data = auto_complete_guest_info(order_data, 'guest')

                        # 自动补充同住人信息
                        order_data = auto_complete_guest_info(order_data, 'roommate')

                        # 自动补充公司名称
                        order_data = await auto_complete_company_name(order_data, project_id)

                        # 添加必要的字段
                        order_data['project_id'] = project_id
                        if 'sequence_number' not in order_data:
                            order_data['sequence_number'] = sequence_number

                        # 为成本中心设置默认空字符串（Excel导入中不包含此字段）
                        if 'cost_center' not in order_data:
                            order_data['cost_center'] = ""

                        # 业务要求：是否含早不做默认填充，若Excel未提供或为空，保持为空字符串
                        if 'include_breakfast' not in order_data or order_data.get('include_breakfast') is None:
                            order_data['include_breakfast'] = ""

                        # 安全兜底：是否违规默认应为“否”，只有明确的“是”才置为“是”
                        iv = order_data.get('is_violation')
                        if iv in (None, ""):
                            order_data['is_violation'] = "否"
                        else:
                            try:
                                if isinstance(iv, bool):
                                    order_data['is_violation'] = "是" if iv else "否"
                                elif isinstance(iv, (int, float)):
                                    order_data['is_violation'] = "是" if int(iv) != 0 else "否"
                                else:
                                    iv_str = str(iv).strip()
                                    if iv_str in ['true', '1', 'yes', 'y', '是', '√', '✓', 'True', 'TRUE', 'YES', 'Y']:
                                        order_data['is_violation'] = "是"
                                    elif iv_str in ['false', '0', 'no', 'n', '否', '×', '✗', 'False', 'FALSE', 'NO', 'N']:
                                        order_data['is_violation'] = "否"
                                    else:
                                        # 非明确值一律按否处理
                                        order_data['is_violation'] = "否"
                            except Exception:
                                order_data['is_violation'] = "否"

                        # 自动计算对客金额：如果单房价格和隐藏服务费不为空，则金额为二者加和
                        cost_per_room = order_data.get('cost_per_room', '')
                        hidden_service_fee = order_data.get('hidden_service_fee', '')
                        
                        if cost_per_room and hidden_service_fee:
                            try:
                                # 尝试将字符串转换为数值进行计算
                                cost_value = float(str(cost_per_room).replace(',', ''))
                                fee_value = float(str(hidden_service_fee).replace(',', ''))
                                calculated_amount = cost_value + fee_value
                                
                                # 更新金额字段
                                order_data['amount'] = Decimal(str(calculated_amount)).quantize(Decimal('0.00'))
                                logger.info(f"第 {sequence_number} 行自动计算金额: {cost_per_room} + {hidden_service_fee} = {order_data['amount']}")
                            except (ValueError, TypeError) as e:
                                logger.warning(f"第 {sequence_number} 行金额计算失败: cost_per_room={cost_per_room}, hidden_service_fee={hidden_service_fee}, 错误: {e}")
                                # 计算失败时保持原有金额值
                        elif cost_per_room:
                            try:
                                # 只有单房价格时，直接使用单房价格作为金额
                                cost_value = float(str(cost_per_room).replace(',', ''))
                                order_data['amount'] = Decimal(str(cost_value)).quantize(Decimal('0.00'))
                                logger.info(f"第 {sequence_number} 行使用单房价格作为金额: {cost_per_room} = {order_data['amount']}")
                            except (ValueError, TypeError) as e:
                                logger.warning(f"第 {sequence_number} 行单房价格转换失败: {cost_per_room}, 错误: {e}")
                        elif hidden_service_fee:
                            try:
                                # 只有隐藏服务费时，直接使用隐藏服务费作为金额
                                fee_value = float(str(hidden_service_fee).replace(',', ''))
                                order_data['amount'] = Decimal(str(fee_value)).quantize(Decimal('0.00'))
                                logger.info(f"第 {sequence_number} 行使用隐藏服务费作为金额: {hidden_service_fee} = {order_data['amount']}")
                            except (ValueError, TypeError) as e:
                                logger.warning(f"第 {sequence_number} 行隐藏服务费转换失败: {hidden_service_fee}, 错误: {e}")
                        # 如果两个字段都为空，保持原有金额值不变

                        # 检查必填字段
                        missing_fields = []
                        missing_field_names = []
                        required_fields = [
                            'guest_full_name',
                            'guest_mobile_phone',
                            'destination',
                            'check_in_time',
                            'check_out_time'
                        ]
                        
                        for field in required_fields:
                            if not order_data.get(field):
                                missing_fields.append(field)
                                missing_field_names.append(DB_FIELD_TO_CHINESE.get(field, field))

                        if missing_fields:
                            validation_failed_count += 1
                            logger.warning(f"第 {sequence_number} 行数据缺少必填字段: {missing_field_names}")
                            validation_errors.append(ValidationError(
                                row=sequence_number,
                                message=f"缺少必填字段: {', '.join(missing_field_names)}",
                                error_type="missing_fields",
                                field=", ".join(missing_field_names),
                                value=""
                            ))
                            order_data['order_status'] = 'check_failed'
                            order_data['fail_reason'] = f"缺少必填字段: {', '.join(missing_field_names)}"
                        else:
                            success_count += 1
                            order_data['order_status'] = 'initial'

                        # 创建订单
                        logger.info(f"准备创建订单 - 行号: {sequence_number}")
                        await HotelOrder.create(**order_data)
                        logger.info(f"成功创建订单 - 行号: {sequence_number}")

                    except Exception as row_error:
                        logger.error(f"处理第 {sequence_number} 行数据时发生错误: {str(row_error)}")
                        validation_errors.append(ValidationError(
                            row=sequence_number,
                            message=str(row_error),
                            error_type="system_error"
                        ))
                        validation_failed_count += 1
                        continue

                logger.info("事务处理完成")

        except Exception as transaction_error:
            logger.error(f"事务处理失败: {str(transaction_error)}")
            raise HTTPException(status_code=500, detail=f"数据导入失败: {str(transaction_error)}")

        # 构建响应消息
        message_parts = []
        if success_count > 0:
            message_parts.append(f"成功导入 {success_count} 条数据")
        if validation_failed_count > 0:
            message_parts.append(f"{validation_failed_count} 条数据验证失败")
        if duplicate_count > 0:
            message_parts.append(f"{duplicate_count} 条重复数据已忽略")
        
        message = "，".join(message_parts)
        logger.info(f"Excel导入完成 - {message}")

        # 构建响应数据
        response = ExcelUploadResponse(
            success=True,
            message=message,
            validation_errors=validation_errors
        )

        return response

    except Exception as e:
        logger.error(f"Excel导入失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Excel导入失败: {str(e)}")

@router.get("/project/{project_id}/stats", response_model=ProjectOrderStatsResponse, summary="获取项目订单统计")
async def get_project_order_stats(project_id: int):
    """获取指定项目的订单统计信息，包括总订单数、预定完成订单数和金额总和"""
    try:
        # 验证项目是否存在
        try:
            await Project.get(id=project_id)
        except DoesNotExist:
            raise HTTPException(status_code=404, detail="项目不存在")
        
        # 查询项目的所有订单（未删除的）
        all_orders_query = HotelOrder.filter(project_id=project_id, is_deleted=False)
        
        # 获取总订单数
        total_orders = await all_orders_query.count()
        
        # 查询预定完成的订单
        completed_orders_query = HotelOrder.filter(
            project_id=project_id, 
            is_deleted=False,
            order_status='completed'
        )
        
        # 获取预定完成订单数
        completed_orders = await completed_orders_query.count()
        
        # 计算预定完成订单的金额总和
        completed_orders_list = await completed_orders_query.all()
        completed_amount = sum(order.amount or 0 for order in completed_orders_list)
        
        logger.info(f"项目 {project_id} 订单统计 - 总订单数: {total_orders}, 预定完成: {completed_orders}, 完成金额: {completed_amount}")
        
        return ProjectOrderStatsResponse(
            project_id=project_id,
            total_orders=total_orders,
            completed_orders=completed_orders,
            completed_amount=completed_amount
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取项目订单统计失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取项目订单统计失败: {str(e)}")

@router.get("/project/{project_id}/detail-stats", response_model=ProjectOrderDetailStatsResponse, summary="获取项目订单详细统计")
async def get_project_order_detail_stats(project_id: int):
    """获取指定项目的详细订单统计信息，包括各状态订单数量和金额统计"""
    try:
        # 验证项目是否存在
        try:
            await Project.get(id=project_id)
        except DoesNotExist:
            raise HTTPException(status_code=404, detail="项目不存在")
        
        # 查询项目的所有订单（未删除的）
        all_orders_query = HotelOrder.filter(project_id=project_id, is_deleted=False)
        all_orders = await all_orders_query.all()
        
        # 统计各状态订单数量
        total_orders = len(all_orders)
        check_failed_orders = len([o for o in all_orders if o.order_status == 'check_failed'])
        initial_orders = len([o for o in all_orders if o.order_status == 'initial'])
        submitted_orders = len([o for o in all_orders if o.order_status == 'submitted'])
        processing_orders = len([o for o in all_orders if o.order_status == 'processing'])
        completed_orders = len([o for o in all_orders if o.order_status == 'completed'])
        failed_orders = len([o for o in all_orders if o.order_status == 'failed'])
        
        # 计算金额统计
        completed_amount = sum(order.amount or 0 for order in all_orders if order.order_status == 'completed')
        total_amount = sum(order.amount or 0 for order in all_orders)
        
        logger.info(f"项目 {project_id} 详细统计 - 总订单: {total_orders}, 验证失败: {check_failed_orders}, 待提交: {initial_orders}, 已提交: {submitted_orders}, 处理中: {processing_orders}, 预定完成: {completed_orders}, 预定失败: {failed_orders}")
        
        return ProjectOrderDetailStatsResponse(
            project_id=project_id,
            total_orders=total_orders,
            check_failed_orders=check_failed_orders,
            initial_orders=initial_orders,
            submitted_orders=submitted_orders,
            processing_orders=processing_orders,
            completed_orders=completed_orders,
            failed_orders=failed_orders,
            completed_amount=completed_amount,
            total_amount=total_amount
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取项目订单详细统计失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取项目订单详细统计失败: {str(e)}")

@router.get("/project/{project_id}", response_model=HotelOrderListResponse, summary="获取项目的酒店订单")
async def get_orders_by_project(
    project_id: int,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    order_status: Optional[str] = Query(None, description="订单状态筛选，支持多个状态用逗号分隔，如：submitted,processing"),
    guest_name: Optional[str] = Query(None, description="入住人姓名搜索"),
    mobile_phone: Optional[str] = Query(None, description="手机号搜索"),
    contact_phone: Optional[str] = Query(None, description="联系人手机号搜索"),
    sort_by_failed_first: Optional[bool] = Query(True, description="是否将验证失败和预定失败的订单排在前面")
):
    """获取指定项目的酒店订单，支持按入住人姓名、手机号、联系人手机号搜索"""
    try:
        # 验证项目是否存在
        try:
            await Project.get(id=project_id)
        except DoesNotExist:
            raise HTTPException(status_code=404, detail="项目不存在")
        
        # 记录搜索参数
        logger.info(f"搜索参数 - project_id: {project_id}, order_status: {order_status}, guest_name: {guest_name}, mobile_phone: {mobile_phone}, contact_phone: {contact_phone}")
        
        # 查询订单
        query = HotelOrder.filter(project_id=project_id, is_deleted=False)
        
        # 处理状态筛选 - 支持多状态
        if order_status and order_status.strip():
            # 将逗号分隔的状态字符串转换为列表
            status_list = [status.strip() for status in order_status.split(',') if status.strip()]
            if status_list:
                query = query.filter(order_status__in=status_list)
                logger.info(f"添加状态筛选条件: {status_list}")
        
        # 添加搜索条件
        if guest_name:
            query = query.filter(guest_full_name__icontains=guest_name)
            logger.info(f"添加入住人姓名搜索条件: {guest_name}")
        
        if mobile_phone:
            query = query.filter(guest_mobile_phone__icontains=mobile_phone)
            logger.info(f"添加手机号搜索条件: {mobile_phone}")
        
        if contact_phone:
            query = query.filter(contact_mobile_phone__icontains=contact_phone)
            logger.info(f"添加联系人手机号搜索条件: {contact_phone}")
        
        # 获取总数
        total = await query.count()
        logger.info(f"搜索结果总数: {total}")
        
        # 应用排序和分页
        if sort_by_failed_first:
            # 分别查询失败订单和其他订单，然后合并
            failed_query = query.filter(order_status__in=['check_failed', 'failed']).order_by("-id")
            other_query = query.exclude(order_status__in=['check_failed', 'failed']).order_by("-id")
            
            # 获取失败订单和其他订单的数量
            failed_count = await failed_query.count()
            
            offset = (page - 1) * page_size
            orders = []
            
            if offset < failed_count:
                # 当前页包含失败订单
                failed_orders = await failed_query.offset(offset).limit(page_size)
                orders.extend(failed_orders)
                
                # 如果当前页还有空间，补充其他订单
                remaining_slots = page_size - len(failed_orders)
                if remaining_slots > 0:
                    other_offset = max(0, offset + len(failed_orders) - failed_count)
                    other_orders = await other_query.offset(other_offset).limit(remaining_slots)
                    orders.extend(other_orders)
            else:
                # 当前页只包含其他订单
                other_offset = offset - failed_count
                other_orders = await other_query.offset(other_offset).limit(page_size)
                orders.extend(other_orders)
            
            logger.info(f"已应用失败订单优先排序 - 失败订单数: {failed_count}, 当前页订单数: {len(orders)}")
        else:
            # 默认按ID降序排序，使用数据库排序
            query = query.order_by("-id")
            offset = (page - 1) * page_size
            orders = await query.offset(offset).limit(page_size)
        
        # 导入TaskToHotelOrder模型以获取order_type
        from src.db.models.task_to_hotel_order import TaskToHotelOrder
        
        # 转换为响应模型
        order_responses = []
        for order in orders:
            # 获取订单的预定类型
            task_mapping = await TaskToHotelOrder.filter(order_id=order.id).first()
            order_type = task_mapping.order_type if task_mapping else None
            
            # 创建订单响应，包含order_type字段
            order_data = {
                'id': order.id,
                'project_id': order.project_id,
                'sequence_number': order.sequence_number,
                'guest_full_name': order.guest_full_name,
                'guest_surname': order.guest_surname,
                'guest_given_name': order.guest_given_name,
                'guest_nationality': order.guest_nationality,
                'guest_gender': order.guest_gender,
                'guest_birth_date': order.guest_birth_date,
                'guest_id_type': order.guest_id_type,
                'guest_id_number': order.guest_id_number,
                'guest_id_expiry_date': order.guest_id_expiry_date,
                'guest_mobile_country_code': order.guest_mobile_country_code,
                'guest_mobile_phone': order.guest_mobile_phone,
                'guest_email': order.guest_email,
                'destination': order.destination,
                'hotel_id': order.hotel_id,
                'room_type': order.room_type,
                'room_count': order.room_count,
                'policy_name': order.policy_name,
                'include_breakfast': order.include_breakfast,
                'is_half_day_room': order.is_half_day_room,
                'check_in_time': order.check_in_time,
                'check_out_time': order.check_out_time,
                'is_group_booking': order.is_group_booking,
                'group_booking_name': order.group_booking_name,
                'room_number': order.room_number,
                'payment_method': order.payment_method,
                'invoice_type': order.invoice_type,
                'tax_rate': int(order.tax_rate) if order.tax_rate is not None else None,  # 转换为整型
                'agreement_type': order.agreement_type,
                'supplier_name': order.supplier_name,
                'payment_channel': order.payment_channel,
                'payment_transaction_id': order.payment_transaction_id,
                'cost_per_room': str(order.cost_per_room) if order.cost_per_room is not None else None,  # 保持字符串
                'hidden_service_fee': str(order.hidden_service_fee) if order.hidden_service_fee is not None else None,  # 保持字符串
                'cancellation_policy': order.cancellation_policy,
                'is_violation': order.is_violation,
                'contact_person': order.contact_person,
                'contact_mobile_country_code': order.contact_mobile_country_code,
                'contact_mobile_phone': order.contact_mobile_phone,
                'order_remarks': order.order_remarks,
                'cost_center': order.cost_center,
                'trip_submission_item': order.trip_submission_item,
                'approver': order.approver,
                'roommate_name': order.roommate_name,
                'roommate_surname': order.roommate_surname,
                'roommate_given_name': order.roommate_given_name,
                'roommate_nationality': order.roommate_nationality,
                'roommate_gender': order.roommate_gender,
                'roommate_birth_date': order.roommate_birth_date,
                'roommate_id_type': order.roommate_id_type,
                'roommate_id_number': order.roommate_id_number,
                'roommate_id_expiry_date': order.roommate_id_expiry_date,
                'roommate_mobile_country_code': order.roommate_mobile_country_code,
                'roommate_mobile_phone': order.roommate_mobile_phone,
                'roommate_email': order.roommate_email,
                'company_name': order.company_name,
                'hotel_name': order.hotel_name,
                'amount': order.amount,  # 保持Decimal类型
                'booking_agent': order.booking_agent,
                'order_number': order.order_number,
                'bill_number': order.bill_number,
                'order_status': order.order_status,
                'fail_reason': order.fail_reason,
                'created_at': order.created_at,
                'updated_at': order.updated_at,
                'is_deleted': order.is_deleted,
                'order_type': order_type
            }
            
            order_response = HotelOrderResponse.model_validate(order_data)
            order_responses.append(order_response)
        
        logger.info(f"返回订单数量: {len(order_responses)}")
        
        return HotelOrderListResponse(
            total=total,
            page=page,
            page_size=page_size,
            items=order_responses
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查询项目订单失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"查询项目订单失败: {str(e)}")

@router.get("/{order_id}", response_model=HotelOrderResponse, summary="获取订单详情")
async def get_order(order_id: int):
    """获取酒店订单详情"""
    try:
        order = await HotelOrder.get(id=order_id)
        return HotelOrderResponse.model_validate(order)
    except DoesNotExist:
        raise HTTPException(status_code=404, detail="订单不存在")

@router.put("/{order_id}", response_model=HotelOrderResponse, summary="更新订单")
async def update_order(order_id: int, order_data: HotelOrderUpdate):
    """更新酒店订单"""
    try:
        order = await HotelOrder.get(id=order_id)
        
        # 更新字段
        update_data = order_data.model_dump(exclude_unset=True)
        
        # 对日期字段进行格式化处理
        if 'guest_birth_date' in update_data and update_data['guest_birth_date']:
            update_data['guest_birth_date'] = parse_birth_date(update_data['guest_birth_date'])
        
        if 'guest_id_expiry_date' in update_data and update_data['guest_id_expiry_date']:
            update_data['guest_id_expiry_date'] = parse_id_expiry_date(update_data['guest_id_expiry_date'])
        
        if 'roommate_birth_date' in update_data and update_data['roommate_birth_date']:
            update_data['roommate_birth_date'] = parse_birth_date(update_data['roommate_birth_date'])
        
        if 'roommate_id_expiry_date' in update_data and update_data['roommate_id_expiry_date']:
            update_data['roommate_id_expiry_date'] = parse_id_expiry_date(update_data['roommate_id_expiry_date'])
        
        if update_data:
            await order.update_from_dict(update_data)
            
            # 数据验证逻辑：如果更新了关键字段，进行验证
            if any(key in update_data for key in ['guest_full_name', 'guest_id_type', 'guest_id_number', 'guest_mobile_phone',
                                                  'check_in_time', 'check_out_time', 'hotel_id', 'hotel_name',
                                                  'destination', 'room_type',
                                                  'contact_person', 'contact_mobile_phone', 'approver']):
                
                # 构建验证数据 - 使用正确的酒店订单字段名
                validation_data = {
                    'guest_full_name': order.guest_full_name,
                    'guest_surname': order.guest_surname,
                    'guest_given_name': order.guest_given_name,
                    'guest_nationality': order.guest_nationality,
                    'guest_gender': order.guest_gender,
                    'guest_birth_date': order.guest_birth_date,
                    'guest_id_type': order.guest_id_type,
                    'guest_id_number': order.guest_id_number,
                    'guest_id_expiry_date': order.guest_id_expiry_date,
                    'guest_mobile_phone': order.guest_mobile_phone,
                    'guest_mobile_country_code': order.guest_mobile_country_code,
                    'check_in_time': order.check_in_time,
                    'check_out_time': order.check_out_time,
                    'hotel_id': order.hotel_id,
                    'hotel_name': order.hotel_name,
                    'destination': order.destination,
                    'room_type': order.room_type,
                    'room_count': order.room_count,
                    'cost_center': order.cost_center,
                    'trip_submission_item': order.trip_submission_item,
                    'contact_person': order.contact_person,
                    'contact_mobile_phone': order.contact_mobile_phone,
                    'approver': order.approver,
                    'company_name': order.company_name,
                    'booking_agent': order.booking_agent,
                    'amount': order.amount,
                    'order_number': order.order_number,
                    'bill_number': order.bill_number
                }
                
                # 执行验证
                validation_errors = validate_hotel_order_data(validation_data, 1)
                
                if validation_errors:
                    # 验证失败，设置状态和失败原因
                    order.order_status = "check_failed"
                    order.fail_reason = "; ".join([f"{error.field}: {error.message}" for error in validation_errors])
                    logger.warning(f"订单 {order_id} 验证失败: {order.fail_reason}")
                else:
                    # 验证通过，设置状态为待提交并清空失败原因
                    order.order_status = "initial"
                    order.fail_reason = None
                    logger.info(f"订单 {order_id} 验证通过，状态已改为待提交")
            
            await order.save()
        
        # 重新获取更新后的订单
        updated_order = await HotelOrder.get(id=order_id)
        return HotelOrderResponse.model_validate(updated_order)
        
    except DoesNotExist:
        raise HTTPException(status_code=404, detail="订单不存在")
    except Exception as e:
        logger.error(f"更新订单失败: {str(e)}")
        raise HTTPException(status_code=400, detail=f"更新订单失败: {str(e)}")

@router.delete("/{order_id}", summary="删除订单")
async def delete_order(order_id: int):
    """软删除酒店订单"""
    try:
        order = await HotelOrder.get(id=order_id)
        await order.delete()
        return JSONResponse(content={"message": "订单删除成功"})
        
    except DoesNotExist:
        raise HTTPException(status_code=404, detail="订单不存在")
    except Exception as e:
        logger.error(f"删除订单失败: {str(e)}")
        raise HTTPException(status_code=400, detail=f"删除订单失败: {str(e)}")

@router.delete("/project/{project_id}/clear-initial", response_model=ClearOrdersResponse, summary="清空项目中可清空状态的酒店订单")
async def clear_initial_orders(project_id: int):
    """清空指定项目中可清空状态的所有酒店订单（包括initial、check_failed、failed状态）"""
    try:
        # 验证项目是否存在
        try:
            await Project.get(id=project_id)
        except DoesNotExist:
            raise HTTPException(status_code=404, detail="项目不存在")

        # 查询可清空状态的订单：initial（待提交）、check_failed（验证失败）、failed（预订失败）
        clearable_orders = await HotelOrder.filter(
            project_id=project_id,
            order_status__in=['initial', 'check_failed', 'failed'],
            is_deleted=False
        ).all()

        deleted_count = len(clearable_orders)

        if deleted_count == 0:
            return ClearOrdersResponse(
                deleted_count=0,
                message="暂无可清空的订单"
            )

        # 统计各状态的订单数量
        status_counts = {}
        for order in clearable_orders:
            status = order.order_status
            status_counts[status] = status_counts.get(status, 0) + 1

        # 软删除订单
        for order in clearable_orders:
            order.is_deleted = True
            await order.save()

        # 构建详细的删除信息
        status_messages = []
        if status_counts.get('initial', 0) > 0:
            status_messages.append(f"{status_counts['initial']} 条待提交订单")
        if status_counts.get('check_failed', 0) > 0:
            status_messages.append(f"{status_counts['check_failed']} 条验证失败订单")
        if status_counts.get('failed', 0) > 0:
            status_messages.append(f"{status_counts['failed']} 条预订失败订单")

        detail_message = "、".join(status_messages)

        logger.info(f"已清空项目 {project_id} 中 {deleted_count} 条订单：{detail_message}")

        return ClearOrdersResponse(
            deleted_count=deleted_count,
            message=f"成功清空 {deleted_count} 条订单（{detail_message}）"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"清空订单失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"清空订单失败: {str(e)}")

@router.delete("/project/{project_id}/clear", response_model=ClearOrdersResponse, summary="清空项目中的所有酒店订单")
async def clear_all_orders(project_id: int):
    """清空指定项目中的所有酒店订单"""
    try:
        # 验证项目是否存在
        try:
            await Project.get(id=project_id)
        except DoesNotExist:
            raise HTTPException(status_code=404, detail="项目不存在")
        
        # 查询项目的所有订单
        all_orders = await HotelOrder.filter(
            project_id=project_id,
            is_deleted=False
        ).all()
        
        deleted_count = len(all_orders)
        
        if deleted_count == 0:
            return ClearOrdersResponse(
                deleted_count=0,
                message="暂无订单需要清空"
            )
        
        # 软删除所有订单
        for order in all_orders:
            order.is_deleted = True
            await order.save()
        
        logger.info(f"已清空项目 {project_id} 中 {deleted_count} 条酒店订单")
        
        return ClearOrdersResponse(
            deleted_count=deleted_count,
            message=f"成功清空 {deleted_count} 条酒店订单"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"清空订单失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"清空订单失败: {str(e)}")

@router.post("/project/{project_id}/book", response_model=BookingResponse, summary="预订酒店订单")
async def book_orders(
    project_id: int,
    booking_data: BookingRequest,
    current_user: User = Depends(get_current_user)
):
    """
    预订酒店订单
    - booking_type: 'book' 仅预订, 'book_and_issue' 预订且出票
    - orders: 订单ID列表
    - sms_notify: 是否短信通知
    - has_agent: 是否有代订人
    - agent_phone: 代订人手机号码
    """
    try:
        # 验证项目是否存在
        try:
            project = await Project.get(id=project_id)
        except DoesNotExist:
            raise HTTPException(status_code=404, detail="项目不存在")
        
        # 验证订单是否存在且属于该项目
        orders = await HotelOrder.filter(
            id__in=booking_data.orders,
            project_id=project_id,
            is_deleted=False
        ).all()
        
        if len(orders) != len(booking_data.orders):
            raise HTTPException(status_code=400, detail="部分订单不存在或不属于该项目")
        
        # 检查是否有代订人但没有提供手机号
        if booking_data.has_agent and not booking_data.agent_phone:
            raise HTTPException(status_code=400, detail="选择有代订人时必须提供代订人手机号码")
        
        # 创建或更新项目任务
        task = None
        try:
            # 查找该项目的酒店预订任务
            task = await ProjectTask.filter(
                project_id=project_id,
                task_type="酒店预订"
            ).first()
            
            if task:
                # 更新任务的短信通知和代订人信息
                task.sms_notify = booking_data.sms_notify or False
                task.has_agent = booking_data.has_agent or False
                task.agent_phone = booking_data.agent_phone if booking_data.has_agent else None
                await task.save()
                logger.info(f"已更新任务 {task.task_id} 的短信通知和代订人设置")
            else:
                # 创建新的酒店预订任务
                from datetime import datetime
                task = await ProjectTask.create(
                    project_id=project_id,
                    creator_user_id=current_user.id,
                    creator_name=current_user.username,
                    task_type="酒店预订",
                    task_title=f"酒店预订任务 - {datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    task_description=f"批量预订 {len(orders)} 条酒店订单",
                    task_status="submitted",
                    sms_notify=booking_data.sms_notify or False,
                    has_agent=booking_data.has_agent or False,
                    agent_phone=booking_data.agent_phone if booking_data.has_agent else None
                )
                logger.info(f"已创建新的酒店预订任务: {task.task_id}")
        except Exception as e:
            logger.warning(f"处理任务信息失败: {str(e)}")
        
        processed_count = len(orders)
        success_count = 0
        failed_count = 0
        
        # 根据预订类型设置订单状态
        target_status = "submitted" if booking_data.booking_type == "book" else "processing"
        
        # 导入TaskToHotelOrder模型
        from src.db.models.task_to_hotel_order import TaskToHotelOrder
        from datetime import datetime
        
        # 处理每个订单
        for order in orders:
            try:
                # 更新订单状态
                order.order_status = target_status
                
                # 如果有代订人信息，更新到订单中
                if booking_data.has_agent and booking_data.agent_phone:
                    order.booking_agent = f"代订人: {booking_data.agent_phone}"
                
                # 如果需要短信通知，更新票务短信字段
                if booking_data.sms_notify:
                    order.ticket_sms = "是"
                
                await order.save()
                
                # 如果有任务，创建任务订单关联记录
                if task:
                    try:
                        # 检查是否已存在关联记录
                        existing_relation = await TaskToHotelOrder.filter(
                            task_id=task.task_id,
                            order_id=order.id
                        ).first()
                        
                        if not existing_relation:
                            # 根据预订类型设置order_type
                            order_type = "book" if booking_data.booking_type == "book_only" else "book_and_issue"
                            
                            # 创建任务订单映射关系
                            current_time = datetime.now()
                            await TaskToHotelOrder.create(
                                project_id=project_id,
                                task_id=task.task_id,
                                order_id=order.id,
                                order_status=target_status,
                                order_type=order_type,
                                start_time=current_time,
                                end_time=current_time
                            )
                            logger.info(f"已创建订单 {order.id} 与任务 {task.task_id} 的关联记录")
                        else:
                            # 更新现有关联记录的状态
                            existing_relation.order_status = target_status
                            existing_relation.end_time = datetime.now()
                            await existing_relation.save()
                            logger.info(f"已更新订单 {order.id} 与任务 {task.task_id} 的关联记录状态")
                    except Exception as relation_error:
                        logger.error(f"处理订单 {order.id} 的任务关联失败: {str(relation_error)}")
                
                success_count += 1
                logger.info(f"订单 {order.id} 状态已更新为 {target_status}")
                
            except Exception as e:
                failed_count += 1
                logger.error(f"处理订单 {order.id} 失败: {str(e)}")
        
        # 生成响应消息
        if booking_data.booking_type == "book_only":
            action = "预订"
        else:
            action = "预订且出票"
        
        message = f"成功{action} {success_count} 条订单"
        if failed_count > 0:
            message += f"，{failed_count} 条订单处理失败"
        
        # 添加设置信息到消息中
        settings_info = []
        if booking_data.sms_notify:
            settings_info.append("短信通知已启用")
        if booking_data.has_agent and booking_data.agent_phone:
            settings_info.append(f"代订人: {booking_data.agent_phone}")
        
        if settings_info:
            message += f"（{', '.join(settings_info)}）"
        
        logger.info(f"项目 {project_id} 预订完成: {message}")
        
        return BookingResponse(
            processed_count=processed_count,
            success_count=success_count,
            failed_count=failed_count,
            message=message
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"预订订单失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"预订订单失败: {str(e)}")

@router.post("/project/{project_id}/create-booking-task", response_model=CreateBookingTaskResponse, summary="创建酒店预订任务")
async def create_booking_task(
    project_id: int,
    task_data: CreateBookingTaskRequest,
    current_user: User = Depends(get_current_user)
):
    """
    创建酒店预订任务，并将所有未提交的订单提交到该任务中
    
    - 创建project_tasks表中的预订任务
    - 将project_id下状态为initial的所有订单提交到该任务
    - 在task_to_hotel_orders表中创建关联记录
    - 更新hotel_orders表中的order_status为submitted
    
    流程:
    1. 验证项目存在
    2. 验证代订人信息
    3. 获取项目中所有待提交订单
    4. 创建项目任务
    5. 创建任务订单映射关系
    6. 更新订单状态
    """
    try:
        # 验证项目是否存在
        try:
            project = await Project.get(id=project_id)
        except DoesNotExist:
            raise HTTPException(status_code=404, detail="项目不存在")
        
        # 验证代订人信息
        if task_data.has_agent and (not task_data.agent_phone or not task_data.agent_name):
            raise HTTPException(status_code=400, detail="选择有代订人时必须提供代订人手机号码和姓名")
        
        # 获取要提交的订单
        if task_data.order_ids:
            # 如果指定了order_ids，只处理这些订单，但只接受initial状态的订单
            initial_orders = await HotelOrder.filter(
                id__in=task_data.order_ids,
                project_id=project_id,
                order_status='initial',  # 只接受待提交状态的订单
                is_deleted=False
            ).all()

            # 检查是否有非initial状态的订单被指定
            all_specified_orders = await HotelOrder.filter(
                id__in=task_data.order_ids,
                project_id=project_id,
                is_deleted=False
            ).all()

            non_initial_orders = [order for order in all_specified_orders if order.order_status != 'initial']
            if non_initial_orders:
                non_initial_statuses = list(set([order.order_status for order in non_initial_orders]))
                status_names = {
                    'check_failed': '验证失败',
                    'failed': '预订失败',
                    'submitted': '已提交',
                    'processing': '处理中',
                    'completed': '已完成'
                }
                status_desc = '、'.join([status_names.get(status, status) for status in non_initial_statuses])
                raise HTTPException(
                    status_code=400,
                    detail=f"指定的订单中包含非待提交状态的订单（{status_desc}），只能提交状态为待提交的订单"
                )

            if len(initial_orders) == 0:
                raise HTTPException(status_code=400, detail="指定的订单中没有可提交的酒店订单（状态为待提交）")
        else:
            # 如果没有指定order_ids，获取项目中所有状态为initial的订单
            initial_orders = await HotelOrder.filter(
                project_id=project_id,
                order_status='initial',
                is_deleted=False
            ).all()

            if len(initial_orders) == 0:
                raise HTTPException(status_code=400, detail="该项目暂无待提交的酒店订单")
        
        # 创建项目任务
        task = await ProjectTask.create(
            project_id=project_id,
            creator_user_id=current_user.id,
            creator_name=current_user.username,
            task_type="酒店预订",
            task_title=task_data.task_title,
            task_description=task_data.task_description,
            task_status="submitted",
            sms_notify=task_data.sms_notify,
            has_agent=task_data.has_agent,
            agent_phone=task_data.agent_phone if task_data.has_agent else None,
            agent_name=task_data.agent_name if task_data.has_agent else ""
        )
        
        logger.info(f"已创建预订任务: {task.task_id}, 项目: {project_id}, 创建人: {current_user.username}")
        
        # 导入TaskToHotelOrder模型
        from src.db.models.task_to_hotel_order import TaskToHotelOrder
        
        submitted_count = 0
        failed_count = 0
        
        # 处理每个订单
        for order in initial_orders:
            try:
                # 根据预订类型设置order_type
                order_type = "book" if task_data.booking_type == "book_only" else "book_and_issue"
                
                # 创建任务订单映射关系
                from datetime import datetime
                current_time = datetime.now()
                await TaskToHotelOrder.create(
                    project_id=project_id,
                    task_id=task.task_id,
                    order_id=order.id,
                    order_status="submitted",
                    order_type=order_type,
                    start_time=current_time,
                    end_time=current_time
                )

                # 自动补充代订人姓名（如果代订人为空且用户输入了代订人姓名）
                if task_data.has_agent and task_data.agent_name and not order.booking_agent:
                    order.booking_agent = task_data.agent_name
                    logger.info(f"订单 {order.id} 自动补充代订人姓名: {task_data.agent_name}")

                # 更新订单状态为submitted
                order.order_status = "submitted"
                await order.save()
                
                submitted_count += 1
                logger.info(f"订单 {order.id} 已提交到任务 {task.task_id}")
                
            except Exception as e:
                failed_count += 1
                logger.error(f"处理订单 {order.id} 失败: {str(e)}")
        
        # 生成响应消息
        if task_data.booking_type == "book_only":
            action_type = "预订任务"
        else:
            action_type = "预订且出票任务"
        
        message = f"成功创建{action_type}：{task.task_id}，已提交 {submitted_count} 条订单"
        if failed_count > 0:
            message += f"，{failed_count} 条订单处理失败"
        
        # 添加设置信息
        settings_info = []
        if task_data.sms_notify:
            settings_info.append("已启用短信通知")
        if task_data.has_agent and task_data.agent_phone:
            agent_info = f"代订人: {task_data.agent_name}({task_data.agent_phone})" if task_data.agent_name else f"代订人: {task_data.agent_phone}"
            settings_info.append(agent_info)
        
        if settings_info:
            message += f"（{', '.join(settings_info)}）"
        
        logger.info(f"预订任务创建完成: {message}")
        
        # 发送Kafka消息通知后端开始预订任务
        try:
            # 获取用户的系统设置（同程管家凭证）
            from src.services.system_settings_service import SystemSettingsService
            credentials = await SystemSettingsService.get_tongcheng_credentials(current_user.id)
            # 获取原始密码用于存储到消息中
            credentials_with_raw_password = await SystemSettingsService.get_tongcheng_credentials_with_raw_password(current_user.id)
            
            if credentials['username'] and credentials['password']:
                # 导入Kafka服务
                from src.services.kafka_service import send_hotel_booking_task
                
                # 获取所有已提交的订单ID
                submitted_order_ids = [order.id for order in initial_orders]
                
                # 获取当前登录用户信息
                login_user_name = current_user.username if current_user else ""

                # 发送Kafka消息 - 使用原始密文
                kafka_result = await send_hotel_booking_task(
                    task_id=task.task_id,
                    order_ids=submitted_order_ids,
                    username=credentials['username'],
                    password=credentials_with_raw_password['password'],  # 使用数据库原始密文
                    company_name=project.client_name,  # 从项目的client_name字段获取公司名称
                    send_sms=task_data.sms_notify,
                    has_agent=task_data.has_agent,
                    agent_phone=task_data.agent_phone if task_data.has_agent else None,
                    agent_name=task_data.agent_name if task_data.has_agent else "",
                    login_user_name=login_user_name
                )
                
                logger.info(
                    f"Kafka消息发送完成 - Task: {task.task_id}, "
                    f"成功: {kafka_result.get('success_count', 0)}, "
                    f"失败: {kafka_result.get('failed_count', 0)}, "
                    f"启用: {kafka_result.get('enabled', False)}"
                )
                
                # 将Kafka发送结果添加到响应消息中
                if kafka_result.get('enabled', False):
                    kafka_success = kafka_result.get('success_count', 0)
                    kafka_failed = kafka_result.get('failed_count', 0)
                    if kafka_success > 0:
                        message += f"，已推送{kafka_success}条任务消息到处理队列"
                        
                        # 推送成功时，将消息内容存储到TaskToHotelOrder的message字段中
                        try:
                            import json
                            # 构建推送消息内容（与Kafka服务中的格式保持一致）
                            # 注意：存储的password使用数据库原始值，不是解密后的值
                            kafka_message_template = {
                                "task_id": task.task_id,
                                "module": "domestic_hotel_filling",
                                "username": credentials['username'],
                                "password": credentials_with_raw_password['password'],  # 使用数据库中的原始密码值
                                "company_name": project.client_name,  # 从项目的client_name字段获取公司名称
                                "send_sms": 1 if task_data.sms_notify else 0,
                                "has_agent": 1 if task_data.has_agent else 0,
                                "agent_phone": task_data.agent_phone if task_data.has_agent else "",
                                "agent_name": task_data.agent_name if task_data.has_agent else "",
                                "login_user_name": login_user_name
                            }
                            
                            # 为每个成功推送的订单更新message字段
                            for order in initial_orders:
                                try:
                                    # 获取对应的TaskToHotelOrder记录
                                    task_order = await TaskToHotelOrder.get(
                                        task_id=task.task_id,
                                        order_id=order.id
                                    )
                                    
                                    # 构建该订单的完整Kafka消息内容
                                    order_kafka_message = kafka_message_template.copy()
                                    order_kafka_message["order_id"] = order.id
                                    
                                    # 将消息内容JSON格式存储到message字段
                                    task_order.message = json.dumps(order_kafka_message, ensure_ascii=False, indent=2)
                                    await task_order.save()
                                    
                                    logger.info(f"已将Kafka消息内容保存到TaskToHotelOrder - Task: {task.task_id}, Order: {order.id}")
                                    
                                except Exception as save_error:
                                    logger.error(f"保存Kafka消息内容失败 - Task: {task.task_id}, Order: {order.id}: {str(save_error)}")
                                    
                        except Exception as store_error:
                            logger.error(f"存储Kafka消息内容时发生异常: {str(store_error)}")
                    
                    if kafka_failed > 0:
                        message += f"，{kafka_failed}条消息推送失败"
                else:
                    message += "，Kafka消息推送未启用"
            else:
                logger.warning(f"用户 {current_user.id} 未配置同程管家凭证，跳过Kafka消息发送")
                message += "，未配置同程管家凭证，请先在系统设置中配置"
                
        except Exception as e:
            logger.error(f"发送Kafka消息失败: {str(e)}")
            message += f"，任务消息推送失败: {str(e)}"
        
        return CreateBookingTaskResponse(
            task_id=task.task_id,
            project_id=project_id,
            task_title=task.task_title,
            submitted_orders_count=submitted_count,
            message=message
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建预订任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建预订任务失败: {str(e)}")

@router.get("/task/{task_id}", response_model=HotelOrderListResponse, summary="获取任务的酒店订单")
async def get_orders_by_task(
    task_id: str,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=1000, description="每页数量"),
    status: Optional[str] = Query(None, description="订单状态筛选"),
    guest_name: Optional[str] = Query(None, description="入住人姓名搜索"),
    mobile_phone: Optional[str] = Query(None, description="手机号搜索"),
    contact_phone: Optional[str] = Query(None, description="联系人手机号搜索"),
    current_user: User = Depends(get_current_user)
):
    """获取任务的酒店订单列表"""
    try:
        logger.info(f"获取任务 {task_id} 的酒店订单 - page: {page}, page_size: {page_size}, status: {status}, guest_name: {guest_name}, mobile_phone: {mobile_phone}, contact_phone: {contact_phone}")

        # 导入TaskToHotelOrder模型
        from src.db.models.task_to_hotel_order import TaskToHotelOrder
        
        # 通过task_to_hotel_orders表获取关联的订单ID
        task_mappings = await TaskToHotelOrder.filter(
            task_id=task_id, 
            is_deleted=False
        ).all()
        order_ids = [mapping.order_id for mapping in task_mappings]
        
        if not order_ids:
            # 如果没有关联的订单，返回空列表
            return HotelOrderListResponse(
                total=0,
                page=page,
                page_size=page_size,
                items=[]
            )
        
        # 构建基础查询
        base_query = HotelOrder.filter(
            id__in=order_ids,
            is_deleted=False
        )

        # 添加状态筛选
        if status:
            base_query = base_query.filter(order_status=status)

        # 添加入住人姓名搜索
        if guest_name:
            base_query = base_query.filter(guest_full_name__icontains=guest_name)

        # 添加手机号搜索
        if mobile_phone:
            base_query = base_query.filter(guest_mobile_phone__icontains=mobile_phone)

        # 添加联系人手机号搜索
        if contact_phone:
            base_query = base_query.filter(contact_phone__icontains=contact_phone)
        
        # 计算总数
        total = await base_query.count()
        
        # 获取分页数据
        orders = await base_query.offset((page - 1) * page_size).limit(page_size).all()
        
        # 构建响应数据
        return HotelOrderListResponse(
            total=total,
            page=page,
            page_size=page_size,
            items=[
                {
                    'id': order.id,
                    'project_id': order.project_id,
                    'sequence_number': order.sequence_number,
                    'guest_full_name': order.guest_full_name,
                    'guest_surname': order.guest_surname,
                    'guest_given_name': order.guest_given_name,
                    'guest_nationality': order.guest_nationality,
                    'guest_gender': order.guest_gender,
                    'guest_birth_date': order.guest_birth_date,
                    'guest_id_type': order.guest_id_type,
                    'guest_id_number': order.guest_id_number,
                    'guest_id_expiry_date': order.guest_id_expiry_date,
                    'guest_mobile_country_code': order.guest_mobile_country_code,
                    'guest_mobile_phone': order.guest_mobile_phone,
                    'guest_email': order.guest_email,
                    'destination': order.destination,
                    'hotel_id': order.hotel_id,
                    'room_type': order.room_type,
                    'room_count': order.room_count,
                    'policy_name': order.policy_name,
                    'include_breakfast': order.include_breakfast,
                    'is_half_day_room': order.is_half_day_room,
                    'check_in_time': order.check_in_time,
                    'check_out_time': order.check_out_time,
                    'is_group_booking': order.is_group_booking,
                    'group_booking_name': order.group_booking_name,
                    'room_number': order.room_number,
                    'payment_method': order.payment_method,
                    'invoice_type': order.invoice_type,
                    'tax_rate': int(order.tax_rate) if order.tax_rate is not None else None,  # 转换为整型
                    'agreement_type': order.agreement_type,
                    'supplier_name': order.supplier_name,
                    'payment_channel': order.payment_channel,
                    'payment_transaction_id': order.payment_transaction_id,
                    'cost_per_room': str(order.cost_per_room) if order.cost_per_room is not None else None,  # 保持字符串
                    'hidden_service_fee': str(order.hidden_service_fee) if order.hidden_service_fee is not None else None,  # 保持字符串
                    'cancellation_policy': order.cancellation_policy,
                    'is_violation': order.is_violation,
                    'contact_person': order.contact_person,
                    'contact_mobile_country_code': order.contact_mobile_country_code,
                    'contact_mobile_phone': order.contact_mobile_phone,
                    'order_remarks': order.order_remarks,
                    'cost_center': order.cost_center,
                    'trip_submission_item': order.trip_submission_item,
                    'approver': order.approver,
                    'roommate_name': order.roommate_name,
                    'roommate_surname': order.roommate_surname,
                    'roommate_given_name': order.roommate_given_name,
                    'roommate_nationality': order.roommate_nationality,
                    'roommate_gender': order.roommate_gender,
                    'roommate_birth_date': order.roommate_birth_date,
                    'roommate_id_type': order.roommate_id_type,
                    'roommate_id_number': order.roommate_id_number,
                    'roommate_id_expiry_date': order.roommate_id_expiry_date,
                    'roommate_mobile_country_code': order.roommate_mobile_country_code,
                    'roommate_mobile_phone': order.roommate_mobile_phone,
                    'roommate_email': order.roommate_email,
                    'company_name': order.company_name,
                    'hotel_name': order.hotel_name,
                    'amount': order.amount,  # 保持Decimal类型
                    'booking_agent': order.booking_agent,
                    'order_number': order.order_number,
                    'bill_number': order.bill_number,
                    'order_status': order.order_status,
                    'fail_reason': order.fail_reason,
                    'created_at': order.created_at,
                    'updated_at': order.updated_at,
                    'is_deleted': order.is_deleted
                }
                for order in orders
            ]
        )

        logger.info(f"任务 {task_id} 订单查询成功 - 总数: {total}, 返回: {len(orders)}")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务 {task_id} 订单失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取任务订单失败: {str(e)}")

@router.post("/task/{task_id}/clean-duplicates", response_model=dict, summary="清理任务中的重复订单")
async def clean_duplicate_orders(
    task_id: str,
    current_user: User = Depends(get_current_user)
):
    """清理任务中的重复订单关联记录"""
    try:
        # 验证任务是否存在
        task = await ProjectTask.get_or_none(id=task_id)
        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        # 导入TaskToHotelOrder模型
        from src.db.models.task_to_hotel_order import TaskToHotelOrder
        
        # 获取所有关联记录
        task_orders = await TaskToHotelOrder.filter(
            task_id=task_id,
            is_deleted=False
        ).order_by('id').all()
        
        # 使用字典来跟踪每个订单的第一条记录
        unique_orders = {}
        duplicate_count = 0
        
        for task_order in task_orders:
            key = (task_order.task_id, task_order.order_id)
            if key not in unique_orders:
                unique_orders[key] = task_order
            else:
                # 标记重复记录为已删除
                task_order.is_deleted = True
                await task_order.save()
                duplicate_count += 1
                logger.info(f"标记重复记录为已删除 - Task: {task_id}, Order: {task_order.order_id}, Record ID: {task_order.id}")
        
        return {
            "message": f"清理完成，共删除 {duplicate_count} 条重复记录",
            "duplicate_count": duplicate_count
        }
        
    except Exception as e:
        logger.error(f"清理重复订单失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"清理重复订单失败: {str(e)}")

@router.get("/task/{task_id}/stats", response_model=TaskOrderStatsResponse, summary="获取任务订单统计")
async def get_task_order_stats(task_id: str):
    """
    获取指定任务的订单统计信息
    包括各个状态的订单数量和总金额
    """
    try:
        # 验证任务是否存在
        try:
            task = await ProjectTask.get(task_id=task_id)
        except DoesNotExist:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        # 导入TaskToHotelOrder模型
        from src.db.models.task_to_hotel_order import TaskToHotelOrder
        
        # 通过task_to_hotel_orders表获取关联的订单ID
        task_mappings = await TaskToHotelOrder.filter(task_id=task_id).all()
        order_ids = [mapping.order_id for mapping in task_mappings]
        
        if not order_ids:
            # 如果没有关联的订单，返回空统计
            return TaskOrderStatsResponse(
                task_id=task_id,
                order_count=0,
                submitted_orders=0,
                processing_orders=0,
                completed_orders=0,
                failed_orders=0,
                total_amount=0,
                total_people=0
            )
        
        # 获取关联的订单
        orders = await HotelOrder.filter(
            id__in=order_ids,
            is_deleted=False
        ).all()
        
        # 统计各个状态的订单数量
        order_count = len(orders)
        submitted_orders = len([o for o in orders if o.order_status == 'submitted'])
        processing_orders = len([o for o in orders if o.order_status == 'processing'])
        completed_orders = len([o for o in orders if o.order_status == 'completed'])
        failed_orders = len([o for o in orders if o.order_status == 'failed'])
        
        # 计算总金额和总人数
        total_amount = sum(order.amount or 0 for order in orders)
        total_people = order_count  # 每个订单代表一个人
        
        logger.info(f"获取任务 {task_id} 统计信息成功：订单{order_count}个，已提交{submitted_orders}，处理中{processing_orders}，已完成{completed_orders}，失败{failed_orders}")
        
        return TaskOrderStatsResponse(
            task_id=task_id,
            order_count=order_count,
            submitted_orders=submitted_orders,
            processing_orders=processing_orders,
            completed_orders=completed_orders,
            failed_orders=failed_orders,
            total_amount=total_amount,
            total_people=total_people
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务统计信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取任务统计信息失败: {str(e)}")

@router.get("/{project_id}/orders", response_model=HotelOrderListResponse, summary="获取项目酒店订单列表")
async def get_project_orders(
    project_id: int,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页数量"),
    order_status: Optional[str] = Query(None, description="订单状态过滤"),
    search_text: Optional[str] = Query(None, description="搜索文本"),
):
    """获取项目酒店订单列表"""
    try:
        # 构建基础查询
        query = HotelOrder.filter(
            project_id=project_id,
            is_deleted=False
        )
        
        # 状态过滤
        if order_status:
            query = query.filter(order_status=order_status)
            
        # 搜索过滤
        if search_text:
            search_conditions = Q(
                Q(guest_full_name__icontains=search_text) |
                Q(guest_id_number__icontains=search_text) |
                Q(guest_mobile_phone__icontains=search_text) |
                Q(hotel_name__icontains=search_text)
            )
            query = query.filter(search_conditions)
        
        # 计算总数
        total = await query.count()
        
        # 添加排序：按创建时间倒序
        query = query.order_by('-created_at')
        
        # 分页
        items = await query.offset((page - 1) * page_size).limit(page_size).all()
        
        return HotelOrderListResponse(
            total=total,
            page=page,
            page_size=page_size,
            items=items
        )
        
    except Exception as e:
        logger.error(f"获取酒店订单列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取酒店订单列表失败: {str(e)}")

@router.post("/task/{task_id}/add-orders", response_model=List[int], summary="添加订单到任务")
async def add_orders_to_task(
    task_id: str,
    order_ids: List[int],
    current_user: User = Depends(get_current_user)
):
    """添加订单到任务"""
    try:
        # 导入TaskToHotelOrder模型
        from src.db.models.task_to_hotel_order import TaskToHotelOrder
        
        # 检查任务是否存在
        task = await ProjectTask.get_or_none(id=task_id)
        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        # 检查订单是否存在
        orders = await HotelOrder.filter(id__in=order_ids).all()
        if len(orders) != len(order_ids):
            raise HTTPException(status_code=400, detail="部分订单不存在")
        
        # 获取已存在的关联记录
        existing_relations = await TaskToHotelOrder.filter(
            task_id=task_id,
            order_id__in=order_ids,
            is_deleted=False
        ).values_list('order_id', flat=True)
        
        # 过滤出需要新增的订单ID
        new_order_ids = [oid for oid in order_ids if oid not in existing_relations]
        
        if not new_order_ids:
            return order_ids  # 所有订单都已经关联，直接返回
        
        # 创建新的关联记录
        relations = [
            TaskToHotelOrder(
                task_id=task_id,
                order_id=order_id
            )
            for order_id in new_order_ids
        ]
        
        # 批量创建关联
        if relations:
            await TaskToHotelOrder.bulk_create(relations)
            logger.info(f"成功添加 {len(relations)} 条新的订单关联到任务 {task_id}")
        
        return order_ids
    except Exception as e:
        logger.error(f"添加订单到任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"添加订单到任务失败: {str(e)}")

@router.post("/task/{task_id}/remove-orders", response_model=List[int], summary="从任务中移除订单")
async def remove_orders_from_task(
    task_id: str,
    order_ids: List[int],
    current_user: User = Depends(get_current_user)
):
    """从任务中移除订单"""
    try:
        # 导入TaskToHotelOrder模型
        from src.db.models.task_to_hotel_order import TaskToHotelOrder
        
        # 检查任务是否存在
        task = await ProjectTask.get_or_none(id=task_id)
        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        # 软删除关联记录
        await TaskToHotelOrder.filter(
            task_id=task_id,
            order_id__in=order_ids
        ).update(is_deleted=True)
        
        return order_ids
    except Exception as e:
        logger.error(f"从任务中移除订单失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"从任务中移除订单失败: {str(e)}")


@router.get("/project/{project_id}/export", summary="导出项目酒店订单Excel")
async def export_project_orders(
    project_id: int,
    order_status: Optional[str] = Query(None, description="订单状态筛选，支持多个状态用逗号分隔"),
    guest_name: Optional[str] = Query(None, description="入住人姓名搜索"),
    mobile_phone: Optional[str] = Query(None, description="手机号搜索"),
    contact_phone: Optional[str] = Query(None, description="联系人手机号搜索"),
    export_format: str = Query("xlsx", description="导出格式：xlsx 或 csv"),
    selected_fields: Optional[str] = Query(None, description="选择的字段，用逗号分隔"),
    max_rows: int = Query(50000, description="最大导出行数")
):
    """
    导出项目酒店订单数据
    
    Args:
        project_id: 项目ID
        order_status: 订单状态筛选
        guest_name: 入住人姓名搜索
        mobile_phone: 手机号搜索
        contact_phone: 联系人手机号搜索
        export_format: 导出格式
        selected_fields: 选择的字段
        max_rows: 最大导出行数
    """
    from fastapi.responses import StreamingResponse
    import xlsxwriter
    import csv
    
    try:
        # 构建查询条件
        query = HotelOrder.filter(project_id=project_id, is_deleted=False)
        
        # 状态筛选
        if order_status:
            status_list = [status.strip() for status in order_status.split(',')]
            query = query.filter(order_status__in=status_list)
        
        # 搜索条件
        if guest_name:
            query = query.filter(guest_full_name__icontains=guest_name)
        if mobile_phone:
            query = query.filter(guest_mobile_phone__icontains=mobile_phone)
        if contact_phone:
            query = query.filter(contact_mobile_phone__icontains=contact_phone)
        
        # 限制导出数量并排序
        orders = await query.limit(max_rows).order_by('-created_at')
        
        if not orders:
            raise HTTPException(status_code=404, detail="没有找到符合条件的订单数据")
        
        # 定义所有可导出字段
        all_fields = {
            'sequence_number': '序号',
            'order_status': '订单状态',
            'guest_full_name': '入住人姓名',
            'guest_surname': '入住人姓',
            'guest_given_name': '入住人名',
            'guest_nationality': '国籍',
            'guest_gender': '性别',
            'guest_birth_date': '出生日期',
            'guest_id_type': '证件类型',
            'guest_id_number': '证件号码',
            'guest_id_expiry_date': '证件有效期至',
            'guest_mobile_phone': '手机号',
            'guest_email': '邮箱',
            'destination': '目的地',
            'hotel_id': '酒店ID',
            'hotel_name': '酒店名称',
            'room_type': '房间类型',
            'room_count': '房间数',
            'policy_name': '政策名称',
            'include_breakfast': '含早餐',
            'is_half_day_room': '半日房',
            'check_in_time': '入住时间',
            'check_out_time': '退房时间',
            'is_group_booking': '团体预订',
            'group_booking_name': '团体预订名称',
            'room_number': '房间号',
            'contact_person': '联系人',
            'contact_mobile_phone': '联系人手机号',
            'cost_center': '成本中心',
            'approver': '审批人',
            'company_name': '公司名称',
            'amount': '金额',
            'payment_method': '支付方式',
            'created_at': '创建时间',
            'updated_at': '更新时间',
            'fail_reason': '失败原因'
        }
        
        # 确定要导出的字段
        if selected_fields:
            field_list = [field.strip() for field in selected_fields.split(',')]
            export_fields = {k: v for k, v in all_fields.items() if k in field_list}
        else:
            export_fields = all_fields
        
        # 准备数据
        export_data = []
        for index, order in enumerate(orders):
            row = {}
            for field_key, field_label in export_fields.items():
                if field_key == 'sequence_number':
                    row[field_label] = index + 1
                elif field_key == 'order_status':
                    status_map = {
                        'initial': '待提交',
                        'submitted': '已提交',
                        'processing': '处理中',
                        'completed': '预定完成',
                        'failed': '预定失败',
                        'check_failed': '验证失败'
                    }
                    row[field_label] = status_map.get(getattr(order, field_key, ''), getattr(order, field_key, ''))
                elif field_key in ['include_breakfast', 'is_half_day_room', 'is_group_booking']:
                    # 处理是否字段
                    value = getattr(order, field_key, '')
                    if value in ['是', '否']:
                        row[field_label] = value
                    elif value in ['1', 1, True, '1.0']:
                        row[field_label] = '是'
                    elif value in ['0', 0, False, '0.0']:
                        row[field_label] = '否'
                    else:
                        row[field_label] = value or '否'
                elif field_key in ['created_at', 'updated_at']:
                    # 处理时间字段
                    value = getattr(order, field_key)
                    if value:
                        row[field_label] = value.strftime('%Y-%m-%d %H:%M:%S')
                    else:
                        row[field_label] = '-'
                elif field_key in ['guest_birth_date', 'guest_id_expiry_date']:
                    # 处理日期字段
                    value = getattr(order, field_key)
                    if value:
                        if isinstance(value, str):
                            row[field_label] = value
                        else:
                            row[field_label] = value.strftime('%Y-%m-%d')
                    else:
                        row[field_label] = '-'
                elif field_key == 'tax_rate':
                    # 处理税率字段，导出时显示为百分号格式
                    value = getattr(order, field_key)
                    if value is not None:
                        row[field_label] = f"{value}%"
                    else:
                        row[field_label] = '-'
                else:
                    row[field_label] = getattr(order, field_key, '') or '-'
            
            export_data.append(row)
        
        # 生成文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        if export_format == 'csv':
            filename = f'酒店订单_项目{project_id}_{timestamp}.csv'
            
            # 生成CSV
            output = BytesIO()
            output_text = StringIO()
            
            if export_data:
                fieldnames = list(export_data[0].keys())
                writer = csv.DictWriter(output_text, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(export_data)
            
            # 转换为字节流并添加BOM
            csv_content = '\ufeff' + output_text.getvalue()
            output.write(csv_content.encode('utf-8'))
            output.seek(0)
            
            return StreamingResponse(
                BytesIO(csv_content.encode('utf-8')),
                media_type='text/csv',
                headers={"Content-Disposition": f"attachment; filename={filename}"}
            )
        else:
            filename = f'酒店订单_项目{project_id}_{timestamp}.xlsx'
            
            # 生成Excel
            output = BytesIO()
            workbook = xlsxwriter.Workbook(output, {'in_memory': True})
            worksheet = workbook.add_worksheet('酒店订单')
            
            # 定义样式
            header_format = workbook.add_format({
                'bold': True,
                'font_name': '微软雅黑',
                'font_size': 10,
                'bg_color': '#F3F4F6',
                'align': 'center',
                'valign': 'vcenter',
                'border': 1
            })
            
            cell_format = workbook.add_format({
                'font_name': '微软雅黑',
                'font_size': 10,
                'align': 'left',
                'valign': 'vcenter',
                'border': 1,
                'text_wrap': True
            })
            
            # 写入表头
            if export_data:
                headers = list(export_data[0].keys())
                for col, header in enumerate(headers):
                    worksheet.write(0, col, header, header_format)
                    # 设置列宽
                    worksheet.set_column(col, col, 15)
                
                # 写入数据
                for row_num, row_data in enumerate(export_data, 1):
                    for col, value in enumerate(row_data.values()):
                        worksheet.write(row_num, col, value, cell_format)
            
            workbook.close()
            output.seek(0)
            
            return StreamingResponse(
                output,
                media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                headers={"Content-Disposition": f"attachment; filename={filename}"}
            )
            
    except Exception as e:
        logger.error(f"导出失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"导出失败: {str(e)}")


def find_header_row(worksheet):
    """
    智能识别标题行位置
    在第1行或第2行中查找包含"编号"和"姓名"字段的行
    """
    for row_num in [1, 2]:
        row_values = []
        for col in range(1, worksheet.max_column + 1):
            cell = worksheet.cell(row=row_num, column=col)
            if cell.value is not None:
                # 确保cell.value转换为字符串
                cell_str = str(cell.value).strip()
                if cell_str:  # 只添加非空字符串
                    row_values.append(cell_str)

        # 检查是否包含关键字段
        row_text = ' '.join(row_values)
        if '编号' in row_text and '姓名' in row_text:
            return row_num

    # 默认返回第2行
    return 2


def find_matching_column(input_headers, target_patterns):
    """
    根据模式列表查找匹配的列
    """
    for header, col in input_headers.items():
        # 确保header是字符串类型
        header_str = str(header).strip() if header is not None else ''
        for pattern in target_patterns:
            if pattern in header_str:
                return col
    return None


def create_flexible_column_mapping(input_headers):
    """
    创建灵活的列映射关系
    """
    mapping = {}

    # 定义列名匹配模式
    column_patterns = {
        '编号': ['编号', '序号', '号码'],
        '姓名': ['姓名', '名字', '客人姓名'],
        '身份证号': ['身份证', '证件号', 'ID'],
        '联系方式': ['联系方式', '联系电话', '联系号码', '手机', '电话'],
        '入住时间': ['入住', '入住时间', '入住日期'],
        '离店时间': ['离店', '退房', '离店时间', '退房时间', '离店日期', '退房日期'],
        '房间安排': ['房间类型', '房间安排', '房型', '房间'],
        '隐藏手续费': ['隐藏手续费', '手续费', '服务费']
    }

    # 为每个目标列查找匹配的源列
    for target_col, patterns in column_patterns.items():
        source_col = find_matching_column(input_headers, patterns)
        if source_col:
            mapping[target_col] = source_col

    return mapping


@router.post("/convert-format")
async def convert_hotel_order_format(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user)
):
    """
    酒店订单格式转换
    将用户上传的Excel文件转换为系统标准模板格式
    """
    try:
        import openpyxl
        from openpyxl.utils import get_column_letter
        from pathlib import Path
        import tempfile
        import os
        from datetime import datetime, timedelta

        logger.info(f"用户 {current_user.username} 开始格式转换")

        # 验证文件类型
        if not file.filename.endswith(('.xlsx', '.xls')):
            raise HTTPException(status_code=400, detail="只支持Excel文件格式(.xlsx, .xls)")

        # 读取上传的文件
        file_content = await file.read()

        # 加载模板文件
        template_path = Path(__file__).parent.parent.parent / "templates" / "hotel_order_template.xlsx"
        if not template_path.exists():
            raise HTTPException(status_code=500, detail="模板文件不存在")

        # 使用临时文件处理
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as temp_input:
            temp_input.write(file_content)
            temp_input_path = temp_input.name

        try:
            # 读取上传的Excel文件
            input_wb = openpyxl.load_workbook(temp_input_path)
            input_ws = input_wb.active

            # 读取模板文件
            template_wb = openpyxl.load_workbook(template_path)
            template_ws = template_wb.active

            # 创建输出工作簿（复制模板）
            output_wb = openpyxl.load_workbook(template_path)
            output_ws = output_wb.active

            logger.info("开始处理Excel格式转换")

            # 智能识别标题行位置
            header_row = find_header_row(input_ws)
            data_start_row = header_row + 1

            logger.info(f"识别到标题行在第 {header_row} 行，数据从第 {data_start_row} 行开始")

            # 读取待转换文件的列名
            input_headers = {}
            for col in range(1, input_ws.max_column + 1):
                header_cell = input_ws.cell(row=header_row, column=col)
                if header_cell.value is not None:
                    # 确保表头值转换为字符串
                    header_str = str(header_cell.value).strip()
                    if header_str:  # 只添加非空表头
                        input_headers[header_str] = col

            logger.info(f"待转换文件列名: {list(input_headers.keys())}")

            # 读取模板文件的列名（第2行）
            template_headers = {}
            for col in range(1, template_ws.max_column + 1):
                header_cell = template_ws.cell(row=2, column=col)
                if header_cell.value is not None:
                    # 确保模板表头值转换为字符串
                    header_str = str(header_cell.value).strip()
                    if header_str:  # 只添加非空表头
                        template_headers[header_str] = col

            logger.info(f"模板文件列名: {list(template_headers.keys())}")

            # 创建灵活的列映射
            column_mapping = create_flexible_column_mapping(input_headers)
            logger.info(f"列映射关系: {column_mapping}")

            # 处理合并单元格数据（包含同住人逻辑）
            logger.info("开始处理合并单元格和同住人逻辑")
            converted_data = process_merged_cells_data(input_ws, input_headers, column_mapping, data_start_row)

            # 为所有单元格添加边框
            from openpyxl.styles import Border, Side
            thin_border = Border(
                left=Side(style='thin', color='000000'),
                right=Side(style='thin', color='000000'),
                top=Side(style='thin', color='000000'),
                bottom=Side(style='thin', color='000000')
            )

            # 为模板的前三行添加边框
            for row in range(1, 4):
                for col in range(1, output_ws.max_column + 1):
                    cell = output_ws.cell(row=row, column=col)
                    cell.border = thin_border

            # 写入转换后的数据到输出文件
            output_row = 4  # 从第4行开始写入
            converted_count = 0

            for converted_row in converted_data:
                # 写入到输出文件
                for field_name, value in converted_row.items():
                    if field_name in template_headers:
                        col_index = template_headers[field_name]
                        cell = output_ws.cell(row=output_row, column=col_index)

                        # 强制保存为字符串格式，避免科学计数法
                        if value is not None:
                            str_value = str(value)
                            # 调试：检查时间字段的值
                            if '时间' in field_name:
                                logger.info(f"写入Excel时间字段 {field_name}: 原始值={value} (类型: {type(value)}), 字符串值={str_value}")
                            cell.value = str_value
                            cell.data_type = 's'  # 强制设置为字符串类型
                            # 对于时间字段，设置数字格式为文本，但不添加单引号
                            if '时间' in field_name:
                                cell.number_format = '@'  # @ 表示文本格式
                        else:
                            cell.value = ''
                            cell.data_type = 's'

                        # 添加边框
                        cell.border = thin_border

                # 为当前行的所有列添加边框（包括空列）
                for col in range(1, output_ws.max_column + 1):
                    cell = output_ws.cell(row=output_row, column=col)
                    if cell.border != thin_border:  # 如果还没有边框，添加边框
                        cell.border = thin_border

                converted_count += 1
                output_row += 1
                logger.info(f"已转换第 {converted_count} 条数据")

            # 保存到临时文件
            with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as temp_output:
                output_wb.save(temp_output.name)
                temp_output_path = temp_output.name

            # 读取输出文件内容
            with open(temp_output_path, 'rb') as f:
                output_content = f.read()

            # 清理临时文件
            os.unlink(temp_input_path)
            os.unlink(temp_output_path)

            logger.info(f"格式转换完成，共转换 {converted_count} 条数据")

            # 生成文件名（使用英文避免编码问题）
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"hotel_order_converted_{timestamp}.xlsx"

            from fastapi.responses import Response
            return Response(
                content=output_content,
                media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                headers={
                    "Content-Disposition": f"attachment; filename={filename}",
                    "Content-Type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                }
            )

        finally:
            # 确保清理临时文件
            if os.path.exists(temp_input_path):
                os.unlink(temp_input_path)

    except Exception as e:
        logger.error(f"格式转换失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"格式转换失败: {str(e)}")


def get_flexible_field_value(row_data: dict, field_patterns: list) -> str:
    """
    根据模式列表灵活获取字段值
    """
    for pattern in field_patterns:
        for key, value in row_data.items():
            key_str = str(key) if key is not None else ''
            if pattern in key_str:
                return str(value).strip() if value else ''
    return ''


def convert_hotel_row_data(row_data: dict) -> dict:
    """
    转换单行酒店订单数据
    """
    # 灵活获取基础信息
    name = get_flexible_field_value(row_data, ['姓名', '名字', '客人姓名'])
    surname, given_name = split_name(name)
    id_card = get_flexible_field_value(row_data, ['身份证', '证件号', 'ID'])
    birth_date = extract_birth_date(id_card)
    phone = get_flexible_field_value(row_data, ['联系方式', '联系电话', '联系号码', '手机', '电话'])

    # 灵活获取房间安排
    room_arrangement = get_flexible_field_value(row_data, ['房间类型', '房间安排', '房型', '房间'])

    # 处理国籍：如果有身份证号则自动填写中国
    nationality = '中国' if is_id_card(id_card) else ''

    # 处理房型转换
    room_type = convert_room_type(room_arrangement)

    # 处理手机号国际区号
    international_code = get_international_code(phone)

    # 灵活获取其他字段
    sequence_number = get_flexible_field_value(row_data, ['编号', '序号', '号码'])
    gender = get_flexible_field_value(row_data, ['性别'])
    checkin_time = get_flexible_field_value(row_data, ['入住', '入住时间', '入住日期'])
    checkout_time = get_flexible_field_value(row_data, ['离店', '退房', '离店时间', '退房时间', '离店日期', '退房日期'])
    remark = get_flexible_field_value(row_data, ['备注', '说明'])
    hidden_fee = get_flexible_field_value(row_data, ['隐藏手续费', '手续费', '服务费'])

    # 生成证件有效期至（如果有入住人信息）
    guest_expiry_date = generate_random_expiry_date() if name and str(name).strip() else ''

    # 转换结果 - 所有字段都保存为字符串格式，避免科学计数法
    converted = {
        '序号': str(sequence_number),
        '入住人姓名': str(name),
        '入住人姓': str(surname),
        '入住人名': str(given_name),
        '入住人国籍': nationality,
        '入住人性别': str(gender),
        '入住人手机号国际区号': international_code,
        '入住人手机号': phone,
        '入住人证件号码': str(id_card),
        '入住人出生日期': str(birth_date),
        '入住人证件类型': '身份证' if is_id_card(id_card) else '',
        '入住人证件有效期至': guest_expiry_date,
        '预订房型': room_type,
        '预订房间数量': '1',  # 默认值：1
        '政策名称': '手工单政策',  # 默认值：手工单政策
        '入住时间': format_date_to_standard(checkin_time),
        '离店时间': format_date_to_standard(checkout_time),
        '是否为半日房': '否',  # 默认值：否
        '是否为团房': '非团房',  # 默认值：非团房
        '支付方式': '预付',  # 默认值：预付
        '取消规则': '不可取消',  # 默认值：不可取消
        '是否违规': '否',  # 默认值：否
        '订单备注': str(remark),
        '每间房成本': str(generate_cost_string(checkin_time, checkout_time)),
        '隐藏手续费': '',  # 保持为空，不做任何处理
        '同住人姓名': '',
        '同住人姓': '',
        '同住人名': '',
        '同住人国籍': '',
        '同住人性别': '',
        '同住人手机号国际区号': '',
        '同住人手机号': '',
        '同住人证件号码': '',
        '同住人出生日期': '',
        '同住人证件类型': '',
        '同住人证件有效期至': ''
    }

    return converted


def generate_random_expiry_date() -> str:
    """生成一年以后的随机日期，格式为YYYY-MM-DD"""
    import random
    from datetime import datetime, timedelta

    # 获取一年后的日期
    one_year_later = datetime.now() + timedelta(days=365)

    # 在一年后的基础上随机增加0-30天
    random_days = random.randint(0, 30)
    random_date = one_year_later + timedelta(days=random_days)

    return random_date.strftime('%Y-%m-%d')


def split_name(full_name: str) -> tuple:
    """拆分姓名为姓和名"""
    if not full_name:
        return '', ''
    full_name = str(full_name).strip()
    surname = full_name[0] if full_name else ''
    given_name = full_name[1:] if len(full_name) > 1 else ''
    return surname, given_name


def extract_birth_date(id_card: str) -> str:
    """从身份证号提取出生日期"""
    if not id_card:
        return ''
    id_card = str(id_card).strip()
    if len(id_card) != 18:
        return ''
    try:
        year = id_card[6:10]
        month = id_card[10:12]
        day = id_card[12:14]
        return f"{year}-{month}-{day}"
    except:
        return ''


def is_id_card(id_card: str) -> bool:
    """判断是否为身份证号"""
    if not id_card:
        return False
    id_card = str(id_card).strip()
    return len(id_card) == 18


def generate_cost_string(check_in: str, check_out: str, daily_cost: str = 'xxx') -> str:
    """生成成本字符串"""
    if not check_in or not check_out:
        return ''
    try:
        from datetime import datetime
        # 处理日期格式
        if isinstance(check_in, str):
            check_in_date = datetime.strptime(check_in, '%Y-%m-%d')
        else:
            check_in_date = check_in

        if isinstance(check_out, str):
            check_out_date = datetime.strptime(check_out, '%Y-%m-%d')
        else:
            check_out_date = check_out

        # 计算天数
        days = (check_out_date - check_in_date).days
        if days <= 0:
            return ''

        # 生成成本字符串
        cost_parts = []
        for i in range(days):
            current_date = check_in_date + timedelta(days=i)
            date_str = current_date.strftime('%Y-%m-%d')
            cost_parts.append(f"{date_str}:{daily_cost}")

        return ';'.join(cost_parts)
    except Exception as e:
        return ''


def convert_room_type(room_arrangement: str) -> str:
    """
    转换房型名称
    """
    if not room_arrangement:
        return ''

    room_str = str(room_arrangement).strip()

    # 房型转换规则
    if '大床' in room_str:
        return '标准大床房'
    elif '标间' in room_str or '双床' in room_str:
        return '标准双床房'
    else:
        return room_str


def get_international_code(phone: str) -> str:
    """
    获取手机号国际区号
    """
    if not phone:
        return ''

    phone_str = str(phone).strip()

    # 判断是否为国内手机号（11位数字，以1开头）
    if phone_str.isdigit() and len(phone_str) == 11 and phone_str.startswith('1'):
        return '86'

    return ''


def format_cell_value(cell_value) -> str:
    """
    格式化单元格值，特别处理长数字避免科学计数法
    """
    if cell_value is None:
        return ''

    # 如果是数字类型
    if isinstance(cell_value, (int, float)):
        # 转换为字符串并检查是否包含科学计数法
        str_value = str(cell_value)
        if 'e+' in str_value.lower() or 'e-' in str_value.lower():
            # 使用高精度格式化去除科学计数法
            if isinstance(cell_value, float):
                # 对于浮点数，使用高精度格式化，保留足够的精度
                formatted = f"{cell_value:.0f}"
                # 确保身份证号等长数字的完整性
                if len(formatted) >= 15:  # 可能是身份证号
                    # 使用decimal模块确保精度
                    from decimal import Decimal
                    try:
                        decimal_value = Decimal(str(cell_value))
                        formatted = str(int(decimal_value))
                    except:
                        formatted = f"{cell_value:.0f}"
            else:
                formatted = str(int(cell_value))
            return formatted
        else:
            # 对于大整数，确保不会被截断
            if isinstance(cell_value, float) and cell_value > 1e15:
                return f"{cell_value:.0f}"
            return str_value
    else:
        return str(cell_value).strip()


def format_date_to_standard(date_value) -> str:
    """
    将日期格式统一转换为 YYYY-MM-DD 格式
    """
    if date_value is None:
        return ''

    from datetime import datetime, timedelta

    logger.info(f"format_date_to_standard 输入: {date_value} (类型: {type(date_value)})")

    # 如果已经是datetime对象
    if isinstance(date_value, datetime):
        result = date_value.strftime('%Y-%m-%d')
        logger.info(f"datetime对象转换结果: {result}")
        return result

    # 如果是数字，可能是Excel日期序列号
    if isinstance(date_value, (int, float)):
        try:
            # Excel日期序列号转换（从1900年1月1日开始计算）
            # 注意：Excel有一个1900年闰年的bug，所以需要减去2天
            excel_epoch = datetime(1899, 12, 30)  # Excel的实际起始日期
            converted_date = excel_epoch + timedelta(days=date_value)
            result = converted_date.strftime('%Y-%m-%d')
            logger.info(f"Excel日期序列号转换: {date_value} -> {result}")
            return result
        except (ValueError, OverflowError):
            logger.warning(f"Excel日期序列号转换失败: {date_value}")
            # 如果转换失败，继续按字符串处理
            pass

    # 如果是字符串形式的数字，也可能是Excel日期序列号
    if isinstance(date_value, str) and date_value.strip().isdigit():
        try:
            # 尝试转换为数字
            numeric_value = int(date_value.strip())
            # 检查是否在合理的Excel日期序列号范围内（1900-2100年大约是1-73000）
            if 1 <= numeric_value <= 100000:
                excel_epoch = datetime(1899, 12, 30)  # Excel的实际起始日期
                converted_date = excel_epoch + timedelta(days=numeric_value)
                result = converted_date.strftime('%Y-%m-%d')
                logger.info(f"字符串Excel日期序列号转换: {date_value} -> {result}")
                return result
        except (ValueError, OverflowError):
            logger.warning(f"字符串Excel日期序列号转换失败: {date_value}")
            # 如果转换失败，继续按字符串处理
            pass

    # 转换为字符串处理
    date_str = str(date_value).strip()
    if not date_str:
        return ''

    # 尝试多种日期格式解析
    formats = [
        '%Y-%m-%d %H:%M:%S',  # 2025-07-13 00:00:00
        '%Y-%m-%d',           # 2025-07-13
        '%m-%d',              # 07-13
        '%d-%m',              # 13-07
        '%Y/%m/%d',           # 2025/07/13
        '%m/%d/%Y',           # 07/13/2025
        '%d/%m/%Y',           # 13/07/2025
    ]

    for fmt in formats:
        try:
            parsed = datetime.strptime(date_str, fmt)
            # 如果只有月日，补充年份
            if fmt in ['%m-%d', '%d-%m']:
                parsed = parsed.replace(year=2025)
            return parsed.strftime('%Y-%m-%d')
        except ValueError:
            continue

    # 如果都失败了，尝试提取数字
    import re
    numbers = re.findall(r'\d+', date_str)
    if len(numbers) >= 3:
        year, month, day = int(numbers[0]), int(numbers[1]), int(numbers[2])
        # 判断年份位置
        if year < 100:  # 可能是两位年份
            if year > 50:
                year += 1900
            else:
                year += 2000
        elif year < 2000:  # 可能是月日年的顺序
            month, day, year = year, month, day
        return f"{year:04d}-{month:02d}-{day:02d}"
    elif len(numbers) == 2:
        month, day = int(numbers[0]), int(numbers[1])
        return f"2025-{month:02d}-{day:02d}"

    # 如果还是无法解析，返回原值
    return date_str


def process_merged_cells_data(input_ws, input_headers: dict, column_mapping: dict, data_start_row: int) -> list:
    """
    处理合并单元格数据，实现同住人逻辑
    规则：
    1. 首先检查房间安排列是否为两行合并单元格
    2. 如果不是合并单元格，则直接转换数据
    3. 如果是合并单元格，检查入住时间和离店时间列：
       - 如果时间列也合并且时间吻合 → 完全同住
       - 如果时间不完全重合 → 拆分为独立时间段和重合时间段
    """
    from openpyxl.utils import get_column_letter

    # 获取合并单元格信息
    merged_ranges = input_ws.merged_cells.ranges
    logger.info(f"发现 {len(merged_ranges)} 个合并单元格区域")

    # 创建合并单元格映射
    merged_map = {}
    for merged_range in merged_ranges:
        for row in range(merged_range.min_row, merged_range.max_row + 1):
            for col in range(merged_range.min_col, merged_range.max_col + 1):
                merged_map[(row, col)] = {
                    'min_row': merged_range.min_row,
                    'max_row': merged_range.max_row,
                    'min_col': merged_range.min_col,
                    'max_col': merged_range.max_col
                }

    # 使用列映射找到关键列
    room_col = column_mapping.get('房间安排')
    checkin_col = column_mapping.get('入住时间')
    checkout_col = column_mapping.get('离店时间')

    if not room_col:
        logger.warning("未找到房间安排列，使用普通处理模式")
        return process_normal_data(input_ws, input_headers, column_mapping, data_start_row)

    # 处理数据行
    processed_data = []
    processed_rows = set()

    for input_row in range(data_start_row, input_ws.max_row + 1):
        if input_row in processed_rows:
            continue

        # 检查是否有数据
        has_data = False
        for col in range(1, input_ws.max_column + 1):
            cell_value = input_ws.cell(row=input_row, column=col).value
            if cell_value and str(cell_value).strip():
                has_data = True
                break

        if not has_data:
            continue

        # 检查房间安排列是否为两行合并单元格
        if (input_row, room_col) in merged_map:
            merge_info = merged_map[(input_row, room_col)]

            # 只处理两行合并的情况
            if merge_info['max_row'] - merge_info['min_row'] == 1:  # 两行合并
                row1 = merge_info['min_row']
                row2 = merge_info['max_row']

                # 确保都是数据行
                if row1 >= data_start_row and row2 >= data_start_row:
                    # 检查时间列是否也合并
                    checkin_merged = checkin_col and (row1, checkin_col) in merged_map
                    checkout_merged = checkout_col and (row1, checkout_col) in merged_map

                    logger.info(f"发现房间安排合并单元格：行 {row1} 和 {row2}")
                    logger.info(f"入住时间列合并: {checkin_merged}, 离店时间列合并: {checkout_merged}")

                    # 读取两行数据
                    row1_data = {}
                    row2_data = {}

                    for header, col in input_headers.items():
                        cell1_value = input_ws.cell(row=row1, column=col).value
                        cell2_value = input_ws.cell(row=row2, column=col).value

                        # 确保header是字符串类型
                        header_str = str(header) if header is not None else ''

                        # 特殊处理时间字段，统一格式为 YYYY-MM-DD
                        if '时间' in header_str:
                            row1_data[header] = format_date_to_standard(cell1_value)
                            row2_data[header] = format_date_to_standard(cell2_value)
                        else:
                            # 特殊处理身份证号等长数字，避免科学计数法
                            row1_data[header] = format_cell_value(cell1_value)
                            row2_data[header] = format_cell_value(cell2_value)

                    # 根据时间列是否合并来决定处理方式
                    if checkin_merged and checkout_merged:
                        # 时间列也合并，说明是完全相同的时间，直接创建同住订单
                        logger.info("时间列也合并，创建完全同住订单")
                        roommate_orders = create_complete_roommate_order(row1_data, row2_data)
                        processed_data.extend(roommate_orders)
                    else:
                        # 时间列不合并，说明时间不同，需要拆分时间段
                        logger.info("时间列不合并，需要拆分时间段")
                        roommate_orders = split_overlapping_periods(row1_data, row2_data)
                        processed_data.extend(roommate_orders)

                    # 标记两行为已处理
                    processed_rows.add(row1)
                    processed_rows.add(row2)
                else:
                    # 不是数据行的合并单元格，按普通数据处理
                    row_data = {}
                    for header, col in input_headers.items():
                        cell_value = input_ws.cell(row=input_row, column=col).value
                        # 确保header是字符串类型
                        header_str = str(header) if header is not None else ''

                        # 特殊处理时间字段，统一格式为 YYYY-MM-DD
                        if '时间' in header_str:
                            row_data[header] = format_date_to_standard(cell_value)
                        else:
                            row_data[header] = format_cell_value(cell_value)

                    converted_row = convert_hotel_row_data(row_data)
                    processed_data.append(converted_row)
                    processed_rows.add(input_row)
            else:
                # 不是两行合并，按普通数据处理
                row_data = {}
                for header, col in input_headers.items():
                    cell_value = input_ws.cell(row=input_row, column=col).value
                    # 确保header是字符串类型
                    header_str = str(header) if header is not None else ''

                    # 特殊处理时间字段，统一格式为 YYYY-MM-DD
                    if '时间' in header_str:
                        row_data[header] = format_date_to_standard(cell_value)
                    else:
                        row_data[header] = format_cell_value(cell_value)

                converted_row = convert_hotel_row_data(row_data)
                processed_data.append(converted_row)
                processed_rows.add(input_row)
        else:
            # 房间安排列不是合并单元格，按普通数据处理
            row_data = {}
            for header, col in input_headers.items():
                cell_value = input_ws.cell(row=input_row, column=col).value
                # 确保header是字符串类型
                header_str = str(header) if header is not None else ''

                # 特殊处理时间字段，统一格式为 YYYY-MM-DD
                if '时间' in header_str:
                    row_data[header] = format_date_to_standard(cell_value)
                else:
                    row_data[header] = format_cell_value(cell_value)

            converted_row = convert_hotel_row_data(row_data)
            processed_data.append(converted_row)
            processed_rows.add(input_row)

    return processed_data


def process_two_person_roommate(row1_data: dict, row2_data: dict, input_ws, merged_map: dict,
                               checkin_col: int, checkout_col: int, row1: int, row2: int) -> list:
    """
    处理两人同住逻辑
    """
    # 检查入住时间和离店时间列是否也是合并单元格
    checkin_merged = (row1, checkin_col) in merged_map and (row2, checkin_col) in merged_map
    checkout_merged = (row1, checkout_col) in merged_map and (row2, checkout_col) in merged_map

    # 获取时间数据
    row1_checkin = row1_data.get('入住时间', '').strip()
    row1_checkout = row1_data.get('离店时间', '').strip()
    row2_checkin = row2_data.get('入住时间', '').strip()
    row2_checkout = row2_data.get('离店时间', '').strip()

    logger.info(f"时间列合并情况 - 入住时间: {checkin_merged}, 离店时间: {checkout_merged}")
    logger.info(f"第一人时间: {row1_checkin} 到 {row1_checkout}")
    logger.info(f"第二人时间: {row2_checkin} 到 {row2_checkout}")

    # 情况1：时间列也合并，或者时间完全相同 → 完全同住
    if ((checkin_merged and checkout_merged) or
        (row1_checkin == row2_checkin and row1_checkout == row2_checkout)):

        logger.info("时间完全吻合，处理为完全同住")
        return create_complete_roommate_order(row1_data, row2_data)

    # 情况2：时间不完全重合 → 需要拆分时间段
    else:
        logger.info("时间不完全重合，需要拆分时间段")
        return split_overlapping_periods(row1_data, row2_data)


def create_complete_roommate_order(row1_data: dict, row2_data: dict) -> list:
    """
    创建完全同住订单（时间完全吻合）
    """
    # 第一人作为主入住人
    main_guest = convert_hotel_row_data(row1_data)

    # 第二人作为同住人 - 使用灵活的字段获取方式
    roommate_name = get_flexible_field_value(row2_data, ['姓名', '名字', '客人姓名'])
    roommate_surname, roommate_given_name = split_name(roommate_name)
    roommate_id = get_flexible_field_value(row2_data, ['身份证', '证件号', 'ID'])
    roommate_birth_date = extract_birth_date(roommate_id)
    roommate_phone = get_flexible_field_value(row2_data, ['联系方式', '联系电话', '联系号码', '手机', '电话'])
    roommate_gender = get_flexible_field_value(row2_data, ['性别'])

    # 处理同住人国籍和手机号国际区号
    roommate_nationality = '中国' if is_id_card(roommate_id) else ''
    roommate_international_code = get_international_code(roommate_phone)

    # 生成同住人证件有效期至（如果有同住人信息）
    roommate_expiry_date = generate_random_expiry_date() if roommate_name and str(roommate_name).strip() else ''

    # 填入同住人信息
    main_guest.update({
        '同住人姓名': roommate_name,
        '同住人姓': roommate_surname,
        '同住人名': roommate_given_name,
        '同住人国籍': roommate_nationality,
        '同住人性别': roommate_gender,
        '同住人手机号国际区号': roommate_international_code,
        '同住人手机号': roommate_phone,
        '同住人证件号码': roommate_id,
        '同住人出生日期': roommate_birth_date,
        '同住人证件类型': '身份证' if is_id_card(roommate_id) else '',
        '同住人证件有效期至': roommate_expiry_date
    })

    logger.info(f"创建同住订单：{get_flexible_field_value(row1_data, ['姓名', '名字', '客人姓名'])} 和 {roommate_name}")
    return [main_guest]


def split_overlapping_periods(row1_data: dict, row2_data: dict) -> list:
    """
    拆分重叠时间段
    """
    from datetime import datetime, timedelta

    try:
        # 解析时间，现在输入数据已经是标准的 YYYY-MM-DD 格式
        def parse_standard_date(date_str):
            if not date_str:
                return None
            date_str = str(date_str).strip()
            if not date_str:
                return None

            try:
                # 标准格式 YYYY-MM-DD
                return datetime.strptime(date_str, '%Y-%m-%d')
            except ValueError:
                # 如果解析失败，尝试其他格式作为备用
                formats = [
                    '%Y-%m-%d %H:%M:%S',  # 2025-07-13 00:00:00
                    '%Y/%m/%d',           # 2025/07/13
                    '%m-%d',              # 07-13
                    '%d-%m'               # 13-07
                ]

                for fmt in formats:
                    try:
                        parsed = datetime.strptime(date_str, fmt)
                        # 如果只有月日，补充年份
                        if fmt in ['%m-%d', '%d-%m']:
                            parsed = parsed.replace(year=2025)
                        return parsed
                    except ValueError:
                        continue

                raise ValueError(f"无法解析日期: {date_str}")

        # 使用灵活的字段获取方式
        row1_checkin = parse_standard_date(get_flexible_field_value(row1_data, ['入住', '入住时间', '入住日期']))
        row1_checkout = parse_standard_date(get_flexible_field_value(row1_data, ['离店', '退房', '离店时间', '退房时间', '离店日期', '退房日期']))
        row2_checkin = parse_standard_date(get_flexible_field_value(row2_data, ['入住', '入住时间', '入住日期']))
        row2_checkout = parse_standard_date(get_flexible_field_value(row2_data, ['离店', '退房', '离店时间', '退房时间', '离店日期', '退房日期']))

        # 计算重叠时间段
        overlap_start = max(row1_checkin, row2_checkin)
        overlap_end = min(row1_checkout, row2_checkout)

        result = []

        # 如果有重叠时间段
        if overlap_start < overlap_end:
            logger.info(f"发现重叠时间段：{overlap_start.strftime('%Y-%m-%d')} 到 {overlap_end.strftime('%Y-%m-%d')}")

            # 按时间顺序处理所有时间段
            # 收集所有时间段，然后按时间顺序排序
            time_segments = []

            # 1. 收集第一人的前置独住时间段（如果存在）
            if row1_checkin < overlap_start:
                time_segments.append({
                    'start': row1_checkin,
                    'end': overlap_start,
                    'type': 'single',
                    'person': 1,
                    'data': row1_data.copy(),
                    'description': f"第一人独住：{row1_checkin.strftime('%Y-%m-%d')} 到 {overlap_start.strftime('%Y-%m-%d')}"
                })

            # 2. 收集第二人的前置独住时间段（如果存在）
            if row2_checkin < overlap_start:
                time_segments.append({
                    'start': row2_checkin,
                    'end': overlap_start,
                    'type': 'single',
                    'person': 2,
                    'data': row2_data.copy(),
                    'description': f"第二人独住：{row2_checkin.strftime('%Y-%m-%d')} 到 {overlap_start.strftime('%Y-%m-%d')}"
                })

            # 3. 收集重叠时间段的同住
            time_segments.append({
                'start': overlap_start,
                'end': overlap_end,
                'type': 'roommate',
                'person': 'both',
                'data1': row1_data.copy(),
                'data2': row2_data.copy(),
                'description': f"合住：{overlap_start.strftime('%Y-%m-%d')} 到 {overlap_end.strftime('%Y-%m-%d')}"
            })

            # 4. 收集第一人的后置独住时间段（如果存在）
            if row1_checkout > overlap_end:
                time_segments.append({
                    'start': overlap_end,
                    'end': row1_checkout,
                    'type': 'single',
                    'person': 1,
                    'data': row1_data.copy(),
                    'description': f"第一人独住：{overlap_end.strftime('%Y-%m-%d')} 到 {row1_checkout.strftime('%Y-%m-%d')}"
                })

            # 5. 收集第二人的后置独住时间段（如果存在）
            if row2_checkout > overlap_end:
                time_segments.append({
                    'start': overlap_end,
                    'end': row2_checkout,
                    'type': 'single',
                    'person': 2,
                    'data': row2_data.copy(),
                    'description': f"第二人独住：{overlap_end.strftime('%Y-%m-%d')} 到 {row2_checkout.strftime('%Y-%m-%d')}"
                })

            # 按开始时间排序
            time_segments.sort(key=lambda x: x['start'])

            # 按顺序处理每个时间段
            for segment in time_segments:
                logger.info(f"处理时间段：{segment['description']}")

                if segment['type'] == 'single':
                    # 单独住宿
                    data = segment['data']
                    # 更新时间字段
                    for checkin_field in ['入住', '入住时间', '入住日期']:
                        if checkin_field in data:
                            data[checkin_field] = segment['start'].strftime('%Y-%m-%d')
                    for checkout_field in ['离店', '退房', '离店时间', '退房时间', '离店日期', '退房日期']:
                        if checkout_field in data:
                            data[checkout_field] = segment['end'].strftime('%Y-%m-%d')

                    order = convert_hotel_row_data(data)
                    result.append(order)

                elif segment['type'] == 'roommate':
                    # 合住
                    data1 = segment['data1']
                    data2 = segment['data2']

                    # 更新第一人的时间字段
                    for checkin_field in ['入住', '入住时间', '入住日期']:
                        if checkin_field in data1:
                            data1[checkin_field] = segment['start'].strftime('%Y-%m-%d')
                    for checkout_field in ['离店', '退房', '离店时间', '退房时间', '离店日期', '退房日期']:
                        if checkout_field in data1:
                            data1[checkout_field] = segment['end'].strftime('%Y-%m-%d')

                    # 更新第二人的时间字段
                    for checkin_field in ['入住', '入住时间', '入住日期']:
                        if checkin_field in data2:
                            data2[checkin_field] = segment['start'].strftime('%Y-%m-%d')
                    for checkout_field in ['离店', '退房', '离店时间', '退房时间', '离店日期', '退房日期']:
                        if checkout_field in data2:
                            data2[checkout_field] = segment['end'].strftime('%Y-%m-%d')

                    roommate_order = create_complete_roommate_order(data1, data2)
                    result.extend(roommate_order)



        else:
            # 没有重叠，分别处理
            logger.info("时间段没有重叠，分别处理")
            result.append(convert_hotel_row_data(row1_data))
            result.append(convert_hotel_row_data(row2_data))

        return result

    except Exception as e:
        logger.error(f"时间段拆分失败: {str(e)}")
        # 出错时分别处理
        return [convert_hotel_row_data(row1_data), convert_hotel_row_data(row2_data)]


def process_normal_data(input_ws, input_headers: dict, column_mapping: dict, data_start_row: int) -> list:
    """
    处理普通数据（无合并单元格）
    """
    processed_data = []

    for input_row in range(data_start_row, input_ws.max_row + 1):
        # 检查是否有数据
        has_data = False
        for col in range(1, input_ws.max_column + 1):
            cell_value = input_ws.cell(row=input_row, column=col).value
            if cell_value and str(cell_value).strip():
                has_data = True
                break

        if not has_data:
            continue

        # 读取当前行数据
        row_data = {}
        for header, col in input_headers.items():
            cell_value = input_ws.cell(row=input_row, column=col).value
            # 确保header是字符串类型
            header_str = str(header) if header is not None else ''

            # 特殊处理时间字段，统一格式为 YYYY-MM-DD
            if '时间' in header_str:
                row_data[header] = format_date_to_standard(cell_value)
            else:
                row_data[header] = format_cell_value(cell_value)

        converted_row = convert_hotel_row_data(row_data)
        processed_data.append(converted_row)

    return processed_data


@router.post("/project/{project_id}/pause-submitted", response_model=PauseOrdersResponse, summary="暂停已提交的酒店订单")
async def pause_submitted_orders(
    project_id: int,
    pause_data: PauseOrdersRequest,
    current_user: User = Depends(get_current_user)
):
    """
    暂停指定项目中已提交状态的酒店订单

    - 如果指定了order_ids，则只暂停指定的订单（必须是submitted状态）
    - 如果未指定order_ids，则暂停项目中所有submitted状态的订单
    """
    try:
        # 验证项目是否存在
        try:
            project = await Project.get(id=project_id)
        except DoesNotExist:
            raise HTTPException(status_code=404, detail="项目不存在")

        # 构建查询条件
        query_conditions = {
            'project_id': project_id,
            'is_deleted': False,
            'order_status': 'submitted'
        }

        # 如果指定了订单ID，添加ID筛选条件
        if pause_data.order_ids:
            query_conditions['id__in'] = pause_data.order_ids

        # 查询符合条件的订单
        orders = await HotelOrder.filter(**query_conditions).all()

        if not orders:
            message = "没有找到可暂停的已提交订单"
            if pause_data.order_ids:
                message = f"指定的订单中没有找到可暂停的已提交订单"

            return PauseOrdersResponse(
                paused_count=0,
                message=message
            )

        # 更新订单状态为paused
        paused_count = 0
        for order in orders:
            order.order_status = 'paused'
            await order.save()
            paused_count += 1
            logger.info(f"酒店订单 {order.id} 已暂停处理，操作人: {current_user.username}")

        message = f"成功暂停 {paused_count} 条已提交酒店订单"
        logger.info(f"项目 {project_id} 暂停酒店订单操作完成: {message}，操作人: {current_user.username}")

        return PauseOrdersResponse(
            paused_count=paused_count,
            message=message
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"暂停酒店订单失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"暂停酒店订单失败: {str(e)}")


@router.post("/project/{project_id}/reset-paused", response_model=ResetPausedOrdersResponse, summary="重置已暂停的酒店订单")
async def reset_paused_orders(
    project_id: int,
    reset_data: ResetPausedOrdersRequest,
    current_user: User = Depends(get_current_user)
):
    """
    重置指定项目中已暂停状态的酒店订单为待提交状态

    - 如果指定了order_ids，则只重置指定的订单（必须是paused状态）
    - 如果未指定order_ids，则重置项目中所有paused状态的订单
    """
    try:
        # 验证项目是否存在
        try:
            project = await Project.get(id=project_id)
        except DoesNotExist:
            raise HTTPException(status_code=404, detail="项目不存在")

        # 构建查询条件
        query_conditions = {
            'project_id': project_id,
            'is_deleted': False,
            'order_status': 'paused'
        }

        # 如果指定了订单ID，添加ID筛选条件
        if reset_data.order_ids:
            query_conditions['id__in'] = reset_data.order_ids

        # 查询符合条件的订单
        orders = await HotelOrder.filter(**query_conditions).all()

        if not orders:
            message = "没有找到可重置的已暂停订单"
            if reset_data.order_ids:
                message = f"指定的订单中没有找到可重置的已暂停订单"

            return ResetPausedOrdersResponse(
                reset_count=0,
                message=message
            )

        # 更新订单状态为initial
        reset_count = 0
        for order in orders:
            order.order_status = 'initial'
            await order.save()
            reset_count += 1
            logger.info(f"酒店订单 {order.id} 已重置为待提交状态，操作人: {current_user.username}")

        message = f"成功重置 {reset_count} 条已暂停酒店订单为待提交状态"
        logger.info(f"项目 {project_id} 重置酒店订单操作完成: {message}，操作人: {current_user.username}")

        return ResetPausedOrdersResponse(
            reset_count=reset_count,
            message=message
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"重置酒店订单失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"重置酒店订单失败: {str(e)}")


@router.post("/project/{project_id}/reset-failed", response_model=ResetFailedOrdersResponse, summary="重置预定失败的酒店订单")
async def reset_failed_orders(
    project_id: int,
    reset_data: ResetFailedOrdersRequest,
    current_user: User = Depends(get_current_user)
):
    """
    重置指定项目中预定失败状态的酒店订单为待提交状态

    - 如果指定了order_ids，则只重置指定的订单（必须是failed状态）
    - 如果未指定order_ids，则重置项目中所有failed状态的订单
    """
    try:
        # 验证项目是否存在
        try:
            project = await Project.get(id=project_id)
        except DoesNotExist:
            raise HTTPException(status_code=404, detail="项目不存在")

        # 构建查询条件
        query_conditions = {
            'project_id': project_id,
            'is_deleted': False,
            'order_status': 'failed'
        }

        # 如果指定了订单ID，添加ID筛选条件
        if reset_data.order_ids:
            query_conditions['id__in'] = reset_data.order_ids

        # 查询符合条件的订单
        orders = await HotelOrder.filter(**query_conditions).all()

        if not orders:
            message = "没有找到可重置的预定失败订单"
            if reset_data.order_ids:
                message = f"指定的订单中没有找到可重置的预定失败订单"

            return ResetFailedOrdersResponse(
                reset_count=0,
                message=message
            )

        # 更新订单状态为initial，并清空失败原因
        reset_count = 0
        for order in orders:
            order.order_status = 'initial'
            order.fail_reason = None
            await order.save()
            reset_count += 1
            logger.info(f"酒店订单 {order.id} 已重置为待提交状态，操作人: {current_user.username}")

        message = f"成功重置 {reset_count} 条预定失败酒店订单为待提交状态"
        logger.info(f"项目 {project_id} 重置预定失败酒店订单操作完成: {message}，操作人: {current_user.username}")

        return ResetFailedOrdersResponse(
            reset_count=reset_count,
            message=message
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"重置预定失败酒店订单失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"重置预定失败酒店订单失败: {str(e)}")




