"""飞机票订单API端点"""

import os
import uuid
import asyncio
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta
from fastapi import APIRouter, HTTPException, Depends, Query, UploadFile, File, Form
from fastapi.responses import FileResponse
from tortoise.exceptions import DoesNotExist
from tortoise.transactions import in_transaction
from loguru import logger
import openpyxl
from openpyxl.styles import Font, PatternFill, Border, Side
import pandas as pd
import re
import random

from src.db.models.flight_order import FlightOrder
from src.db.models.task_to_flight_order import TaskToFlightOrder
from src.db.models.project_task import ProjectTask
from src.db.models.project import Project
from src.db.models.user import User
from src.services.kafka_service import send_flight_booking_task
from src.services.system_settings_service import SystemSettingsService
from src.api.dependencies import get_current_user
from .schemas import (
    FlightOrderCreate,
    FlightOrderUpdate,
    FlightOrderResponse,
    FlightOrderListResponse,
    ExcelUploadResponse,
    ValidationError,
    ExcelValidationResponse,
    BookingRequest,
    BookingResponse,
    ClearOrdersResponse,
    CreateBookingTaskRequest,
    CreateBookingTaskResponse,
    ProjectOrderStatsResponse,
    ProjectOrderDetailStatsResponse,
    TaskOrderStatsResponse,
    PauseOrdersRequest,
    PauseOrdersResponse,
    ResetPausedOrdersRequest,
    ResetPausedOrdersResponse,
    ResetFailedOrdersRequest,
    ResetFailedOrdersResponse
)

router = APIRouter()


def is_id_card_number(id_number: str) -> bool:
    """判断是否为身份证号码格式"""
    if not id_number:
        return False

    id_number = str(id_number).strip()
    # 18位身份证号码格式验证
    pattern = r'^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$'
    return bool(re.match(pattern, id_number))


def extract_birth_date_from_id_card(id_number: str) -> str:
    """从身份证号码提取出生日期"""
    if not id_number or len(id_number) != 18:
        return ""

    try:
        year = id_number[6:10]
        month = id_number[10:12]
        day = id_number[12:14]
        return f"{year}-{month}-{day}"
    except:
        return ""


def extract_gender_from_id_card(id_number: str) -> str:
    """从身份证号码提取性别"""
    if not id_number or len(id_number) != 18:
        return ""

    try:
        # 第17位数字，奇数为男，偶数为女
        gender_digit = int(id_number[16])
        return "男" if gender_digit % 2 == 1 else "女"
    except:
        return ""


def split_full_name(full_name: str) -> tuple:
    """拆分姓名为姓和名"""
    if not full_name:
        return "", ""

    full_name = str(full_name).strip()
    if len(full_name) == 0:
        return "", ""

    surname = full_name[0]
    given_name = full_name[1:] if len(full_name) > 1 else ""
    return surname, given_name


def generate_random_expiry_date() -> str:
    """生成随机的证件有效期（未来5-10年）"""
    current_date = datetime.now()
    years_to_add = random.randint(5, 10)
    expiry_date = current_date + timedelta(days=years_to_add * 365)
    return expiry_date.strftime('%Y-%m-%d')


def auto_complete_traveler_info(order_data: dict, project_client_name: str = "") -> dict:
    """自动补充出行人信息"""
    completed_data = order_data.copy()

    # 获取相关字段
    id_number = completed_data.get('id_number', '').strip() if completed_data.get('id_number') else ''
    id_type = completed_data.get('id_type', '').strip() if completed_data.get('id_type') else ''
    full_name = completed_data.get('traveler_full_name', '').strip() if completed_data.get('traveler_full_name') else ''

    logger.info(f"开始自动补充出行人信息: id_number={id_number}, id_type={id_type}, full_name={full_name}")

    # 如果证件号码有内容并且是身份证格式
    if id_number and is_id_card_number(id_number):
        logger.info(f"检测到身份证号码: {id_number}，开始自动补齐信息")

        # 补齐证件类型
        if not id_type:
            completed_data['id_type'] = '身份证'
            logger.info(f"自动补齐证件类型: 身份证")

        # 补齐国籍
        if not completed_data.get('nationality', '').strip():
            completed_data['nationality'] = '中国'
            logger.info(f"自动补齐国籍: 中国")

        # 补齐性别
        if not completed_data.get('gender', '').strip():
            gender = extract_gender_from_id_card(id_number)
            if gender:
                completed_data['gender'] = gender
                logger.info(f"自动补齐性别: {gender}")

        # 补齐出生日期
        if not completed_data.get('birth_date', '').strip():
            birth_date = extract_birth_date_from_id_card(id_number)
            if birth_date:
                completed_data['birth_date'] = birth_date
                logger.info(f"自动补齐出生日期: {birth_date}")

        # 补齐证件有效期
        if not completed_data.get('id_expiry_date', '').strip():
            expiry_date = generate_random_expiry_date()
            completed_data['id_expiry_date'] = expiry_date
            logger.info(f"自动补齐证件有效期: {expiry_date}")

    # 如果姓名有内容，补齐姓和名
    if full_name:
        surname = completed_data.get('traveler_surname', '').strip() if completed_data.get('traveler_surname') else ''
        given_name = completed_data.get('traveler_given_name', '').strip() if completed_data.get('traveler_given_name') else ''

        if not surname or not given_name:
            auto_surname, auto_given_name = split_full_name(full_name)
            if not surname and auto_surname:
                completed_data['traveler_surname'] = auto_surname
                logger.info(f"自动补齐出行人姓: {auto_surname}")
            if not given_name and auto_given_name:
                completed_data['traveler_given_name'] = auto_given_name
                logger.info(f"自动补齐出行人名: {auto_given_name}")

    # 如果公司名称为空且有项目客户名称，则使用项目客户名称
    if not completed_data.get('company_name', '').strip() and project_client_name:
        completed_data['company_name'] = project_client_name
        logger.info(f"自动补齐公司名称: {project_client_name}")

    return completed_data


# 飞机票订单字段映射 - 基于29个字段的Excel模板
EXCEL_FIELD_MAPPING = {
    # Excel列名 -> 数据库字段名
    '序号': 'sequence_number',
    '出行人姓名': 'traveler_full_name',
    '出行人姓': 'traveler_surname', 
    '出行人名': 'traveler_given_name',
    '国籍': 'nationality',
    '性别': 'gender',
    '出生日期': 'birth_date',
    '证件类型': 'id_type',
    '证件号码': 'id_number',
    '证件有效期至': 'id_expiry_date',
    '手机号国际区号': 'mobile_country_code',
    '手机号': 'mobile_phone',
    '出行日期': 'travel_date',
    '出发机场名': 'departure_airport',
    '到达机场名': 'arrival_airport',
    '航班号': 'flight_number',
    '出发时间': 'departure_time',
    '到达时间': 'arrival_time',
    '行程提交项': 'trip_submission_item',
    '联系人': 'contact_person',
    '联系人手机号': 'contact_mobile_phone',
    '联系人邮箱': 'contact_email',
    '审批参照人': 'approver',
    '公司名称': 'company_name',
    '代订人': 'booking_agent',
    '出票短信': 'ticket_sms',
    '金额': 'amount',
    '订单号': 'order_number',
    '账单号': 'bill_number',
    '保险名称': 'insurance_name',
}

# 反向映射：数据库字段名 -> Excel列名
FIELD_TO_EXCEL_MAPPING = {v: k for k, v in EXCEL_FIELD_MAPPING.items()}

# 基础必填字段定义
BASIC_REQUIRED_FIELDS = {
    'traveler_full_name',  # 出行人姓名
    'id_type',             # 证件类型
    'id_number',           # 证件号码
    'mobile_phone',        # 手机号
    'travel_date',         # 出行日期
    'departure_airport',   # 出发机场名
    'arrival_airport',     # 到达机场名
    'flight_number',       # 航班号
    'departure_time',      # 出发时间
    'arrival_time',        # 到达时间
    'contact_person',      # 联系人
    'contact_mobile_phone', # 联系人手机号
    'approver',            # 审批参照人
}

# 非身份证证件类型的额外必填字段
NON_ID_CARD_REQUIRED_FIELDS = {
    'traveler_surname',    # 出行人姓
    'traveler_given_name', # 出行人名
    'nationality',         # 国籍
    'gender',              # 性别
    'birth_date',          # 出生日期
    'id_expiry_date',      # 证件有效期至
}

# 字段验证规则
FIELD_VALIDATION_RULES = {
    'traveler_full_name': {'max_length': 100},
    'traveler_surname': {'max_length': 50},
    'traveler_given_name': {'max_length': 50},
    'nationality': {'max_length': 50},
    'gender': {'max_length': 10},
    'birth_date': {'max_length': 50},
    'id_type': {'max_length': 30},
    'id_number': {'max_length': 50},
    'id_expiry_date': {'max_length': 50},
    'mobile_country_code': {'max_length': 10},
    'mobile_phone': {'max_length': 20},
    'travel_date': {'max_length': 50},
    'departure_airport': {'max_length': 100},
    'arrival_airport': {'max_length': 100},
    'flight_number': {'max_length': 20},
    'departure_time': {'max_length': 50},
    'arrival_time': {'max_length': 50},
    'contact_person': {'max_length': 50},
    'contact_mobile_phone': {'max_length': 20},
    'contact_email': {'max_length': 100},
    'approver': {'max_length': 50},
    'company_name': {'max_length': 100},
    'booking_agent': {'max_length': 50},
    'order_number': {'max_length': 100},
    'bill_number': {'max_length': 100},
    'insurance_name': {'max_length': 255},
}


def validate_field_value(field_name: str, value: Any, row_num: int, row_data: Dict[str, Any] = None) -> List[ValidationError]:
    """验证字段值"""
    errors = []

    # 获取证件类型以判断是否需要额外验证
    id_type = row_data.get('id_type', '') if row_data else ''
    is_id_card = id_type == '身份证'

    # 判断是否为必填字段
    is_required = field_name in BASIC_REQUIRED_FIELDS
    if not is_id_card and field_name in NON_ID_CARD_REQUIRED_FIELDS:
        is_required = True

    if value is None or (isinstance(value, str) and value.strip() == ''):
        if is_required:
            errors.append(ValidationError(
                row=row_num,
                field=FIELD_TO_EXCEL_MAPPING.get(field_name, field_name),
                message="必填字段不能为空",
                value=str(value) if value is not None else ""
            ))
        return errors

    # 转换为字符串进行验证
    str_value = str(value).strip()

    # 长度验证
    if field_name in FIELD_VALIDATION_RULES:
        rules = FIELD_VALIDATION_RULES[field_name]
        if 'max_length' in rules and len(str_value) > rules['max_length']:
            errors.append(ValidationError(
                row=row_num,
                field=FIELD_TO_EXCEL_MAPPING.get(field_name, field_name),
                message=f"字段长度不能超过{rules['max_length']}个字符",
                value=str_value
            ))

    return errors


def parse_excel_data(file_path: str, project_client_name: str = "") -> tuple[List[Dict[str, Any]], List[ValidationError]]:
    """解析Excel数据"""
    try:
        # 读取Excel文件，第二行作为表头，跳过第三行，从第四行开始读取数据
        df = pd.read_excel(file_path, header=1, skiprows=[2], engine='openpyxl')

        # 移除完全空白的行
        df = df.dropna(how='all')

        orders_data = []
        validation_errors = []

        for index, row in df.iterrows():
            row_num = index + 4  # Excel行号（第四行开始）
            
            # 跳过空行
            if row.isna().all():
                continue
            
            order_data = {'project_id': None}  # 项目ID将在调用时设置
            row_errors = []
            
            # 第一遍：收集所有字段值
            for excel_col, db_field in EXCEL_FIELD_MAPPING.items():
                if excel_col in df.columns:
                    value = row[excel_col]

                    # 处理NaN值
                    if pd.isna(value):
                        value = None
                    elif isinstance(value, str):
                        value = value.strip()
                        if value == '':
                            value = None

                    # 设置字段值
                    if db_field == 'sequence_number':
                        try:
                            order_data[db_field] = int(value) if value is not None else row_num - 2
                        except (ValueError, TypeError):
                            order_data[db_field] = row_num - 2
                    elif db_field == 'amount':
                        # 金额字段处理
                        if value is not None:
                            try:
                                # 尝试转换为浮点数
                                float_value = float(value)
                                order_data[db_field] = f"{float_value:.2f}"
                            except (ValueError, TypeError):
                                order_data[db_field] = str(value)
                        else:
                            order_data[db_field] = "0.00"
                    elif db_field == 'mobile_country_code':
                        # 手机号国际区号默认值处理
                        if value is None or str(value).strip() == '':
                            order_data[db_field] = "+86"
                        else:
                            order_data[db_field] = str(value)
                    else:
                        order_data[db_field] = str(value) if value is not None else ""

            # 自动补充出行人信息（基于身份证号码）
            order_data = auto_complete_traveler_info(order_data, project_client_name)

            # 第二遍：进行字段验证（现在有完整的row_data）
            for excel_col, db_field in EXCEL_FIELD_MAPPING.items():
                if excel_col in df.columns:
                    value = order_data.get(db_field)
                    # 验证字段值，传递完整的row_data用于条件验证
                    field_errors = validate_field_value(db_field, value, row_num, order_data)
                    row_errors.extend(field_errors)
            
            # 如果有验证错误，记录但继续处理
            validation_errors.extend(row_errors)
            
            # 即使有错误也添加到订单数据中，让上层决定如何处理
            orders_data.append(order_data)
        
        return orders_data, validation_errors
        
    except Exception as e:
        logger.error(f"解析Excel文件失败: {str(e)}")
        raise HTTPException(status_code=400, detail=f"解析Excel文件失败: {str(e)}")


@router.get("/project/{project_id}", response_model=FlightOrderListResponse, summary="获取项目的飞机票订单")
async def get_orders_by_project(
    project_id: int,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    order_status: Optional[str] = Query(None, description="订单状态筛选，支持多个状态用逗号分隔，如：submitted,processing"),
    traveler_name: Optional[str] = Query(None, description="出行人姓名搜索"),
    mobile_phone: Optional[str] = Query(None, description="手机号搜索"),
    contact_phone: Optional[str] = Query(None, description="联系人手机号搜索"),
    sort_by_failed_first: Optional[bool] = Query(True, description="是否将验证失败和预定失败的订单排在前面")
):
    """获取指定项目的飞机票订单，支持按出行人姓名、手机号、联系人手机号搜索"""
    try:
        # 验证项目是否存在
        try:
            await Project.get(id=project_id)
        except DoesNotExist:
            raise HTTPException(status_code=404, detail="项目不存在")
        
        # 构建基础查询
        query = FlightOrder.filter(
            project_id=project_id,
            is_deleted=False
        )
        
        # 状态筛选
        if order_status:
            status_list = [status.strip() for status in order_status.split(',')]
            query = query.filter(order_status__in=status_list)
        
        # 出行人姓名搜索
        if traveler_name:
            query = query.filter(traveler_full_name__icontains=traveler_name)
        
        # 手机号搜索
        if mobile_phone:
            query = query.filter(mobile_phone__icontains=mobile_phone)
        
        # 联系人手机号搜索
        if contact_phone:
            query = query.filter(contact_mobile_phone__icontains=contact_phone)
        
        # 排序
        if sort_by_failed_first:
            # 将验证失败和预定失败的订单排在前面
            query = query.order_by(
                "-order_status",  # failed, check_failed 会排在前面
                "-created_at"
            )
        else:
            query = query.order_by("-created_at")
        
        # 获取总数
        total = await query.count()
        
        # 分页
        offset = (page - 1) * page_size
        orders = await query.offset(offset).limit(page_size).all()
        
        # 转换为响应格式
        order_responses = []
        for order in orders:
            order_dict = {}
            for field in order._meta.fields_map.keys():
                value = getattr(order, field)
                # 处理Decimal类型的amount字段
                if field == 'amount' and value is not None:
                    value = str(value)
                order_dict[field] = value
            order_responses.append(FlightOrderResponse(**order_dict))
        
        return FlightOrderListResponse(
            total=total,
            page=page,
            page_size=page_size,
            items=order_responses
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取飞机票订单失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取飞机票订单失败: {str(e)}")


@router.get("/{order_id}", response_model=FlightOrderResponse, summary="获取飞机票订单详情")
async def get_order(order_id: int):
    """获取指定ID的飞机票订单详情"""
    try:
        order = await FlightOrder.get(id=order_id, is_deleted=False)
        
        # 转换为响应格式
        order_dict = {}
        for field in order._meta.fields_map.keys():
            value = getattr(order, field)
            order_dict[field] = value
        
        return FlightOrderResponse(**order_dict)
        
    except DoesNotExist:
        raise HTTPException(status_code=404, detail="飞机票订单不存在")
    except Exception as e:
        logger.error(f"获取飞机票订单详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取飞机票订单详情失败: {str(e)}")


@router.post("/", response_model=FlightOrderResponse, summary="创建飞机票订单")
async def create_order(
    order_data: FlightOrderCreate,
    current_user: User = Depends(get_current_user)
):
    """创建新的飞机票订单"""
    try:
        # 验证项目是否存在
        try:
            await Project.get(id=order_data.project_id)
        except DoesNotExist:
            raise HTTPException(status_code=404, detail="项目不存在")
        
        # 创建订单
        order_dict = order_data.model_dump()
        order = await FlightOrder.create(**order_dict)
        
        # 转换为响应格式
        response_dict = {}
        for field in order._meta.fields_map.keys():
            value = getattr(order, field)
            response_dict[field] = value
        
        return FlightOrderResponse(**response_dict)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建飞机票订单失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建飞机票订单失败: {str(e)}")


@router.put("/{order_id}", response_model=FlightOrderResponse, summary="更新飞机票订单")
async def update_order(
    order_id: int,
    order_data: FlightOrderUpdate,
    current_user: User = Depends(get_current_user)
):
    """更新指定ID的飞机票订单"""
    try:
        # 获取订单
        order = await FlightOrder.get(id=order_id, is_deleted=False)
        
        # 检查订单状态，某些状态下不允许编辑
        if order.order_status in ['submitted', 'processing', 'completed']:
            # 状态中文映射
            status_map = {
                'submitted': '已提交',
                'processing': '处理中',
                'completed': '已完成',
                'initial': '待提交',
                'failed': '失败'
            }
            status_cn = status_map.get(order.order_status, order.order_status)
            raise HTTPException(
                status_code=400,
                detail=f"订单状态为{status_cn}，不允许编辑"
            )
        
        # 更新字段
        update_data = order_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(order, field, value)
        
        await order.save()
        
        # 转换为响应格式
        response_dict = {}
        for field in order._meta.fields_map.keys():
            value = getattr(order, field)
            response_dict[field] = value
        
        return FlightOrderResponse(**response_dict)
        
    except DoesNotExist:
        raise HTTPException(status_code=404, detail="飞机票订单不存在")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新飞机票订单失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新飞机票订单失败: {str(e)}")


@router.put("/{order_id}/reconciliation", response_model=FlightOrderResponse, summary="更新飞机票订单对账单信息")
async def update_order_reconciliation(
    order_id: int,
    reconciliation_data: dict,
    current_user: User = Depends(get_current_user)
):
    """更新指定ID的飞机票订单对账单信息，不受订单状态限制"""
    try:
        # 获取订单
        order = await FlightOrder.get(id=order_id, is_deleted=False)

        # 定义允许更新的对账单字段
        allowed_fields = {
            'company_name', 'booking_agent', 'order_number',
            'bill_number', 'amount', 'ticket_sms'
        }

        # 只更新对账单相关字段
        for field, value in reconciliation_data.items():
            if field in allowed_fields:
                if field == 'amount' and value is not None:
                    # 处理金额字段，确保是有效的数字
                    try:
                        if isinstance(value, str) and value.strip():
                            value = float(value)
                        elif value == '' or value is None:
                            value = 0.0
                        setattr(order, field, value)
                    except (ValueError, TypeError):
                        raise HTTPException(
                            status_code=400,
                            detail=f"金额字段值无效: {value}"
                        )
                else:
                    setattr(order, field, value or "")

        await order.save()

        # 转换为响应格式
        response_dict = {}
        for field in order._meta.fields_map.keys():
            value = getattr(order, field)
            response_dict[field] = value

        return FlightOrderResponse(**response_dict)

    except DoesNotExist:
        raise HTTPException(status_code=404, detail="飞机票订单不存在")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新飞机票订单对账单信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新飞机票订单对账单信息失败: {str(e)}")


@router.delete("/{order_id}", summary="删除飞机票订单")
async def delete_order(
    order_id: int,
    current_user: User = Depends(get_current_user)
):
    """软删除指定ID的飞机票订单"""
    try:
        order = await FlightOrder.get(id=order_id, is_deleted=False)

        # 检查订单状态，某些状态下不允许删除
        if order.order_status in ['submitted', 'processing', 'completed']:
            raise HTTPException(
                status_code=400,
                detail=f"订单状态为 {order.order_status}，不允许删除"
            )

        await order.soft_delete()

        return {"message": "飞机票订单删除成功"}

    except DoesNotExist:
        raise HTTPException(status_code=404, detail="飞机票订单不存在")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除飞机票订单失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除飞机票订单失败: {str(e)}")


@router.post("/project/{project_id}/validate-excel", response_model=ExcelValidationResponse, summary="验证Excel文件")
async def validate_excel(
    project_id: int,
    file: UploadFile = File(...),
    sms_notify: bool = Form(False),
    current_user: User = Depends(get_current_user)
):
    """验证上传的Excel文件格式和数据"""
    try:
        # 验证项目是否存在并获取项目信息
        try:
            project = await Project.get(id=project_id)
        except DoesNotExist:
            raise HTTPException(status_code=404, detail="项目不存在")

        # 验证文件类型
        if not file.filename.endswith(('.xlsx', '.xls')):
            raise HTTPException(status_code=400, detail="只支持Excel文件格式(.xlsx, .xls)")

        # 保存临时文件
        temp_file_path = f"/tmp/{uuid.uuid4()}_{file.filename}"
        try:
            with open(temp_file_path, "wb") as buffer:
                content = await file.read()
                buffer.write(content)

            # 解析Excel数据
            orders_data, validation_errors = parse_excel_data(temp_file_path, project.client_name or "")

            # 检查重复订单
            duplicate_count = 0
            for i, order_data in enumerate(orders_data):
                row_num = i + 4  # Excel行号（第四行开始）

                # 检查是否存在重复订单（基于证件号码-出行日期-航班号）
                existing_order = await FlightOrder.filter(
                    project_id=project_id,
                    id_number=order_data.get('id_number'),
                    travel_date=order_data.get('travel_date'),
                    flight_number=order_data.get('flight_number'),
                    is_deleted=False
                ).first()

                if existing_order:
                    duplicate_count += 1
                    validation_errors.append(ValidationError(
                        row=row_num,
                        field="重复检查",
                        message=f"数据已存在：证件号码({order_data.get('id_number')}) + 出行日期({order_data.get('travel_date')}) + 航班号({order_data.get('flight_number')})",
                        value=""
                    ))

            total_rows = len(orders_data)
            valid_rows = total_rows - len([e for e in validation_errors if e.row <= total_rows])

            has_errors = len(validation_errors) > 0

            if has_errors:
                error_count = len(validation_errors) - duplicate_count
                if duplicate_count > 0 and error_count > 0:
                    message = f"验证完成，发现 {error_count} 个错误，{duplicate_count} 条重复数据，共 {total_rows} 行数据，{valid_rows} 行有效"
                elif duplicate_count > 0:
                    message = f"验证完成，发现 {duplicate_count} 条重复数据，共 {total_rows} 行数据，{valid_rows} 行有效"
                else:
                    message = f"验证完成，发现 {error_count} 个错误，共 {total_rows} 行数据，{valid_rows} 行有效"
            else:
                message = f"验证通过，共 {total_rows} 行数据，全部有效"

            return ExcelValidationResponse(
                has_errors=has_errors,
                errors=validation_errors,
                total_rows=total_rows,
                valid_rows=valid_rows,
                message=message
            )

        finally:
            # 清理临时文件
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"验证Excel文件失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"验证Excel文件失败: {str(e)}")


@router.post("/project/{project_id}/upload-excel", response_model=ExcelUploadResponse, summary="上传Excel文件")
async def upload_excel(
    project_id: int,
    file: UploadFile = File(...),
    sms_notify: bool = Form(False),
    current_user: User = Depends(get_current_user)
):
    """上传Excel文件并创建飞机票订单"""
    try:
        # 验证项目是否存在
        try:
            project = await Project.get(id=project_id)
        except DoesNotExist:
            raise HTTPException(status_code=404, detail="项目不存在")

        # 验证文件类型
        if not file.filename.endswith(('.xlsx', '.xls')):
            raise HTTPException(status_code=400, detail="只支持Excel文件格式(.xlsx, .xls)")

        # 保存临时文件
        temp_file_path = f"/tmp/{uuid.uuid4()}_{file.filename}"
        try:
            with open(temp_file_path, "wb") as buffer:
                content = await file.read()
                buffer.write(content)

            # 解析Excel数据
            orders_data, validation_errors = parse_excel_data(temp_file_path)

            # 批量创建订单
            created_orders = []
            failed_count = 0
            skipped_count = 0
            validation_failed_count = 0

            # 创建行号到验证错误的映射
            row_errors_map = {}
            for error in validation_errors:
                if error.row not in row_errors_map:
                    row_errors_map[error.row] = []
                row_errors_map[error.row].append(f"{error.field}: {error.message}")

            async with in_transaction():
                for i, order_data in enumerate(orders_data):
                    try:
                        order_data['project_id'] = project_id
                        row_num = i + 4  # Excel行号（第四行开始）

                        # 检查是否存在重复订单（基于证件号码-出行日期-航班号）
                        existing_order = await FlightOrder.filter(
                            project_id=project_id,
                            id_number=order_data.get('id_number'),
                            travel_date=order_data.get('travel_date'),
                            flight_number=order_data.get('flight_number'),
                            is_deleted=False
                        ).first()

                        if existing_order:
                            skipped_count += 1
                            logger.info(f"跳过重复订单：证件号码={order_data.get('id_number')}, 出行日期={order_data.get('travel_date')}, 航班号={order_data.get('flight_number')}")
                            continue

                        # 检查该行是否有验证错误
                        if row_num in row_errors_map:
                            # 有验证错误，设置失败原因和状态
                            fail_reason = "; ".join(row_errors_map[row_num])
                            order_data['fail_reason'] = fail_reason
                            order_data['order_status'] = 'check_failed'
                            validation_failed_count += 1
                        else:
                            # 无验证错误，设置为待提交状态
                            order_data['order_status'] = 'initial'

                        # 创建订单
                        order = await FlightOrder.create(**order_data)
                        created_orders.append(order)

                    except Exception as e:
                        logger.error(f"创建飞机票订单失败: {str(e)}")
                        failed_count += 1

            total_orders = len(created_orders)
            success_count = total_orders - validation_failed_count

            message_parts = []
            if success_count > 0:
                message_parts.append(f"成功导入 {success_count} 条飞机票订单")
            if validation_failed_count > 0:
                message_parts.append(f"{validation_failed_count} 条验证失败但已导入")
            if failed_count > 0:
                message_parts.append(f"{failed_count} 条创建失败")
            if skipped_count > 0:
                message_parts.append(f"{skipped_count} 条重复跳过")

            message = "，".join(message_parts) if message_parts else "导入完成"

            return ExcelUploadResponse(
                project_id=project_id,
                total_orders=total_orders,
                failed_orders=failed_count,
                skipped_duplicate_orders=skipped_count,
                message=message
            )

        finally:
            # 清理临时文件
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"上传Excel文件失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"上传Excel文件失败: {str(e)}")


@router.get("/project/{project_id}/stats", response_model=ProjectOrderStatsResponse, summary="获取项目飞机票订单统计")
async def get_project_stats(project_id: int):
    """获取项目的飞机票订单统计信息"""
    try:
        # 验证项目是否存在
        try:
            await Project.get(id=project_id)
        except DoesNotExist:
            raise HTTPException(status_code=404, detail="项目不存在")

        # 统计各状态订单数量
        total_orders = await FlightOrder.filter(project_id=project_id, is_deleted=False).count()
        initial_orders = await FlightOrder.filter(project_id=project_id, order_status="initial", is_deleted=False).count()
        submitted_orders = await FlightOrder.filter(project_id=project_id, order_status="submitted", is_deleted=False).count()
        processing_orders = await FlightOrder.filter(project_id=project_id, order_status="processing", is_deleted=False).count()
        completed_orders = await FlightOrder.filter(project_id=project_id, order_status="completed", is_deleted=False).count()
        failed_orders = await FlightOrder.filter(project_id=project_id, order_status="failed", is_deleted=False).count()
        check_failed_orders = await FlightOrder.filter(project_id=project_id, order_status="check_failed", is_deleted=False).count()

        return ProjectOrderStatsResponse(
            total_orders=total_orders,
            initial_orders=initial_orders,
            submitted_orders=submitted_orders,
            processing_orders=processing_orders,
            completed_orders=completed_orders,
            failed_orders=failed_orders,
            check_failed_orders=check_failed_orders
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取项目飞机票订单统计失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取项目飞机票订单统计失败: {str(e)}")


@router.get("/project/{project_id}/detail-stats", response_model=ProjectOrderDetailStatsResponse, summary="获取项目飞机票订单详细统计")
async def get_project_detail_stats(project_id: int):
    """获取项目的飞机票订单详细统计信息，包括金额统计"""
    try:
        # 验证项目是否存在
        try:
            await Project.get(id=project_id)
        except DoesNotExist:
            raise HTTPException(status_code=404, detail="项目不存在")

        # 统计各状态订单数量
        total_orders = await FlightOrder.filter(project_id=project_id, is_deleted=False).count()
        initial_orders = await FlightOrder.filter(project_id=project_id, order_status="initial", is_deleted=False).count()
        submitted_orders = await FlightOrder.filter(project_id=project_id, order_status="submitted", is_deleted=False).count()
        processing_orders = await FlightOrder.filter(project_id=project_id, order_status="processing", is_deleted=False).count()
        completed_orders = await FlightOrder.filter(project_id=project_id, order_status="completed", is_deleted=False).count()
        failed_orders = await FlightOrder.filter(project_id=project_id, order_status="failed", is_deleted=False).count()
        check_failed_orders = await FlightOrder.filter(project_id=project_id, order_status="check_failed", is_deleted=False).count()

        # 计算总金额
        orders = await FlightOrder.filter(project_id=project_id, is_deleted=False).all()
        total_amount = 0.0
        for order in orders:
            try:
                amount = float(order.amount) if order.amount else 0.0
                total_amount += amount
            except (ValueError, TypeError):
                continue

        return ProjectOrderDetailStatsResponse(
            total_orders=total_orders,
            initial_orders=initial_orders,
            submitted_orders=submitted_orders,
            processing_orders=processing_orders,
            completed_orders=completed_orders,
            failed_orders=failed_orders,
            check_failed_orders=check_failed_orders,
            total_amount=total_amount
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取项目飞机票订单详细统计失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取项目飞机票订单详细统计失败: {str(e)}")


@router.post("/project/{project_id}/create-booking-task", response_model=CreateBookingTaskResponse, summary="创建飞机票预订任务")
async def create_booking_task(
    project_id: int,
    task_data: CreateBookingTaskRequest,
    current_user: User = Depends(get_current_user)
):
    """
    创建飞机票预订任务，并将所有未提交的订单提交到该任务中

    - 创建project_tasks表中的预订任务
    - 将project_id下状态为initial的所有订单提交到该任务
    - 在task_to_flight_orders表中创建关联记录
    - 更新flight_orders表中的order_status为submitted
    """
    try:
        # 验证项目是否存在
        try:
            project = await Project.get(id=project_id)
        except DoesNotExist:
            raise HTTPException(status_code=404, detail="项目不存在")

        # 获取要处理的订单
        if task_data.order_ids:
            # 使用指定的订单ID列表
            orders = await FlightOrder.filter(
                id__in=task_data.order_ids,
                project_id=project_id,
                order_status="initial",
                is_deleted=False
            ).all()
        else:
            # 使用所有待提交订单
            orders = await FlightOrder.filter(
                project_id=project_id,
                order_status="initial",
                is_deleted=False
            ).all()

        if not orders:
            raise HTTPException(status_code=400, detail="没有可提交的飞机票订单")

        # 生成任务ID
        task_id = f"FLIGHT{datetime.now().strftime('%y%m%d%H%M%S')}"

        async with in_transaction():
            # 创建项目任务
            project_task = await ProjectTask.create(
                task_id=task_id,
                project_id=project_id,
                task_type="飞机票预订",
                task_title=task_data.task_title,
                task_description=task_data.task_description,
                task_status="submitted",
                sms_notify=task_data.sms_notify,
                has_agent=task_data.has_agent,
                agent_phone=task_data.agent_phone if task_data.has_agent else None,
                agent_name=task_data.agent_name if task_data.has_agent else "",
                creator_user_id=current_user.id,
                creator_name=current_user.username
            )

            # 创建任务订单关联记录并更新订单状态
            processed_count = 0
            for order in orders:
                # 创建关联记录
                await TaskToFlightOrder.create(
                    project_id=project_id,
                    task_id=task_id,
                    order_id=order.id,
                    order_status="initial",
                    order_type=task_data.booking_type
                )

                # 更新订单状态为已提交
                order.order_status = "submitted"

                # 自动补充公司名称和代订人信息
                # 如果公司名称为空且项目有客户名称，则使用项目客户名称
                if not order.company_name or not order.company_name.strip():
                    if project.client_name and project.client_name.strip():
                        order.company_name = project.client_name.strip()
                        logger.info(f"订单 {order.id} 自动补齐公司名称: {project.client_name}")

                # 如果代订人为空且任务有代订人信息，则使用任务代订人信息
                if not order.booking_agent or not order.booking_agent.strip():
                    if task_data.has_agent and task_data.agent_name and task_data.agent_name.strip():
                        order.booking_agent = task_data.agent_name.strip()
                        logger.info(f"订单 {order.id} 自动补齐代订人: {task_data.agent_name}")

                await order.save()

                processed_count += 1

        message = f"成功创建飞机票预订任务，任务ID: {task_id}，包含 {processed_count} 条订单"

        # 发送Kafka消息
        try:
            # 获取用户的同程管家凭证
            credentials = await SystemSettingsService.get_tongcheng_credentials(current_user.id)

            # 获取原始密码用于存储到消息中
            credentials_with_raw_password = await SystemSettingsService.get_tongcheng_credentials_with_raw_password(current_user.id)

            if credentials['username'] and credentials['password']:
                # 获取当前登录用户的姓名
                login_user_name = current_user.username if current_user else ""

                # 获取提交的订单ID列表
                submitted_order_ids = [order.id for order in orders]

                # 发送Kafka消息 - 使用原始密文
                kafka_result = await send_flight_booking_task(
                    task_id=task_id,
                    order_ids=submitted_order_ids,
                    username=credentials['username'],
                    password=credentials_with_raw_password['password'],  # 使用数据库原始密文
                    company_name=project.client_name,  # 从项目的client_name字段获取公司名称
                    send_sms=task_data.sms_notify,
                    has_agent=task_data.has_agent,
                    agent_phone=task_data.agent_phone if task_data.has_agent else None,
                    agent_name=task_data.agent_name if task_data.has_agent else "",
                    login_user_name=login_user_name
                )

                logger.info(
                    f"Kafka消息发送完成 - Task: {task_id}, "
                    f"成功: {kafka_result.get('success_count', 0)}, "
                    f"失败: {kafka_result.get('failed_count', 0)}, "
                    f"启用: {kafka_result.get('enabled', False)}"
                )

                # 更新消息内容
                if kafka_result.get('enabled', False):
                    kafka_success = kafka_result.get('success_count', 0)
                    kafka_failed = kafka_result.get('failed_count', 0)

                    if kafka_success > 0:
                        message += f"，{kafka_success}条消息推送成功"

                    if kafka_failed > 0:
                        message += f"，{kafka_failed}条消息推送失败"
                else:
                    message += "，Kafka消息推送未启用"
            else:
                logger.warning(f"用户 {current_user.id} 未配置同程管家凭证，跳过Kafka消息发送")
                message += "，未配置同程管家凭证，请先在系统设置中配置"

        except Exception as e:
            logger.error(f"发送Kafka消息失败: {str(e)}")
            message += f"，任务消息推送失败: {str(e)}"

        logger.info(f"用户 {current_user.id} 创建飞机票预订任务: {task_id}, 项目: {project_id}, 订单数: {processed_count}")

        return CreateBookingTaskResponse(
            task_id=task_id,
            processed_count=processed_count,
            message=message
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建飞机票预订任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建飞机票预订任务失败: {str(e)}")


@router.get("/task/{task_id}", response_model=FlightOrderListResponse, summary="获取任务的飞机票订单")
async def get_orders_by_task(
    task_id: str,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=1000, description="每页数量"),
    status: Optional[str] = Query(None, description="订单状态筛选"),
    traveler_name: Optional[str] = Query(None, description="出行人姓名搜索"),
    mobile_phone: Optional[str] = Query(None, description="手机号搜索"),
    contact_phone: Optional[str] = Query(None, description="联系人手机号搜索"),
    current_user: User = Depends(get_current_user)
):
    """获取任务的飞机票订单列表"""
    try:
        logger.info(f"获取任务 {task_id} 的飞机票订单 - page: {page}, page_size: {page_size}, status: {status}, traveler_name: {traveler_name}, mobile_phone: {mobile_phone}, contact_phone: {contact_phone}")

        # 验证任务是否存在
        try:
            task = await ProjectTask.get(task_id=task_id)
        except DoesNotExist:
            raise HTTPException(status_code=404, detail="任务不存在")

        # 获取任务关联的订单ID
        task_orders = await TaskToFlightOrder.filter(
            task_id=task_id,
            is_deleted=False
        ).all()

        if not task_orders:
            return FlightOrderListResponse(
                total=0,
                page=page,
                page_size=page_size,
                items=[]
            )

        order_ids = [to.order_id for to in task_orders]

        # 构建查询
        query = FlightOrder.filter(
            id__in=order_ids,
            is_deleted=False
        )

        # 状态筛选
        if status:
            query = query.filter(order_status=status)

        # 出行人姓名搜索
        if traveler_name:
            query = query.filter(traveler_full_name__icontains=traveler_name)

        # 手机号搜索
        if mobile_phone:
            query = query.filter(mobile_phone__icontains=mobile_phone)

        # 联系人手机号搜索
        if contact_phone:
            query = query.filter(contact_mobile_phone__icontains=contact_phone)

        # 排序：失败的订单排在前面
        query = query.order_by("-order_status", "-created_at")

        # 获取总数
        total = await query.count()

        # 分页
        offset = (page - 1) * page_size
        orders = await query.offset(offset).limit(page_size).all()

        # 转换为响应格式
        order_responses = []
        for order in orders:
            order_dict = {}
            for field in order._meta.fields_map.keys():
                value = getattr(order, field)
                order_dict[field] = value
            order_responses.append(FlightOrderResponse(**order_dict))

        logger.info(f"任务 {task_id} 飞机票订单查询完成 - 总数: {total}, 当前页: {len(order_responses)}")

        return FlightOrderListResponse(
            total=total,
            page=page,
            page_size=page_size,
            items=order_responses
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务飞机票订单失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取任务飞机票订单失败: {str(e)}")


@router.get("/task/{task_id}/stats", response_model=TaskOrderStatsResponse, summary="获取任务飞机票订单统计")
async def get_task_stats(task_id: str):
    """获取任务的飞机票订单统计信息"""
    try:
        # 验证任务是否存在
        try:
            await ProjectTask.get(task_id=task_id)
        except DoesNotExist:
            raise HTTPException(status_code=404, detail="任务不存在")

        # 获取任务关联的订单ID
        task_orders = await TaskToFlightOrder.filter(
            task_id=task_id,
            is_deleted=False
        ).all()

        if not task_orders:
            return TaskOrderStatsResponse(
                task_id=task_id,
                total_orders=0,
                initial_orders=0,
                submitted_orders=0,
                processing_orders=0,
                completed_orders=0,
                failed_orders=0
            )

        order_ids = [to.order_id for to in task_orders]

        # 统计各状态订单数量
        total_orders = await FlightOrder.filter(id__in=order_ids, is_deleted=False).count()
        initial_orders = await FlightOrder.filter(id__in=order_ids, order_status="initial", is_deleted=False).count()
        submitted_orders = await FlightOrder.filter(id__in=order_ids, order_status="submitted", is_deleted=False).count()
        processing_orders = await FlightOrder.filter(id__in=order_ids, order_status="processing", is_deleted=False).count()
        completed_orders = await FlightOrder.filter(id__in=order_ids, order_status="completed", is_deleted=False).count()
        failed_orders = await FlightOrder.filter(id__in=order_ids, order_status="failed", is_deleted=False).count()

        return TaskOrderStatsResponse(
            task_id=task_id,
            total_orders=total_orders,
            initial_orders=initial_orders,
            submitted_orders=submitted_orders,
            processing_orders=processing_orders,
            completed_orders=completed_orders,
            failed_orders=failed_orders
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务飞机票订单统计失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取任务飞机票订单统计失败: {str(e)}")


@router.post("/project/{project_id}/clear", response_model=ClearOrdersResponse, summary="清空项目飞机票订单")
async def clear_orders(
    project_id: int,
    current_user: User = Depends(get_current_user)
):
    """清空指定项目的所有飞机票订单（软删除）"""
    try:
        # 验证项目是否存在
        try:
            await Project.get(id=project_id)
        except DoesNotExist:
            raise HTTPException(status_code=404, detail="项目不存在")

        # 获取所有未删除的订单
        orders = await FlightOrder.filter(
            project_id=project_id,
            is_deleted=False
        ).all()

        # 软删除所有订单
        deleted_count = 0
        async with in_transaction():
            for order in orders:
                await order.soft_delete()
                deleted_count += 1

        message = f"成功清空 {deleted_count} 条飞机票订单"

        logger.info(f"用户 {current_user.id} 清空项目 {project_id} 的飞机票订单，共 {deleted_count} 条")

        return ClearOrdersResponse(
            deleted_count=deleted_count,
            message=message
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"清空飞机票订单失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"清空飞机票订单失败: {str(e)}")


@router.post("/project/{project_id}/clear-specific", response_model=ClearOrdersResponse, summary="清空项目中特定状态的飞机票订单")
async def clear_specific_orders(
    project_id: int,
    current_user: User = Depends(get_current_user)
):
    """清空指定项目中特定状态的飞机票订单（验证失败、待提交、已暂停、预定失败）"""
    try:
        # 验证项目是否存在
        try:
            await Project.get(id=project_id)
        except DoesNotExist:
            raise HTTPException(status_code=404, detail="项目不存在")

        # 查询可清空状态的订单：check_failed（验证失败）、initial（待提交）、paused（已暂停）、failed（预定失败）
        clearable_orders = await FlightOrder.filter(
            project_id=project_id,
            order_status__in=['check_failed', 'initial', 'paused', 'failed'],
            is_deleted=False
        ).all()

        deleted_count = len(clearable_orders)

        if deleted_count == 0:
            return ClearOrdersResponse(
                deleted_count=0,
                message="暂无可清空的订单"
            )

        # 软删除所有符合条件的订单
        async with in_transaction():
            for order in clearable_orders:
                await order.soft_delete()

        logger.info(f"成功清空项目 {project_id} 的 {deleted_count} 条特定状态飞机票订单")

        return ClearOrdersResponse(
            deleted_count=deleted_count,
            message=f"成功清空 {deleted_count} 条飞机票订单（验证失败、待提交、已暂停、预定失败）"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"清空特定状态飞机票订单失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"清空特定状态飞机票订单失败: {str(e)}")


@router.post("/project/{project_id}/pause-submitted", response_model=PauseOrdersResponse, summary="暂停已提交的飞机票订单")
async def pause_submitted_orders(
    project_id: int,
    request: PauseOrdersRequest,
    current_user: User = Depends(get_current_user)
):
    """将已提交的飞机票订单状态暂停"""
    try:
        # 验证项目是否存在
        try:
            await Project.get(id=project_id)
        except DoesNotExist:
            raise HTTPException(status_code=404, detail="项目不存在")

        # 构建查询条件
        query_conditions = {
            'project_id': project_id,
            'order_status': 'submitted',
            'is_deleted': False
        }

        # 如果指定了订单ID，则只处理指定的订单
        if request.order_ids:
            query_conditions['id__in'] = request.order_ids

        # 查询已提交的订单
        submitted_orders = await FlightOrder.filter(**query_conditions).all()

        paused_count = len(submitted_orders)

        if paused_count == 0:
            return PauseOrdersResponse(
                paused_count=0,
                message="暂无已提交的订单可暂停"
            )

        # 将订单状态暂停
        async with in_transaction():
            for order in submitted_orders:
                order.order_status = 'paused'
                await order.save()

        logger.info(f"成功暂停项目 {project_id} 的 {paused_count} 条已提交飞机票订单")

        return PauseOrdersResponse(
            paused_count=paused_count,
            message=f"成功暂停 {paused_count} 条已提交订单"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"暂停已提交飞机票订单失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"暂停已提交飞机票订单失败: {str(e)}")


@router.post("/project/{project_id}/reset-paused", response_model=ResetPausedOrdersResponse, summary="重置已暂停的飞机票订单")
async def reset_paused_orders(
    project_id: int,
    request: ResetPausedOrdersRequest,
    current_user: User = Depends(get_current_user)
):
    """将已暂停的飞机票订单状态重置为待提交"""
    try:
        # 验证项目是否存在
        try:
            await Project.get(id=project_id)
        except DoesNotExist:
            raise HTTPException(status_code=404, detail="项目不存在")

        # 构建查询条件
        query_conditions = {
            'project_id': project_id,
            'order_status': 'paused',
            'is_deleted': False
        }

        # 如果指定了订单ID，则只处理指定的订单
        if request.order_ids:
            query_conditions['id__in'] = request.order_ids

        # 查询已暂停的订单
        paused_orders = await FlightOrder.filter(**query_conditions).all()

        reset_count = len(paused_orders)

        if reset_count == 0:
            return ResetPausedOrdersResponse(
                reset_count=0,
                message="暂无已暂停的订单可重置"
            )

        # 将订单状态重置为待提交
        async with in_transaction():
            for order in paused_orders:
                order.order_status = 'initial'
                order.fail_reason = None  # 清空失败原因
                await order.save()

        logger.info(f"成功重置项目 {project_id} 的 {reset_count} 条已暂停飞机票订单")

        return ResetPausedOrdersResponse(
            reset_count=reset_count,
            message=f"成功重置 {reset_count} 条已暂停订单为待提交状态"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"重置已暂停飞机票订单失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"重置已暂停飞机票订单失败: {str(e)}")


@router.post("/project/{project_id}/reset-failed", response_model=ResetFailedOrdersResponse, summary="重置预定失败的飞机票订单")
async def reset_failed_orders(
    project_id: int,
    request: ResetFailedOrdersRequest,
    current_user: User = Depends(get_current_user)
):
    """将预定失败的飞机票订单状态重置为待提交"""
    try:
        # 验证项目是否存在
        try:
            await Project.get(id=project_id)
        except DoesNotExist:
            raise HTTPException(status_code=404, detail="项目不存在")

        # 构建查询条件
        query_conditions = {
            'project_id': project_id,
            'order_status': 'failed',
            'is_deleted': False
        }

        # 如果指定了订单ID，则只处理指定的订单
        if request.order_ids:
            query_conditions['id__in'] = request.order_ids

        # 查询预定失败的订单
        failed_orders = await FlightOrder.filter(**query_conditions).all()

        reset_count = len(failed_orders)

        if reset_count == 0:
            return ResetFailedOrdersResponse(
                reset_count=0,
                message="暂无预定失败的订单可重置"
            )

        # 将订单状态重置为待提交
        async with in_transaction():
            for order in failed_orders:
                order.order_status = 'initial'
                order.fail_reason = None  # 清空失败原因
                await order.save()

        logger.info(f"成功重置项目 {project_id} 的 {reset_count} 条预定失败飞机票订单")

        return ResetFailedOrdersResponse(
            reset_count=reset_count,
            message=f"成功重置 {reset_count} 条预定失败订单为待提交状态"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"重置预定失败飞机票订单失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"重置预定失败飞机票订单失败: {str(e)}")
