"""飞机票订单API数据模式定义"""

from typing import Optional, List
from datetime import datetime
from decimal import Decimal
from pydantic import BaseModel, Field, ConfigDict, field_serializer


class FlightOrderBase(BaseModel):
    """飞机票订单基础数据模式"""
    project_id: int
    sequence_number: int
    
    # 出行人基础信息
    traveler_full_name: Optional[str] = None
    traveler_surname: Optional[str] = None
    traveler_given_name: Optional[str] = None
    nationality: Optional[str] = None
    gender: Optional[str] = None
    birth_date: Optional[str] = None
    id_type: Optional[str] = None
    id_number: Optional[str] = None
    id_expiry_date: Optional[str] = None
    mobile_country_code: Optional[str] = Field(default="+86", description="手机号国际区号")
    mobile_phone: Optional[str] = None
    
    # 出行信息
    travel_date: Optional[str] = None
    departure_airport: Optional[str] = None
    arrival_airport: Optional[str] = None
    flight_number: Optional[str] = None
    departure_time: Optional[str] = None
    arrival_time: Optional[str] = None
    trip_submission_item: Optional[str] = None
    
    # 联系人信息
    contact_person: Optional[str] = None
    contact_mobile_phone: Optional[str] = None
    contact_email: Optional[str] = None
    approver: Optional[str] = None
    
    # 对账单信息
    company_name: Optional[str] = None
    booking_agent: Optional[str] = None
    ticket_sms: Optional[str] = None
    amount: Optional[str] = Field(default="0", description="金额")
    order_number: Optional[str] = None
    bill_number: Optional[str] = None

    # 保险信息
    insurance_name: Optional[str] = Field(default="", description="保险名称")
    
    # 订单状态
    order_status: Optional[str] = Field(default="initial", description="订单状态")
    fail_reason: Optional[str] = Field(None, description="失败原因")


class FlightOrderCreate(FlightOrderBase):
    """创建飞机票订单的数据模式"""
    pass


class FlightOrderUpdate(BaseModel):
    """更新飞机票订单的数据模式"""
    sequence_number: Optional[int] = None
    
    # 出行人基础信息
    traveler_full_name: Optional[str] = None
    traveler_surname: Optional[str] = None
    traveler_given_name: Optional[str] = None
    nationality: Optional[str] = None
    gender: Optional[str] = None
    birth_date: Optional[str] = None
    id_type: Optional[str] = None
    id_number: Optional[str] = None
    id_expiry_date: Optional[str] = None
    mobile_country_code: Optional[str] = None
    mobile_phone: Optional[str] = None
    
    # 出行信息
    travel_date: Optional[str] = None
    departure_airport: Optional[str] = None
    arrival_airport: Optional[str] = None
    flight_number: Optional[str] = None
    departure_time: Optional[str] = None
    arrival_time: Optional[str] = None
    trip_submission_item: Optional[str] = None
    
    # 联系人信息
    contact_person: Optional[str] = None
    contact_mobile_phone: Optional[str] = None
    contact_email: Optional[str] = None
    approver: Optional[str] = None
    
    # 对账单信息
    company_name: Optional[str] = None
    booking_agent: Optional[str] = None
    ticket_sms: Optional[str] = None
    amount: Optional[str] = None
    order_number: Optional[str] = None
    bill_number: Optional[str] = None

    # 保险信息
    insurance_name: Optional[str] = None
    
    # 订单状态
    order_status: Optional[str] = None
    fail_reason: Optional[str] = None


class FlightOrderResponse(BaseModel):
    """飞机票订单响应数据模式"""
    id: int
    project_id: int
    sequence_number: int
    
    # 出行人基础信息
    traveler_full_name: str
    traveler_surname: Optional[str] = None
    traveler_given_name: Optional[str] = None
    nationality: Optional[str] = None
    gender: Optional[str] = None
    birth_date: Optional[str] = None
    id_type: Optional[str] = None
    id_number: Optional[str] = None
    id_expiry_date: Optional[str] = None
    mobile_country_code: Optional[str] = None
    mobile_phone: Optional[str] = None
    
    # 出行信息
    travel_date: Optional[str] = None
    departure_airport: Optional[str] = None
    arrival_airport: Optional[str] = None
    flight_number: Optional[str] = None
    departure_time: Optional[str] = None
    arrival_time: Optional[str] = None
    trip_submission_item: Optional[str] = None
    
    # 联系人信息
    contact_person: Optional[str] = None
    contact_mobile_phone: Optional[str] = None
    contact_email: Optional[str] = None
    approver: Optional[str] = None
    
    # 对账单信息
    company_name: Optional[str] = None
    booking_agent: Optional[str] = None
    ticket_sms: Optional[str] = None
    amount: Optional[Decimal] = Field(default=None, description="金额")
    order_number: Optional[str] = None
    bill_number: Optional[str] = None

    # 保险信息
    insurance_name: Optional[str] = None
    
    # 订单状态和元数据
    order_status: str
    fail_reason: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    is_deleted: bool

    model_config = ConfigDict(from_attributes=True)

    @field_serializer('amount')
    def serialize_amount(self, value):
        """序列化金额字段，将Decimal转换为字符串"""
        if value is None:
            return None
        if isinstance(value, Decimal):
            return str(value)
        return str(value) if value is not None else None

    @field_serializer('created_at', 'updated_at')
    def serialize_datetime(self, dt: datetime) -> str:
        return dt.isoformat() if dt else None


class FlightOrderListResponse(BaseModel):
    """飞机票订单列表响应"""
    total: int
    page: int
    page_size: int
    items: List[FlightOrderResponse]


class ExcelUploadResponse(BaseModel):
    """Excel上传响应"""
    project_id: int = Field(..., description="项目ID")
    total_orders: int = Field(..., description="成功创建的订单数量")
    failed_orders: int = Field(default=0, description="失败的订单数量")
    skipped_duplicate_orders: int = Field(default=0, description="跳过的重复订单数量")
    file_path: Optional[str] = Field(None, description="保存的Excel文件路径")
    message: str = Field(..., description="处理结果消息")


class ValidationError(BaseModel):
    """验证错误信息"""
    row: int = Field(..., description="错误行号")
    field: str = Field(..., description="错误字段")
    message: str = Field(..., description="错误信息")
    value: Optional[str] = Field(None, description="错误值")


class ExcelValidationResponse(BaseModel):
    """Excel验证响应"""
    has_errors: bool = Field(..., description="是否有错误")
    errors: List[ValidationError] = Field(default_factory=list, description="错误列表")
    total_rows: int = Field(..., description="总行数")
    valid_rows: int = Field(..., description="有效行数")
    message: str = Field(..., description="验证结果消息")


class BookingRequest(BaseModel):
    """预订请求"""
    booking_type: str = Field(..., description="预订类型: book 或 book_and_issue")
    orders: List[int] = Field(..., description="订单ID列表")
    sms_notify: bool = Field(default=False, description="是否短信通知")
    has_agent: bool = Field(default=False, description="是否有代订人")
    agent_phone: Optional[str] = Field(None, description="代订人手机号")
    agent_name: Optional[str] = Field(None, description="代订人姓名")


class BookingResponse(BaseModel):
    """预订响应"""
    processed_count: int = Field(..., description="处理的订单数量")
    success_count: int = Field(..., description="成功的订单数量")
    failed_count: int = Field(..., description="失败的订单数量")
    message: str = Field(..., description="处理结果消息")


class CreateBookingTaskRequest(BaseModel):
    """创建预订任务请求"""
    booking_type: str = Field(..., description="预订类型: book 或 book_and_issue")
    task_title: str = Field(..., description="任务标题")
    task_description: str = Field(..., description="任务描述")
    sms_notify: bool = Field(default=False, description="是否短信通知")
    has_agent: bool = Field(default=False, description="是否有代订人")
    agent_phone: Optional[str] = Field(None, description="代订人手机号")
    agent_name: Optional[str] = Field(None, description="代订人姓名")
    order_ids: Optional[List[int]] = Field(None, description="指定的订单ID列表，如果为空则使用所有待提交订单")


class CreateBookingTaskResponse(BaseModel):
    """创建预订任务响应"""
    task_id: str = Field(..., description="任务ID")
    processed_count: int = Field(..., description="处理的订单数量")
    message: str = Field(..., description="处理结果消息")


class ClearOrdersResponse(BaseModel):
    """清空订单响应"""
    deleted_count: int = Field(..., description="删除的订单数量")
    message: str = Field(..., description="处理结果消息")


class ProjectOrderStatsResponse(BaseModel):
    """项目订单统计响应"""
    total_orders: int = Field(..., description="总订单数")
    initial_orders: int = Field(..., description="待提交订单数")
    submitted_orders: int = Field(..., description="已提交订单数")
    processing_orders: int = Field(..., description="处理中订单数")
    completed_orders: int = Field(..., description="已完成订单数")
    failed_orders: int = Field(..., description="失败订单数")
    check_failed_orders: int = Field(..., description="验证失败订单数")


class ProjectOrderDetailStatsResponse(BaseModel):
    """项目订单详细统计响应"""
    total_orders: int = Field(..., description="总订单数")
    initial_orders: int = Field(..., description="待提交订单数")
    submitted_orders: int = Field(..., description="已提交订单数")
    processing_orders: int = Field(..., description="处理中订单数")
    completed_orders: int = Field(..., description="已完成订单数")
    failed_orders: int = Field(..., description="失败订单数")
    check_failed_orders: int = Field(..., description="验证失败订单数")
    total_amount: float = Field(..., description="总金额")


class TaskOrderStatsResponse(BaseModel):
    """任务订单统计响应"""
    task_id: str = Field(..., description="任务ID")
    total_orders: int = Field(..., description="总订单数")
    initial_orders: int = Field(..., description="待预订订单数")
    submitted_orders: int = Field(..., description="已提交订单数")
    processing_orders: int = Field(..., description="处理中订单数")
    completed_orders: int = Field(..., description="已完成订单数")
    failed_orders: int = Field(..., description="失败订单数")


class PauseOrdersRequest(BaseModel):
    """暂停订单请求"""
    order_ids: Optional[List[int]] = Field(None, description="订单ID列表，如果为空则暂停所有已提交订单")


class PauseOrdersResponse(BaseModel):
    """暂停订单响应"""
    paused_count: int = Field(..., description="暂停的订单数量")
    message: str = Field(..., description="处理结果消息")


class ResetPausedOrdersRequest(BaseModel):
    """重置暂停订单请求"""
    order_ids: List[int] = Field(..., description="订单ID列表")


class ResetPausedOrdersResponse(BaseModel):
    """重置暂停订单响应"""
    reset_count: int = Field(..., description="重置的订单数量")
    message: str = Field(..., description="处理结果消息")


class ResetFailedOrdersRequest(BaseModel):
    """重置失败订单请求"""
    order_ids: List[int] = Field(..., description="订单ID列表")


class ResetFailedOrdersResponse(BaseModel):
    """重置失败订单响应"""
    reset_count: int = Field(..., description="重置的订单数量")
    message: str = Field(..., description="处理结果消息")


# 创建__init__.py文件
__all__ = [
    "FlightOrderBase",
    "FlightOrderCreate",
    "FlightOrderUpdate",
    "FlightOrderResponse",
    "FlightOrderListResponse",
    "ExcelUploadResponse",
    "ValidationError",
    "ExcelValidationResponse",
    "BookingRequest",
    "BookingResponse",
    "CreateBookingTaskRequest",
    "CreateBookingTaskResponse",
    "ClearOrdersResponse",
    "ProjectOrderStatsResponse",
    "ProjectOrderDetailStatsResponse",
    "TaskOrderStatsResponse",
    "PauseOrdersRequest",
    "PauseOrdersResponse",
    "ResetPausedOrdersRequest",
    "ResetPausedOrdersResponse",
    "ResetFailedOrdersRequest",
    "ResetFailedOrdersResponse"
]
