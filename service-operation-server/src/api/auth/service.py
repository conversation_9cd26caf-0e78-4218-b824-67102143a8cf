"""
认证模块的业务逻辑服务。

此模块集中处理与认证相关的业务逻辑，将其与路由处理分离，提高代码可维护性和可测试性。
"""

from loguru import logger
from fastapi import status

from src.api.auth.exceptions import InvalidApiKeyError, InvalidApiSecretError, DeveloperNotFoundError
from src.api.auth.constants import CREDENTIALS_EXCEPTION_HEADERS
from src.core.security import create_access_token, verify_password, create_token_pair, verify_refresh_token_format
from src.services.api_key_service import get_api_key_by_key, update_api_key_last_used
from src.db.models.api_key import ApiKey
from src.db.models.refresh_token import RefreshToken
from src.core.config import settings
from datetime import datetime, timezone
from typing import Tuple


async def authenticate_api_key(api_key: str, api_secret: str) -> str:
    """
    验证API密钥和秘钥，成功后返回JWT令牌。
    
    Args:
        api_key: API密钥
        api_secret: API密钥秘钥
        
    Returns:
        str: JWT访问令牌
        
    Raises:
        InvalidApiKeyError: 如果API密钥无效或不存在
        InvalidApiSecretError: 如果API密钥秘钥无效
        DeveloperNotFoundError: 如果找不到关联的开发者信息
    """
    # 获取API密钥记录
    api_key_record: ApiKey | None = await get_api_key_by_key(api_key_value=api_key)
    
    # 验证API密钥是否存在且有效
    if not api_key_record:
        logger.warning(f"尝试使用无效的API密钥: {api_key}")
        raise InvalidApiKeyError(
            message="无效的API密钥或秘钥",
            headers=CREDENTIALS_EXCEPTION_HEADERS
        )

    # 验证秘钥
    is_password_valid = verify_password(api_secret, api_key_record.secret)
    if not is_password_valid:
        logger.warning(f"API密钥 {api_key} 的秘钥验证失败")
        raise InvalidApiSecretError(
            message="无效的API密钥或秘钥",
            headers=CREDENTIALS_EXCEPTION_HEADERS
        )

    # 检查是否有关联的开发者
    if not api_key_record.developer_id:
        # 这种情况理论上不应该发生，如果数据完整性得到维护
        logger.error(f"API密钥 {api_key} 没有关联的开发者ID")
        raise DeveloperNotFoundError()

    # 创建主题（开发者ID）
    subject = str(api_key_record.developer_id)
    
    # 更新API密钥的最后使用时间
    await update_api_key_last_used(api_key_record)
    
    # 创建JWT访问令牌
    access_token = create_access_token(subject=subject)
    
    # 记录成功登录
    logger.info(f"开发者 {api_key_record.developer_id} 使用API密钥 {api_key} 成功认证")
    
    return access_token


async def authenticate_api_key_with_refresh(api_key: str, api_secret: str, device_info: str = None) -> Tuple[str, str]:
    """
    验证API密钥和秘钥，成功后返回JWT令牌对。
    
    Args:
        api_key: API密钥
        api_secret: API密钥秘钥
        device_info: 设备信息（可选）
        
    Returns:
        Tuple[str, str]: (access_token, refresh_token)
        
    Raises:
        InvalidApiKeyError: 如果API密钥无效或不存在
        InvalidApiSecretError: 如果API密钥秘钥无效
        DeveloperNotFoundError: 如果找不到关联的开发者信息
    """
    # 获取API密钥记录
    api_key_record: ApiKey | None = await get_api_key_by_key(api_key_value=api_key)
    
    # 验证API密钥是否存在且有效
    if not api_key_record:
        logger.warning(f"尝试使用无效的API密钥: {api_key}")
        raise InvalidApiKeyError(
            message="无效的API密钥或秘钥",
            headers=CREDENTIALS_EXCEPTION_HEADERS
        )

    # 验证秘钥
    is_password_valid = verify_password(api_secret, api_key_record.secret)
    if not is_password_valid:
        logger.warning(f"API密钥 {api_key} 的秘钥验证失败")
        raise InvalidApiSecretError(
            message="无效的API密钥或秘钥",
            headers=CREDENTIALS_EXCEPTION_HEADERS
        )

    # 检查是否有关联的开发者
    if not api_key_record.developer_id:
        logger.error(f"API密钥 {api_key} 没有关联的开发者ID")
        raise DeveloperNotFoundError()

    # 创建令牌对
    user_id = api_key_record.developer_id
    access_token, refresh_token, refresh_token_expiry = create_token_pair(user_id)
    
    # 保存refresh token到数据库
    await RefreshToken.create_token(
        user_id=user_id,
        token=refresh_token,
        expires_at=refresh_token_expiry,
        device_info=device_info
    )
    
    # 更新API密钥的最后使用时间
    await update_api_key_last_used(api_key_record)
    
    # 记录成功登录
    logger.info(f"开发者 {user_id} 使用API密钥 {api_key} 成功认证并获取令牌对")
    
    return access_token, refresh_token


async def refresh_access_token(refresh_token: str) -> Tuple[str, str]:
    """
    使用refresh token刷新access token
    
    Args:
        refresh_token: 刷新令牌
        
    Returns:
        Tuple[str, str]: (new_access_token, new_refresh_token)
        
    Raises:
        InvalidApiKeyError: 如果refresh token无效
    """
    # 验证refresh token格式
    if not verify_refresh_token_format(refresh_token):
        logger.warning("收到格式无效的refresh token")
        raise InvalidApiKeyError(message="无效的刷新令牌")
    
    # 从数据库查找有效的refresh token
    token_record = await RefreshToken.get_active_token(refresh_token)
    if not token_record:
        logger.warning(f"收到无效或过期的refresh token")
        raise InvalidApiKeyError(message="无效或过期的刷新令牌")
    
    # 撤销旧的refresh token
    await RefreshToken.revoke_token(refresh_token)
    
    # 创建新的令牌对
    user_id = token_record.user_id
    new_access_token, new_refresh_token, new_refresh_token_expiry = create_token_pair(user_id)
    
    # 保存新的refresh token
    await RefreshToken.create_token(
        user_id=user_id,
        token=new_refresh_token,
        expires_at=new_refresh_token_expiry,
        device_info=token_record.device_info
    )
    
    logger.info(f"用户 {user_id} 成功刷新令牌")
    
    return new_access_token, new_refresh_token


async def revoke_refresh_token(refresh_token: str) -> bool:
    """
    撤销refresh token
    
    Args:
        refresh_token: 要撤销的刷新令牌
        
    Returns:
        bool: 是否成功撤销
    """
    try:
        await RefreshToken.revoke_token(refresh_token)
        logger.info("成功撤销refresh token")
        return True
    except Exception as e:
        logger.error(f"撤销refresh token时出错: {e}")
        return False


async def revoke_user_tokens(user_id: int) -> bool:
    """
    撤销用户所有refresh token
    
    Args:
        user_id: 用户ID
        
    Returns:
        bool: 是否成功撤销
    """
    try:
        await RefreshToken.revoke_user_tokens(user_id)
        logger.info(f"成功撤销用户 {user_id} 的所有refresh token")
        return True
    except Exception as e:
        logger.error(f"撤销用户 {user_id} 的refresh token时出错: {e}")
        return False
