"""
认证模块特定异常定义。

定义与认证相关的特定异常类型，所有异常继承自全局基础异常类。
"""

from fastapi import status
from src.core.exceptions import APIError, AuthenticationError


class InvalidApiKeyError(AuthenticationError):
    """无效的API密钥异常。"""

    def __init__(self, message: str = "无效的API密钥", headers: dict = None):
        super().__init__(
            message=message,
            headers=headers
        )


class InvalidApiSecretError(AuthenticationError):
    """无效的API密钥秘钥异常。"""

    def __init__(self, message: str = "无效的API密钥秘钥", headers: dict = None):
        super().__init__(
            message=message,
            headers=headers
        )


class ExpiredTokenError(AuthenticationError):
    """令牌过期异常。"""

    def __init__(self, message: str = "访问令牌已过期", headers: dict = None):
        super().__init__(
            message=message,
            headers=headers
        )


class DeveloperNotFoundError(APIError):
    """开发者未找到异常。"""

    def __init__(self, message: str = "找不到关联的开发者信息"):
        super().__init__(
            message=message,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


class SSOAuthenticationError(AuthenticationError):
    """SSO认证错误异常。"""

    def __init__(self, message: str = "SSO认证失败", headers: dict = None):
        super().__init__(
            message=message,
            headers=headers
        )
