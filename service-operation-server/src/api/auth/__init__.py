"""
认证模块 (Authentication Module)

此模块负责处理API认证相关功能，包括：
- API密钥认证
- JWT令牌生成和验证
- 认证异常处理

典型用法:
```python
from src.api.auth import router                # 导入路由器
from src.api.auth.schemas import TokenResponse # 导入响应模型
```
"""

from fastapi import APIRouter

from src.api.auth.endpoints import router as auth_router
from src.api.auth.sso_endpoints import router as sso_router
from .schemas import TokenRequest, TokenResponse, Token, TokenPayload
from .constants import TOKEN_TYPE, INVALID_CREDENTIALS_MSG

router = APIRouter(prefix="/auth", tags=["auth"])
router.include_router(auth_router)
router.include_router(sso_router)

__all__ = [
    "router",                   # 路由器
    "TokenRequest",             # 请求模型
    "TokenResponse",            # 响应模型
    "Token",                    # 令牌模型
    "TokenPayload",             # 令牌载荷模型
    "TOKEN_TYPE",               # 常量
    "INVALID_CREDENTIALS_MSG",  # 错误信息
]
