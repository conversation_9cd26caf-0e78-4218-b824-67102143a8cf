"""
SSO认证相关的API端点。

此模块提供与SSO认证相关的API端点，包括获取SSO登录URL、验证SSO票据和获取SSO登出URL。
"""

from fastapi import APIRouter, status, Depends, Form
from fastapi.responses import JSONResponse
from loguru import logger
from datetime import datetime

from src.api.auth.sso_schemas import SSOUrlResponse, SSOTicketRequest, SSOTokenResponse, SSOUserInfo, SSOLogoutRequest
from src.api.auth.schemas import Token
from src.api.auth.sso_service import get_sso_login_url, exchange_code_for_token, get_sso_logout_url, sso_logout
from src.api.auth.constants import TOKEN_TYPE
from src.core.exceptions import APIError
from src.core.config import settings
from src.db.models.user import User

router = APIRouter(prefix="/sso")


@router.get(
    "/login-url",
    response_model=SSOUrlResponse,
    status_code=status.HTTP_200_OK,
    summary="获取SSO登录URL",
    description="获取同程统一授权登录中心的登录URL"
)
async def get_login_url() -> SSOUrlResponse:
    """
    获取SSO登录URL。
    
    Returns:
        SSOUrlResponse: 包含SSO登录URL的响应
    """
    try:
        login_url = await get_sso_login_url()
        return SSOUrlResponse(data={"login_url": login_url})
    except Exception as e:
        logger.exception(f"获取SSO登录URL时发生错误: {str(e)}")
        raise APIError(
            message="获取SSO登录URL失败",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            details=str(e) if str(e) else None
        )


@router.get(
    "/callback",
    response_model=SSOTokenResponse,
    status_code=status.HTTP_200_OK,
    summary="处理SSO回调(GET)",
    description="处理同程统一授权登录中心的授权回调(GET重定向)"
)
async def handle_callback_get(code: str, state: str) -> SSOTokenResponse:
    """
    处理SSO授权回调(GET方法)。
    
    Args:
        code: 授权码
        state: 状态值
    
    Returns:
        SSOTokenResponse: 包含访问令牌和用户信息的响应
    """
    return await _handle_callback_logic(code, state)


@router.post(
    "/callback",
    response_model=SSOTokenResponse,
    status_code=status.HTTP_200_OK,
    summary="处理SSO回调(POST)",
    description="处理同程统一授权登录中心的授权回调(POST请求)"
)
async def handle_callback_post(code: str, state: str) -> SSOTokenResponse:
    """
    处理SSO授权回调(POST方法)。
    
    Args:
        code: 授权码
        state: 状态值
    
    Returns:
        SSOTokenResponse: 包含访问令牌和用户信息的响应
    """
    return await _handle_callback_logic(code, state)


async def _handle_callback_logic(code: str, state: str) -> SSOTokenResponse:
    """
    处理SSO授权回调。
    
    Args:
        code: 授权码
        state: 状态值
    
    Returns:
        SSOTokenResponse: 包含访问令牌和用户信息的响应
    """
    # 记录请求参数
    logger.info(f"开始处理SSO回调 - code: {code[:20]}..., state: {state}")
    
    try:
        access_token, user_info, app_token = await exchange_code_for_token(code, state)
        
        # 打印用户信息详情
        logger.info(f"SSO 返回的用户信息类型: {type(user_info)}")
        logger.info(f"SSO 返回的用户信息字段: {list(user_info.keys()) if isinstance(user_info, dict) else 'Not a dict'}")
        logger.info(f"SSO 返回的用户信息内容: {user_info}")
        
        # 从users表中获取用户信息
        # user_info 是从 get_or_create_user_from_sso 返回的，userId 已经是数据库的 id
        user_id = user_info.get("userId", "")
        db_user = await User.filter(id=user_id).first()
        
        if db_user:
            # 使用数据库中的用户信息
            user = SSOUserInfo(
                id=str(db_user.id),  # 使用数据库的id字段
                name=db_user.username,
                email=db_user.email or "",
                role="user",
                department=db_user.department,
                work_id=db_user.work_id
            )
        else:
            # 如果数据库中没有用户信息，使用SSO返回的信息
            user = SSOUserInfo(
                id=user_id,  # 使用user_id作为临时ID
                name=user_info.get("username", ""),
                email=user_info.get("email", ""),
                role="user"
            )
        
        # 创建令牌对象
        token_data = Token(
            access_token=app_token,
            token_type="bearer"
        )
        
        # 返回正确格式的响应
        logger.info(f"SSO回调处理成功 - user_id: {user.id}")
        return SSOTokenResponse(
            data=token_data,
            user=user
        )
    except Exception as e:
        # 记录详细的错误信息
        logger.error("=" * 80)
        logger.error("🚨 SSO回调处理失败")
        logger.error("=" * 80)
        logger.error(f"📍 回调参数:")
        logger.error(f"   - 授权码: {code[:12]}...{code[-8:] if len(code) > 20 else ''}")
        logger.error(f"   - 授权码长度: {len(code)}字符")
        logger.error(f"   - 状态参数: {state}")
        logger.error(f"   - 状态参数长度: {len(state)}字符")
        logger.error(f"")
        logger.error(f"❌ 异常详情:")
        logger.error(f"   - 异常类型: {type(e).__name__}")
        logger.error(f"   - 异常消息: {str(e)}")
        logger.error(f"")
        logger.error(f"🔍 错误分类:")

        # 根据异常类型提供更详细的分析
        from src.api.auth.exceptions import SSOAuthenticationError
        if isinstance(e, SSOAuthenticationError):
            logger.error(f"   - 类别: SSO认证错误")
            logger.error(f"   - 说明: 与SSO服务器交互失败")
            logger.error(f"   - 建议: 检查SSO配置和网络连接")
        elif "database" in str(e).lower() or "sql" in str(e).lower():
            logger.error(f"   - 类别: 数据库错误")
            logger.error(f"   - 说明: 用户数据处理失败")
            logger.error(f"   - 建议: 检查数据库连接和用户表结构")
        elif "timeout" in str(e).lower():
            logger.error(f"   - 类别: 超时错误")
            logger.error(f"   - 说明: 请求处理超时")
            logger.error(f"   - 建议: 检查网络延迟和服务器性能")
        elif "json" in str(e).lower() or "parse" in str(e).lower():
            logger.error(f"   - 类别: 数据解析错误")
            logger.error(f"   - 说明: SSO响应数据格式异常")
            logger.error(f"   - 建议: 检查SSO服务器响应格式")
        else:
            logger.error(f"   - 类别: 未知错误")
            logger.error(f"   - 说明: 系统内部错误")
            logger.error(f"   - 建议: 查看完整堆栈跟踪")

        logger.error(f"")
        logger.error(f"🛠️  排查步骤:")
        logger.error(f"   1. 检查授权码是否有效（重新登录获取新授权码）")
        logger.error(f"   2. 验证SSO配置参数（client_id, client_secret, redirect_uri）")
        logger.error(f"   3. 检查网络连接和SSO服务器状态")
        logger.error(f"   4. 查看数据库连接和用户表结构")
        logger.error(f"   5. 检查服务器时间同步")
        logger.error("=" * 80)
        logger.exception("完整异常堆栈跟踪:")

        # 根据异常类型返回不同的错误信息
        if isinstance(e, SSOAuthenticationError):
            error_message = f"SSO认证失败: {str(e)}"
            status_code = status.HTTP_401_UNAUTHORIZED
        else:
            error_message = "处理SSO回调失败，请重新登录"
            status_code = status.HTTP_500_INTERNAL_SERVER_ERROR

        # 抛出包含详细错误信息的APIError
        raise APIError(
            message=error_message,
            status_code=status_code,
            details={
                "error_type": type(e).__name__,
                "error_message": str(e),
                "code_length": len(code),
                "state_length": len(state),
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
        )


@router.get(
    "/logout-url",
    response_model=SSOUrlResponse,
    status_code=status.HTTP_200_OK,
    summary="获取SSO登出URL",
    description="获取同程统一授权登录中心的登出URL"
)
async def get_logout_url(access_token: str) -> SSOUrlResponse:
    """
    获取SSO登出URL。
    
    Args:
        access_token: SSO访问令牌
    
    Returns:
        SSOUrlResponse: 包含SSO登出URL的响应
    """
    try:
        logout_url = await get_sso_logout_url(access_token)
        return SSOUrlResponse(data={"logout_url": logout_url})
    except Exception as e:
        logger.exception(f"获取SSO登出URL时发生错误: {str(e)}")
        raise APIError(
            message="获取SSO登出URL失败",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            details=str(e) if str(e) else None
        )


@router.post(
    "/verify",
    response_model=SSOTokenResponse,
    status_code=status.HTTP_200_OK,
    summary="验证SSO授权码",
    description="验证同程统一授权登录中心的授权码并返回访问令牌",
    responses={
        status.HTTP_401_UNAUTHORIZED: {
            "description": "SSO授权码验证失败",
        },
        status.HTTP_500_INTERNAL_SERVER_ERROR: {
            "description": "服务器内部错误"
        }
    }
)
async def verify_code(code: str, state: str) -> SSOTokenResponse:
    """
    验证SSO授权码并返回访问令牌。
    
    Args:
        code: 授权码
        state: 状态值
    
    Returns:
        SSOTokenResponse: 包含访问令牌和用户信息的响应
    """
    try:
        access_token, user_info, app_token = await exchange_code_for_token(code, state)
        
        # 打印用户信息详情
        logger.info(f"SSO 返回的用户信息类型: {type(user_info)}")
        logger.info(f"SSO 返回的用户信息字段: {list(user_info.keys()) if isinstance(user_info, dict) else 'Not a dict'}")
        logger.info(f"SSO 返回的用户信息内容: {user_info}")
        
        # 从数据库获取用户信息以确保使用正确的ID
        user_id = user_info.get("userId", "")
        db_user = await User.filter(id=user_id).first()

        if db_user:
            # 使用数据库中的用户信息
            user = SSOUserInfo(
                id=str(db_user.id),  # 使用数据库的id字段
                name=db_user.username,
                email=db_user.email or "",
                role="user",
                department=db_user.department,
                work_id=db_user.work_id
            )
        else:
            # 如果数据库中没有用户信息，使用SSO返回的信息
            user = SSOUserInfo(
                id=user_id,  # 使用user_id作为临时ID
                name=user_info.get("username", ""),
                email=user_info.get("email", ""),
                role="user"
            )
        
        # 创建令牌对象
        token_data = Token(
            access_token=app_token,
            token_type="bearer"
        )
        
        # 返回正确格式的响应
        return SSOTokenResponse(
            data=token_data,
            user=user
        )
    except Exception as e:
        # 其他未预期的异常记录并包装为APIError
        logger.exception(f"验证SSO授权码时发生错误: {str(e)}")
        raise APIError(
            message="验证SSO授权码时发生错误",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            details=str(e) if str(e) else None
        )


@router.post(
    "/logout",
    status_code=status.HTTP_200_OK,
    summary="执行SSO登出",
    description="执行同程统一授权登录中心的登出操作"
)
async def logout(access_token: str = Form(...)) -> dict:
    """
    执行SSO登出操作。

    Args:
        access_token: SSO访问令牌（从表单数据中获取）

    Returns:
        dict: 登出结果
    """
    try:
        logger.info(f"收到SSO登出请求，token: {access_token[:20] if access_token else 'None'}...")

        success = await sso_logout(access_token)
        return {
            "success": success,
            "message": "登出成功" if success else "登出失败，但本地清理已完成"
        }
    except Exception as e:
        logger.exception(f"执行SSO登出时发生错误: {str(e)}")
        # 即使出错也返回成功，确保前端能够清理本地状态
        return {
            "success": True,
            "message": "登出过程中出现错误，但本地清理已完成"
        }


@router.get(
    "/config-check",
    status_code=status.HTTP_200_OK,
    summary="检查SSO配置",
    description="检查当前SSO配置是否正确（仅开发环境可用）"
)
async def check_sso_config():
    """
    检查SSO配置状态。

    Returns:
        dict: SSO配置检查结果
    """
    try:
        # 只在非生产环境提供此接口
        if settings.environment == "production":
            return JSONResponse(
                status_code=403,
                content={"error": "此接口在生产环境不可用"}
            )

        config_status = {
            "sso_enabled": settings.sso_enabled,
            "sso_domain": settings.sso_domain,
            "sso_client_id": settings.sso_client_id,
            "sso_client_secret_configured": bool(settings.sso_client_secret),
            "sso_client_secret_length": len(settings.sso_client_secret) if settings.sso_client_secret else 0,
            "sso_redirect_uri": settings.sso_redirect_uri,
            "sso_return_url": settings.sso_return_url,
            "environment": settings.environment
        }

        # 检查配置完整性
        issues = []
        if not settings.sso_enabled:
            issues.append("SSO未启用")
        if not settings.sso_domain:
            issues.append("SSO域名未配置")
        if not settings.sso_client_id:
            issues.append("SSO客户端ID未配置")
        if not settings.sso_client_secret:
            issues.append("SSO客户端密钥未配置")
        if not settings.sso_redirect_uri:
            issues.append("SSO回调URI未配置")
        elif not settings.sso_redirect_uri.startswith("https://"):
            issues.append("SSO回调URI不是HTTPS")

        config_status["issues"] = issues
        config_status["is_valid"] = len(issues) == 0

        return config_status

    except Exception as e:
        logger.exception(f"检查SSO配置时发生错误: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"error": f"检查配置失败: {str(e)}"}
        )


@router.get(
    "/diagnose",
    status_code=status.HTTP_200_OK,
    summary="SSO连接诊断",
    description="诊断SSO服务器连接状态和配置（仅开发环境可用）"
)
async def diagnose_sso_connection():
    """
    诊断SSO连接状态。

    Returns:
        dict: SSO连接诊断结果
    """
    try:
        # 只在非生产环境提供此接口
        if settings.environment == "production":
            return JSONResponse(
                status_code=403,
                content={"error": "此接口在生产环境不可用"}
            )

        import httpx
        import time

        diagnosis = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "sso_domain": settings.sso_domain,
            "tests": []
        }

        # 测试1: 基本网络连接
        try:
            start_time = time.time()
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{settings.sso_domain}", timeout=5.0)
                end_time = time.time()

                diagnosis["tests"].append({
                    "name": "基本网络连接",
                    "status": "success" if response.status_code < 500 else "warning",
                    "details": {
                        "status_code": response.status_code,
                        "response_time": f"{(end_time - start_time):.2f}秒",
                        "server_header": response.headers.get("Server", "未知")
                    }
                })
        except Exception as e:
            diagnosis["tests"].append({
                "name": "基本网络连接",
                "status": "failed",
                "error": str(e)
            })

        # 测试2: OAuth令牌端点可达性
        try:
            start_time = time.time()
            async with httpx.AsyncClient() as client:
                # 发送一个无效请求来测试端点是否存在
                response = await client.post(
                    f"{settings.sso_domain}/oauth/token",
                    headers={"Content-Type": "application/x-www-form-urlencoded"},
                    data={"test": "connectivity"},
                    timeout=5.0
                )
                end_time = time.time()

                diagnosis["tests"].append({
                    "name": "OAuth令牌端点",
                    "status": "success" if response.status_code in [400, 401, 422] else "warning",
                    "details": {
                        "status_code": response.status_code,
                        "response_time": f"{(end_time - start_time):.2f}秒",
                        "endpoint": f"{settings.sso_domain}/oauth/token"
                    }
                })
        except Exception as e:
            diagnosis["tests"].append({
                "name": "OAuth令牌端点",
                "status": "failed",
                "error": str(e)
            })

        # 测试3: 用户信息端点可达性
        try:
            start_time = time.time()
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{settings.sso_domain}/oauth/rs/getuserinfo",
                    headers={"Content-Type": "application/x-www-form-urlencoded"},
                    data={"test": "connectivity"},
                    timeout=5.0
                )
                end_time = time.time()

                diagnosis["tests"].append({
                    "name": "用户信息端点",
                    "status": "success" if response.status_code in [400, 401, 422] else "warning",
                    "details": {
                        "status_code": response.status_code,
                        "response_time": f"{(end_time - start_time):.2f}秒",
                        "endpoint": f"{settings.sso_domain}/oauth/rs/getuserinfo"
                    }
                })
        except Exception as e:
            diagnosis["tests"].append({
                "name": "用户信息端点",
                "status": "failed",
                "error": str(e)
            })

        # 汇总结果
        failed_tests = [t for t in diagnosis["tests"] if t["status"] == "failed"]
        warning_tests = [t for t in diagnosis["tests"] if t["status"] == "warning"]

        diagnosis["summary"] = {
            "total_tests": len(diagnosis["tests"]),
            "passed": len(diagnosis["tests"]) - len(failed_tests) - len(warning_tests),
            "warnings": len(warning_tests),
            "failed": len(failed_tests),
            "overall_status": "failed" if failed_tests else ("warning" if warning_tests else "success")
        }

        return diagnosis

    except Exception as e:
        logger.exception(f"SSO连接诊断时发生错误: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"error": f"诊断失败: {str(e)}"}
        )


@router.get(
    "/code-stats",
    status_code=status.HTTP_200_OK,
    summary="授权码使用统计",
    description="查看授权码使用统计，用于排查循环登录问题（仅开发环境可用）"
)
async def get_code_statistics():
    """
    获取授权码使用统计。

    根据官方说明：如果出现大量的code换取token出现400，基本是因为系统iframe嵌入了别的系统，
    开了同程管家的情况下，循环登录，由于http https下，http获取不到https种的cookie导致。

    Returns:
        dict: 授权码使用统计信息
    """
    try:
        # 只在非生产环境提供此接口
        if settings.environment == "production":
            return JSONResponse(
                status_code=403,
                content={"error": "此接口在生产环境不可用"}
            )

        from src.api.auth.sso_service import _processed_codes
        from datetime import datetime, timedelta
        import time

        current_time = datetime.now()

        # 统计不同时间段的授权码使用情况
        stats = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "total_codes": len(_processed_codes),
            "time_periods": {}
        }

        # 定义时间段
        periods = {
            "last_1_minute": timedelta(minutes=1),
            "last_5_minutes": timedelta(minutes=5),
            "last_15_minutes": timedelta(minutes=15),
            "last_30_minutes": timedelta(minutes=30),
            "last_1_hour": timedelta(hours=1),
            "last_6_hours": timedelta(hours=6)
        }

        for period_name, period_delta in periods.items():
            cutoff_time = current_time - period_delta
            period_codes = [
                {
                    "code_prefix": f"{code[:8]}...{code[-4:]}",
                    "timestamp": code_time.strftime("%Y-%m-%d %H:%M:%S"),
                    "seconds_ago": int((current_time - code_time).total_seconds())
                }
                for code, code_time in _processed_codes.items()
                if code_time > cutoff_time
            ]

            stats["time_periods"][period_name] = {
                "count": len(period_codes),
                "codes": sorted(period_codes, key=lambda x: x["seconds_ago"])
            }

        # 循环登录风险评估
        recent_5min = stats["time_periods"]["last_5_minutes"]["count"]
        recent_1min = stats["time_periods"]["last_1_minute"]["count"]

        risk_level = "low"
        risk_message = "正常使用"

        if recent_1min > 3:
            risk_level = "high"
            risk_message = "1分钟内使用了多个授权码，可能存在严重的循环登录问题"
        elif recent_5min > 5:
            risk_level = "medium"
            risk_message = "5分钟内使用了多个授权码，可能存在循环登录问题"
        elif recent_5min > 2:
            risk_level = "low"
            risk_message = "授权码使用频率稍高，需要关注"

        stats["risk_assessment"] = {
            "level": risk_level,
            "message": risk_message,
            "recommendations": []
        }

        # 根据风险级别提供建议
        if risk_level == "high":
            stats["risk_assessment"]["recommendations"] = [
                "立即检查页面是否被iframe嵌入",
                "确认是否开启了同程管家等插件",
                "检查HTTP/HTTPS混合使用问题",
                "暂停SSO登录，排查循环登录原因",
                "联系技术支持进行深度排查"
            ]
        elif risk_level == "medium":
            stats["risk_assessment"]["recommendations"] = [
                "检查页面是否被其他系统嵌入",
                "确认cookie设置是否正确",
                "检查是否存在自动刷新或重定向",
                "监控后续授权码使用情况"
            ]

        # 官方说明提醒
        stats["official_note"] = {
            "title": "官方400错误说明",
            "content": [
                "redirect_uri不要urlencode",
                "一个code只能换一次token",
                "grant_type固定的值：authorization_code",
                "如果出现大量的code换取token出现400，基本是因为：",
                "  - 系统iframe嵌入了别的系统",
                "  - 开了同程管家的情况下循环登录",
                "  - 由于http https下，http获取不到https种的cookie导致"
            ]
        }

        return stats

    except Exception as e:
        logger.exception(f"获取授权码统计时发生错误: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"error": f"获取统计失败: {str(e)}"}
        )


@router.post(
    "/clear-code-cache",
    status_code=status.HTTP_200_OK,
    summary="清理授权码缓存",
    description="清理授权码使用缓存，用于重置循环登录检测（仅开发环境可用）"
)
async def clear_code_cache():
    """
    清理授权码使用缓存。

    Returns:
        dict: 清理结果
    """
    try:
        # 只在非生产环境提供此接口
        if settings.environment == "production":
            return JSONResponse(
                status_code=403,
                content={"error": "此接口在生产环境不可用"}
            )

        from src.api.auth.sso_service import _processed_codes

        cleared_count = len(_processed_codes)
        _processed_codes.clear()

        logger.info(f"🧹 手动清理授权码缓存: {cleared_count} 个记录")

        return {
            "success": True,
            "message": f"已清理 {cleared_count} 个授权码缓存记录",
            "cleared_count": cleared_count,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

    except Exception as e:
        logger.exception(f"清理授权码缓存时发生错误: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"error": f"清理失败: {str(e)}"}
        )
