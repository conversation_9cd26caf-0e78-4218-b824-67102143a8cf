"""
SSO认证服务模块。

此模块处理与同程统一授权登录中心的交互，包括生成登录URL、验证票据和登出URL。
"""

import urllib.parse
import httpx
from fastapi import status
from loguru import logger
from datetime import datetime, timedelta

from src.core.config import settings
from src.core.security import create_access_token
from src.db.models.user import User
from src.api.auth.exceptions import AuthenticationError, SSOAuthenticationError
from tortoise import Tortoise

# 简单的内存缓存，存储已处理的授权码
_processed_codes = {}



async def get_sso_login_url() -> str:
    """
    获取SSO登录URL。
    
    Returns:
        str: SSO登录URL
    
    Raises:
        SSOAuthenticationError: 如果获取SSO登录URL失败
    """
    try:
        # 强制使用正确的同程 SSO 域名，而不是从配置中获取
        sso_domain = settings.sso_domain
        client_id = settings.sso_client_id
        # 使用端口 5174，因为 Vite 现在使用这个端口
        redirect_uri = settings.sso_redirect_uri
        response_type = "code"  # 固定为 code
        scope = "read"  # 固定为 read
        state = "random_state"  # 实际应用中应该生成随机字符串并存储在会话中
        
        # 构建授权URL (符合同程SSO规范)
        login_url = f"{sso_domain}/oauth/authorize?response_type={response_type}&scope={scope}&client_id={client_id}&redirect_uri={urllib.parse.quote(redirect_uri)}&state={state}"
        
        logger.info(f"生成SSO登录URL: {login_url}")
        return login_url
    except Exception as e:
        logger.exception(f"获取SSO登录URL时发生错误: {str(e)}")
        raise SSOAuthenticationError(message="获取SSO登录URL失败")


async def exchange_code_for_token(code: str, state: str):
    """
    使用授权码获取访问令牌和用户信息。
    
    Args:
        code: 授权码
        state: 状态值
    
    Returns:
        tuple: (访问令牌, 用户信息, 应用访问令牌)
    
    Raises:
        SSOAuthenticationError: 如果获取访问令牌失败
    """
    global _processed_codes  # 声明使用全局变量
    
    try:
        # 防重复处理：检查授权码是否已经被处理过
        current_time = datetime.now()
        if code in _processed_codes:
            last_processed_time = _processed_codes[code]
            # 如果在5分钟内已经处理过，直接抛出错误
            if current_time - last_processed_time < timedelta(minutes=5):
                logger.warning(f"授权码 {code[:8]}... 已在 {last_processed_time} 被处理过，拒绝重复处理")
                raise SSOAuthenticationError(message="授权码已被使用，请重新获取")
        
        # 记录授权码处理时间
        _processed_codes[code] = current_time
        
        # 清理超过1小时的旧记录，防止内存泄漏
        cutoff_time = current_time - timedelta(hours=1)
        _processed_codes = {k: v for k, v in _processed_codes.items() if v > cutoff_time}
        
        # 使用授权码获取访问令牌
        # 从配置文件获取SSO相关配置
        sso_domain = settings.sso_domain
        client_id = settings.sso_client_id
        client_secret = settings.sso_client_secret
        redirect_uri = settings.sso_redirect_uri
        grant_type = "authorization_code"  # 固定为 authorization_code
        
        async with httpx.AsyncClient() as client:
            # 记录请求参数（隐藏敏感信息）
            logger.info(f"请求SSO令牌接口 - URL: {sso_domain}/oauth/token")
            logger.info(f"请求参数 - client_id: {client_id}, redirect_uri: {redirect_uri}, grant_type: {grant_type}")
            logger.info(f"授权码: {code[:8]}...")  # 只显示前8位

            # 验证关键配置
            if not client_secret:
                logger.error("SSO客户端密钥未配置")
                raise SSOAuthenticationError(message="SSO配置错误：缺少客户端密钥")

            if not redirect_uri.startswith("https://"):
                logger.warning(f"回调URI不是HTTPS: {redirect_uri}")

            # 检查授权码格式
            if len(code) < 10:
                logger.warning(f"授权码长度异常: {len(code)} 字符")

            # 🔍 官方400错误预防检查
            logger.info("=" * 60)
            logger.info("🔍 SSO官方400错误预防检查")
            logger.info("=" * 60)

            # 1. 确保redirect_uri没有被URL编码
            import urllib.parse
            if urllib.parse.quote(redirect_uri, safe='') != redirect_uri:
                logger.info(f"✅ redirect_uri未被URL编码: {redirect_uri}")
            else:
                logger.warning(f"⚠️  redirect_uri可能被URL编码，这可能导致400错误")

            # 2. 确保grant_type是固定值
            if grant_type != "authorization_code":
                logger.error(f"❌ grant_type错误: {grant_type}，应该是 'authorization_code'")
                raise SSOAuthenticationError(message="grant_type配置错误")
            else:
                logger.info(f"✅ grant_type正确: {grant_type}")

            # 3. 确认授权码使用记录（已在前面检查过重复使用）
            logger.info(f"✅ 授权码重复使用检查通过")

            # 4. 记录授权码使用情况，用于排查循环登录
            logger.info(f"📝 授权码使用记录:")
            logger.info(f"   - 授权码: {code[:12]}...{code[-8:]}")
            logger.info(f"   - 使用时间: {current_time}")
            logger.info(f"   - 客户端ID: {client_id}")
            logger.info(f"   - 回调URI: {redirect_uri}")

            logger.info(f"客户端密钥长度: {len(client_secret)} 字符")
            logger.info("=" * 60)
            
            # 调用同程SSO的令牌接口
            token_response = await client.post(
                f"{sso_domain}/oauth/token",
                headers={
                    "Content-Type": "application/x-www-form-urlencoded",
                    "Accept": "application/json"
                },
                data={
                    "client_id": client_id,
                    "client_secret": client_secret,
                    "redirect_uri": redirect_uri,
                    "code": code,
                    "grant_type": grant_type
                },
                timeout=10.0
            )
            
            logger.info(f"SSO令牌接口响应状态码: {token_response.status_code}")
            logger.info(f"SSO令牌接口响应内容: {token_response.text}")

            if token_response.status_code != 200:
                # 解析错误响应
                try:
                    error_data = token_response.json()
                    error_type = error_data.get("error", "unknown_error")
                    error_description = error_data.get("error_description", "未知错误")

                    # 记录完整的错误详情
                    logger.error("=" * 80)
                    logger.error("🚨 SSO认证失败 - 详细错误信息")
                    logger.error("=" * 80)
                    logger.error(f"📍 请求信息:")
                    logger.error(f"   - SSO域名: {sso_domain}")
                    logger.error(f"   - 令牌接口: {sso_domain}/oauth/token")
                    logger.error(f"   - HTTP状态码: {token_response.status_code}")
                    logger.error(f"   - 响应时间: {token_response.elapsed.total_seconds():.2f}秒")
                    logger.error(f"")
                    logger.error(f"🔑 认证参数:")
                    logger.error(f"   - 客户端ID: {client_id}")
                    logger.error(f"   - 客户端密钥长度: {len(client_secret)}字符")
                    logger.error(f"   - 客户端密钥前缀: {client_secret[:8]}...")
                    logger.error(f"   - 回调URI: {redirect_uri}")
                    logger.error(f"   - 授权类型: {grant_type}")
                    logger.error(f"")
                    logger.error(f"📝 授权码信息:")
                    logger.error(f"   - 授权码长度: {len(code)}字符")
                    logger.error(f"   - 授权码前缀: {code[:12]}...")
                    logger.error(f"   - 授权码后缀: ...{code[-8:]}")
                    logger.error(f"   - 状态参数: {state}")
                    logger.error(f"")
                    logger.error(f"❌ 错误详情:")
                    logger.error(f"   - 错误类型: {error_type}")
                    logger.error(f"   - 错误描述: {error_description}")
                    logger.error(f"   - 完整响应: {token_response.text}")
                    logger.error(f"")
                    logger.error(f"🔍 可能原因:")

                    # 根据错误类型提供详细的诊断信息
                    if error_type == "invalid_grant":
                        logger.error(f"   🔍 invalid_grant错误详细分析:")
                        logger.error(f"   1. 授权码已过期（OAuth2授权码通常5-10分钟内有效）")
                        logger.error(f"   2. 授权码已被使用（官方说明：一个code只能换一次token）")
                        logger.error(f"   3. 授权码格式错误或被篡改")
                        logger.error(f"   4. 服务器时间不同步")
                        logger.error(f"   5. 🚨 iframe嵌入循环登录问题（官方重点说明）:")
                        logger.error(f"      - 系统iframe嵌入了别的系统")
                        logger.error(f"      - 开了同程管家的情况下循环登录")
                        logger.error(f"      - HTTP/HTTPS混合导致cookie获取失败")

                        # 检查是否可能是循环登录问题
                        recent_codes = [k for k, v in _processed_codes.items()
                                      if current_time - v < timedelta(minutes=5)]
                        if len(recent_codes) > 3:
                            logger.error(f"   ⚠️  检测到可能的循环登录:")
                            logger.error(f"      - 5分钟内处理了 {len(recent_codes)} 个授权码")
                            logger.error(f"      - 这可能表明存在iframe嵌入循环登录问题")
                            logger.error(f"      - 建议检查页面是否被iframe嵌入")

                        if "Invalid code" in error_description:
                            error_msg = "授权码无效或已过期，请重新登录"
                        else:
                            error_msg = "授权失败，可能存在循环登录问题"
                    elif error_type == "invalid_client":
                        logger.error(f"   1. 客户端ID配置错误")
                        logger.error(f"   2. 客户端密钥配置错误")
                        logger.error(f"   3. 客户端未在SSO服务器注册")
                        logger.error(f"   4. 客户端权限不足")
                        error_msg = "客户端配置错误，请联系管理员"
                    elif error_type == "invalid_request":
                        logger.error(f"   1. 请求参数缺失或格式错误")
                        logger.error(f"   2. 回调URI不匹配")
                        logger.error(f"   3. 请求头格式错误")
                        error_msg = "请求参数错误，请重新登录"
                    elif error_type == "unsupported_grant_type":
                        logger.error(f"   1. 授权类型不支持")
                        logger.error(f"   2. SSO服务器配置问题")
                        error_msg = "授权类型不支持，请联系管理员"
                    else:
                        logger.error(f"   1. 未知错误类型: {error_type}")
                        logger.error(f"   2. 请检查SSO服务器状态")
                        logger.error(f"   3. 请检查网络连接")
                        error_msg = f"SSO认证失败: {error_description}"

                    logger.error(f"")
                    logger.error(f"🛠️  建议解决方案:")
                    logger.error(f"   1. 重新获取授权码（重新登录）")
                    logger.error(f"   2. 检查是否存在iframe嵌入问题")
                    logger.error(f"   3. 确认页面没有被其他系统嵌入")
                    logger.error(f"   4. 检查HTTP/HTTPS混合使用问题")
                    logger.error(f"   5. 验证同程管家等插件影响")
                    logger.error(f"   6. 检查SSO服务器配置")
                    logger.error(f"   7. 验证客户端注册信息")
                    logger.error(f"   8. 检查服务器时间同步")

                    # 输出授权码使用统计（用于排查循环登录）
                    logger.error(f"")
                    logger.error(f"📊 授权码使用统计（用于排查循环登录）:")
                    logger.error(f"   - 当前缓存的授权码数量: {len(_processed_codes)}")
                    recent_codes = [k for k, v in _processed_codes.items()
                                  if current_time - v < timedelta(minutes=5)]
                    logger.error(f"   - 最近5分钟的授权码: {len(recent_codes)}")
                    if recent_codes:
                        logger.error(f"   - 最近授权码列表:")
                        for i, recent_code in enumerate(recent_codes[-5:], 1):
                            code_time = _processed_codes[recent_code]
                            logger.error(f"     {i}. {recent_code[:8]}...{recent_code[-4:]} ({code_time})")
                    logger.error("=" * 80)

                except Exception as parse_error:
                    error_msg = "获取访问令牌失败"
                    logger.error("=" * 80)
                    logger.error("🚨 SSO认证失败 - 无法解析错误响应")
                    logger.error("=" * 80)
                    logger.error(f"📍 请求信息:")
                    logger.error(f"   - SSO域名: {sso_domain}")
                    logger.error(f"   - HTTP状态码: {token_response.status_code}")
                    logger.error(f"   - 原始响应: {token_response.text}")
                    logger.error(f"")
                    logger.error(f"❌ 解析错误:")
                    logger.error(f"   - 解析异常: {str(parse_error)}")
                    logger.error(f"   - 响应可能不是有效的JSON格式")
                    logger.error("=" * 80)

                logger.error(f"🔚 最终错误: {token_response.status_code} - {token_response.text}")
                raise SSOAuthenticationError(message=error_msg)
            
            token_data = token_response.json()
            access_token = token_data.get("access_token")
            
            if not access_token:
                raise SSOAuthenticationError(message="获取访问令牌失败: 响应中没有访问令牌")
            
            # 使用访问令牌获取用户信息
            user_info_response = await client.post(
                f"{sso_domain}/oauth/rs/getuserinfo",
                headers={
                    "Content-Type": "application/x-www-form-urlencoded"
                },
                data={
                    "access_token": access_token
                },
                timeout=10.0
            )
            
            if user_info_response.status_code != 200:
                logger.error("=" * 80)
                logger.error("🚨 获取SSO用户信息失败")
                logger.error("=" * 80)
                logger.error(f"📍 请求信息:")
                logger.error(f"   - 用户信息接口: {sso_domain}/oauth/rs/getuserinfo")
                logger.error(f"   - HTTP状态码: {user_info_response.status_code}")
                logger.error(f"   - 响应时间: {user_info_response.elapsed.total_seconds():.2f}秒")
                logger.error(f"")
                logger.error(f"🔑 访问令牌信息:")
                logger.error(f"   - 令牌长度: {len(access_token)}字符")
                logger.error(f"   - 令牌前缀: {access_token[:20]}...")
                logger.error(f"")
                logger.error(f"❌ 错误详情:")
                logger.error(f"   - 响应内容: {user_info_response.text}")
                logger.error(f"")
                logger.error(f"🔍 可能原因:")
                if user_info_response.status_code == 401:
                    logger.error(f"   1. 访问令牌无效或已过期")
                    logger.error(f"   2. 访问令牌格式错误")
                elif user_info_response.status_code == 403:
                    logger.error(f"   1. 访问令牌权限不足")
                    logger.error(f"   2. 用户信息接口访问被拒绝")
                elif user_info_response.status_code == 404:
                    logger.error(f"   1. 用户信息接口地址错误")
                    logger.error(f"   2. SSO服务器配置问题")
                elif user_info_response.status_code >= 500:
                    logger.error(f"   1. SSO服务器内部错误")
                    logger.error(f"   2. SSO服务器暂时不可用")
                else:
                    logger.error(f"   1. 未知HTTP错误: {user_info_response.status_code}")
                logger.error("=" * 80)
                raise SSOAuthenticationError(message="获取用户信息失败")
            
            user_data = user_info_response.json()

        # 🎉 成功获取用户信息，记录授权码使用
        _processed_codes[code] = current_time
        logger.info(f"✅ 授权码使用成功记录:")
        logger.info(f"   - 授权码: {code[:12]}...{code[-8:]}")
        logger.info(f"   - 处理时间: {current_time}")
        logger.info(f"   - 用户信息获取成功")

        # 清理过期的授权码记录（保留24小时）
        cutoff_time = current_time - timedelta(hours=24)
        expired_codes = [k for k, v in _processed_codes.items() if v < cutoff_time]
        for expired_code in expired_codes:
            del _processed_codes[expired_code]

        if expired_codes:
            logger.info(f"🧹 清理了 {len(expired_codes)} 个过期授权码记录")

        # 在本地数据库中查找或创建用户
        user_dict = await get_or_create_user_from_sso(user_data)

        # 创建应用的JWT访问令牌
        app_access_token = create_access_token(subject=str(user_dict["userId"]))

        return access_token, user_dict, app_access_token
    except httpx.RequestError as e:
        logger.error("=" * 80)
        logger.error("🚨 SSO服务网络请求失败")
        logger.error("=" * 80)
        logger.error(f"📍 网络错误详情:")
        logger.error(f"   - 异常类型: {type(e).__name__}")
        logger.error(f"   - 错误消息: {str(e)}")
        logger.error(f"   - SSO域名: {sso_domain}")
        logger.error(f"")
        logger.error(f"🔍 可能原因:")
        logger.error(f"   1. 网络连接问题")
        logger.error(f"   2. SSO服务器不可达")
        logger.error(f"   3. DNS解析失败")
        logger.error(f"   4. 防火墙阻止连接")
        logger.error(f"   5. 请求超时（当前超时设置: 10秒）")
        logger.error(f"")
        logger.error(f"🛠️  建议解决方案:")
        logger.error(f"   1. 检查网络连接")
        logger.error(f"   2. 验证SSO域名配置")
        logger.error(f"   3. 检查防火墙设置")
        logger.error(f"   4. 联系网络管理员")
        logger.error("=" * 80)
        logger.exception("完整网络异常堆栈:")
        raise SSOAuthenticationError(message="SSO服务连接失败，请检查网络连接")
    except SSOAuthenticationError:
        # 重新抛出SSO认证错误，不做额外处理
        raise
    except Exception as e:
        logger.error("=" * 80)
        logger.error("🚨 处理SSO授权码时发生未知错误")
        logger.error("=" * 80)
        logger.error(f"📍 异常详情:")
        logger.error(f"   - 异常类型: {type(e).__name__}")
        logger.error(f"   - 错误消息: {str(e)}")
        logger.error(f"   - 授权码: {code[:12]}...{code[-8:] if len(code) > 20 else ''}")
        logger.error(f"   - 状态参数: {state}")
        logger.error(f"")
        logger.error(f"🔍 可能原因:")
        logger.error(f"   1. 代码逻辑错误")
        logger.error(f"   2. 数据格式异常")
        logger.error(f"   3. 数据库连接问题")
        logger.error(f"   4. 内存不足")
        logger.error(f"   5. 系统资源限制")
        logger.error("=" * 80)
        logger.exception("完整异常堆栈:")
        raise SSOAuthenticationError(message=f"处理授权码失败: {str(e)}")


async def get_or_create_user_from_sso(user_data: dict) -> dict:
    """
    根据SSO用户数据获取或创建用户记录。
    
    Args:
        user_data: SSO返回的用户数据
    
    Returns:
        dict: 用户信息字典
    """
    try:
        # 记录完整的用户数据，便于调试
        logger.info(f"SSO返回的用户数据: {user_data}")
        
        # 尝试通过工号查找用户（工号在测试和生产环境中是相同的）
        work_id = user_data.get("workId") or user_data.get("work_id")
        tc_user_id = user_data.get("userId")

        if not work_id and not tc_user_id:
            logger.error(f"SSO返回的用户数据中没有workId或userId字段: {user_data}")
            raise SSOAuthenticationError(message="SSO返回的用户数据无效")

        # 优先使用work_id查找用户，如果没有work_id则使用tc_user_id
        user = None
        if work_id:
            user = await User.filter(work_id=work_id).first()
            logger.info(f"通过work_id查找用户: {work_id}, 结果: {'找到' if user else '未找到'}")

        if not user and tc_user_id:
            user = await User.filter(tc_user_id=tc_user_id).first()
            logger.info(f"通过tc_user_id查找用户: {tc_user_id}, 结果: {'找到' if user else '未找到'}")
        
        if not user:
            # 创建新用户
            logger.info(f"创建新用户: work_id={work_id}, tc_user_id={tc_user_id}")
            user = User(
                username=user_data.get("username", "SSO User"),
                department=user_data.get("department"),
                tc_user_id=tc_user_id,
                work_id=user_data.get("workId") or user_data.get("work_id"),
                new_work_id=user_data.get("newWorkId"),
                department_id=user_data.get("departmentId"),
                gender=user_data.get("gender"),
                email=user_data.get("email"),
                dept_level_id=user_data.get("deptLevelId"),
                dept_level_name=user_data.get("deptLevelName"),
                phone_number=user_data.get("phoneNumber"),
                mtid=user_data.get("MTID"),
                ctids=user_data.get("CTIDS"),
                gid=user_data.get("GID"),
                mobile=user_data.get("mobile"),
                member_id=user_data.get("memberId"),
                is_virtual=user_data.get("isVirtual"),
                tid=user_data.get("TID"),
                device_id=user_data.get("deviceId")
            )
            await user.save()
            logger.info(f"从SSO创建新用户成功: {tc_user_id}")

            # 为新用户分配默认权限
            await _assign_default_permissions_to_user(user.id)
            logger.info(f"为新用户分配默认权限成功: user_id={user.id}")
        else:
            # 更新现有用户信息
            logger.info(f"更新现有用户: work_id={user.work_id}, tc_user_id={user.tc_user_id}")
            user.username = user_data.get("username", user.username)
            user.department = user_data.get("department", user.department)
            user.work_id = user_data.get("workId", user.work_id) or user_data.get("work_id", user.work_id)
            user.new_work_id = user_data.get("newWorkId", user.new_work_id)
            user.department_id = user_data.get("departmentId", user.department_id)
            user.gender = user_data.get("gender", user.gender)
            user.email = user_data.get("email", user.email)
            user.dept_level_id = user_data.get("deptLevelId", user.dept_level_id)
            user.dept_level_name = user_data.get("deptLevelName", user.dept_level_name)
            user.phone_number = user_data.get("phoneNumber", user.phone_number)
            user.mtid = user_data.get("MTID", user.mtid)
            user.ctids = user_data.get("CTIDS", user.ctids)
            user.gid = user_data.get("GID", user.gid)
            user.mobile = user_data.get("mobile", user.mobile)
            user.member_id = user_data.get("memberId", user.member_id)
            user.is_virtual = user_data.get("isVirtual", user.is_virtual)
            user.tid = user_data.get("TID", user.tid)
            user.device_id = user_data.get("deviceId", user.device_id)
            await user.save()
            logger.info(f"更新现有用户成功: {user.id}")
        
        # 返回用户信息时，确保包含work_id和department
        user_dict = {
            "userId": str(user.id),  # 返回数据库id作为userId（前端主要使用）
            "id": str(user.id),  # 数据库id字段
            "tc_user_id": user.tc_user_id,  # 保留tc_user_id用于集成
            "username": user.username,
            "department": user.department,
            "work_id": user.work_id,
            "email": user.email or "",
            "role": "user"
        }
        logger.info(f"返回的用户信息: {user_dict}")
        return user_dict
    except Exception as e:
        logger.exception(f"处理SSO用户数据时发生错误: {str(e)}")
        raise SSOAuthenticationError(message=f"处理SSO用户数据失败: {str(e)}")


async def get_sso_logout_url(access_token: str) -> str:
    """
    获取SSO登出URL。
    根据同程SSO官方文档：建议使用POST方式的/oauth/logoutapi接口
    
    Args:
        access_token: SSO访问令牌
    
    Returns:
        str: SSO登出URL
    
    Raises:
        SSOAuthenticationError: 如果获取SSO登出URL失败
    """
    try:
        # 构建SSO登出URL
        sso_domain = settings.sso_domain
        
        # 根据官方文档，建议使用POST方式的logoutapi接口
        logout_url = f"{sso_domain}/oauth/logoutapi?access_token={access_token}"
        
        logger.info(f"生成SSO登出URL: {logout_url}")
        return logout_url
    except Exception as e:
        logger.exception(f"获取SSO登出URL时发生错误: {str(e)}")
        raise SSOAuthenticationError(message="获取SSO登出URL失败")


async def sso_logout(access_token: str) -> bool:
    """
    执行SSO登出操作。
    根据同程SSO官方文档：
    - 推荐使用POST方式的 /oauth/logoutapi 接口
    - 支持线下、预发、正式环境
    - 返回格式：{"success_code": "1"} 正常，{"success_code": "0"} 异常
    
    Args:
        access_token: SSO访问令牌
    
    Returns:
        bool: 登出是否成功
    
    Raises:
        SSOAuthenticationError: 如果SSO登出失败
    """
    try:
        sso_domain = settings.sso_domain
        
        logger.info(f"开始执行SSO登出，使用官方logoutapi接口...")
        logger.info(f"SSO域名: {sso_domain}")
        logger.info(f"Token: {access_token[:20]}...")
        
        async with httpx.AsyncClient(timeout=15.0) as client:
            
            # === 方式1: 官方推荐的POST方式 /oauth/logoutapi ===
            logger.info("🔥 使用官方推荐的POST方式登出...")
            try:
                # 根据官方文档，使用POST方式调用logoutapi
                logout_response = await client.post(
                    f"{sso_domain}/oauth/logoutapi?access_token={access_token}",
                    headers={
                        "Content-Type": "application/x-www-form-urlencoded",
                        "Accept": "application/json",
                        "User-Agent": "DTTrip-SSO-Client/1.0"
                    },
                    data={}  # POST方式，参数在URL中
                )
                
                logger.info(f"logoutapi响应状态: {logout_response.status_code}")
                
                if logout_response.status_code == 200:
                    try:
                        response_data = logout_response.json()
                        logger.info(f"logoutapi响应数据: {response_data}")
                        
                        # 根据官方文档检查success_code
                        success_code = response_data.get("success_code")
                        if success_code == "1":
                            logger.info("✅ 官方logoutapi登出成功")
                            return True
                        elif success_code == "0":
                            logger.warning("⚠️ 官方logoutapi登出异常，但继续执行")
                        else:
                            logger.warning(f"⚠️ 官方logoutapi返回未知success_code: {success_code}")
                            
                    except Exception as json_error:
                        logger.warning(f"解析logoutapi响应JSON失败: {json_error}")
                        logger.warning(f"原始响应: {logout_response.text}")
                
                # 即使success_code为0或解析失败，也尝试备用方式
                        
            except Exception as logoutapi_error:
                logger.warning(f"logoutapi调用失败: {logoutapi_error}")
            
            # === 方式2: 备用的GET方式 /oauth/logout ===
            logger.info("📤 尝试备用GET方式登出...")
            try:
                # 官方文档提到的GET方式（退出到统一登录页面）
                get_logout_response = await client.get(
                    f"{sso_domain}/oauth/logout?access_token={access_token}",
                    headers={
                        "Accept": "text/html,application/json",
                        "User-Agent": "DTTrip-SSO-Client/1.0"
                    }
                )
                
                logger.info(f"GET logout响应状态: {get_logout_response.status_code}")
                
                if get_logout_response.status_code in [200, 302]:
                    logger.info("✅ GET方式登出成功")
                    return True
                    
            except Exception as get_error:
                logger.warning(f"GET方式登出失败: {get_error}")
            
            # === 方式3: 备用POST方式 /oauth/logout ===
            logger.info("📥 尝试备用POST方式登出...")
            try:
                post_logout_response = await client.post(
                    f"{sso_domain}/oauth/logout",
                    headers={
                        "Content-Type": "application/x-www-form-urlencoded",
                        "Accept": "application/json"
                    },
                    data={
                        "access_token": access_token
                    }
                )
                
                logger.info(f"POST logout响应状态: {post_logout_response.status_code}")
                
                if post_logout_response.status_code in [200, 302]:
                    logger.info("✅ 备用POST方式登出成功")
                    return True
                    
            except Exception as post_error:
                logger.warning(f"备用POST方式登出失败: {post_error}")
            
            # 即使所有方式都失败，也返回True，因为本地清理更重要
            logger.warning("⚠️ 所有SSO登出方式都失败，但本地清理将继续")
            return True
                
    except Exception as e:
        logger.exception(f"SSO登出时发生错误: {str(e)}")
        # 任何错误都返回True，确保本地清理能够执行
        return True


async def _assign_default_permissions_to_user(user_id: int) -> None:
    """
    为新用户分配默认权限：普通用户角色和DTTrip应用权限

    Args:
        user_id: 用户ID
    """
    try:
        connection = Tortoise.get_connection("default")

        # 分配普通用户角色 (role_id = 10)
        role_sql = """
        INSERT INTO user_roles (user_id, role_id, status, created_at, updated_at, assigned_by)
        VALUES (%s, %s, 1, NOW(), NOW(), 'sso_system')
        """
        await connection.execute_query(role_sql, [user_id, 10])
        logger.info(f"为用户 {user_id} 分配普通用户角色成功")

        # 分配DTTrip应用权限 (app_id = 5)
        app_sql = """
        INSERT INTO user_app_permissions (user_id, app_id, status, created_at, updated_at, granted_by)
        VALUES (%s, %s, 1, NOW(), NOW(), 'sso_system')
        """
        await connection.execute_query(app_sql, [user_id, 5])
        logger.info(f"为用户 {user_id} 分配DTTrip应用权限成功")

    except Exception as e:
        logger.error(f"为用户 {user_id} 分配默认权限失败: {str(e)}")
        # 不抛出异常，避免影响用户创建流程
        pass
