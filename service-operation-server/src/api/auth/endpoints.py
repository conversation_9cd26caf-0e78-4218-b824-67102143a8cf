from fastapi import APIRouter, status, Request, Header
from typing import Optional
from loguru import logger

from src.api.auth.schemas import Token, TokenRequest, TokenResponse, TokenPair, TokenPairResponse, RefreshTokenRequest, LogoutRequest
from src.api.auth.constants import TOKEN_TYPE, CREDENTIALS_EXCEPTION_HEADERS
from src.api.auth.service import authenticate_api_key, authenticate_api_key_with_refresh, refresh_access_token, revoke_refresh_token
from src.api.auth.exceptions import InvalidApiKeyError, InvalidApiSecretError, DeveloperNotFoundError
from src.core.exceptions import APIError
from src.api.auth.sso_endpoints import router as sso_router

router = APIRouter()

# 包含 SSO 路由器
router.include_router(sso_router, tags=["SSO认证"])


@router.post(
    "/token", 
    response_model=TokenResponse,
    status_code=status.HTTP_200_OK,
    summary="获取访问令牌",
    description="使用API密钥和秘钥进行认证，返回JWT访问令牌",
    responses={
        status.HTTP_401_UNAUTHORIZED: {
            "description": "认证失败，API密钥或秘钥无效",
            "headers": CREDENTIALS_EXCEPTION_HEADERS,
        },
        status.HTTP_500_INTERNAL_SERVER_ERROR: {
            "description": "服务器内部错误，可能是开发者关联信息丢失"
        }
    }
)
async def login_for_access_token(form_data: TokenRequest) -> TokenResponse:
    """
    使用API密钥和秘钥进行认证，返回JWT访问令牌。
    
    参数:
        form_data: 包含API密钥和秘钥的请求数据
        
    返回:
        TokenResponse: 包含JWT访问令牌的响应
        
    异常:
        InvalidApiKeyError: 如果API密钥无效或不存在
        InvalidApiSecretError: 如果API密钥秘钥无效
        DeveloperNotFoundError: 如果找不到关联的开发者信息
        APIError: 如果发生其他未预期的错误
    """
    try:
        # 调用服务层进行认证，获取访问令牌
        access_token = await authenticate_api_key(
            api_key=form_data.api_key,
            api_secret=form_data.api_secret
        )
        
        # 返回响应
        return TokenResponse(
            data=Token(
                access_token=access_token,
                token_type=TOKEN_TYPE
            )
        )
        
    except (InvalidApiKeyError, InvalidApiSecretError, DeveloperNotFoundError):
        # 已定义的特定异常直接重新抛出
        raise
    except Exception as e:
        # 其他未预期的异常记录并包装为APIError
        logger.exception(f"处理认证请求时发生错误: {str(e)}")
        raise APIError(
            message="处理认证请求时发生错误",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            details=str(e) if str(e) else None
        )


@router.post(
    "/token/refresh", 
    response_model=TokenPairResponse,
    status_code=status.HTTP_200_OK,
    summary="获取访问令牌对（包含刷新令牌）",
    description="使用API密钥和秘钥进行认证，返回JWT访问令牌和刷新令牌对",
    responses={
        status.HTTP_401_UNAUTHORIZED: {
            "description": "认证失败，API密钥或秘钥无效",
            "headers": CREDENTIALS_EXCEPTION_HEADERS,
        },
        status.HTTP_500_INTERNAL_SERVER_ERROR: {
            "description": "服务器内部错误"
        }
    }
)
async def login_for_token_pair(form_data: TokenRequest, request: Request, user_agent: Optional[str] = Header(None)) -> TokenPairResponse:
    """
    使用API密钥和秘钥进行认证，返回JWT访问令牌和刷新令牌对。
    
    参数:
        form_data: 包含API密钥和秘钥的请求数据
        request: FastAPI请求对象
        user_agent: 用户代理头（可选）
        
    返回:
        TokenPairResponse: 包含JWT访问令牌和刷新令牌的响应
    """
    try:
        # 收集设备信息
        device_info = f"IP:{request.client.host}"
        if user_agent:
            device_info += f",UA:{user_agent[:200]}"  # 限制长度
        
        # 调用服务层进行认证，获取令牌对
        access_token, refresh_token = await authenticate_api_key_with_refresh(
            api_key=form_data.api_key,
            api_secret=form_data.api_secret,
            device_info=device_info
        )
        
        # 返回响应
        return TokenPairResponse(
            data=TokenPair(
                access_token=access_token,
                refresh_token=refresh_token,
                token_type=TOKEN_TYPE,
                expires_in=settings.access_token_expire_minutes * 60
            )
        )
        
    except (InvalidApiKeyError, InvalidApiSecretError, DeveloperNotFoundError):
        raise
    except Exception as e:
        logger.exception(f"处理认证请求时发生错误: {str(e)}")
        raise APIError(
            message="处理认证请求时发生错误",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            details=str(e) if str(e) else None
        )


@router.post(
    "/refresh", 
    response_model=TokenPairResponse,
    status_code=status.HTTP_200_OK,
    summary="刷新访问令牌",
    description="使用刷新令牌获取新的访问令牌和刷新令牌对",
    responses={
        status.HTTP_401_UNAUTHORIZED: {
            "description": "刷新令牌无效或过期",
            "headers": CREDENTIALS_EXCEPTION_HEADERS,
        },
        status.HTTP_500_INTERNAL_SERVER_ERROR: {
            "description": "服务器内部错误"
        }
    }
)
async def refresh_token_endpoint(form_data: RefreshTokenRequest) -> TokenPairResponse:
    """
    使用刷新令牌获取新的访问令牌和刷新令牌对。
    
    参数:
        form_data: 包含刷新令牌的请求数据
        
    返回:
        TokenPairResponse: 包含新的JWT访问令牌和刷新令牌的响应
    """
    try:
        # 调用服务层刷新令牌
        new_access_token, new_refresh_token = await refresh_access_token(
            refresh_token=form_data.refresh_token
        )
        
        # 返回响应
        return TokenPairResponse(
            data=TokenPair(
                access_token=new_access_token,
                refresh_token=new_refresh_token,
                token_type=TOKEN_TYPE,
                expires_in=settings.access_token_expire_minutes * 60
            ),
            message="令牌刷新成功"
        )
        
    except InvalidApiKeyError:
        raise
    except Exception as e:
        logger.exception(f"刷新令牌时发生错误: {str(e)}")
        raise APIError(
            message="刷新令牌时发生错误",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            details=str(e) if str(e) else None
        )


@router.post(
    "/logout", 
    status_code=status.HTTP_200_OK,
    summary="用户登出",
    description="撤销刷新令牌，用户登出",
    responses={
        status.HTTP_200_OK: {
            "description": "登出成功"
        },
        status.HTTP_500_INTERNAL_SERVER_ERROR: {
            "description": "服务器内部错误"
        }
    }
)
async def logout_endpoint(form_data: LogoutRequest):
    """
    用户登出，撤销刷新令牌。
    
    参数:
        form_data: 包含刷新令牌的请求数据（可选）
        
    返回:
        成功消息
    """
    try:
        if form_data.refresh_token:
            success = await revoke_refresh_token(form_data.refresh_token)
            if success:
                return {"message": "登出成功"}
            else:
                return {"message": "登出部分成功，可能令牌已经失效"}
        else:
            return {"message": "登出成功"}
        
    except Exception as e:
        logger.exception(f"登出时发生错误: {str(e)}")
        raise APIError(
            message="登出时发生错误",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            details=str(e) if str(e) else None
        )