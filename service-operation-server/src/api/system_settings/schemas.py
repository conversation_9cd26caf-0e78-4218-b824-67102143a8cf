"""系统设置API数据模型"""
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
from datetime import datetime


class SystemSettingResponse(BaseModel):
    """系统设置响应模型"""
    id: int
    config_key: str
    config_name: str
    config_value: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class SystemSettingCreate(BaseModel):
    """创建系统设置请求模型"""
    config_key: str = Field(..., description="配置项key")
    config_value: str = Field(..., description="配置项值")


class SystemSettingUpdate(BaseModel):
    """更新系统设置请求模型"""
    config_value: str = Field(..., description="配置项值")


class TongchengCredentialsResponse(BaseModel):
    """同程管家凭证响应模型"""
    username: Optional[str] = None
    password: Optional[str] = None


class TongchengCredentialsUpdate(BaseModel):
    """同程管家凭证更新模型"""
    username: Optional[str] = Field(None, description="同程管家用户名")
    password: Optional[str] = Field(None, description="同程管家密码") 