from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime


class ErrorLogResponse(BaseModel):
    """错误日志响应"""
    id: int = Field(..., description="错误日志ID")
    task_id: str = Field(..., description="任务ID")
    order_id: int = Field(..., description="订单ID")
    error_type: str = Field(..., description="错误类型")
    error_message: str = Field(..., description="错误信息")
    error_details: Optional[str] = Field(None, description="错误详情")
    stack_trace: Optional[str] = Field(None, description="堆栈跟踪")
    retry_count: int = Field(..., description="重试次数")
    is_resolved: bool = Field(..., description="是否已解决")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    # 关联信息
    project_id: Optional[int] = Field(None, description="项目ID")
    project_name: Optional[str] = Field(None, description="项目名称")
    creator_name: Optional[str] = Field(None, description="创建人")
    
    class Config:
        from_attributes = True


class ErrorLogListResponse(BaseModel):
    """错误日志列表响应"""
    total: int = Field(..., description="总数量")
    items: List[ErrorLogResponse] = Field(..., description="错误日志列表")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页大小")


class ProjectOption(BaseModel):
    """项目选项"""
    project_id: int = Field(..., description="项目ID")
    project_name: str = Field(..., description="项目名称")


class CreatorOption(BaseModel):
    """创建人选项"""
    creator_name: str = Field(..., description="创建人姓名")


class ErrorLogOptionsResponse(BaseModel):
    """错误日志筛选选项响应"""
    projects: List[ProjectOption] = Field(..., description="项目列表")
    creators: List[CreatorOption] = Field(..., description="创建人列表")
