from fastapi import APIRouter, Depends, Query, HTTPException
from typing import Optional, List
from sqlalchemy import text
from tortoise import Tortoise

from src.core.auth import get_current_user
from src.db.models.user import User
from .schemas import (
    ErrorLogListResponse, 
    ErrorLogResponse, 
    ErrorLogOptionsResponse,
    ProjectOption,
    CreatorOption
)

router = APIRouter(prefix="/error-logs", tags=["错误日志"])


@router.get("/options", response_model=ErrorLogOptionsResponse)
async def get_error_log_options(
    current_user: User = Depends(get_current_user)
):
    """
    获取错误日志筛选选项
    """
    try:
        conn = Tortoise.get_connection("default")
        
        # 获取项目列表
        projects_sql = """
        SELECT DISTINCT p.id as project_id, p.project_name
        FROM projects p
        INNER JOIN project_tasks pt ON p.id = pt.project_id
        INNER JOIN rpa_error_logs rel ON pt.id = rel.task_id
        ORDER BY p.project_name
        """
        
        projects_result = await conn.execute_query(projects_sql)
        projects = [
            ProjectOption(
                project_id=row['project_id'],
                project_name=row['project_name']
            )
            for row in projects_result[1]
        ]
        
        # 获取创建人列表
        creators_sql = """
        SELECT DISTINCT p.creator_name
        FROM projects p
        INNER JOIN project_tasks pt ON p.id = pt.project_id
        INNER JOIN rpa_error_logs rel ON pt.id = rel.task_id
        WHERE p.creator_name IS NOT NULL AND p.creator_name != ''
        ORDER BY p.creator_name
        """
        
        creators_result = await conn.execute_query(creators_sql)
        creators = [
            CreatorOption(creator_name=row['creator_name'])
            for row in creators_result[1]
        ]
        
        return ErrorLogOptionsResponse(
            projects=projects,
            creators=creators
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取筛选选项失败: {str(e)}")


@router.get("/", response_model=ErrorLogListResponse)
async def get_error_logs(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页大小"),
    creator_name: Optional[str] = Query(None, description="创建人姓名"),
    project_name: Optional[str] = Query(None, description="项目名称"),
    order_id: Optional[int] = Query(None, description="订单ID"),
    current_user: User = Depends(get_current_user)
):
    """
    获取错误日志列表
    """
    try:
        conn = Tortoise.get_connection("default")
        
        # 构建WHERE条件
        where_conditions = []
        params = []
        
        if creator_name:
            where_conditions.append("p.creator_name LIKE %s")
            params.append(f"%{creator_name}%")
            
        if project_name:
            where_conditions.append("p.project_name LIKE %s")
            params.append(f"%{project_name}%")
            
        if order_id:
            where_conditions.append("rel.order_id = %s")
            params.append(order_id)
        
        where_clause = ""
        if where_conditions:
            where_clause = "WHERE " + " AND ".join(where_conditions)
        
        # 查询总数
        count_sql = f"""
        SELECT COUNT(*) as total
        FROM rpa_error_logs rel
        INNER JOIN project_tasks pt ON rel.task_id = pt.id
        INNER JOIN projects p ON pt.project_id = p.id
        {where_clause}
        """
        
        count_result = await conn.execute_query(count_sql, params)
        total = count_result[1][0]['total']
        
        # 查询数据
        offset = (page - 1) * page_size
        data_sql = f"""
        SELECT 
            rel.id,
            rel.task_id,
            rel.order_id,
            rel.error_type,
            rel.error_message,
            rel.error_details,
            rel.stack_trace,
            rel.retry_count,
            rel.is_resolved,
            rel.created_at,
            rel.updated_at,
            p.id as project_id,
            p.project_name,
            p.creator_name
        FROM rpa_error_logs rel
        INNER JOIN project_tasks pt ON rel.task_id = pt.id
        INNER JOIN projects p ON pt.project_id = p.id
        {where_clause}
        ORDER BY rel.created_at DESC
        LIMIT %s OFFSET %s
        """
        
        data_params = params + [page_size, offset]
        data_result = await conn.execute_query(data_sql, data_params)
        
        items = [
            ErrorLogResponse(
                id=row['id'],
                task_id=row['task_id'],
                order_id=row['order_id'],
                error_type=row['error_type'],
                error_message=row['error_message'],
                error_details=row['error_details'],
                stack_trace=row['stack_trace'],
                retry_count=row['retry_count'],
                is_resolved=bool(row['is_resolved']),
                created_at=row['created_at'],
                updated_at=row['updated_at'],
                project_id=row['project_id'],
                project_name=row['project_name'],
                creator_name=row['creator_name']
            )
            for row in data_result[1]
        ]
        
        return ErrorLogListResponse(
            total=total,
            items=items,
            page=page,
            page_size=page_size
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取错误日志失败: {str(e)}")
