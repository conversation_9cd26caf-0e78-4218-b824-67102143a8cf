from fastapi import APIRouter, Depends, status
from loguru import logger
from typing import Dict, Any

from src.core.config import settings
from src.api.dependencies import get_current_developer_id
from src.api.health.schemas import HealthResponse, HealthCheck
from src.api.health.constants import STATUS_HEALTHY, STATUS_UNHEALTHY
from src.api.health.service import perform_health_check, get_system_metrics
from src.api.health.exceptions import HealthCheckError, DatabaseConnectionError
from src.core.exceptions import APIError, AuthenticationError

router = APIRouter()


@router.get(
    "/simple",
    status_code=status.HTTP_200_OK,
    summary="简单健康检查",
    description="返回简单的服务器运行状态",
    responses={
        status.HTTP_200_OK: {"description": "服务器正常运行"}
    }
)
async def simple_health_check() -> Dict[str, str]:
    """
    简单健康检查端点
    
    返回简单的服务器运行状态信息。
    
    返回:
        Dict[str, str]: 包含简单状态信息的字典
    """
    return {"message": "the server is running"}


@router.get(
    "", 
    response_model=HealthResponse,
    status_code=status.HTTP_200_OK,
    summary="系统健康检查",
    description="返回应用程序的当前状态和基本信息，用于监控系统是否正常运行",
    responses={
        status.HTTP_200_OK: {"description": "系统正常运行"},
        status.HTTP_500_INTERNAL_SERVER_ERROR: {"description": "系统异常"},
        status.HTTP_503_SERVICE_UNAVAILABLE: {"description": "系统服务不可用"}
    }
)
async def health_check() -> HealthResponse:
    """
    系统健康检查端点
    
    返回应用程序的当前状态和基本信息。
    这个端点可以用于监控系统是否正常运行。
    
    返回:
        HealthResponse: 包含系统状态和基本信息的响应
        
    异常:
        HealthCheckError: 如果健康检查失败
        DatabaseConnectionError: 如果数据库连接失败
    """
    try:
        # 调用服务层执行健康检查，不包含详细系统指标
        logger.debug("健康检查请求")
        health_data = await perform_health_check(include_metrics=False)
        
        # 构建响应
        return HealthResponse(
            status=health_data["status"],
            version=settings.version,
            environment=str(settings.environment),
            details=HealthCheck(
                **health_data
            )
        )
    except (HealthCheckError, DatabaseConnectionError) as e:
        # 特定健康检查异常直接重新抛出
        raise
    except Exception as e:
        # 其他未预期的异常
        logger.exception(f"健康检查时发生未预期的错误: {str(e)}")
        return HealthResponse(
            status=STATUS_UNHEALTHY,
            version=settings.version,
            environment=str(settings.environment),
            details={
                "error": str(e),
                "timestamp": str(settings.app_name)
            }
        )


@router.get(
    "/protected", 
    response_model=HealthResponse,
    status_code=status.HTTP_200_OK,
    summary="需认证的系统健康检查",
    description="返回应用程序的当前状态和更详细的信息，需要有效的认证才能访问",
    responses={
        status.HTTP_200_OK: {"description": "系统正常运行"},
        status.HTTP_401_UNAUTHORIZED: {"description": "认证失败"},
        status.HTTP_500_INTERNAL_SERVER_ERROR: {"description": "系统异常"},
        status.HTTP_503_SERVICE_UNAVAILABLE: {"description": "系统服务不可用"}
    }
)
async def protected_health_check(developer_id: str = Depends(get_current_developer_id)) -> HealthResponse:
    """
    需要认证的健康检查端点
    
    返回应用程序的当前状态和更详细的信息。
    此端点需要有效的认证才能访问。
    
    参数:
        developer_id: 通过依赖注入获取的开发者ID
        
    返回:
        HealthResponse: 包含系统状态和详细信息的响应
        
    异常:
        AuthenticationError: 如果认证失败
        HealthCheckError: 如果健康检查失败
    """
    try:
        logger.debug(f"开发者 {developer_id} 请求了需认证的健康检查")
        
        # 调用服务层获取健康检查结果，包含详细的系统指标
        health_data = await perform_health_check(include_metrics=True)
        
        # 获取更详细的系统指标
        system_metrics = await get_system_metrics()
        
        # 添加开发者信息
        health_data["developer_id"] = developer_id
        health_data["auth_type"] = "JWT"
        
        # 构建响应
        return HealthResponse(
            status=health_data["status"],
            version=settings.version,
            environment=str(settings.environment),
            details={
                **health_data,
                "metrics": system_metrics,
                "sensitive_info": {
                    "db_host": settings.db_host,
                    "db_user": settings.db_user,
                    "worker_count": settings.workers_count,
                    "host": settings.host,
                    "port": settings.port,
                }
            }
        )
    except AuthenticationError:
        # 认证错误由依赖处理，直接重新抛出
        raise
    except HealthCheckError as e:
        # 健康检查异常直接重新抛出
        raise
    except Exception as e:
        # 其他未预期的异常
        logger.exception(f"认证健康检查失败: {str(e)}")
        return HealthResponse(
            status=STATUS_UNHEALTHY,
            version=settings.version,
            environment=str(settings.environment),
            details={
                "error": str(e),
                "developer_id": developer_id,
                "timestamp": str(settings.app_name)
            }
        )
