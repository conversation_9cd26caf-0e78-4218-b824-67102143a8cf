"""
健康检查模块特定异常定义。

定义与健康检查相关的特定异常类型，所有异常继承自全局基础异常类。
"""

from fastapi import status
from src.core.exceptions import APIError


class HealthCheckError(APIError):
    """健康检查失败异常。"""
    
    def __init__(self, message: str = "系统健康检查失败", details: dict = None):
        super().__init__(
            message=message,
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            details=details
        )


class DatabaseConnectionError(HealthCheckError):
    """数据库连接异常。"""
    
    def __init__(self, message: str = "数据库连接失败", details: str = None):
        super().__init__(
            message=message,
            details={"component": "database", "error": details}
        )


class ExternalServiceError(HealthCheckError):
    """外部服务异常。"""
    
    def __init__(self, service_name: str, message: str = None, details: str = None):
        super().__init__(
            message=message or f"外部服务 {service_name} 不可用",
            details={"component": service_name, "error": details}
        )
