"""
健康检查模块的业务逻辑服务。

此模块集中处理与系统健康状态相关的业务逻辑，包括数据库连接检查、系统指标收集等功能。
"""

import time
import psutil
import platform
from datetime import datetime
from loguru import logger
from tortoise import Tortoise
from tortoise.exceptions import OperationalError

from src.api.health.exceptions import DatabaseConnectionError, HealthCheckError
from src.api.health.constants import STATUS_HEALTHY, STATUS_UNHEALTHY
from src.core.config import settings


async def check_database_connection() -> bool:
    """
    检查数据库连接状态。
    
    Returns:
        bool: 数据库连接是否正常
        
    Raises:
        DatabaseConnectionError: 如果数据库连接失败
    """
    try:
        # 获取连接并执行简单查询
        conn = Tortoise.get_connection("default")
        await conn.execute_query("SELECT 1")
        return True
    except OperationalError as e:
        logger.error(f"数据库连接检查失败: {str(e)}")
        raise DatabaseConnectionError(details=str(e))
    except Exception as e:
        logger.exception(f"执行数据库健康检查时发生异常: {str(e)}")
        raise DatabaseConnectionError(details=str(e))


async def get_system_metrics() -> dict:
    """
    获取系统性能指标。
    
    Returns:
        dict: 包含CPU、内存、磁盘使用率等系统指标
    """
    try:
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=0.1)
        
        # 内存使用情况
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        
        # 磁盘使用情况
        disk = psutil.disk_usage('/')
        disk_percent = disk.percent
        
        # 系统信息
        system_info = {
            "system": platform.system(),
            "release": platform.release(),
            "version": platform.version(),
            "python_version": platform.python_version(),
        }
        
        # 运行时间
        # 注意：在某些环境中可能无法获取系统启动时间
        try:
            boot_time = datetime.fromtimestamp(psutil.boot_time()).isoformat()
        except:
            boot_time = "未知"
            
        # 返回所有指标
        return {
            "cpu_percent": cpu_percent,
            "memory_percent": memory_percent,
            "disk_percent": disk_percent,
            "system_info": system_info,
            "boot_time": boot_time,
            "app_name": settings.app_name,
            "environment": settings.environment,
            "check_time": datetime.now().isoformat()
        }
    except Exception as e:
        logger.exception(f"获取系统指标时发生异常: {str(e)}")
        return {
            "error": str(e),
            "app_name": settings.app_name,
            "environment": settings.environment,
            "check_time": datetime.now().isoformat()
        }


async def perform_health_check(include_metrics: bool = False) -> dict:
    """
    执行完整的健康检查，包括数据库连接检查和系统指标收集。
    
    Args:
        include_metrics: 是否包含详细系统指标
        
    Returns:
        dict: 健康检查结果
        
    Raises:
        HealthCheckError: 如果健康检查失败
    """
    start_time = time.time()
    health_data = {
        "status": STATUS_HEALTHY,
        "version": settings.version,
        "environment": settings.environment,
        "timestamp": datetime.now().isoformat(),
        "checks": {},
    }
    
    try:
        # 检查数据库连接
        db_ok = await check_database_connection()
        health_data["checks"]["database"] = "ok" if db_ok else "error"
        
        # 如果需要，添加系统指标
        if include_metrics:
            health_data["metrics"] = await get_system_metrics()
            
        # 计算检查耗时
        health_data["response_time_ms"] = round((time.time() - start_time) * 1000, 2)
        
        return health_data
    except Exception as e:
        logger.exception(f"执行健康检查时发生异常: {str(e)}")
        
        # 更新状态为错误
        health_data["status"] = STATUS_UNHEALTHY
        health_data["error"] = str(e)
        health_data["response_time_ms"] = round((time.time() - start_time) * 1000, 2)
        
        # 尽可能返回已收集的数据
        return health_data
