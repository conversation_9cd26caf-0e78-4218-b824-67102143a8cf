"""
健康检查模块 (Health Check Module)

此模块提供系统健康状态监控功能，包括：
- 基本健康状态检查
- 需要认证的健康状态详情
- 系统状态信息收集

典型用法:
```python
from src.api.health import router               # 导入路由器
from src.api.health.schemas import HealthResponse # 导入响应模型
```
"""

from .endpoints import router
from .schemas import HealthResponse, HealthCheck
from .constants import STATUS_HEALTHY, STATUS_UNHEALTHY

__all__ = [
    "router",                # 路由器
    "HealthResponse",        # 健康检查响应模型
    "HealthCheck",           # 健康检查数据模型
    "STATUS_HEALTHY",      # 系统正常状态常量
    "STATUS_UNHEALTHY",   # 系统错误状态常量
]
