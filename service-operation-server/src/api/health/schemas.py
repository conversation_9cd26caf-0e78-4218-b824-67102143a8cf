"""
健康检查模块的数据模型定义。

定义与健康检查相关的请求和响应模型。
"""
from typing import Dict, Any, Optional, List
from datetime import datetime
from pydantic import Field, validator

from src.core.base_models import CustomBaseModel, DataResponse


class SystemMetrics(CustomBaseModel):
    """系统指标模型。"""
    cpu_percent: float = Field(..., description="CPU使用率百分比")
    memory_percent: float = Field(..., description="内存使用率百分比")
    disk_percent: float = Field(..., description="磁盘使用率百分比")
    system_info: Dict[str, str] = Field(..., description="系统信息，包括操作系统类型、版本等")
    boot_time: str = Field(..., description="系统启动时间")
    check_time: str = Field(..., description="检查时间")


class CheckResult(CustomBaseModel):
    """单项检查结果模型。"""
    status: str = Field(..., description="检查状态，如 'ok', 'error'")
    message: Optional[str] = Field(None, description="详细信息")
    response_time_ms: Optional[float] = Field(None, description="响应时间（毫秒）")


class HealthCheck(CustomBaseModel):
    """健康检查数据模型。"""
    status: str = Field(..., description="系统状态，如 'ok', 'error'")
    version: str = Field(..., description="应用程序版本")
    environment: str = Field(..., description="运行环境")
    timestamp: str = Field(..., description="检查时间戳")
    checks: Dict[str, str] = Field(..., description="各项检查结果")
    response_time_ms: Optional[float] = Field(None, description="响应时间（毫秒）")
    metrics: Optional[Dict[str, Any]] = Field(None, description="详细系统指标")
    error: Optional[str] = Field(None, description="错误信息，如果检查失败")
    
    class Config:
        json_schema_extra = {
            "example": {
                "status": "ok",
                "version": "1.0.0",
                "environment": "development",
                "timestamp": "2025-05-22T14:00:00.000Z",
                "checks": {
                    "database": "ok",
                    "redis": "ok"
                },
                "response_time_ms": 123.45
            }
        }


class HealthResponse(DataResponse[Dict[str, Any]]):
    """健康检查响应模型。"""
    status: str = Field(..., description="系统状态，如 'ok' 或 'error'")
    version: str = Field(..., description="应用程序版本")
    environment: str = Field(..., description="运行环境")
    message: str = Field("系统状态检查", description="响应消息")
    
    class Config:
        json_schema_extra = {
            "example": {
                "status": "ok",
                "version": "1.0.0",
                "environment": "development",
                "message": "系统状态检查",
                "data": {
                    "status": "ok",
                    "version": "1.0.0",
                    "environment": "development",
                    "timestamp": "2025-05-22T14:00:00.000Z",
                    "checks": {
                        "database": "ok"
                    },
                    "response_time_ms": 123.45
                }
            }
        }
