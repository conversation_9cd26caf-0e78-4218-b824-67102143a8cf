from fastapi import Depends, HTTPException, status
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import Optional

from src.core.security import verify_token
from src.api.auth.schemas import TokenPayload
from src.db.models import User

# 使用 HTTPBearer 来处理 Bearer Token 认证
# 这样在 Swagger UI 中会显示为 JWT 认证而不是 OAuth2
security = HTTPBearer(description="JWT Token 认证", auto_error=True)

async def get_current_developer_id(credentials: HTTPAuthorizationCredentials = Depends(security)) -> str:
    """
    验证 JWT 令牌并返回开发者 ID（主题）的依赖项。
    
    如果令牌无效或过期，则引发 HTTPException 401。
    """
    token = credentials.credentials  # 从 HTTPAuthorizationCredentials 中获取令牌
    developer_id = verify_token(token)
    if developer_id is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无法验证凭据",
            headers={"WWW-Authenticate": "Bearer"},
        )
    # 如果需要，您可以在这里使用 developer_id 和 DB 会话加载完整的 Developer 对象
    # 但现在，只需返回 ID
    return developer_id

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> User:
    """
    验证 JWT 令牌并返回当前用户对象的依赖项。
    
    如果令牌无效或过期，则引发 HTTPException 401。
    """
    token = credentials.credentials  # 从 HTTPAuthorizationCredentials 中获取令牌
    user_id = verify_token(token)
    if user_id is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无法验证凭据",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # 根据user_id获取用户对象（现在user_id实际是数据库id）
    user = await User.filter(id=user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="找不到用户",
        )
    
    return user
