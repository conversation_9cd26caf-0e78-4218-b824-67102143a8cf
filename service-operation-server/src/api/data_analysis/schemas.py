"""数据分析API数据模式定义"""

from typing import Optional, List, Dict, Any
from datetime import datetime, date
from pydantic import BaseModel, Field


class DateRangeRequest(BaseModel):
    """日期范围请求"""
    start_date: date = Field(..., description="开始日期")
    end_date: date = Field(..., description="结束日期")


class UserAnalysisRequest(BaseModel):
    """用户分析请求"""
    user_id: Optional[int] = Field(None, description="用户ID，为空则分析所有用户")
    user_name: Optional[str] = Field(None, description="用户姓名，为空则分析所有用户")
    start_date: date = Field(..., description="开始日期")
    end_date: date = Field(..., description="结束日期")


class PassportAnalysisResponse(BaseModel):
    """护照识别分析响应"""
    total_recognitions: int = Field(..., description="总识别次数")
    unique_users: int = Field(..., description="使用用户数")
    success_rate: float = Field(..., description="识别成功率")
    daily_stats: List[Dict[str, Any]] = Field(..., description="每日统计")
    user_stats: List[Dict[str, Any]] = Field(..., description="用户统计")


class BookingAnalysisResponse(BaseModel):
    """预订分析响应"""
    total_orders: int = Field(..., description="总订单数")
    completed_orders: int = Field(..., description="已完成订单数")
    failed_orders: int = Field(..., description="失败订单数")
    success_rate: float = Field(..., description="成功率")
    total_amount: float = Field(..., description="总金额")
    daily_stats: List[Dict[str, Any]] = Field(..., description="每日统计")
    user_stats: List[Dict[str, Any]] = Field(..., description="用户统计")
    project_stats: List[Dict[str, Any]] = Field(..., description="项目统计")


class TrainBookingAnalysisResponse(BookingAnalysisResponse):
    """火车票预订分析响应"""
    pass


class HotelBookingAnalysisResponse(BookingAnalysisResponse):
    """酒店预订分析响应"""
    pass


class FlightBookingAnalysisResponse(BookingAnalysisResponse):
    """飞机票预订分析响应"""
    pass


class OverallAnalysisResponse(BaseModel):
    """综合分析响应"""
    passport_analysis: PassportAnalysisResponse
    train_analysis: TrainBookingAnalysisResponse
    hotel_analysis: HotelBookingAnalysisResponse
    flight_analysis: FlightBookingAnalysisResponse
    summary: Dict[str, Any] = Field(..., description="汇总信息")
