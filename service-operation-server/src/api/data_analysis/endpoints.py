"""数据分析API端点"""

from fastapi import APIRouter, Depends, HTTPException, Query
from typing import Optional
from datetime import datetime, date, timedelta
import logging

from src.api.dependencies import get_current_user
from src.db.models.user import User
from src.db.models.passport import Passport
from src.db.models.train_order import TrainOrder
from src.db.models.hotel_order import HotelOrder
from src.db.models.flight_order import FlightOrder
from src.db.models.project import Project
from tortoise.functions import Count, Sum
from tortoise.expressions import Q

from .schemas import (
    DateRangeRequest,
    UserAnalysisRequest,
    PassportAnalysisResponse,
    TrainBookingAnalysisResponse,
    HotelBookingAnalysisResponse,
    FlightBookingAnalysisResponse,
    OverallAnalysisResponse
)

router = APIRouter(prefix="/data-analysis", tags=["数据分析"])
logger = logging.getLogger(__name__)


@router.get("/passport", response_model=PassportAnalysisResponse, summary="护照识别使用情况分析")
async def analyze_passport_usage(
    start_date: date = Query(..., description="开始日期"),
    end_date: date = Query(..., description="结束日期"),
    user_id: Optional[int] = Query(None, description="用户ID，为空则分析所有用户"),
    user_name: Optional[str] = Query(None, description="用户姓名，为空则分析所有用户"),
    current_user: User = Depends(get_current_user)
):
    """分析护照识别使用情况"""
    try:
        # 构建查询条件
        query = Passport.filter(
            created_at__gte=datetime.combine(start_date, datetime.min.time()),
            created_at__lte=datetime.combine(end_date, datetime.max.time())
        )

        # 用户筛选条件
        if user_id:
            query = query.filter(user_id=user_id)
        elif user_name:
            # 根据用户姓名查找用户ID
            user = await User.filter(username=user_name.strip()).first()
            if user:
                query = query.filter(user_id=user.id)
            else:
                # 如果找不到用户，返回空结果
                return PassportAnalysisResponse(
                    total_recognitions=0,
                    unique_users=0,
                    success_rate=0.0,
                    daily_stats=[],
                    user_stats=[]
                )

        # 总识别次数
        total_recognitions = await query.count()

        # 使用用户数 - 通过获取所有记录然后统计唯一用户
        all_passports = await query.all()
        unique_user_ids = set()
        for passport in all_passports:
            if passport.user_id:
                unique_user_ids.add(passport.user_id)
        unique_users = len(unique_user_ids)

        # 成功率计算（基于processing_status字段）
        success_count = await query.filter(processing_status='completed').count()
        success_rate = (success_count / total_recognitions * 100) if total_recognitions > 0 else 0
        
        # 近日使用统计（近一周）- 不受搜索条件影响
        daily_stats = []
        # 计算近一周的日期范围
        today = datetime.now().date()
        week_start = today - timedelta(days=6)  # 包含今天在内的7天

        # 创建不受搜索条件影响的基础查询（只有时间范围）
        base_query = Passport.filter(
            created_at__gte=datetime.combine(week_start, datetime.min.time()),
            created_at__lte=datetime.combine(today, datetime.max.time())
        )

        # 先收集所有日期的数据
        temp_stats = []
        current_date = week_start
        while current_date <= today:
            day_start = datetime.combine(current_date, datetime.min.time())
            day_end = datetime.combine(current_date, datetime.max.time())

            # 使用基础查询，不受用户筛选影响
            day_query = base_query.filter(
                created_at__gte=day_start,
                created_at__lte=day_end
            )
            day_count = await day_query.count()

            # 格式化日期显示（显示星期几）
            weekday_names = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
            weekday = weekday_names[current_date.weekday()]
            date_str = f"{current_date.strftime('%m-%d')} {weekday}"

            temp_stats.append({
                'date': date_str,
                'count': day_count,
                'sort_date': current_date  # 用于排序的日期
            })
            current_date += timedelta(days=1)

        # 按日期倒序排列（最新的在前面）
        temp_stats.sort(key=lambda x: x['sort_date'], reverse=True)

        # 移除排序用的字段
        daily_stats = [{'date': item['date'], 'count': item['count']} for item in temp_stats]
        
        # 用户统计
        user_stats = []
        if not user_id and not user_name:  # 只有在不指定用户时才返回用户统计
            # 统计每个用户的使用次数
            user_count_map = {}

            for passport in all_passports:
                if passport.user_id:
                    if passport.user_id not in user_count_map:
                        user_count_map[passport.user_id] = {
                            'user_id': passport.user_id,
                            'username': None,
                            'department': None,
                            'count': 0
                        }
                    user_count_map[passport.user_id]['count'] += 1

            # 获取用户名和部门信息
            for user_id in user_count_map.keys():
                try:
                    user = await User.get(id=user_id)
                    user_count_map[user_id]['username'] = user.username
                    user_count_map[user_id]['department'] = user.department or ''
                except:
                    user_count_map[user_id]['username'] = f'用户{user_id}'
                    user_count_map[user_id]['department'] = ''

            # 按使用次数从高到低排序
            user_stats = sorted(list(user_count_map.values()), key=lambda x: x['count'], reverse=True)
        
        return PassportAnalysisResponse(
            total_recognitions=total_recognitions,
            unique_users=unique_users,
            success_rate=round(success_rate, 2),
            daily_stats=daily_stats,
            user_stats=user_stats
        )
        
    except Exception as e:
        logger.error(f"护照识别分析失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"护照识别分析失败: {str(e)}")


@router.get("/train-booking", response_model=TrainBookingAnalysisResponse, summary="火车票预订情况分析")
async def analyze_train_booking(
    start_date: date = Query(..., description="开始日期"),
    end_date: date = Query(..., description="结束日期"),
    user_id: Optional[int] = Query(None, description="用户ID，为空则分析所有用户"),
    user_name: Optional[str] = Query(None, description="用户姓名，为空则分析所有用户"),
    current_user: User = Depends(get_current_user)
):
    """分析火车票预订情况"""
    try:
        # 构建查询条件
        query = TrainOrder.filter(
            created_at__gte=datetime.combine(start_date, datetime.min.time()),
            created_at__lte=datetime.combine(end_date, datetime.max.time()),
            is_deleted=False
        )

        # 添加用户筛选条件
        if user_name:
            # 通过项目创建者姓名筛选
            projects = await Project.filter(creator_name__icontains=user_name).all()
            if projects:
                project_ids = [p.id for p in projects]
                query = query.filter(project_id__in=project_ids)
            else:
                # 如果没有找到匹配的项目，返回空结果
                query = query.filter(id__in=[])
        
        # 总订单数
        total_orders = await query.count()
        
        # 已完成订单数
        completed_orders = await query.filter(order_status='completed').count()
        
        # 失败订单数
        failed_orders = await query.filter(order_status='failed').count()
        
        # 成功率
        success_rate = (completed_orders / total_orders * 100) if total_orders > 0 else 0
        
        # 总金额 - 使用正确的Tortoise ORM语法
        completed_query = query.filter(order_status='completed')
        amount_result = await completed_query.annotate(total=Sum('amount')).values('total')
        total_amount = float(amount_result[0]['total'] if amount_result and amount_result[0]['total'] else 0)
        
        # 近一周统计（不受搜索条件影响）
        daily_stats = []
        today = datetime.now().date()
        week_start = today - timedelta(days=6)  # 包含今天的7天

        # 基础查询，不受用户筛选影响
        base_query = TrainOrder.filter(is_deleted=False)

        current_date = week_start
        while current_date <= today:
            day_start = datetime.combine(current_date, datetime.min.time())
            day_end = datetime.combine(current_date, datetime.max.time())

            day_total = await base_query.filter(
                created_at__gte=day_start,
                created_at__lte=day_end
            ).count()

            day_completed = await base_query.filter(
                created_at__gte=day_start,
                created_at__lte=day_end,
                order_status='completed'
            ).count()

            # 格式化日期显示（显示星期几）
            weekday_names = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
            weekday = weekday_names[current_date.weekday()]
            date_str = f"{current_date.strftime('%m-%d')} {weekday}"

            daily_stats.append({
                'date': date_str,
                'count': day_total,  # 使用count字段以兼容ECharts组件
                'total_orders': day_total,
                'completed_orders': day_completed,
                'success_rate': (day_completed / day_total * 100) if day_total > 0 else 0
            })
            current_date += timedelta(days=1)
        
        # 用户统计（基于项目创建者）
        user_stats = []

        # 获取所有相关项目的创建者统计
        projects_with_orders = await query.group_by('project_id').values('project_id')
        project_ids = [p['project_id'] for p in projects_with_orders]

        if project_ids:
            # 获取项目创建者信息
            projects = await Project.filter(id__in=project_ids).all()
            creator_stats = {}

            # 按创建者分组统计
            for project in projects:
                creator_name = project.creator_name
                if creator_name not in creator_stats:
                    creator_stats[creator_name] = {
                        'creator_name': creator_name,
                        'total_orders': 0,
                        'completed_orders': 0
                    }

                # 统计该项目的订单数
                project_total = await query.filter(project_id=project.id).count()
                project_completed = await query.filter(project_id=project.id, order_status='completed').count()

                creator_stats[creator_name]['total_orders'] += project_total
                creator_stats[creator_name]['completed_orders'] += project_completed

            # 计算成功率并转换为列表
            for creator_data in creator_stats.values():
                success_rate = (creator_data['completed_orders'] / creator_data['total_orders'] * 100) if creator_data['total_orders'] > 0 else 0
                creator_data['success_rate'] = success_rate
                user_stats.append(creator_data)

            # 按总订单数排序
            user_stats.sort(key=lambda x: x['total_orders'], reverse=True)

        project_stats = []
        
        # 项目统计
        projects_data = await query.group_by('project_id').annotate(
            total_count=Count('id'),
            completed_count=Count('id', _filter=Q(order_status='completed'))
        ).values('project_id', 'total_count', 'completed_count')
        
        for project_data in projects_data:
            try:
                project = await Project.get(id=project_data['project_id'])
                project_stats.append({
                    'project_id': project_data['project_id'],
                    'project_name': project.project_name,
                    'creator_name': project.creator_name,
                    'total_orders': project_data['total_count'],
                    'completed_orders': project_data['completed_count'],
                    'success_rate': (project_data['completed_count'] / project_data['total_count'] * 100) if project_data['total_count'] > 0 else 0
                })
            except:
                continue
        
        return TrainBookingAnalysisResponse(
            total_orders=total_orders,
            completed_orders=completed_orders,
            failed_orders=failed_orders,
            success_rate=round(success_rate, 2),
            total_amount=total_amount,
            daily_stats=daily_stats,
            user_stats=user_stats,
            project_stats=project_stats
        )
        
    except Exception as e:
        logger.error(f"火车票预订分析失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"火车票预订分析失败: {str(e)}")


@router.get("/hotel-booking", response_model=HotelBookingAnalysisResponse, summary="酒店预订情况分析")
async def analyze_hotel_booking(
    start_date: date = Query(..., description="开始日期"),
    end_date: date = Query(..., description="结束日期"),
    user_id: Optional[int] = Query(None, description="用户ID，为空则分析所有用户"),
    current_user: User = Depends(get_current_user)
):
    """分析酒店预订情况"""
    try:
        # 构建查询条件
        query = HotelOrder.filter(
            created_at__gte=datetime.combine(start_date, datetime.min.time()),
            created_at__lte=datetime.combine(end_date, datetime.max.time()),
            is_deleted=False
        )
        
        # 总订单数
        total_orders = await query.count()
        
        # 已完成订单数
        completed_orders = await query.filter(order_status='completed').count()
        
        # 失败订单数
        failed_orders = await query.filter(order_status='failed').count()
        
        # 成功率
        success_rate = (completed_orders / total_orders * 100) if total_orders > 0 else 0
        
        # 总金额 - 计算已完成订单的金额总和
        completed_query = query.filter(order_status='completed')
        completed_orders_list = await completed_query.all()
        total_amount = sum(float(order.amount or 0) for order in completed_orders_list)
        
        # 近一周统计（不受搜索条件影响）
        daily_stats = []
        today = datetime.now().date()
        week_start = today - timedelta(days=6)  # 包含今天的7天

        # 基础查询，不受用户筛选影响
        base_query = HotelOrder.filter(is_deleted=False)

        current_date = week_start
        while current_date <= today:
            day_start = datetime.combine(current_date, datetime.min.time())
            day_end = datetime.combine(current_date, datetime.max.time())

            day_total = await base_query.filter(
                created_at__gte=day_start,
                created_at__lte=day_end
            ).count()

            day_completed = await base_query.filter(
                created_at__gte=day_start,
                created_at__lte=day_end,
                order_status='completed'
            ).count()

            # 格式化日期显示（显示星期几）
            weekday_names = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
            weekday = weekday_names[current_date.weekday()]
            date_str = f"{current_date.strftime('%m-%d')} {weekday}"

            daily_stats.append({
                'date': date_str,
                'count': day_total,  # 使用count字段以兼容ECharts组件
                'total_orders': day_total,
                'completed_orders': day_completed,
                'success_rate': (day_completed / day_total * 100) if day_total > 0 else 0
            })
            current_date += timedelta(days=1)

        # 用户统计（基于项目创建者）
        user_stats = []

        # 获取所有相关项目的创建者统计
        projects_with_orders = await query.group_by('project_id').values('project_id')
        project_ids = [p['project_id'] for p in projects_with_orders]

        if project_ids:
            # 获取项目创建者信息
            projects = await Project.filter(id__in=project_ids).all()
            creator_stats = {}

            # 按创建者分组统计
            for project in projects:
                creator_name = project.creator_name
                if creator_name not in creator_stats:
                    creator_stats[creator_name] = {
                        'creator_name': creator_name,
                        'total_orders': 0,
                        'completed_orders': 0
                    }

                # 统计该项目的订单数
                project_total = await query.filter(project_id=project.id).count()
                project_completed = await query.filter(project_id=project.id, order_status='completed').count()

                creator_stats[creator_name]['total_orders'] += project_total
                creator_stats[creator_name]['completed_orders'] += project_completed

            # 计算成功率并转换为列表
            for creator_data in creator_stats.values():
                success_rate = (creator_data['completed_orders'] / creator_data['total_orders'] * 100) if creator_data['total_orders'] > 0 else 0
                creator_data['success_rate'] = success_rate
                user_stats.append(creator_data)

            # 按总订单数排序
            user_stats.sort(key=lambda x: x['total_orders'], reverse=True)

        # 项目统计
        project_stats = []
        projects_data = await query.group_by('project_id').annotate(
            total_count=Count('id'),
            completed_count=Count('id', _filter=Q(order_status='completed'))
        ).values('project_id', 'total_count', 'completed_count')
        
        for project_data in projects_data:
            try:
                project = await Project.get(id=project_data['project_id'])
                project_stats.append({
                    'project_id': project_data['project_id'],
                    'project_name': project.project_name,
                    'creator_name': project.creator_name,
                    'total_orders': project_data['total_count'],
                    'completed_orders': project_data['completed_count'],
                    'success_rate': (project_data['completed_count'] / project_data['total_count'] * 100) if project_data['total_count'] > 0 else 0
                })
            except:
                continue
        
        return HotelBookingAnalysisResponse(
            total_orders=total_orders,
            completed_orders=completed_orders,
            failed_orders=failed_orders,
            success_rate=round(success_rate, 2),
            total_amount=total_amount,
            daily_stats=daily_stats,
            user_stats=user_stats,
            project_stats=project_stats
        )
        
    except Exception as e:
        logger.error(f"酒店预订分析失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"酒店预订分析失败: {str(e)}")


@router.get("/flight-booking", response_model=FlightBookingAnalysisResponse, summary="飞机票预订情况分析")
async def analyze_flight_booking(
    start_date: date = Query(..., description="开始日期"),
    end_date: date = Query(..., description="结束日期"),
    user_id: Optional[int] = Query(None, description="用户ID，为空则分析所有用户"),
    user_name: Optional[str] = Query(None, description="用户姓名，为空则分析所有用户"),
    current_user: User = Depends(get_current_user)
):
    """分析飞机票预订情况"""
    try:
        # 构建查询条件
        query = FlightOrder.filter(
            created_at__gte=datetime.combine(start_date, datetime.min.time()),
            created_at__lte=datetime.combine(end_date, datetime.max.time()),
            is_deleted=False
        )

        # 添加用户筛选条件
        if user_name:
            # 通过项目创建者姓名筛选
            projects = await Project.filter(creator_name__icontains=user_name).all()
            if projects:
                project_ids = [p.id for p in projects]
                query = query.filter(project_id__in=project_ids)
            else:
                # 如果没有找到匹配的项目，返回空结果
                query = query.filter(id__in=[])

        # 总订单数
        total_orders = await query.count()

        # 已完成订单数
        completed_orders = await query.filter(order_status='completed').count()

        # 失败订单数
        failed_orders = await query.filter(order_status='failed').count()

        # 成功率
        success_rate = (completed_orders / total_orders * 100) if total_orders > 0 else 0

        # 总金额 - 使用正确的Tortoise ORM语法
        completed_query = query.filter(order_status='completed')
        amount_result = await completed_query.annotate(total=Sum('amount')).values('total')
        total_amount = float(amount_result[0]['total'] if amount_result and amount_result[0]['total'] else 0)

        # 近一周统计（不受搜索条件影响）
        daily_stats = []
        today = datetime.now().date()
        week_start = today - timedelta(days=6)  # 包含今天的7天

        # 基础查询，不受用户筛选影响
        base_query = FlightOrder.filter(is_deleted=False)

        current_date = week_start
        while current_date <= today:
            day_start = datetime.combine(current_date, datetime.min.time())
            day_end = datetime.combine(current_date, datetime.max.time())

            day_total = await base_query.filter(
                created_at__gte=day_start,
                created_at__lte=day_end
            ).count()

            day_completed = await base_query.filter(
                created_at__gte=day_start,
                created_at__lte=day_end,
                order_status='completed'
            ).count()

            # 格式化日期显示（显示星期几）
            weekday_names = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
            weekday = weekday_names[current_date.weekday()]
            date_str = f"{current_date.strftime('%m-%d')} {weekday}"

            daily_stats.append({
                'date': date_str,
                'count': day_total,  # 使用count字段以兼容ECharts组件
                'total_orders': day_total,
                'completed_orders': day_completed,
                'success_rate': (day_completed / day_total * 100) if day_total > 0 else 0
            })
            current_date += timedelta(days=1)

        # 用户统计（基于项目创建者）
        user_stats = []

        # 获取所有相关项目的创建者统计
        projects_with_orders = await query.group_by('project_id').values('project_id')
        project_ids = [p['project_id'] for p in projects_with_orders]

        if project_ids:
            # 获取项目创建者信息
            projects = await Project.filter(id__in=project_ids).all()
            creator_stats = {}

            # 按创建者分组统计
            for project in projects:
                creator_name = project.creator_name
                if creator_name not in creator_stats:
                    creator_stats[creator_name] = {
                        'creator_name': creator_name,
                        'total_orders': 0,
                        'completed_orders': 0
                    }

                # 统计该项目的订单数
                project_total = await query.filter(project_id=project.id).count()
                project_completed = await query.filter(project_id=project.id, order_status='completed').count()

                creator_stats[creator_name]['total_orders'] += project_total
                creator_stats[creator_name]['completed_orders'] += project_completed

            # 计算成功率并转换为列表
            for creator_data in creator_stats.values():
                success_rate = (creator_data['completed_orders'] / creator_data['total_orders'] * 100) if creator_data['total_orders'] > 0 else 0
                creator_data['success_rate'] = success_rate
                user_stats.append(creator_data)

            # 按总订单数排序
            user_stats.sort(key=lambda x: x['total_orders'], reverse=True)

        # 项目统计
        project_stats = []
        projects_data = await query.group_by('project_id').annotate(
            total_count=Count('id'),
            completed_count=Count('id', _filter=Q(order_status='completed'))
        ).values('project_id', 'total_count', 'completed_count')

        for project_data in projects_data:
            try:
                project = await Project.get(id=project_data['project_id'])
                project_stats.append({
                    'project_id': project_data['project_id'],
                    'project_name': project.project_name,
                    'creator_name': project.creator_name,
                    'total_orders': project_data['total_count'],
                    'completed_orders': project_data['completed_count'],
                    'success_rate': (project_data['completed_count'] / project_data['total_count'] * 100) if project_data['total_count'] > 0 else 0
                })
            except:
                continue

        return FlightBookingAnalysisResponse(
            total_orders=total_orders,
            completed_orders=completed_orders,
            failed_orders=failed_orders,
            success_rate=round(success_rate, 2),
            total_amount=total_amount,
            daily_stats=daily_stats,
            user_stats=user_stats,
            project_stats=project_stats
        )

    except Exception as e:
        logger.error(f"飞机票预订分析失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"飞机票预订分析失败: {str(e)}")


@router.get("/overall", response_model=OverallAnalysisResponse, summary="综合数据分析")
async def analyze_overall(
    start_date: date = Query(..., description="开始日期"),
    end_date: date = Query(..., description="结束日期"),
    user_id: Optional[int] = Query(None, description="用户ID，为空则分析所有用户"),
    current_user: User = Depends(get_current_user)
):
    """综合数据分析"""
    try:
        # 调用各个分析接口
        passport_analysis = await analyze_passport_usage(start_date, end_date, user_id, current_user)
        train_analysis = await analyze_train_booking(start_date, end_date, user_id, current_user)
        hotel_analysis = await analyze_hotel_booking(start_date, end_date, user_id, current_user)
        flight_analysis = await analyze_flight_booking(start_date, end_date, user_id, current_user)

        # 汇总信息
        summary = {
            'total_passport_recognitions': passport_analysis.total_recognitions,
            'total_train_orders': train_analysis.total_orders,
            'total_hotel_orders': hotel_analysis.total_orders,
            'total_flight_orders': flight_analysis.total_orders,
            'total_orders': train_analysis.total_orders + hotel_analysis.total_orders + flight_analysis.total_orders,
            'total_amount': train_analysis.total_amount + hotel_analysis.total_amount + flight_analysis.total_amount,
            'overall_success_rate': (
                (train_analysis.completed_orders + hotel_analysis.completed_orders + flight_analysis.completed_orders) /
                (train_analysis.total_orders + hotel_analysis.total_orders + flight_analysis.total_orders) * 100
            ) if (train_analysis.total_orders + hotel_analysis.total_orders + flight_analysis.total_orders) > 0 else 0
        }

        return OverallAnalysisResponse(
            passport_analysis=passport_analysis,
            train_analysis=train_analysis,
            hotel_analysis=hotel_analysis,
            flight_analysis=flight_analysis,
            summary=summary
        )

    except Exception as e:
        logger.error(f"综合数据分析失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"综合数据分析失败: {str(e)}")
