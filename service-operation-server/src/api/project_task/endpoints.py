"""项目任务管理API端点"""

from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi.responses import JSO<PERSON>esponse
from typing import Optional
from tortoise.exceptions import DoesNotExist
import logging

from src.db.models.project_task import ProjectTask
from src.db.models.project import Project
from src.db.models.user import User
from src.api.dependencies import get_current_user
from .schemas import (
    ProjectTaskCreate,
    ProjectTaskUpdate,
    ProjectTaskResponse,
    ProjectTaskListResponse,
    ProjectTaskQuery
)

router = APIRouter()
logger = logging.getLogger(__name__)


@router.post("/", response_model=ProjectTaskResponse, summary="创建项目任务")
async def create_project_task(
    task_data: ProjectTaskCreate,
    current_user: User = Depends(get_current_user)
):
    """
    创建新项目任务
    - 任务ID会自动生成TASKYYMMDDHHMM+序号格式
    - 创建者信息自动从当前登录用户获取
    """
    try:
        # 验证项目是否存在且未删除
        try:
            project = await Project.get(id=task_data.project_id)
        except DoesNotExist:
            raise HTTPException(status_code=400, detail="项目不存在")
        
        # 创建任务
        task = await ProjectTask.create(
            project_id=task_data.project_id,
            creator_user_id=current_user.id,
            creator_name=current_user.username,
            task_type=task_data.task_type,
            task_title=task_data.task_title,
            task_description=task_data.task_description,
            task_status=task_data.task_status
        )
        
        return ProjectTaskResponse.model_validate(task)
        
    except Exception as e:
        logger.error(f"创建任务失败: {str(e)}")
        raise HTTPException(status_code=400, detail=f"创建任务失败: {str(e)}")


@router.get("/{task_id}", response_model=ProjectTaskResponse, summary="获取任务详情")
async def get_project_task(task_id: int):
    """根据任务数据库ID获取任务详情"""
    try:
        task = await ProjectTask.get(id=task_id)
        return ProjectTaskResponse.model_validate(task)
    except DoesNotExist:
        raise HTTPException(status_code=404, detail="任务不存在")


@router.get("/by-task-id/{task_id}", response_model=ProjectTaskResponse, summary="根据任务ID获取任务")
async def get_project_task_by_task_id(task_id: str):
    """根据任务ID获取任务详情"""
    try:
        task = await ProjectTask.get(task_id=task_id)
        return ProjectTaskResponse.model_validate(task)
    except DoesNotExist:
        raise HTTPException(status_code=404, detail="任务不存在")


@router.get("/", response_model=ProjectTaskListResponse, summary="获取任务列表")
async def list_project_tasks(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    project_id: Optional[int] = Query(None, description="项目ID筛选"),
    task_type: Optional[str] = Query(None, description="任务类型筛选"),
    task_status: Optional[str] = Query(None, description="任务状态筛选"),
    creator_name: Optional[str] = Query(None, description="创建人姓名（模糊查询）")
):
    """
    获取任务列表，支持分页和筛选
    """
    try:
        # 构建查询条件
        query = ProjectTask.all()
        
        if project_id:
            query = query.filter(project_id=project_id)
        if task_type:
            query = query.filter(task_type__icontains=task_type)
        if task_status:
            query = query.filter(task_status=task_status)
        if creator_name:
            query = query.filter(creator_name__icontains=creator_name)
        
        # 获取总数
        total = await query.count()
        
        # 分页查询
        offset = (page - 1) * page_size
        tasks = await query.offset(offset).limit(page_size)
        
        # 转换为响应模型
        task_responses = []
        for task in tasks:
            try:
                task_response = ProjectTaskResponse.model_validate(task)
                task_responses.append(task_response)
            except Exception as e:
                logger.error(f"转换任务数据失败: {str(e)}, 任务ID: {task.id}")
                continue
        
        return ProjectTaskListResponse(
            total=total,
            items=task_responses
        )
        
    except Exception as e:
        logger.error(f"查询任务列表失败: {str(e)}")
        raise HTTPException(
            status_code=400,
            detail={
                "message": "查询任务列表失败",
                "error": str(e)
            }
        )


@router.put("/{task_id}", response_model=ProjectTaskResponse, summary="更新任务")
async def update_project_task(task_id: int, task_data: ProjectTaskUpdate):
    """更新任务信息"""
    try:
        task = await ProjectTask.get(id=task_id)
        
        # 更新字段
        update_data = task_data.model_dump(exclude_unset=True)
        
        if update_data:
            await task.update_from_dict(update_data)
            await task.save()
        
        # 重新获取更新后的任务
        updated_task = await ProjectTask.get(id=task_id)
        return ProjectTaskResponse.model_validate(updated_task)
        
    except DoesNotExist:
        raise HTTPException(status_code=404, detail="任务不存在")
    except Exception as e:
        logger.error(f"更新任务失败: {str(e)}")
        raise HTTPException(status_code=400, detail=f"更新任务失败: {str(e)}")


@router.delete("/{task_id}", summary="删除任务")
async def delete_project_task(task_id: int):
    """软删除任务"""
    try:
        task = await ProjectTask.get(id=task_id)
        await task.soft_delete()
        return JSONResponse(content={"message": "任务删除成功"})
        
    except DoesNotExist:
        raise HTTPException(status_code=404, detail="任务不存在")
    except Exception as e:
        logger.error(f"删除任务失败: {str(e)}")
        raise HTTPException(status_code=400, detail=f"删除任务失败: {str(e)}")


@router.get("/project/{project_id}/tasks", response_model=ProjectTaskListResponse, summary="获取项目的所有任务")
async def get_tasks_by_project(
    project_id: int,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量")
):
    """获取指定项目的所有任务"""
    try:
        # 验证项目是否存在
        try:
            await Project.get(id=project_id)
        except DoesNotExist:
            raise HTTPException(status_code=404, detail="项目不存在")
        
        # 查询该项目的任务
        query = ProjectTask.filter(project_id=project_id)
        
        # 获取总数
        total = await query.count()
        
        # 分页查询
        offset = (page - 1) * page_size
        tasks = await query.offset(offset).limit(page_size)
        
        # 转换为响应模型
        task_responses = [ProjectTaskResponse.model_validate(task) for task in tasks]
        
        return ProjectTaskListResponse(
            total=total,
            items=task_responses
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查询项目任务失败: {str(e)}")
        raise HTTPException(status_code=400, detail=f"查询项目任务失败: {str(e)}")


@router.get("/stats/summary", summary="获取任务统计信息")
async def get_task_stats():
    """获取任务统计信息"""
    try:
        total_tasks = await ProjectTask.all().count()
        pending_tasks = await ProjectTask.filter(task_status="pending").count()
        completed_tasks = await ProjectTask.filter(task_status="completed").count()
        
        return {
            "total_tasks": total_tasks,
            "pending_tasks": pending_tasks,
            "completed_tasks": completed_tasks,
            "message": "任务统计信息获取成功"
        }
        
    except Exception as e:
        logger.error(f"获取任务统计信息失败: {str(e)}")
        raise HTTPException(status_code=400, detail=f"获取统计信息失败: {str(e)}")


@router.get("/project/{project_id}/latest-settings", summary="获取项目最新任务设置")
async def get_project_latest_settings(
    project_id: int,
    task_type: Optional[str] = Query(None, description="任务类型，如：火车票预订、酒店预订")
):
    """
    获取指定项目最新的任务设置
    用于在新任务创建时自动填充上次的设置信息
    支持按任务类型筛选，获取对应类型的最新设置
    """
    try:
        # 验证项目是否存在
        try:
            await Project.get(id=project_id)
        except DoesNotExist:
            raise HTTPException(status_code=404, detail="项目不存在")
        
        # 构建查询条件
        query = ProjectTask.filter(
            project_id=project_id,
            is_deleted=False
        )
        
        # 如果指定了任务类型，只查询该类型的任务
        if task_type:
            query = query.filter(task_type=task_type)
        
        # 查询该项目最新的任务（按创建时间倒序）
        latest_task = await query.order_by('-created_at').first()
        
        if not latest_task:
            # 如果项目没有历史任务，返回默认设置
            task_type_msg = f"该项目暂无{task_type}历史任务设置" if task_type else "该项目暂无历史任务设置"
            return JSONResponse(content={
                "has_history": False,
                "sms_notify": False,
                "has_agent": False,
                "agent_phone": None,
                "agent_name": None,
                "message": task_type_msg
            })
        
        # 返回最新任务的设置信息
        return JSONResponse(content={
            "has_history": True,
            "sms_notify": latest_task.sms_notify,
            "has_agent": latest_task.has_agent,
            "agent_phone": latest_task.agent_phone,
            "agent_name": getattr(latest_task, 'agent_name', ''),
            "last_task_title": latest_task.task_title,
            "last_task_created": latest_task.created_at.isoformat(),
            "message": f"已获取最新{task_type or ''}任务设置（来自：{latest_task.task_title}）"
        })
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取项目最新设置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取项目最新设置失败: {str(e)}") 