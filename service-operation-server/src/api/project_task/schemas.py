"""项目任务管理相关的Pydantic模型定义"""

from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field, ConfigDict


class ProjectTaskBase(BaseModel):
    """项目任务基础模型"""
    project_id: int = Field(..., description="项目ID")
    task_type: str = Field(..., max_length=50, description="任务类型")
    task_title: str = Field(..., max_length=200, description="任务标题")
    task_description: Optional[str] = Field(None, description="任务描述")
    task_status: str = Field(default="pending", description="任务状态")
    sms_notify: bool = Field(default=False, description="是否短信通知")
    has_agent: bool = Field(default=False, description="是否有代订人")
    agent_phone: Optional[str] = Field(None, max_length=20, description="代订人手机号码")
    agent_name: str = Field(default="", max_length=100, description="代订人姓名")


class ProjectTaskCreate(ProjectTaskBase):
    """创建项目任务的请求模型"""
    pass  # creator_user_id 和 creator_name 将从当前登录用户获取


class ProjectTaskUpdate(BaseModel):
    """更新项目任务的请求模型"""
    task_type: Optional[str] = Field(None, max_length=50, description="任务类型")
    task_title: Optional[str] = Field(None, max_length=200, description="任务标题")
    task_description: Optional[str] = Field(None, description="任务描述")
    task_status: Optional[str] = Field(None, description="任务状态")
    sms_notify: Optional[bool] = Field(None, description="是否短信通知")
    has_agent: Optional[bool] = Field(None, description="是否有代订人")
    agent_phone: Optional[str] = Field(None, max_length=20, description="代订人手机号码")
    sms_notify: Optional[bool] = Field(None, description="是否短信通知")
    has_agent: Optional[bool] = Field(None, description="是否有代订人")
    agent_phone: Optional[str] = Field(None, max_length=20, description="代订人手机号码")


class ProjectTaskResponse(BaseModel):
    """项目任务响应模型"""
    model_config = ConfigDict(from_attributes=True)
    
    id: int = Field(..., description="任务数据库ID")
    task_id: str = Field(..., description="任务ID(格式：TASKYYMMDDHHMM+序号)")
    project_id: int = Field(..., description="项目ID")
    creator_user_id: int = Field(..., description="创建用户ID")
    creator_name: str = Field(..., description="创建人姓名")
    task_type: str = Field(..., description="任务类型")
    task_title: str = Field(..., description="任务标题")
    task_description: Optional[str] = Field(None, description="任务描述")
    task_status: str = Field(..., description="任务状态")
    sms_notify: bool = Field(..., description="是否短信通知")
    has_agent: bool = Field(..., description="是否有代订人")
    agent_phone: Optional[str] = Field(None, description="代订人手机号码")
    agent_name: str = Field(..., description="代订人姓名")
    created_at: datetime = Field(..., description="记录创建时间")
    updated_at: datetime = Field(..., description="记录更新时间")


class ProjectTaskListResponse(BaseModel):
    """项目任务列表响应模型"""
    total: int = Field(..., description="总记录数")
    items: List[ProjectTaskResponse] = Field(..., description="任务列表")


class ProjectTaskQuery(BaseModel):
    """项目任务查询参数"""
    page: int = Field(default=1, ge=1, description="页码")
    page_size: int = Field(default=20, ge=1, le=100, description="每页数量")
    project_id: Optional[int] = Field(None, description="项目ID筛选")
    task_type: Optional[str] = Field(None, description="任务类型筛选")
    task_status: Optional[str] = Field(None, description="任务状态筛选")
    creator_name: Optional[str] = Field(None, description="创建人姓名（模糊查询）") 