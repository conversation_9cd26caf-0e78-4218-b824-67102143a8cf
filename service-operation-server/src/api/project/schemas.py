"""项目管理相关的Pydantic模型定义"""

from datetime import date, datetime
from typing import Optional, List, Any
from pydantic import BaseModel, Field, ConfigDict, field_serializer, model_validator


class ProjectBase(BaseModel):
    """项目基础模型"""
    project_name: str = Field(..., max_length=200, description="项目名称")
    project_description: Optional[str] = Field(None, description="项目描述")
    client_name: str = Field(..., max_length=200, description="客户名称")
    cost_center: Optional[str] = Field(None, max_length=100, description="成本中心")
    booking_agent_phone: Optional[str] = Field(None, max_length=20, description="代订人手机号码")
    project_date: str = Field(..., description="项目创建日期")


class ProjectCreate(ProjectBase):
    """创建项目的请求模型"""
    pass  # 不再需要 creator_user_id 和 creator_name，将从当前登录用户获取


class ProjectUpdate(BaseModel):
    """更新项目的请求模型"""
    project_name: Optional[str] = Field(None, max_length=200, description="项目名称")
    project_description: Optional[str] = Field(None, description="项目描述")
    client_name: Optional[str] = Field(None, max_length=200, description="客户名称")
    cost_center: Optional[str] = Field(None, max_length=100, description="成本中心")
    booking_agent_phone: Optional[str] = Field(None, max_length=20, description="代订人手机号码")
    project_date: Optional[str] = Field(None, description="项目创建日期")


class ProjectResponse(BaseModel):
    """项目响应模型"""
    model_config = ConfigDict(from_attributes=True)
    
    id: int = Field(..., description="项目ID")
    project_number: int = Field(..., description="项目编号")
    project_name: str = Field(..., max_length=200, description="项目名称")
    project_description: Optional[str] = Field(None, description="项目描述")
    client_name: str = Field(..., max_length=200, description="客户名称")
    cost_center: Optional[str] = Field(None, description="成本中心")
    booking_agent_phone: Optional[str] = Field(None, description="代订人手机号码")
    project_date: datetime = Field(..., description="项目创建日期")
    creator_user_id: int = Field(..., description="创建用户ID")
    creator_name: str = Field(..., description="创建人姓名")
    creator_department: Optional[str] = Field(None, description="创建人部门")
    created_at: datetime = Field(..., description="记录创建时间")
    updated_at: datetime = Field(..., description="记录更新时间")

    @field_serializer('project_date')
    def serialize_project_date(self, project_date: datetime) -> str:
        """序列化project_date字段，只返回日期部分"""
        return project_date.date().isoformat()

    @field_serializer('created_at', 'updated_at')
    def serialize_datetime(self, dt: datetime) -> str:
        """序列化datetime字段"""
        return dt.isoformat()


class ProjectListResponse(BaseModel):
    """项目列表响应模型"""
    total: int = Field(..., description="总记录数")
    items: List[ProjectResponse] = Field(..., description="项目列表")


class ProjectQuery(BaseModel):
    """项目查询参数"""
    page: int = Field(default=1, ge=1, description="页码")
    page_size: int = Field(default=20, ge=1, le=100, description="每页数量")
    project_name: Optional[str] = Field(None, description="项目名称（模糊查询）")
    client_name: Optional[str] = Field(None, description="客户名称（模糊查询）")
    creator_name: Optional[str] = Field(None, description="创建人姓名（模糊查询）") 