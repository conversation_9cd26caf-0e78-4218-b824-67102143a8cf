"""RPA错误日志API的数据模式定义"""

from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime


class RpaErrorLogResponse(BaseModel):
    """RPA错误日志响应模型"""
    id: int
    error_id: str
    task_id: str
    order_id: str
    error_timestamp: datetime
    module: str
    function_name: str
    error_type: str
    error_message: Optional[str] = None
    severity: str
    page_url: Optional[str] = None
    page_title: str
    action_attempted: Optional[str] = None
    user_agent: str
    browser_info: Optional[str] = None
    stack_trace: Optional[str] = None
    additional_context: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    # 关联信息
    project_id: Optional[int] = None
    project_name: Optional[str] = None
    creator_name: Optional[str] = None
    task_type: Optional[str] = None

    class Config:
        from_attributes = True


class RpaErrorLogListResponse(BaseModel):
    """RPA错误日志列表响应模型"""
    items: List[RpaErrorLogResponse]
    total: int
    page: int
    page_size: int
    total_pages: int


class RpaErrorLogCreate(BaseModel):
    """创建RPA错误日志请求模型"""
    error_id: str = Field(..., description="错误唯一标识")
    task_id: str = Field(..., description="关联任务ID")
    order_id: str = Field(..., description="关联订单ID")
    error_timestamp: datetime = Field(..., description="错误发生时间")
    module: str = Field(..., description="模块名称")
    function_name: str = Field(..., description="函数名称")
    error_type: str = Field(..., description="错误类型")
    error_message: Optional[str] = Field(None, description="错误消息")
    severity: str = Field("medium", description="严重程度")
    page_url: Optional[str] = Field(None, description="页面URL")
    page_title: str = Field("", description="页面标题")
    action_attempted: Optional[str] = Field(None, description="尝试的操作")
    user_agent: str = Field("", description="用户代理")
    browser_info: Optional[str] = Field(None, description="浏览器信息")
    stack_trace: Optional[str] = Field(None, description="堆栈跟踪")
    additional_context: Optional[str] = Field(None, description="额外上下文信息")
