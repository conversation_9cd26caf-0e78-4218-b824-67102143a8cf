"""RPA错误日志API端点"""

import logging
from fastapi import APIRouter, HTTPException, Query, Depends
from typing import Optional, List
from tortoise import Tortoise

from src.api.dependencies import get_current_user
from src.db.models.user import User
from src.db.models.rpa_error_log import RpaErrorLog
from .schemas import RpaErrorLogResponse, RpaErrorLogListResponse, RpaErrorLogCreate

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/by-order/{order_id}", response_model=RpaErrorLogListResponse, summary="根据订单ID获取RPA错误日志")
async def get_rpa_error_logs_by_order(
    order_id: str,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页数量")
):
    """根据订单ID获取RPA错误日志列表"""
    try:
        # 构建查询条件
        query = RpaErrorLog.filter(order_id=order_id)

        # 获取总数
        total = await query.count()

        # 分页查询
        offset = (page - 1) * page_size
        error_logs = await query.offset(offset).limit(page_size).all()

        # 计算总页数
        total_pages = (total + page_size - 1) // page_size

        return RpaErrorLogListResponse(
            items=[RpaErrorLogResponse.model_validate(log) for log in error_logs],
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages
        )

    except Exception as e:
        logger.error(f"获取RPA错误日志失败: {str(e)}")
        raise HTTPException(status_code=400, detail=f"获取RPA错误日志失败: {str(e)}")


@router.get("/by-task/{task_id}", response_model=RpaErrorLogListResponse, summary="根据任务ID获取RPA错误日志")
async def get_rpa_error_logs_by_task(
    task_id: str,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页数量")
):
    """根据任务ID获取RPA错误日志列表"""
    try:
        # 构建查询条件
        query = RpaErrorLog.filter(task_id=task_id)

        # 获取总数
        total = await query.count()

        # 分页查询
        offset = (page - 1) * page_size
        error_logs = await query.offset(offset).limit(page_size).all()

        # 计算总页数
        total_pages = (total + page_size - 1) // page_size

        return RpaErrorLogListResponse(
            items=[RpaErrorLogResponse.model_validate(log) for log in error_logs],
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages
        )

    except Exception as e:
        logger.error(f"获取RPA错误日志失败: {str(e)}")
        raise HTTPException(status_code=400, detail=f"获取RPA错误日志失败: {str(e)}")


@router.get("/analysis", response_model=RpaErrorLogListResponse, summary="错误排查分析")
async def get_error_logs_for_analysis(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页大小"),
    creator_name: Optional[str] = Query(None, description="创建人姓名"),
    project_name: Optional[str] = Query(None, description="项目名称"),
    task_type: Optional[str] = Query(None, description="任务类型"),
    order_id: Optional[str] = Query(None, description="订单ID"),
    current_user: User = Depends(get_current_user)
):
    """
    获取错误日志列表用于错误排查分析
    使用纯 Tortoise ORM 方式实现
    """
    try:
        # 由于需要跨表查询，这里仍然使用原生SQL，但通过Tortoise的连接
        conn = Tortoise.get_connection("default")

        # 构建WHERE条件
        where_conditions = []
        params = []

        if creator_name:
            where_conditions.append("p.creator_name LIKE %s")
            params.append(f"%{creator_name}%")

        if project_name:
            where_conditions.append("p.project_name LIKE %s")
            params.append(f"%{project_name}%")

        if task_type:
            where_conditions.append("pt.task_type LIKE %s")
            params.append(f"%{task_type}%")

        if order_id:
            where_conditions.append("rel.order_id LIKE %s")
            params.append(f"%{order_id}%")

        where_clause = ""
        if where_conditions:
            where_clause = "WHERE " + " AND ".join(where_conditions)

        # 查询总数
        count_sql = f"""
        SELECT COUNT(*) as total
        FROM rpa_error_logs rel
        LEFT JOIN project_tasks pt ON rel.task_id = pt.task_id
        LEFT JOIN projects p ON pt.project_id = p.id
        {where_clause}
        """

        count_result = await conn.execute_query(count_sql, params)
        total = count_result[1][0]['total']

        # 查询数据
        offset = (page - 1) * page_size
        data_sql = f"""
        SELECT
            rel.*,
            p.id as project_id,
            p.project_name,
            p.creator_name,
            pt.task_type
        FROM rpa_error_logs rel
        LEFT JOIN project_tasks pt ON rel.task_id = pt.task_id
        LEFT JOIN projects p ON pt.project_id = p.id
        {where_clause}
        ORDER BY rel.error_timestamp DESC
        LIMIT %s OFFSET %s
        """

        data_params = params + [page_size, offset]
        data_result = await conn.execute_query(data_sql, data_params)

        # 计算总页数
        total_pages = (total + page_size - 1) // page_size

        items = []
        for row in data_result[1]:
            # 创建 RpaErrorLogResponse 对象
            error_log = RpaErrorLogResponse(
                id=row['id'],
                error_id=row['error_id'],
                task_id=row['task_id'],
                order_id=row['order_id'],
                error_timestamp=row['error_timestamp'],
                module=row['module'],
                function_name=row['function_name'],
                error_type=row['error_type'],
                error_message=row['error_message'],
                severity=row['severity'],
                page_url=row['page_url'],
                page_title=row['page_title'],
                action_attempted=row['action_attempted'],
                user_agent=row['user_agent'],
                browser_info=row['browser_info'],
                stack_trace=row['stack_trace'],
                additional_context=row['additional_context'],
                created_at=row['created_at'],
                updated_at=row['updated_at'],
                # 添加关联信息
                project_id=row['project_id'],
                project_name=row['project_name'],
                creator_name=row['creator_name'],
                task_type=row['task_type']
            )
            items.append(error_log)

        return RpaErrorLogListResponse(
            items=items,
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages
        )

    except Exception as e:
        logger.error(f"获取错误日志失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取错误日志失败: {str(e)}")


@router.get("/analysis/options", summary="获取错误排查筛选选项")
async def get_error_log_analysis_options(
    current_user: User = Depends(get_current_user)
):
    """
    获取错误日志筛选选项
    使用纯 Tortoise ORM 方式实现
    """
    try:
        conn = Tortoise.get_connection("default")

        # 获取项目列表
        projects_sql = """
        SELECT DISTINCT p.id as project_id, p.project_name
        FROM projects p
        INNER JOIN project_tasks pt ON p.id = pt.project_id
        INNER JOIN rpa_error_logs rel ON pt.task_id = rel.task_id
        WHERE p.project_name IS NOT NULL AND p.project_name != ''
        ORDER BY p.project_name
        """

        projects_result = await conn.execute_query(projects_sql)
        projects = [
            {
                "project_id": row['project_id'],
                "project_name": row['project_name']
            }
            for row in projects_result[1]
        ]

        # 获取创建人列表
        creators_sql = """
        SELECT DISTINCT p.creator_name
        FROM projects p
        INNER JOIN project_tasks pt ON p.id = pt.project_id
        INNER JOIN rpa_error_logs rel ON pt.task_id = rel.task_id
        WHERE p.creator_name IS NOT NULL AND p.creator_name != ''
        ORDER BY p.creator_name
        """

        creators_result = await conn.execute_query(creators_sql)
        creators = [
            {"creator_name": row['creator_name']}
            for row in creators_result[1]
        ]

        # 获取任务类型列表
        task_types_sql = """
        SELECT DISTINCT pt.task_type
        FROM project_tasks pt
        INNER JOIN rpa_error_logs rel ON pt.task_id = rel.task_id
        WHERE pt.task_type IS NOT NULL AND pt.task_type != ''
        ORDER BY pt.task_type
        """

        task_types_result = await conn.execute_query(task_types_sql)
        task_types = [
            {"task_type": row['task_type']}
            for row in task_types_result[1]
        ]

        return {
            "projects": projects,
            "creators": creators,
            "task_types": task_types
        }

    except Exception as e:
        logger.error(f"获取筛选选项失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取筛选选项失败: {str(e)}")


@router.get("/{log_id}", response_model=RpaErrorLogResponse, summary="获取RPA错误日志详情")
async def get_rpa_error_log(log_id: int):
    """获取RPA错误日志详情"""
    try:
        error_log = await RpaErrorLog.get(id=log_id)
        return RpaErrorLogResponse.model_validate(error_log)
    except Exception as e:
        logger.error(f"获取RPA错误日志详情失败: {str(e)}")
        raise HTTPException(status_code=404, detail="RPA错误日志不存在")


@router.get("/by-error-id/{error_id}", response_model=RpaErrorLogResponse, summary="根据错误ID获取RPA错误日志详情")
async def get_rpa_error_log_by_error_id(error_id: str):
    """根据错误ID获取RPA错误日志详情"""
    try:
        error_log = await RpaErrorLog.get(error_id=error_id)
        return RpaErrorLogResponse.model_validate(error_log)
    except Exception as e:
        logger.error(f"获取RPA错误日志详情失败: {str(e)}")
        raise HTTPException(status_code=404, detail="RPA错误日志不存在")


@router.post("/", response_model=RpaErrorLogResponse, summary="创建RPA错误日志")
async def create_rpa_error_log(error_log_data: RpaErrorLogCreate):
    """创建RPA错误日志"""
    try:
        error_log = await RpaErrorLog.create(**error_log_data.model_dump())
        return RpaErrorLogResponse.model_validate(error_log)
    except Exception as e:
        logger.error(f"创建RPA错误日志失败: {str(e)}")
        raise HTTPException(status_code=400, detail=f"创建RPA错误日志失败: {str(e)}")
