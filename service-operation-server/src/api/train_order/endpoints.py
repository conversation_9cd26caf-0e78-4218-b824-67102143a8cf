"""火车票订单管理API端点"""

import logging
import pandas as pd
from io import BytesIO
from fastapi import APIRouter, Depends, HTTPException, Query, UploadFile, File, Form
from fastapi.responses import J<PERSON>NResponse
from typing import Optional
from tortoise.exceptions import DoesNotExist
from datetime import datetime, date, time
from decimal import Decimal
import random
import re

from src.db.models.train_order import TrainOrder
from src.db.models.project_task import ProjectTask
from src.db.models.project import Project
from src.db.models.user import User
from src.api.dependencies import get_current_user
from .schemas import (
    TrainOrderCreate,
    TrainOrderUpdate,
    TrainOrderResponse,
    TrainOrderListResponse,
    ExcelUploadResponse,
    ValidationError,
    ExcelValidationResponse,
    BookingRequest,
    BookingResponse,
    ClearOrdersResponse,
    CreateBookingTaskRequest,
    CreateBookingTaskResponse,
    ProjectOrderStatsResponse,
    ProjectOrderDetailStatsResponse,
    TaskOrderStatsResponse,
    PauseOrdersRequest,
    PauseOrdersResponse,
    ResetPausedOrdersRequest,
    ResetPausedOrdersResponse,
    ResetFailedOrdersRequest,
    ResetFailedOrdersResponse
)

router = APIRouter()
logger = logging.getLogger(__name__)


def validate_id_number(id_number: str) -> bool:
    """验证身份证号码"""
    if not id_number:
        return False
    
    # 身份证号码正则表达式
    pattern = r'^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$'
    
    if not re.match(pattern, id_number):
        return False
    
    # 验证校验码
    try:
        coefficients = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
        check_codes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']
        
        total = sum(int(id_number[i]) * coefficients[i] for i in range(17))
        check_code = check_codes[total % 11]
        
        return id_number[17].upper() == check_code
    except:
        return False


def validate_email(email: str) -> bool:
    """验证邮箱格式"""
    if not email:
        return False
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None


def validate_phone(phone: str) -> bool:
    """验证手机号码格式"""
    if not phone:
        return False
    pattern = r'^1[3-9]\d{9}$'
    return re.match(pattern, phone) is not None


def is_id_expired(expiry_date: date) -> bool:
    """检查身份证是否过期"""
    if not expiry_date:
        return True
    return expiry_date < date.today()


def is_id_card_number(id_number: str) -> bool:
    """判断是否为身份证号码格式"""
    if not id_number:
        return False

    id_number = id_number.strip()
    # 18位身份证号码格式验证
    pattern = r'^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$'
    return bool(re.match(pattern, id_number))


def extract_birth_date_from_id_card(id_number: str) -> str:
    """从身份证号码提取出生日期"""
    if not id_number or len(id_number) != 18:
        return ""

    try:
        year = id_number[6:10]
        month = id_number[10:12]
        day = id_number[12:14]
        return f"{year}-{month}-{day}"
    except:
        return ""


def extract_gender_from_id_card(id_number: str) -> str:
    """从身份证号码提取性别"""
    if not id_number or len(id_number) != 18:
        return ""

    try:
        # 第17位数字，奇数为男，偶数为女
        gender_digit = int(id_number[16])
        return "男" if gender_digit % 2 == 1 else "女"
    except:
        return ""


def split_full_name(full_name: str) -> tuple:
    """拆分姓名为姓和名"""
    if not full_name:
        return "", ""

    full_name = full_name.strip()
    if len(full_name) == 0:
        return "", ""

    surname = full_name[0]
    given_name = full_name[1:] if len(full_name) > 1 else ""
    return surname, given_name


def generate_random_expiry_date() -> str:
    """生成六个月后的随机证件有效期"""
    from datetime import datetime, timedelta
    import random

    # 六个月后的基准日期
    base_date = datetime.now() + timedelta(days=180)

    # 在基准日期前后30天内随机选择一天
    random_days = random.randint(-30, 30)
    expiry_date = base_date + timedelta(days=random_days)

    return expiry_date.strftime("%Y-%m-%d")


def auto_complete_train_order_data(row_data: dict) -> dict:
    """
    自动补齐火车票订单数据

    如果证件号码有内容并识别为身份证类型，但是证件类型、性别、国籍没有填写，则需要补齐：
    - 证件类型补齐为身份证
    - 补齐出生日期和国籍（国籍为中国）
    - 证件有效期如果没填写，也随机填写六个月后某一天
    - 出行人姓、出行人名如果未填写，则从出行人姓名进行解析
    """
    # 创建数据副本，避免修改原始数据
    completed_data = row_data.copy()

    id_number = completed_data.get('id_number', '').strip()

    # 如果证件号码有内容并且是身份证格式
    if id_number and is_id_card_number(id_number):
        logger.info(f"检测到身份证号码: {id_number}，开始自动补齐信息")

        # 补齐证件类型
        if not completed_data.get('id_type') or completed_data.get('id_type').strip() == '':
            completed_data['id_type'] = '身份证'
            logger.info(f"自动补齐证件类型: 身份证")

        # 补齐国籍
        if not completed_data.get('nationality') or completed_data.get('nationality').strip() == '':
            completed_data['nationality'] = '中国'
            logger.info(f"自动补齐国籍: 中国")

        # 补齐性别
        if not completed_data.get('gender') or completed_data.get('gender').strip() == '':
            gender = extract_gender_from_id_card(id_number)
            if gender:
                completed_data['gender'] = gender
                logger.info(f"自动补齐性别: {gender}")

        # 补齐出生日期
        if not completed_data.get('birth_date') or completed_data.get('birth_date').strip() == '':
            birth_date = extract_birth_date_from_id_card(id_number)
            if birth_date:
                completed_data['birth_date'] = birth_date
                logger.info(f"自动补齐出生日期: {birth_date}")

        # 补齐证件有效期
        if not completed_data.get('id_expiry_date') or completed_data.get('id_expiry_date').strip() == '':
            expiry_date = generate_random_expiry_date()
            completed_data['id_expiry_date'] = expiry_date
            logger.info(f"自动补齐证件有效期: {expiry_date}")

    # 补齐出行人姓、出行人名
    full_name = completed_data.get('traveler_full_name', '').strip()
    if full_name:
        # 如果出行人姓为空，则从姓名中解析
        if not completed_data.get('traveler_surname') or completed_data.get('traveler_surname').strip() == '':
            surname, given_name = split_full_name(full_name)
            if surname:
                completed_data['traveler_surname'] = surname
                logger.info(f"自动补齐出行人姓: {surname}")

        # 如果出行人名为空，则从姓名中解析
        if not completed_data.get('traveler_given_name') or completed_data.get('traveler_given_name').strip() == '':
            surname, given_name = split_full_name(full_name)
            if given_name:
                completed_data['traveler_given_name'] = given_name
                logger.info(f"自动补齐出行人名: {given_name}")

    return completed_data


async def check_duplicate_id_number(project_id: int, id_number: str, exclude_id: int = None) -> bool:
    """检查项目内是否有重复身份证号"""
    if not id_number:
        return False
    
    query = TrainOrder.filter(project_id=project_id, id_number=id_number, is_deleted=False)
    if exclude_id:
        query = query.exclude(id=exclude_id)
    
    count = await query.count()
    return count > 0


async def check_duplicate_order(project_id: int, id_number: str, travel_date: str, train_number: str, exclude_id: int = None) -> bool:
    """检查项目内是否有重复订单（基于证件号码+出行日期+车次）"""
    if not id_number or not travel_date or not train_number:
        return False
    
    query = TrainOrder.filter(
        project_id=project_id, 
        id_number=id_number, 
        travel_date=travel_date,
        train_number=train_number,
        is_deleted=False
    )
    if exclude_id:
        query = query.exclude(id=exclude_id)
    
    count = await query.count()
    return count > 0


def validate_train_order_data(row_data: dict, row_index: int) -> list:
    """验证单行火车票订单数据"""
    errors = []
    
    # 基础必填字段验证（所有情况下都必填）
    basic_required_fields = {
        'traveler_full_name': '出行人姓名',
        'id_type': '证件类型',
        'id_number': '证件号码',
        'mobile_phone': '手机号',
        'travel_date': '出行日期',
        'departure_station': '出发站名',
        'arrival_station': '到达站名',
        'train_number': '车次',
        'seat_type': '座位类型',
        'departure_time': '出发时间',
        'arrival_time': '到达时间',
        'contact_person': '联系人',
        'contact_phone': '联系人手机号码',
        'approval_reference': '审批参考人'
    }
    
    for field_key, field_name in basic_required_fields.items():
        value = row_data.get(field_key)
        if not value or (isinstance(value, str) and value.strip() == ''):
            errors.append(ValidationError(
                row=row_index,
                field=field_name,
                error_type="required",
                message=f"{field_name}为必填项",
                value=str(value) if value else ""
            ))
    
    # 证件类型枚举验证
    id_type = row_data.get('id_type')
    valid_id_types = [
        '身份证', '公务护照', '普通护照', '港澳通行证', '台胞证', '回乡证', 
        '军人证', '海员证', '台湾通行证', '外国永久居留证', '港澳台居民居住证', '其他'
    ]
    
    if id_type and id_type not in valid_id_types:
        errors.append(ValidationError(
            row=row_index,
            field="证件类型",
            error_type="invalid_value",
            message=f"证件类型必须是以下值之一：{', '.join(valid_id_types)}",
            value=id_type
        ))
    
    # 证件类型不为身份证时的额外必填字段验证
    if id_type and id_type != '身份证':
        non_id_required_fields = {
            'traveler_surname': '出行人姓',
            'traveler_given_name': '出行人名',
            'nationality': '国籍',
            'gender': '性别',
            'birth_date': '出生日期',
            'id_expiry_date': '证件有效期至'
        }
        
        for field_key, field_name in non_id_required_fields.items():
            value = row_data.get(field_key)
            if not value or (isinstance(value, str) and value.strip() == ''):
                errors.append(ValidationError(
                    row=row_index,
                    field=field_name,
                    error_type="required",
                    message=f"证件类型不为身份证时，{field_name}为必填项",
                    value=str(value) if value else ""
                ))
    
    # 身份证号码格式验证
    id_number = row_data.get('id_number')
    if id_number and id_type == '身份证':
        # 简单的身份证号码格式验证：15位或18位数字或字母X
        import re
        id_pattern = r'^[0-9Xx]{15}$|^[0-9Xx]{18}$'
        if not re.match(id_pattern, id_number):
            errors.append(ValidationError(
                row=row_index,
                field="证件号码",
                error_type="format",
                message="身份证号码格式不正确（15位或18位数字或字母X）",
                value=id_number
            ))
    
    # 手机号码非空验证（不验证格式）
    mobile_phone = row_data.get('mobile_phone')
    if not mobile_phone or (isinstance(mobile_phone, str) and mobile_phone.strip() == ''):
        errors.append(ValidationError(
            row=row_index,
            field="手机号",
            error_type="required",
            message="手机号不能为空",
            value=str(mobile_phone) if mobile_phone else ""
        ))

    # 联系人手机号码非空验证（不验证格式）
    contact_phone = row_data.get('contact_phone')
    if not contact_phone or (isinstance(contact_phone, str) and contact_phone.strip() == ''):
        errors.append(ValidationError(
            row=row_index,
            field="联系人手机号码",
            error_type="required",
            message="联系人手机号不能为空",
            value=str(contact_phone) if contact_phone else ""
        ))
    
    # 邮箱格式验证（非必填，但如果填写了需要验证格式）
    contact_email = row_data.get('contact_email')
    if contact_email and not validate_email(contact_email):
        errors.append(ValidationError(
            row=row_index,
            field="联系人邮箱",
            error_type="format",
            message="邮箱格式不正确",
            value=contact_email
        ))
    
    # 证件有效期验证
    id_expiry_date = row_data.get('id_expiry_date')
    if id_expiry_date and id_expiry_date.strip():
        try:
            # 尝试解析日期字符串进行验证
            check_date = datetime.strptime(id_expiry_date, "%Y-%m-%d").date()
            if is_id_expired(check_date):
                errors.append(ValidationError(
                    row=row_index,
                    field="证件有效期至",
                    error_type="expired",
                    message="证件已过期",
                    value=id_expiry_date
                ))
        except ValueError:
            # 日期格式不正确
            errors.append(ValidationError(
                row=row_index,
                field="证件有效期至",
                error_type="format",
                message="日期格式不正确，应为YYYY-MM-DD格式",
                value=id_expiry_date
            ))
    
    return errors


async def validate_excel_data(df: pd.DataFrame, project_id: int) -> ExcelValidationResponse:
    """验证Excel数据"""
    total_rows = len(df)
    valid_rows = 0
    error_rows = 0
    all_errors = []
    duplicate_combinations_in_file = set()  # 改为存储组合而不是单独的身份证号
    
    for index, row in df.iterrows():
        row_index = index + 1
        row_errors = []
        
        # 准备行数据
        row_data = {
            'traveler_full_name': safe_str(row.get('出行人姓名')),
            'traveler_surname': safe_str(row.get('出行人姓')),
            'traveler_given_name': safe_str(row.get('出行人名')),
            'nationality': safe_str(row.get('国籍')),
            'gender': safe_str(row.get('性别')),
            'birth_date': parse_excel_date(row.get('出生日期')),
            'id_type': safe_str(row.get('证件类型')),
            'id_number': safe_str(row.get('证件号码')),
            'id_expiry_date': parse_excel_date(row.get('证件有效期至')),
            'mobile_phone': safe_str(row.get('手机号')),
            'travel_date': parse_excel_date(row.get('出行日期')),
            'departure_station': safe_str(row.get('出发站名')),
            'arrival_station': safe_str(row.get('到达站名')),
            'train_number': safe_str(row.get('车次')),
            'seat_type': safe_str(row.get('座位类型')),
            'departure_time': parse_excel_time(row.get('出发时间')),
            'arrival_time': parse_excel_time(row.get('到达时间')),
            'cost_center': safe_str(row.get('成本中心')) or "",  # 确保成本中心为空字符串而不是None
            'contact_person': safe_str(row.get('联系人')),
            'contact_phone': safe_str(row.get('联系人手机号码')) or safe_str(row.get('联系人手机号')),
            'contact_email': safe_str(row.get('联系人邮箱')),
            'approval_reference': safe_str(row.get('审批参考人'))
        }

        # 自动补齐数据（在验证之前）
        logger.info(f"第 {row_index} 行开始自动补齐数据")
        completed_data = auto_complete_train_order_data(row_data)

        # 基础字段验证（使用补齐后的数据）
        row_errors.extend(validate_train_order_data(completed_data, row_index))
        
        # 检查重复订单（基于证件号码+出行日期+车次组合）
        id_number = row_data.get('id_number')
        travel_date = row_data.get('travel_date')
        train_number = row_data.get('train_number')
        
        if id_number and travel_date and train_number:
            # 创建组合标识符
            combination_key = f"{id_number}|{travel_date}|{train_number}"
            
            # 检查文件内重复
            if combination_key in duplicate_combinations_in_file:
                row_errors.append(ValidationError(
                    row=row_index,
                    field="证件号码",
                    error_type="duplicate_in_file",
                    message="文件内存在重复的订单（相同证件号码+出行日期+车次），导入时将忽略",
                    value=f"{id_number} | {travel_date} | {train_number}"
                ))
            else:
                duplicate_combinations_in_file.add(combination_key)
                
                # 检查数据库内重复订单
                is_duplicate = await check_duplicate_order(project_id, id_number, travel_date, train_number)
                if is_duplicate:
                    row_errors.append(ValidationError(
                        row=row_index,
                        field="证件号码",
                        error_type="duplicate_in_db",
                        message="该订单已存在于项目中（相同证件号码+出行日期+车次），导入时将忽略",
                        value=f"{id_number} | {travel_date} | {train_number}"
                    ))
        
        if row_errors:
            error_rows += 1
            all_errors.extend(row_errors)
        else:
            valid_rows += 1
    
    has_errors = len(all_errors) > 0
    
    # 所有验证错误都允许用户选择是否继续导入
    can_proceed = True  # 始终允许继续导入
    
    if has_errors:
        # 根据错误类型生成不同的提示消息
        duplicate_errors = [error for error in all_errors if error.error_type in ['duplicate_in_file', 'duplicate_in_db']]
        validation_errors = [error for error in all_errors if error.error_type not in ['duplicate_in_file', 'duplicate_in_db']]
        
        if duplicate_errors and validation_errors:
            message = f"⚠️ 发现 {len(duplicate_errors)} 个重复订单和 {len(validation_errors)} 个验证错误，重复数据将忽略，验证失败数据将标记为'验证失败'状态"
        elif duplicate_errors:
            message = f"⚠️ 发现 {len(duplicate_errors)} 个重复订单，导入时将自动忽略重复数据"
        else:
            message = f"⚠️ 发现 {len(validation_errors)} 个验证错误，验证失败的数据将标记为'验证失败'状态，您可以选择继续导入"
    else:
        message = f"✅ 数据验证通过：所有 {total_rows} 行数据格式正确，可以进行导入"
    
    return ExcelValidationResponse(
        project_id=project_id,
        total_rows=total_rows,
        valid_rows=valid_rows,
        error_rows=error_rows,
        has_errors=has_errors,
        errors=all_errors,
        message=message,
        can_proceed=can_proceed
    )


def parse_excel_date(value):
    """解析Excel中的日期为字符串格式"""
    if pd.isna(value) or value == "" or value is None:
        return ""
    
    if isinstance(value, str):
        # 尝试解析常见的日期格式
        for fmt in ["%Y-%m-%d", "%Y/%m/%d", "%m/%d/%Y", "%d/%m/%Y"]:
            try:
                # 解析日期并返回标准格式字符串
                dt = datetime.strptime(value, fmt)
                return dt.strftime("%Y-%m-%d")
            except ValueError:
                continue
        # 如果无法解析，返回原始字符串
        return str(value).strip()
    elif isinstance(value, datetime):
        return value.strftime("%Y-%m-%d")
    elif isinstance(value, date):
        return value.strftime("%Y-%m-%d")
    else:
        return str(value).strip() if value else ""


def parse_excel_time(value):
    """解析Excel中的时间为字符串格式"""
    if pd.isna(value) or value == "" or value is None:
        return ""
    
    if isinstance(value, str):
        # 尝试解析常见的时间格式
        for fmt in ["%H:%M", "%H:%M:%S", "%I:%M %p"]:
            try:
                # 解析时间并返回标准格式字符串
                time_obj = datetime.strptime(value, fmt).time()
                return time_obj.strftime("%H:%M")
            except ValueError:
                continue
        # 如果无法解析，返回原始字符串
        return str(value).strip()
    elif isinstance(value, datetime):
        return value.strftime("%H:%M")
    elif isinstance(value, time):
        return value.strftime("%H:%M")
    else:
        return str(value).strip() if value else ""


def safe_str(value):
    """安全转换为字符串"""
    if pd.isna(value) or value == "" or value is None:
        return None
    return str(value).strip()


def safe_decimal(value):
    """安全转换为Decimal"""
    if pd.isna(value) or value == "" or value is None:
        return 0  # 当数据为空时返回0而不是None
    try:
        return Decimal(str(value))
    except:
        return 0  # 转换失败时也返回0


def safe_int(value):
    """安全转换为整数"""
    if pd.isna(value) or value == "" or value is None:
        return None
    try:
        return int(float(value))
    except:
        return None


def parse_phone_with_country_code(phone_value, country_code_value=None):
    """
    解析手机号和国际区号
    
    Args:
        phone_value: 手机号字段值，可能包含国际区号
        country_code_value: 单独的国际区号字段值
        
    Returns:
        tuple: (手机号, 国际区号)
    """
    phone = safe_str(phone_value)
    country_code = safe_str(country_code_value)
    
    if not phone:
        return None, country_code or "86"
    
    # 如果手机号以+开头，尝试分离国际区号
    if phone.startswith('+'):
        # 查找常见国际区号模式
        import re
        # 更精确的匹配常见国际区号
        # +86 (中国), +1 (美国/加拿大), +81 (日本), +82 (韩国), +44 (英国), +33 (法国), +49 (德国), +61 (澳大利亚)
        patterns = [
            (r'^(\+86)(\d{11})$', '86'),      # 中国: +86 + 11位数字
            (r'^(\+1)(\d{10})$', '1'),        # 美国/加拿大: +1 + 10位数字
            (r'^(\+81)(\d{10,11})$', '81'),   # 日本: +81 + 10-11位数字
            (r'^(\+82)(\d{9,11})$', '82'),    # 韩国: +82 + 9-11位数字
            (r'^(\+44)(\d{10})$', '44'),      # 英国: +44 + 10位数字
            (r'^(\+33)(\d{9})$', '33'),       # 法国: +33 + 9位数字
            (r'^(\+49)(\d{10,12})$', '49'),   # 德国: +49 + 10-12位数字
            (r'^(\+61)(\d{9})$', '61'),       # 澳大利亚: +61 + 9位数字
        ]
        
        for pattern, expected_code in patterns:
            match = re.match(pattern, phone)
            if match:
                extracted_phone = match.group(2)
                # 如果没有单独提供country_code，使用从手机号中提取的
                if not country_code:
                    country_code = expected_code
                return extracted_phone, country_code
        
        # 如果没有匹配到特定模式，使用通用模式
        match = re.match(r'^(\+(\d{1,3}))(\d+)$', phone)
        if match:
            extracted_country_code = match.group(2)  # 不包含+号的区号
            extracted_phone = match.group(3)
            # 如果没有单独提供country_code，使用从手机号中提取的
            if not country_code:
                country_code = extracted_country_code
            return extracted_phone, country_code
    
    # 如果提供了单独的国际区号字段，使用它
    if country_code:
        # 移除+号，只保留数字
        if country_code.startswith('+'):
            country_code = country_code[1:]
        return phone, country_code
    
    # 默认使用中国区号86（不带+号）
    return phone, "86"


@router.post("/validate-excel", response_model=ExcelValidationResponse, summary="验证Excel文件数据")
async def validate_excel(
    project_id: int = Form(...),
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user)
):
    """
    验证Excel文件并返回验证结果，不进行实际导入
    
    功能特性：
    - 验证文件格式和项目存在性
    - 必填字段验证
    - 身份证号码格式和有效期验证
    - 重复身份证号检查
    - 邮箱和手机号格式验证
    - 返回详细的验证错误列表
    """
    logger.info(f"开始验证Excel文件，用户: {current_user.username}, 项目ID: {project_id}, 文件: {file.filename}")
    
    try:
        # 验证项目是否存在
        try:
            project = await Project.get(id=project_id)
            logger.info(f"项目验证成功: {project.project_name}")
        except DoesNotExist:
            logger.error(f"项目不存在: {project_id}")
            raise HTTPException(status_code=404, detail="项目不存在")
        
        # 验证文件类型
        if not file.filename or not file.filename.lower().endswith(('.xlsx', '.xls')):
            logger.error(f"无效的文件类型: {file.filename}")
            raise HTTPException(status_code=400, detail="请上传Excel文件(.xlsx或.xls)")
        
        # 验证文件大小
        file_content = await file.read()
        file_size = len(file_content)
        max_size = 10 * 1024 * 1024  # 10MB
        if file_size > max_size:
            logger.error(f"文件过大: {file_size} bytes")
            raise HTTPException(status_code=400, detail="文件大小不能超过10MB")
        
        logger.info(f"文件验证成功: {file.filename}, 大小: {file_size} bytes")
        
        # 读取Excel文件
        try:
            # 智能检测Excel格式
            df_preview = pd.read_excel(BytesIO(file_content), header=None, nrows=5, engine='openpyxl')
            logger.info(f"Excel文件预览成功，检测到 {len(df_preview)} 行数据")
            
            # 智能检测header位置和数据起始位置
            header_row = 0  # 默认第1行是header
            
            # 检查是否是真实模板格式（第3行是列名，第5行开始是数据）
            if len(df_preview) >= 3:
                potential_header = df_preview.iloc[2].dropna().astype(str).str.strip()
                expected_columns = ['序号', '出行人姓名', '出行人姓', '出行人名', '国籍', '性别']
                
                if any(col.strip() in potential_header.values for col in expected_columns[:3]):
                    header_row = 2  # 第3行是header（索引从0开始）
                    logger.info("检测到真实模板格式：第3行为列名")
                else:
                    logger.info("检测到简化测试格式：第1行为列名")
            
            # 根据检测结果读取Excel文件
            if header_row == 2:
                # 真实模板格式：跳过前4行，从第5行开始读取数据
                df = pd.read_excel(BytesIO(file_content), header=header_row, skiprows=[3], engine='openpyxl')
            else:
                # 简化测试格式：第1行是header，第2行开始是数据
                df = pd.read_excel(BytesIO(file_content), header=header_row, engine='openpyxl')
            
            logger.info(f"Excel文件读取成功，header在第{header_row+1}行，共 {len(df)} 行数据")
            
            # 清理列名（移除空格和特殊字符）
            original_columns = df.columns.tolist()
            df.columns = [col.strip() if isinstance(col, str) else str(col).strip() for col in df.columns]
            cleaned_columns = df.columns.tolist()
            
            logger.info(f"原始列名: {original_columns}")
            logger.info(f"清理后列名: {cleaned_columns}")
            
            # 检查关键列是否存在
            required_columns = ['出行人姓名']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                logger.error(f"缺少必需的列: {missing_columns}")
                logger.error(f"可用列名: {list(df.columns)}")
                raise HTTPException(status_code=400, detail=f"Excel文件缺少必需的列: {missing_columns}")
                
        except Exception as e:
            logger.error(f"Excel文件读取失败: {str(e)}")
            raise HTTPException(status_code=400, detail=f"Excel文件格式错误: {str(e)}")
        
        if df.empty:
            logger.error("Excel文件为空或无有效数据")
            raise HTTPException(status_code=400, detail="Excel文件为空或无有效数据")
        
        # 验证Excel数据
        validation_result = await validate_excel_data(df, project_id)
        
        logger.info(f"Excel验证完成: 总行数={validation_result.total_rows}, "
                   f"有效行数={validation_result.valid_rows}, "
                   f"错误行数={validation_result.error_rows}, "
                   f"错误数量={len(validation_result.errors)}")
        
        return validation_result
        
    except HTTPException:
        raise
    except Exception as e:
        error_msg = f"Excel验证处理异常: {str(e)}"
        logger.error(error_msg)
        raise HTTPException(status_code=500, detail=error_msg)


@router.post("/upload-excel", response_model=ExcelUploadResponse, summary="上传Excel文件")
async def upload_excel(
    project_id: int = Form(...),
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user)
):
    """
    上传Excel文件并解析火车票订单数据
    
    功能特性：
    - 验证文件格式和项目存在性
    - 保存上传的Excel文件
    - 直接批量解析和创建订单记录（不创建project_task）
    - 详细的错误处理和日志记录
    """
    logger.info(f"开始处理Excel上传，用户: {current_user.username}, 项目ID: {project_id}, 文件: {file.filename}")
    
    try:
        # 验证项目是否存在
        try:
            project = await Project.get(id=project_id)
            logger.info(f"项目验证成功: {project.project_name}")
        except DoesNotExist:
            logger.error(f"项目不存在: {project_id}")
            raise HTTPException(status_code=404, detail="项目不存在")
        
        # 验证文件类型
        if not file.filename or not file.filename.lower().endswith(('.xlsx', '.xls')):
            logger.error(f"无效的文件类型: {file.filename}")
            raise HTTPException(status_code=400, detail="请上传Excel文件(.xlsx或.xls)")
        
        # 验证文件大小
        file_content = await file.read()
        file_size = len(file_content)
        max_size = 10 * 1024 * 1024  # 10MB
        if file_size > max_size:
            logger.error(f"文件过大: {file_size} bytes")
            raise HTTPException(status_code=400, detail="文件大小不能超过10MB")
        
        logger.info(f"文件验证成功: {file.filename}, 大小: {file_size} bytes")
        
        # 保存上传的Excel文件（可选，用于后续查看或重新处理）
        saved_file_path = None
        try:
            from src.services.file_service import file_service
            # 将文件内容写回到UploadFile对象
            import tempfile
            import os
            
            with tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx') as temp_file:
                temp_file.write(file_content)
                temp_file.flush()
                
                # 创建新的UploadFile对象用于保存
                from fastapi import UploadFile
                from io import BytesIO
                
                # 重置文件内容用于保存
                file_for_save = UploadFile(
                    filename=file.filename,
                    file=BytesIO(file_content),
                    size=file_size,
                    headers=file.headers
                )
                
                relative_path, absolute_path = await file_service.save_uploaded_file(file_for_save, "train_excel")
                saved_file_path = relative_path
                logger.info(f"Excel文件已保存: {saved_file_path}")
                
                # 清理临时文件
                os.unlink(temp_file.name)
        except Exception as e:
            logger.warning(f"保存Excel文件失败（不影响处理）: {str(e)}")
        
        # 读取Excel文件
        try:
            # 首先尝试智能检测Excel格式
            # 先读取前5行来分析格式
            df_preview = pd.read_excel(BytesIO(file_content), header=None, nrows=5, engine='openpyxl')
            logger.info(f"Excel文件预览成功，检测到 {len(df_preview)} 行数据")
            
            # 智能检测header位置和数据起始位置
            header_row = 0  # 默认第1行是header
            data_start_row = 1  # 默认第2行开始是数据
            
            # 检查是否是真实模板格式（第3行是列名，第5行开始是数据）
            if len(df_preview) >= 3:
                # 检查第3行（索引2）是否包含预期的列名
                potential_header = df_preview.iloc[2].dropna().astype(str).str.strip()
                expected_columns = ['序号', '出行人姓名', '出行人姓', '出行人名', '国籍', '性别']
                
                # 如果第3行包含预期列名，则使用真实模板格式
                if any(col.strip() in potential_header.values for col in expected_columns[:3]):
                    header_row = 2  # 第3行是header（索引从0开始）
                    data_start_row = 4  # 第5行开始是数据（跳过第4行的单位说明）
                    logger.info("检测到真实模板格式：第3行为列名，第5行开始为数据")
                else:
                    logger.info("检测到简化测试格式：第1行为列名，第2行开始为数据")
            
            # 根据检测结果读取Excel文件
            if header_row == 2:
                # 真实模板格式：跳过前4行，从第5行开始读取数据
                df = pd.read_excel(BytesIO(file_content), header=header_row, skiprows=[3], engine='openpyxl')
            else:
                # 简化测试格式：第1行是header，第2行开始是数据
                df = pd.read_excel(BytesIO(file_content), header=header_row, engine='openpyxl')
            
            logger.info(f"Excel文件读取成功，header在第{header_row+1}行，共 {len(df)} 行数据")
            
            # 清理列名（移除空格和特殊字符）
            original_columns = df.columns.tolist()
            df.columns = [col.strip() if isinstance(col, str) else str(col).strip() for col in df.columns]
            cleaned_columns = df.columns.tolist()
            
            logger.info(f"原始列名: {original_columns}")
            logger.info(f"清理后列名: {cleaned_columns}")
            
            # 检查关键列是否存在
            required_columns = ['出行人姓名']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                logger.error(f"缺少必需的列: {missing_columns}")
                logger.error(f"可用列名: {list(df.columns)}")
                raise HTTPException(status_code=400, detail=f"Excel文件缺少必需的列: {missing_columns}")
            
        except Exception as e:
            logger.error(f"Excel文件读取失败: {str(e)}")
            raise HTTPException(status_code=400, detail=f"Excel文件格式错误: {str(e)}")
        
        if df.empty:
            logger.error("Excel文件为空或无有效数据")
            raise HTTPException(status_code=400, detail="Excel文件为空或无有效数据")
        
        # 直接解析Excel数据并创建订单，不创建project_task
        orders_created = 0
        orders_failed = 0
        orders_skipped_duplicate = 0
        failed_rows = []
        
        for index, row in df.iterrows():
            try:
                # 调试：记录正在处理的行数据
                traveler_name_raw = row.get('出行人姓名')
                traveler_name = safe_str(traveler_name_raw)
                id_number = safe_str(row.get('证件号码'))
                travel_date = parse_excel_date(row.get('出行日期'))
                train_number = safe_str(row.get('车次'))
                
                logger.debug(f"处理第 {index + 1} 行: 出行人姓名原始值='{traveler_name_raw}', 处理后='{traveler_name}', 证件号码='{id_number}', 出行日期='{travel_date}', 车次='{train_number}'")
                
                # 检查是否存在重复订单（基于证件号码+出行日期+车次）
                if id_number and travel_date and train_number:
                    is_duplicate = await check_duplicate_order(project_id, id_number, travel_date, train_number)
                    if is_duplicate:
                        logger.info(f"跳过重复数据：第 {index + 1} 行，证件号码 {id_number} + 出行日期 {travel_date} + 车次 {train_number} 已存在")
                        orders_skipped_duplicate += 1
                        continue
                
                # 解析手机号和国际区号
                mobile_phone, mobile_phone_country_code = parse_phone_with_country_code(
                    row.get('手机号'), 
                    row.get('手机号国际区号')
                )
                
                # 准备行数据用于验证
                row_data_for_validation = {
                    'traveler_full_name': traveler_name,
                    'traveler_surname': safe_str(row.get('出行人姓')),
                    'traveler_given_name': safe_str(row.get('出行人名')),
                    'nationality': safe_str(row.get('国籍')),
                    'gender': safe_str(row.get('性别')),
                    'birth_date': parse_excel_date(row.get('出生日期')),
                    'id_type': safe_str(row.get('证件类型')),
                    'id_number': id_number,
                    'id_expiry_date': parse_excel_date(row.get('证件有效期至')),
                    'mobile_phone': mobile_phone,
                    'travel_date': travel_date,
                    'departure_station': safe_str(row.get('出发站名')),
                    'arrival_station': safe_str(row.get('到达站名')),
                    'train_number': train_number,
                    'seat_type': safe_str(row.get('座位类型')),
                    'departure_time': parse_excel_time(row.get('出发时间')),
                    'arrival_time': parse_excel_time(row.get('到达时间')),
                    'cost_center': safe_str(row.get('成本中心')) or "",  # 确保成本中心为空字符串而不是None
                    'trip_submission_item': safe_str(row.get('行程提交项')),
                    'contact_person': safe_str(row.get('联系人')),
                    'contact_phone': safe_str(row.get('联系人手机号码')) or safe_str(row.get('联系人手机号')),
                    'contact_email': safe_str(row.get('联系人邮箱')),
                    'approval_reference': safe_str(row.get('审批参考人'))
                }

                # 自动补齐数据（在验证之前）
                logger.info(f"第 {index + 1} 行开始自动补齐数据")
                completed_data = auto_complete_train_order_data(row_data_for_validation)

                # 对补齐后的数据进行验证
                validation_errors = validate_train_order_data(completed_data, index + 1)
                
                # 根据验证结果设置订单状态和失败原因
                order_status = "initial"  # 默认状态
                fail_reason = None
                
                if validation_errors:
                    order_status = "check_failed"
                    fail_reason = "; ".join([f"{error.field}: {error.message}" for error in validation_errors])
                    logger.warning(f"第 {index + 1} 行验证失败: {fail_reason}")
                
                # 创建火车票订单（使用补齐后的数据）
                order_data = {
                    'project_id': project_id,
                    'sequence_number': safe_int(row.get('序号', index + 1)) or (index + 1),
                    'traveler_full_name': completed_data.get('traveler_full_name') or "",
                    'traveler_surname': completed_data.get('traveler_surname') or "",
                    'traveler_given_name': completed_data.get('traveler_given_name') or "",
                    'nationality': completed_data.get('nationality') or "",
                    'gender': completed_data.get('gender') or "",
                    'birth_date': completed_data.get('birth_date') or "",
                    'id_type': completed_data.get('id_type') or "",
                    'id_number': completed_data.get('id_number') or "",
                    'id_expiry_date': completed_data.get('id_expiry_date') or "",
                    'mobile_phone': mobile_phone,
                    'mobile_phone_country_code': mobile_phone_country_code,
                    'travel_date': parse_excel_date(row.get('出行日期')),
                    'departure_station': safe_str(row.get('出发站名')),
                    'arrival_station': safe_str(row.get('到达站名')),
                    'train_number': safe_str(row.get('车次')),
                    'seat_type': safe_str(row.get('座位类型')),
                    'departure_time': parse_excel_time(row.get('出发时间')),
                    'arrival_time': parse_excel_time(row.get('到达时间')),
                    'cost_center': safe_str(row.get('成本中心')) or "",  # 确保成本中心为空字符串而不是None
                    'trip_submission_item': safe_str(row.get('行程提交项')),
                    'contact_person': safe_str(row.get('联系人')),
                    'contact_phone': safe_str(row.get('联系人手机号码')) or safe_str(row.get('联系人手机号')),
                    'contact_email': safe_str(row.get('联系人邮箱')),
                    'approval_reference': safe_str(row.get('审批参考人')),
                    'company_name': safe_str(row.get('公司名称')),
                    'booking_agent': safe_str(row.get('代订人')),
                    'ticket_sms': safe_str(row.get('出票短信')),
                    'amount': safe_decimal(row.get('金额')),
                    'order_number': safe_str(row.get('订单号')),
                    'bill_number': safe_str(row.get('账单号')),
                    'order_status': order_status,
                    'fail_reason': fail_reason
                }
                
                await TrainOrder.create(**order_data)
                orders_created += 1
                
                if orders_created % 10 == 0:  # 每处理10条记录记录一次日志
                    logger.info(f"已处理 {orders_created} 条订单记录")
                
            except Exception as e:
                orders_failed += 1
                error_msg = f"第 {index + 1} 行数据处理失败: {str(e)}"
                logger.error(error_msg)
                failed_rows.append({
                    'row': index + 1,
                    'error': str(e),
                    'data': row.to_dict()
                })
                continue
        
        # 计算统计信息
        total_processed = orders_created + orders_failed + orders_skipped_duplicate
        success_rate = round(orders_created / total_processed * 100, 2) if total_processed > 0 else 0
        
        logger.info(f"Excel上传处理完成: 成功 {orders_created} 条，失败 {orders_failed} 条，跳过重复 {orders_skipped_duplicate} 条")
        
        # 构建响应消息
        if orders_created > 0:
            if orders_failed == 0 and orders_skipped_duplicate == 0:
                message = f"🎉 成功导入 {orders_created} 条火车票订单"
            elif orders_skipped_duplicate > 0:
                message = f"✅ 导入完成：成功导入 {orders_created} 条，跳过重复数据 {orders_skipped_duplicate} 条"
                if orders_failed > 0:
                    message += f"，失败 {orders_failed} 条"
                message += f"（成功率: {success_rate}%）"
            else:
                message = f"✅ 导入完成：成功 {orders_created} 条，失败 {orders_failed} 条（成功率: {success_rate}%）"
        else:
            if orders_skipped_duplicate > 0:
                message = f"⚠️ 所有数据均为重复数据，跳过 {orders_skipped_duplicate} 条记录"
            else:
                message = f"❌ 导入失败：{orders_failed} 条记录处理失败"
        
        response = ExcelUploadResponse(
            project_id=project_id,
            total_orders=orders_created,
            failed_orders=orders_failed,
            skipped_duplicate_orders=orders_skipped_duplicate,
            file_path=saved_file_path,
            message=message
        )
        
        # 如果有失败记录，记录详细信息（可选：返回给前端）
        if failed_rows and len(failed_rows) <= 5:  # 只返回前5个错误
            logger.warning(f"失败记录详情: {failed_rows}")
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        error_msg = f"Excel上传处理异常: {str(e)}"
        logger.error(error_msg)
        raise HTTPException(status_code=500, detail=error_msg)


@router.get("/project/{project_id}/stats", response_model=ProjectOrderStatsResponse, summary="获取项目订单统计")
async def get_project_order_stats(project_id: int):
    """获取指定项目的订单统计信息，包括总订单数、预定完成订单数和金额总和"""
    try:
        # 验证项目是否存在
        try:
            await Project.get(id=project_id)
        except DoesNotExist:
            raise HTTPException(status_code=404, detail="项目不存在")
        
        # 查询项目的所有订单（未删除的）
        all_orders_query = TrainOrder.filter(project_id=project_id, is_deleted=False)
        
        # 获取总订单数
        total_orders = await all_orders_query.count()
        
        # 查询预定完成的订单
        completed_orders_query = TrainOrder.filter(
            project_id=project_id, 
            is_deleted=False,
            order_status='completed'
        )
        
        # 获取预定完成订单数
        completed_orders = await completed_orders_query.count()
        
        # 计算预定完成订单的金额总和
        completed_orders_list = await completed_orders_query.all()
        completed_amount = sum(order.amount or 0 for order in completed_orders_list)
        
        logger.info(f"项目 {project_id} 订单统计 - 总订单数: {total_orders}, 预定完成: {completed_orders}, 完成金额: {completed_amount}")
        
        return ProjectOrderStatsResponse(
            project_id=project_id,
            total_orders=total_orders,
            completed_orders=completed_orders,
            completed_amount=completed_amount
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取项目订单统计失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取项目订单统计失败: {str(e)}")


@router.get("/project/{project_id}/detail-stats", response_model=ProjectOrderDetailStatsResponse, summary="获取项目订单详细统计")
async def get_project_order_detail_stats(project_id: int):
    """获取指定项目的详细订单统计信息，包括各状态订单数量和金额统计"""
    try:
        # 验证项目是否存在
        try:
            await Project.get(id=project_id)
        except DoesNotExist:
            raise HTTPException(status_code=404, detail="项目不存在")
        
        # 查询项目的所有订单（未删除的）
        all_orders_query = TrainOrder.filter(project_id=project_id, is_deleted=False)
        all_orders = await all_orders_query.all()
        
        # 统计各状态订单数量
        total_orders = len(all_orders)
        check_failed_orders = len([o for o in all_orders if o.order_status == 'check_failed'])
        initial_orders = len([o for o in all_orders if o.order_status == 'initial'])
        submitted_orders = len([o for o in all_orders if o.order_status == 'submitted'])
        processing_orders = len([o for o in all_orders if o.order_status == 'processing'])
        completed_orders = len([o for o in all_orders if o.order_status == 'completed'])
        failed_orders = len([o for o in all_orders if o.order_status == 'failed'])
        
        # 计算金额统计
        completed_amount = sum(order.amount or 0 for order in all_orders if order.order_status == 'completed')
        total_amount = sum(order.amount or 0 for order in all_orders)
        
        logger.info(f"项目 {project_id} 详细统计 - 总订单: {total_orders}, 验证失败: {check_failed_orders}, 待提交: {initial_orders}, 已提交: {submitted_orders}, 处理中: {processing_orders}, 预定完成: {completed_orders}, 预定失败: {failed_orders}")
        
        return ProjectOrderDetailStatsResponse(
            project_id=project_id,
            total_orders=total_orders,
            check_failed_orders=check_failed_orders,
            initial_orders=initial_orders,
            submitted_orders=submitted_orders,
            processing_orders=processing_orders,
            completed_orders=completed_orders,
            failed_orders=failed_orders,
            completed_amount=completed_amount,
            total_amount=total_amount
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取项目订单详细统计失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取项目订单详细统计失败: {str(e)}")


@router.get("/project/{project_id}", response_model=TrainOrderListResponse, summary="获取项目的火车票订单")
async def get_orders_by_project(
    project_id: int,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    order_status: Optional[str] = Query(None, description="订单状态筛选，支持多个状态用逗号分隔，如：submitted,processing"),
    traveler_name: Optional[str] = Query(None, description="出行人姓名搜索"),
    mobile_phone: Optional[str] = Query(None, description="手机号搜索"),
    contact_phone: Optional[str] = Query(None, description="联系人手机号搜索"),
    sort_by_failed_first: Optional[bool] = Query(False, description="是否将验证失败和预定失败的订单排在前面")
):
    """获取指定项目的火车票订单，支持按出行人姓名、手机号、联系人手机号搜索"""
    try:
        # 验证项目是否存在
        try:
            await Project.get(id=project_id)
        except DoesNotExist:
            raise HTTPException(status_code=404, detail="项目不存在")
        
        # 记录搜索参数
        logger.info(f"搜索参数 - project_id: {project_id}, order_status: {order_status}, traveler_name: {traveler_name}, mobile_phone: {mobile_phone}, contact_phone: {contact_phone}")
        
        # 查询订单
        query = TrainOrder.filter(project_id=project_id, is_deleted=False)
        
        # 处理状态筛选 - 支持多状态
        if order_status and order_status.strip():
            # 将逗号分隔的状态字符串转换为列表
            status_list = [status.strip() for status in order_status.split(',') if status.strip()]
            if status_list:
                query = query.filter(order_status__in=status_list)
                logger.info(f"添加状态筛选条件: {status_list}")
        
        # 添加搜索条件
        if traveler_name:
            query = query.filter(traveler_full_name__icontains=traveler_name)
            logger.info(f"添加出行人姓名搜索条件: {traveler_name}")
        
        if mobile_phone:
            query = query.filter(mobile_phone__icontains=mobile_phone)
            logger.info(f"添加手机号搜索条件: {mobile_phone}")
        
        if contact_phone:
            query = query.filter(contact_phone__icontains=contact_phone)
            logger.info(f"添加联系人手机号搜索条件: {contact_phone}")
        
        # 获取总数
        total = await query.count()
        logger.info(f"搜索结果总数: {total}")
        
        # 应用排序和分页
        if sort_by_failed_first:
            # 分别查询失败订单和其他订单，然后合并
            failed_query = query.filter(order_status__in=['check_failed', 'failed']).order_by("-id")
            other_query = query.exclude(order_status__in=['check_failed', 'failed']).order_by("-id")
            
            # 获取失败订单和其他订单的数量
            failed_count = await failed_query.count()
            
            offset = (page - 1) * page_size
            orders = []
            
            if offset < failed_count:
                # 当前页包含失败订单
                failed_orders = await failed_query.offset(offset).limit(page_size)
                orders.extend(failed_orders)
                
                # 如果当前页还有空间，补充其他订单
                remaining_slots = page_size - len(failed_orders)
                if remaining_slots > 0:
                    other_offset = max(0, offset + len(failed_orders) - failed_count)
                    other_orders = await other_query.offset(other_offset).limit(remaining_slots)
                    orders.extend(other_orders)
            else:
                # 当前页只包含其他订单
                other_offset = offset - failed_count
                other_orders = await other_query.offset(other_offset).limit(page_size)
                orders.extend(other_orders)
            
            logger.info(f"已应用失败订单优先排序 - 失败订单数: {failed_count}, 当前页订单数: {len(orders)}")
        else:
            # 默认按ID降序排序，使用数据库排序
            query = query.order_by("-id")
            offset = (page - 1) * page_size
            orders = await query.offset(offset).limit(page_size)
        
        # 导入TaskToTrainOrder模型以获取order_type
        from src.db.models.task_to_train_order import TaskToTrainOrder
        
        # 转换为响应模型
        order_responses = []
        for order in orders:
            # 获取订单的预定类型
            task_mapping = await TaskToTrainOrder.filter(order_id=order.id).first()
            order_type = task_mapping.order_type if task_mapping else None
            
            # 创建订单响应，包含order_type字段
            order_data = order.__dict__.copy()
            order_data['order_type'] = order_type
            
            order_response = TrainOrderResponse.model_validate(order_data)
            order_responses.append(order_response)
        
        logger.info(f"返回订单数量: {len(order_responses)}")
        
        return TrainOrderListResponse(
            total=total,
            page=page,
            page_size=page_size,
            items=order_responses
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查询项目订单失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"查询项目订单失败: {str(e)}")


@router.post("/", response_model=TrainOrderResponse, summary="创建单个订单")
async def create_order(
    order_data: TrainOrderCreate,
    current_user: User = Depends(get_current_user)
):
    """创建单个火车票订单"""
    try:
        logger.info(f"开始创建单个订单，用户: {current_user.username}")

        # 验证项目是否存在
        if order_data.project_id:
            try:
                await Project.get(id=order_data.project_id)
            except DoesNotExist:
                raise HTTPException(status_code=404, detail="项目不存在")

        # 创建订单数据
        order_dict = order_data.model_dump(exclude_unset=True)

        # 设置默认值
        if 'order_status' not in order_dict:
            order_dict['order_status'] = 'initial'
        if 'is_deleted' not in order_dict:
            order_dict['is_deleted'] = False

        # 创建订单
        order = await TrainOrder.create(**order_dict)

        logger.info(f"成功创建订单: {order.id}")
        return TrainOrderResponse.model_validate(order)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建订单失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建订单失败: {str(e)}")


@router.get("/check-duplicate", response_model=dict, summary="检查重复订单")
async def check_duplicate_order_endpoint(
    project_id: int = Query(..., description="项目ID"),
    id_number: str = Query(..., description="证件号码"),
    travel_date: str = Query(..., description="出行日期"),
    train_number: str = Query(..., description="车次"),
    exclude_id: Optional[int] = Query(None, description="排除的订单ID（编辑时使用）")
):
    """检查是否存在重复订单（基于证件号码+出行日期+车次）"""
    try:
        is_duplicate = await check_duplicate_order(project_id, id_number, travel_date, train_number, exclude_id)
        return {"isDuplicate": is_duplicate}
    except Exception as e:
        logger.error(f"检查重复订单失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"检查重复订单失败: {str(e)}")


@router.get("/{order_id}", response_model=TrainOrderResponse, summary="获取订单详情")
async def get_order(order_id: int):
    """获取火车票订单详情"""
    try:
        order = await TrainOrder.get(id=order_id)
        return TrainOrderResponse.model_validate(order)
    except DoesNotExist:
        raise HTTPException(status_code=404, detail="订单不存在")


@router.put("/{order_id}", response_model=TrainOrderResponse, summary="更新订单")
async def update_order(order_id: int, order_data: TrainOrderUpdate):
    """更新火车票订单"""
    try:
        order = await TrainOrder.get(id=order_id)
        
        # 更新字段
        update_data = order_data.model_dump(exclude_unset=True)
        
        if update_data:
            await order.update_from_dict(update_data)
            
            # 数据验证逻辑：如果更新了关键字段，进行验证
            if any(key in update_data for key in ['traveler_full_name', 'id_type', 'id_number', 'mobile_phone', 
                                                  'travel_date', 'departure_station', 'arrival_station', 
                                                  'train_number', 'seat_type', 'departure_time', 'arrival_time',
                                                  'cost_center', 'contact_person', 'contact_phone', 'approval_reference']):
                
                # 构建验证数据
                validation_data = {
                    'traveler_full_name': order.traveler_full_name,
                    'traveler_surname': order.traveler_surname,
                    'traveler_given_name': order.traveler_given_name,
                    'nationality': order.nationality,
                    'gender': order.gender,
                    'birth_date': order.birth_date,
                    'id_type': order.id_type,
                    'id_number': order.id_number,
                    'id_expiry_date': order.id_expiry_date,
                    'mobile_phone': order.mobile_phone,
                    'mobile_phone_country_code': order.mobile_phone_country_code,
                    'travel_date': order.travel_date,
                    'departure_station': order.departure_station,
                    'arrival_station': order.arrival_station,
                    'train_number': order.train_number,
                    'seat_type': order.seat_type,
                    'departure_time': order.departure_time,
                    'arrival_time': order.arrival_time,
                    'cost_center': order.cost_center,
                    'trip_submission_item': order.trip_submission_item,
                    'contact_person': order.contact_person,
                    'contact_phone': order.contact_phone,
                    'contact_email': order.contact_email,
                    'approval_reference': order.approval_reference,
                }
                
                # 执行验证
                validation_errors = validate_train_order_data(validation_data, 1)
                
                if validation_errors:
                    # 验证失败，设置状态和失败原因
                    order.order_status = "check_failed"
                    order.fail_reason = "; ".join([f"{error.field}: {error.message}" for error in validation_errors])
                    logger.warning(f"订单 {order_id} 验证失败: {order.fail_reason}")
                else:
                    # 验证通过，设置状态为待提交并清空失败原因
                    order.order_status = "initial"
                    order.fail_reason = None
                    logger.info(f"订单 {order_id} 验证通过，状态已改为待提交")
            
            await order.save()
        
        # 重新获取更新后的订单
        updated_order = await TrainOrder.get(id=order_id)
        return TrainOrderResponse.model_validate(updated_order)
        
    except DoesNotExist:
        raise HTTPException(status_code=404, detail="订单不存在")
    except Exception as e:
        logger.error(f"更新订单失败: {str(e)}")
        raise HTTPException(status_code=400, detail=f"更新订单失败: {str(e)}")


@router.delete("/{order_id}", summary="删除订单")
async def delete_order(order_id: int):
    """软删除火车票订单"""
    try:
        order = await TrainOrder.get(id=order_id)
        await order.delete()
        return JSONResponse(content={"message": "订单删除成功"})
        
    except DoesNotExist:
        raise HTTPException(status_code=404, detail="订单不存在")
    except Exception as e:
        logger.error(f"删除订单失败: {str(e)}")
        raise HTTPException(status_code=400, detail=f"删除订单失败: {str(e)}")


@router.delete("/project/{project_id}/clear-initial", response_model=ClearOrdersResponse, summary="清空项目中可清空状态的火车票订单")
async def clear_initial_orders(project_id: int):
    """清空指定项目中可清空状态的所有火车票订单（包括initial、check_failed、failed状态）"""
    try:
        # 验证项目是否存在
        try:
            await Project.get(id=project_id)
        except DoesNotExist:
            raise HTTPException(status_code=404, detail="项目不存在")

        # 查询可清空状态的订单：initial（待提交）、check_failed（验证失败）、failed（预订失败）
        clearable_orders = await TrainOrder.filter(
            project_id=project_id,
            order_status__in=['initial', 'check_failed', 'failed'],
            is_deleted=False
        ).all()

        deleted_count = len(clearable_orders)

        if deleted_count == 0:
            return ClearOrdersResponse(
                deleted_count=0,
                message="暂无可清空的订单"
            )

        # 统计各状态的订单数量
        status_counts = {}
        for order in clearable_orders:
            status = order.order_status
            status_counts[status] = status_counts.get(status, 0) + 1

        # 软删除订单
        for order in clearable_orders:
            order.is_deleted = True
            await order.save()

        # 构建详细的删除信息
        status_messages = []
        if status_counts.get('initial', 0) > 0:
            status_messages.append(f"{status_counts['initial']} 条待提交订单")
        if status_counts.get('check_failed', 0) > 0:
            status_messages.append(f"{status_counts['check_failed']} 条验证失败订单")
        if status_counts.get('failed', 0) > 0:
            status_messages.append(f"{status_counts['failed']} 条预订失败订单")

        detail_message = "、".join(status_messages)

        logger.info(f"已清空项目 {project_id} 中 {deleted_count} 条订单：{detail_message}")

        return ClearOrdersResponse(
            deleted_count=deleted_count,
            message=f"成功清空 {deleted_count} 条订单（{detail_message}）"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"清空订单失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"清空订单失败: {str(e)}")


@router.post("/project/{project_id}/book", response_model=BookingResponse, summary="预订火车票订单")
async def book_orders(
    project_id: int,
    booking_data: BookingRequest,
    current_user: User = Depends(get_current_user)
):
    """
    预订火车票订单
    - booking_type: 'book' 仅预订, 'book_and_issue' 预订且出票
    - orders: 订单ID列表
    - sms_notify: 是否短信通知
    - has_agent: 是否有代订人
    - agent_phone: 代订人手机号码
    """
    try:
        # 验证项目是否存在
        try:
            project = await Project.get(id=project_id)
        except DoesNotExist:
            raise HTTPException(status_code=404, detail="项目不存在")
        
        # 验证订单是否存在且属于该项目
        orders = await TrainOrder.filter(
            id__in=booking_data.orders,
            project_id=project_id,
            is_deleted=False
        ).all()
        
        if len(orders) != len(booking_data.orders):
            raise HTTPException(status_code=400, detail="部分订单不存在或不属于该项目")
        
        # 检查是否有代订人但没有提供手机号
        if booking_data.has_agent and not booking_data.agent_phone:
            raise HTTPException(status_code=400, detail="选择有代订人时必须提供代订人手机号码")
        
        # 更新项目任务信息（如果存在）
        try:
            # 查找该项目的火车票任务
            task = await ProjectTask.filter(
                project_id=project_id,
                task_type="train_booking"
            ).first()
            
            if task:
                # 更新任务的短信通知和代订人信息
                task.sms_notify = booking_data.sms_notify or False
                task.has_agent = booking_data.has_agent or False
                task.agent_phone = booking_data.agent_phone if booking_data.has_agent else None
                await task.save()
                logger.info(f"已更新任务 {task.task_id} 的短信通知和代订人设置")
        except Exception as e:
            logger.warning(f"更新任务信息失败: {str(e)}")
        
        processed_count = len(orders)
        success_count = 0
        failed_count = 0
        
        # 根据预订类型设置订单状态
        target_status = "submitted" if booking_data.booking_type == "book_only" else "processing"
        
        # 处理每个订单
        for order in orders:
            try:
                # 更新订单状态
                order.order_status = target_status

                # 自动保存项目客户名称到订单的公司名称字段
                if project.client_name and project.client_name.strip():
                    order.company_name = project.client_name
                    logger.info(f"订单 {order.id} 自动保存项目客户名称: {project.client_name}")

                # 如果有代订人信息，更新到订单中
                if booking_data.has_agent and booking_data.agent_phone:
                    order.booking_agent = f"代订人: {booking_data.agent_phone}"

                # 如果需要短信通知，更新票务短信字段
                if booking_data.sms_notify:
                    order.ticket_sms = "是"

                await order.save()
                success_count += 1

                logger.info(f"订单 {order.id} 状态已更新为 {target_status}")

            except Exception as e:
                failed_count += 1
                logger.error(f"处理订单 {order.id} 失败: {str(e)}")
        
        # 生成响应消息
        if booking_data.booking_type == "book":
            action = "预订"
        else:
            action = "预订且出票"
        
        message = f"成功{action} {success_count} 条订单"
        if failed_count > 0:
            message += f"，{failed_count} 条订单处理失败"
        
        # 添加设置信息到消息中
        settings_info = []
        if booking_data.sms_notify:
            settings_info.append("短信通知已启用")
        if booking_data.has_agent and booking_data.agent_phone:
            settings_info.append(f"代订人: {booking_data.agent_phone}")
        
        if settings_info:
            message += f"（{', '.join(settings_info)}）"
        
        logger.info(f"项目 {project_id} 预订完成: {message}")
        
        return BookingResponse(
            processed_count=processed_count,
            success_count=success_count,
            failed_count=failed_count,
            message=message
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"预订订单失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"预订订单失败: {str(e)}")


@router.post("/project/{project_id}/create-booking-task", response_model=CreateBookingTaskResponse, summary="创建火车票预订任务")
async def create_booking_task(
    project_id: int,
    task_data: CreateBookingTaskRequest,
    current_user: User = Depends(get_current_user)
):
    """
    创建火车票预订任务，并将所有未提交的订单提交到该任务中
    
    - 创建project_tasks表中的预订任务
    - 将project_id下状态为initial的所有订单提交到该任务
    - 在task_to_train_orders表中创建关联记录
    - 更新train_orders表中的order_status为submitted
    
    流程:
    1. 验证项目存在
    2. 验证代订人信息
    3. 获取项目中所有待提交订单
    4. 创建项目任务
    5. 创建任务订单映射关系
    6. 更新订单状态
    """
    try:
        # 验证项目是否存在
        try:
            project = await Project.get(id=project_id)
        except DoesNotExist:
            raise HTTPException(status_code=404, detail="项目不存在")
        
        # 验证代订人信息
        if task_data.has_agent and not task_data.agent_phone:
            raise HTTPException(status_code=400, detail="选择有代订人时必须提供代订人手机号码")
        
        # 获取要提交的订单
        if task_data.order_ids:
            # 如果指定了order_ids，只处理这些订单，但只接受initial状态的订单
            initial_orders = await TrainOrder.filter(
                id__in=task_data.order_ids,
                project_id=project_id,
                order_status='initial',  # 只接受待提交状态的订单
                is_deleted=False
            ).all()

            # 检查是否有非initial状态的订单被指定
            all_specified_orders = await TrainOrder.filter(
                id__in=task_data.order_ids,
                project_id=project_id,
                is_deleted=False
            ).all()

            non_initial_orders = [order for order in all_specified_orders if order.order_status != 'initial']
            if non_initial_orders:
                non_initial_statuses = list(set([order.order_status for order in non_initial_orders]))
                status_names = {
                    'check_failed': '验证失败',
                    'failed': '预订失败',
                    'submitted': '已提交',
                    'processing': '处理中',
                    'completed': '已完成'
                }
                status_desc = '、'.join([status_names.get(status, status) for status in non_initial_statuses])
                raise HTTPException(
                    status_code=400,
                    detail=f"指定的订单中包含非待提交状态的订单（{status_desc}），只能提交状态为待提交的订单"
                )

            if len(initial_orders) == 0:
                raise HTTPException(status_code=400, detail="指定的订单中没有可提交的火车票订单（状态为待提交）")
        else:
            # 如果没有指定order_ids，获取项目中所有状态为initial的订单
            initial_orders = await TrainOrder.filter(
                project_id=project_id,
                order_status='initial',
                is_deleted=False
            ).all()

            if len(initial_orders) == 0:
                raise HTTPException(status_code=400, detail="该项目暂无待提交的火车票订单")
        
        # 创建项目任务
        task = await ProjectTask.create(
            project_id=project_id,
            creator_user_id=current_user.id,
            creator_name=current_user.username,
            task_type="火车票预订",
            task_title=task_data.task_title,
            task_description=task_data.task_description,
            task_status="submitted",
            sms_notify=task_data.sms_notify,
            has_agent=task_data.has_agent,
            agent_phone=task_data.agent_phone if task_data.has_agent else None,
            agent_name=task_data.agent_name if task_data.has_agent else ""
        )
        
        logger.info(f"已创建预订任务: {task.task_id}, 项目: {project_id}, 创建人: {current_user.username}")
        
        # 导入TaskToTrainOrder模型
        from src.db.models.task_to_train_order import TaskToTrainOrder
        
        submitted_count = 0
        failed_count = 0
        
        # 处理每个订单
        for order in initial_orders:
            try:
                # 根据预订类型设置order_type
                order_type = "book" if task_data.booking_type == "book" else "book_and_issue"

                # 创建任务订单映射关系
                from datetime import datetime
                current_time = datetime.now()
                await TaskToTrainOrder.create(
                    project_id=project_id,
                    task_id=task.task_id,
                    order_id=order.id,
                    order_status="submitted",
                    order_type=order_type,
                    start_time=current_time,
                    end_time=current_time
                )

                # 更新订单状态为submitted
                order.order_status = "submitted"

                # 自动保存项目客户名称到订单的公司名称字段
                if project.client_name and project.client_name.strip():
                    order.company_name = project.client_name
                    logger.info(f"订单 {order.id} 自动保存项目客户名称: {project.client_name}")

                # 如果有代订人姓名，保存到订单的booking_agent字段
                if task_data.has_agent and task_data.agent_name and task_data.agent_name.strip():
                    order.booking_agent = task_data.agent_name
                    logger.info(f"订单 {order.id} 自动保存代订人姓名: {task_data.agent_name}")

                await order.save()

                submitted_count += 1
                logger.info(f"订单 {order.id} 已提交到任务 {task.task_id}")

            except Exception as e:
                failed_count += 1
                logger.error(f"处理订单 {order.id} 失败: {str(e)}")
        
        # 生成响应消息
        if task_data.booking_type == "book_only":
            action_type = "预订任务"
        else:
            action_type = "预订且出票任务"
        
        message = f"成功创建{action_type}：{task.task_id}，已提交 {submitted_count} 条订单"
        if failed_count > 0:
            message += f"，{failed_count} 条订单处理失败"
        
        # 添加设置信息
        settings_info = []
        if task_data.sms_notify:
            settings_info.append("已启用短信通知")
        if task_data.has_agent and task_data.agent_phone:
            settings_info.append(f"代订人: {task_data.agent_phone}")
        
        if settings_info:
            message += f"（{', '.join(settings_info)}）"
        
        logger.info(f"预订任务创建完成: {message}")
        
        # 发送Kafka消息通知后端开始预订任务
        try:
            # 获取用户的系统设置（同程管家凭证）
            from src.services.system_settings_service import SystemSettingsService
            credentials = await SystemSettingsService.get_tongcheng_credentials(current_user.id)
            # 获取原始密码用于存储到消息中
            credentials_with_raw_password = await SystemSettingsService.get_tongcheng_credentials_with_raw_password(current_user.id)
            
            if credentials['username'] and credentials['password']:
                # 导入Kafka服务
                from src.services.kafka_service import send_train_booking_task
                
                # 获取所有已提交的订单ID
                submitted_order_ids = [order.id for order in initial_orders]
                
                # 获取当前登录用户信息
                login_user_name = current_user.username if current_user else ""

                # 发送Kafka消息 - 使用原始密文
                kafka_result = await send_train_booking_task(
                    task_id=task.task_id,
                    order_ids=submitted_order_ids,
                    username=credentials['username'],
                    password=credentials_with_raw_password['password'],  # 使用数据库原始密文
                    company_name=project.client_name,  # 从项目的client_name字段获取公司名称
                    send_sms=task_data.sms_notify,
                    has_agent=task_data.has_agent,
                    agent_phone=task_data.agent_phone if task_data.has_agent else None,
                    agent_name=task_data.agent_name if task_data.has_agent else "",
                    login_user_name=login_user_name
                )
                
                logger.info(
                    f"Kafka消息发送完成 - Task: {task.task_id}, "
                    f"成功: {kafka_result.get('success_count', 0)}, "
                    f"失败: {kafka_result.get('failed_count', 0)}, "
                    f"启用: {kafka_result.get('enabled', False)}"
                )
                
                # 将Kafka发送结果添加到响应消息中
                if kafka_result.get('enabled', False):
                    kafka_success = kafka_result.get('success_count', 0)
                    kafka_failed = kafka_result.get('failed_count', 0)
                    if kafka_success > 0:
                        message += f"，已推送{kafka_success}条任务消息到处理队列"
                        
                        # 推送成功时，将消息内容存储到TaskToTrainOrder的message字段中
                        try:
                            import json
                            # 构建推送消息内容（与Kafka服务中的格式保持一致）
                            # 注意：存储的password使用数据库原始值，不是解密后的值
                            kafka_message_template = {
                                "task_id": task.task_id,
                                "module": "train_ticket_booking",
                                "username": credentials['username'],
                                "password": credentials_with_raw_password['password'],  # 使用数据库中的原始密码值
                                "company_name": project.client_name,  # 从项目的client_name字段获取公司名称
                                "send_sms": 1 if task_data.sms_notify else 0,
                                "has_agent": 1 if task_data.has_agent else 0,
                                "agent_phone": task_data.agent_phone if task_data.has_agent else "",
                                "agent_name": task_data.agent_name if task_data.has_agent else "",
                                "login_user_name": login_user_name
                            }
                            
                            # 为每个成功推送的订单更新message字段
                            for order in initial_orders:
                                try:
                                    # 获取对应的TaskToTrainOrder记录
                                    task_order = await TaskToTrainOrder.get(
                                        task_id=task.task_id,
                                        order_id=order.id
                                    )
                                    
                                    # 构建该订单的完整Kafka消息内容
                                    order_kafka_message = kafka_message_template.copy()
                                    order_kafka_message["order_id"] = order.id
                                    
                                    # 将消息内容JSON格式存储到message字段
                                    task_order.message = json.dumps(order_kafka_message, ensure_ascii=False, indent=2)
                                    await task_order.save()
                                    
                                    logger.info(f"已将Kafka消息内容保存到TaskToTrainOrder - Task: {task.task_id}, Order: {order.id}")
                                    
                                except Exception as save_error:
                                    logger.error(f"保存Kafka消息内容失败 - Task: {task.task_id}, Order: {order.id}: {str(save_error)}")
                                    
                        except Exception as store_error:
                            logger.error(f"存储Kafka消息内容时发生异常: {str(store_error)}")
                    
                    if kafka_failed > 0:
                        message += f"，{kafka_failed}条消息推送失败"
                else:
                    message += "，Kafka消息推送未启用"
            else:
                logger.warning(f"用户 {current_user.id} 未配置同程管家凭证，跳过Kafka消息发送")
                message += "，未配置同程管家凭证，请先在系统设置中配置"
                
        except Exception as e:
            logger.error(f"发送Kafka消息失败: {str(e)}")
            message += f"，任务消息推送失败: {str(e)}"
        
        return CreateBookingTaskResponse(
            task_id=task.task_id,
            project_id=project_id,
            task_title=task.task_title,
            submitted_orders_count=submitted_count,
            message=message
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建预订任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建预订任务失败: {str(e)}")


@router.get("/task/{task_id}", response_model=TrainOrderListResponse, summary="获取任务的火车票订单")
async def get_orders_by_task(
    task_id: str,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    status: Optional[str] = Query(None, description="订单状态筛选"),
    traveler_name: Optional[str] = Query(None, description="出行人姓名搜索"),
    mobile_phone: Optional[str] = Query(None, description="手机号搜索"),
    contact_phone: Optional[str] = Query(None, description="联系人手机号搜索"),
    current_user: User = Depends(get_current_user)
):
    """
    根据任务ID获取该任务相关的火车票订单
    通过task_to_train_orders表关联查询
    """
    try:
        # 验证任务是否存在
        try:
            task = await ProjectTask.get(task_id=task_id)
        except DoesNotExist:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        # 导入TaskToTrainOrder模型
        from src.db.models.task_to_train_order import TaskToTrainOrder
        
        # 通过task_to_train_orders表获取关联的订单ID
        task_mappings = await TaskToTrainOrder.filter(task_id=task_id).all()
        order_ids = [mapping.order_id for mapping in task_mappings]
        
        if not order_ids:
            # 如果没有关联的订单，返回空列表
            return TrainOrderListResponse(
                total=0,
                page=page,
                page_size=page_size,
                items=[]
            )
        
        # 构建查询条件
        query = TrainOrder.filter(
            id__in=order_ids,
            is_deleted=False
        )
        
        # 应用筛选条件
        if status:
            query = query.filter(order_status=status)
        if traveler_name:
            query = query.filter(traveler_full_name__icontains=traveler_name)
        if mobile_phone:
            query = query.filter(mobile_phone__icontains=mobile_phone)
        if contact_phone:
            query = query.filter(contact_phone__icontains=contact_phone)
        
        # 获取总数
        total = await query.count()
        
        # 分页查询
        offset = (page - 1) * page_size
        orders = await query.offset(offset).limit(page_size).order_by('-created_at')
        
        # 转换为响应格式
        order_responses = []
        for order in orders:
            try:
                # 获取订单的预定类型
                task_mapping = await TaskToTrainOrder.filter(order_id=order.id, task_id=task_id).first()
                order_type = task_mapping.order_type if task_mapping else None
                
                # 创建订单响应，包含order_type字段
                order_data = order.__dict__.copy()
                order_data['order_type'] = order_type
                
                order_response = TrainOrderResponse.model_validate(order_data)
                order_responses.append(order_response)
            except Exception as e:
                logger.error(f"转换订单数据失败: {str(e)}, 订单ID: {order.id}")
                continue
        
        logger.info(f"获取任务 {task_id} 的订单列表成功，共 {total} 条记录")
        
        return TrainOrderListResponse(
            total=total,
            page=page,
            page_size=page_size,
            items=order_responses
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务订单列表失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "message": "获取任务订单列表失败",
                "error": str(e)
            }
        )


@router.get("/task/{task_id}/stats", response_model=TaskOrderStatsResponse, summary="获取任务订单统计")
async def get_task_order_stats(task_id: str):
    """
    获取指定任务的订单统计信息
    包括各个状态的订单数量和总金额
    """
    try:
        # 验证任务是否存在
        try:
            task = await ProjectTask.get(task_id=task_id)
        except DoesNotExist:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        # 导入TaskToTrainOrder模型
        from src.db.models.task_to_train_order import TaskToTrainOrder
        
        # 通过task_to_train_orders表获取关联的订单ID
        task_mappings = await TaskToTrainOrder.filter(task_id=task_id).all()
        order_ids = [mapping.order_id for mapping in task_mappings]
        
        if not order_ids:
            # 如果没有关联的订单，返回空统计
            return TaskOrderStatsResponse(
                task_id=task_id,
                order_count=0,
                submitted_orders=0,
                processing_orders=0,
                completed_orders=0,
                failed_orders=0,
                total_amount=0,
                total_people=0
            )
        
        # 获取关联的订单
        orders = await TrainOrder.filter(
            id__in=order_ids,
            is_deleted=False
        ).all()
        
        # 统计各个状态的订单数量
        order_count = len(orders)
        submitted_orders = len([o for o in orders if o.order_status == 'submitted'])
        processing_orders = len([o for o in orders if o.order_status == 'processing'])
        completed_orders = len([o for o in orders if o.order_status == 'completed'])
        failed_orders = len([o for o in orders if o.order_status == 'failed'])
        
        # 计算总金额和总人数
        total_amount = sum(order.amount or 0 for order in orders)
        total_people = order_count  # 每个订单代表一个人
        
        logger.info(f"获取任务 {task_id} 统计信息成功：订单{order_count}个，已提交{submitted_orders}，处理中{processing_orders}，已完成{completed_orders}，失败{failed_orders}")
        
        return TaskOrderStatsResponse(
            task_id=task_id,
            order_count=order_count,
            submitted_orders=submitted_orders,
            processing_orders=processing_orders,
            completed_orders=completed_orders,
            failed_orders=failed_orders,
            total_amount=total_amount,
            total_people=total_people
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务统计信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取任务统计信息失败: {str(e)}")


@router.post("/project/{project_id}/pause-submitted", response_model=PauseOrdersResponse, summary="暂停已提交的火车票订单")
async def pause_submitted_orders(
    project_id: int,
    pause_data: PauseOrdersRequest,
    current_user: User = Depends(get_current_user)
):
    """
    暂停指定项目中已提交状态的火车票订单

    - 如果指定了order_ids，则只暂停指定的订单（必须是submitted状态）
    - 如果未指定order_ids，则暂停项目中所有submitted状态的订单
    """
    try:
        # 验证项目是否存在
        try:
            project = await Project.get(id=project_id)
        except DoesNotExist:
            raise HTTPException(status_code=404, detail="项目不存在")

        # 构建查询条件
        query_conditions = {
            'project_id': project_id,
            'is_deleted': False,
            'order_status': 'submitted'
        }

        # 如果指定了订单ID，添加ID筛选条件
        if pause_data.order_ids:
            query_conditions['id__in'] = pause_data.order_ids

        # 查询符合条件的订单
        orders = await TrainOrder.filter(**query_conditions).all()

        if not orders:
            message = "没有找到可暂停的已提交订单"
            if pause_data.order_ids:
                message = f"指定的订单中没有找到可暂停的已提交订单"

            return PauseOrdersResponse(
                paused_count=0,
                message=message
            )

        # 更新订单状态为paused
        paused_count = 0
        for order in orders:
            order.order_status = 'paused'
            await order.save()
            paused_count += 1
            logger.info(f"订单 {order.id} 已暂停处理，操作人: {current_user.username}")

        message = f"成功暂停 {paused_count} 条已提交订单"
        logger.info(f"项目 {project_id} 暂停订单操作完成: {message}，操作人: {current_user.username}")

        return PauseOrdersResponse(
            paused_count=paused_count,
            message=message
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"暂停订单失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"暂停订单失败: {str(e)}")


@router.post("/project/{project_id}/reset-paused", response_model=ResetPausedOrdersResponse, summary="重置已暂停的火车票订单")
async def reset_paused_orders(
    project_id: int,
    reset_data: ResetPausedOrdersRequest,
    current_user: User = Depends(get_current_user)
):
    """
    重置指定项目中已暂停状态的火车票订单为待提交状态

    - 如果指定了order_ids，则只重置指定的订单（必须是paused状态）
    - 如果未指定order_ids，则重置项目中所有paused状态的订单
    """
    try:
        # 验证项目是否存在
        try:
            project = await Project.get(id=project_id)
        except DoesNotExist:
            raise HTTPException(status_code=404, detail="项目不存在")

        # 构建查询条件
        query_conditions = {
            'project_id': project_id,
            'is_deleted': False,
            'order_status': 'paused'
        }

        # 如果指定了订单ID，添加ID筛选条件
        if reset_data.order_ids:
            query_conditions['id__in'] = reset_data.order_ids

        # 查询符合条件的订单
        orders = await TrainOrder.filter(**query_conditions).all()

        if not orders:
            message = "没有找到可重置的已暂停订单"
            if reset_data.order_ids:
                message = f"指定的订单中没有找到可重置的已暂停订单"

            return ResetPausedOrdersResponse(
                reset_count=0,
                message=message
            )

        # 更新订单状态为initial
        reset_count = 0
        for order in orders:
            order.order_status = 'initial'
            await order.save()
            reset_count += 1
            logger.info(f"订单 {order.id} 已重置为待提交状态，操作人: {current_user.username}")

        message = f"成功重置 {reset_count} 条已暂停订单为待提交状态"
        logger.info(f"项目 {project_id} 重置订单操作完成: {message}，操作人: {current_user.username}")

        return ResetPausedOrdersResponse(
            reset_count=reset_count,
            message=message
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"重置订单失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"重置订单失败: {str(e)}")


@router.post("/project/{project_id}/reset-failed", response_model=ResetFailedOrdersResponse, summary="重置预定失败的火车票订单")
async def reset_failed_orders(
    project_id: int,
    reset_data: ResetFailedOrdersRequest,
    current_user: User = Depends(get_current_user)
):
    """
    重置指定项目中预定失败状态的火车票订单为待提交状态

    - 如果指定了order_ids，则只重置指定的订单（必须是failed状态）
    - 如果未指定order_ids，则重置项目中所有failed状态的订单
    """
    try:
        # 验证项目是否存在
        try:
            project = await Project.get(id=project_id)
        except DoesNotExist:
            raise HTTPException(status_code=404, detail="项目不存在")

        # 构建查询条件
        query_conditions = {
            'project_id': project_id,
            'is_deleted': False,
            'order_status': 'failed'
        }

        # 如果指定了订单ID，则只重置指定的订单
        if reset_data.order_ids:
            query_conditions['id__in'] = reset_data.order_ids
            logger.info(f"重置指定的预定失败订单: {reset_data.order_ids}，项目ID: {project_id}，操作人: {current_user.username}")
        else:
            logger.info(f"重置项目 {project_id} 中所有预定失败订单，操作人: {current_user.username}")

        # 查询符合条件的订单
        orders = await TrainOrder.filter(**query_conditions).all()

        if not orders:
            message = "没有找到符合条件的预定失败订单"
            logger.info(f"项目 {project_id} {message}，操作人: {current_user.username}")
            return ResetFailedOrdersResponse(
                reset_count=0,
                message=message
            )

        # 重置订单状态
        reset_count = 0
        for order in orders:
            order.order_status = 'initial'
            await order.save()
            reset_count += 1
            logger.info(f"订单 {order.id} 已重置为待提交状态，操作人: {current_user.username}")

        message = f"成功重置 {reset_count} 条预定失败订单为待提交状态"
        logger.info(f"项目 {project_id} 重置预定失败订单操作完成: {message}，操作人: {current_user.username}")

        return ResetFailedOrdersResponse(
            reset_count=reset_count,
            message=message
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"重置预定失败订单失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"重置预定失败订单失败: {str(e)}")