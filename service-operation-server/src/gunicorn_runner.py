import logging
import sys
from typing import Any, Dict, List, Tuple, Union

import gunicorn.app.base


class GunicornApplication(gunicorn.app.base.BaseApplication):
    """用于运行 ASGI 应用的 Gunicorn 应用程序类。"""

    def __init__(
        self,
        app: str,
        host: str = "127.0.0.1",
        port: int = 8000,
        workers: int = 1,
        **kwargs: Any,
    ) -> None:
        """
        初始化 Gunicorn 应用程序。
        
        :param app: 应用程序导入路径，如 'src.web.application:get_app'
        :param host: 主机地址
        :param port: 端口号
        :param workers: 工作进程数
        :param kwargs: 其它 Gunicorn 配置选项
        """
        self.options: Dict[str, Union[str, int, Dict[str, str], List[str], Tuple[str]]] = {
            "bind": f"{host}:{port}",
            "workers": workers,
            "worker_class": "uvicorn.workers.UvicornWorker",
        }
        self.options.update(kwargs)
        self.application = app
        super().__init__()

    def load_config(self) -> None:
        """从选项中加载配置。"""
        config = {
            key: value
            for key, value in self.options.items()
            if key in self.cfg.settings and value is not None
        }
        for key, value in config.items():
            self.cfg.set(key.lower(), value)

    def load(self) -> str:
        """加载应用程序。"""
        return self.application


def set_logger_config_from_file(path: str, logger: logging.Logger) -> None:
    """从文件中设置日志记录器的配置。
    
    :param path: 日志配置文件路径
    :param logger: 要配置的日志记录器实例
    """
    if not path:
        return

    import json
    import os

    if not os.path.exists(path):
        print(f"文件 {path} 不存在。")
        return

    with open(path, "r") as f:
        try:
            config = json.load(f)
        except json.JSONDecodeError:
            print(f"文件 {path} 不是有效的 JSON。")
            return

    handlers = {}
    formatters = {}

    if "formatters" in config:
        for formatter_name, formatter_config in config["formatters"].items():
            formatters[formatter_name] = logging.Formatter(**formatter_config)

    if "handlers" in config:
        for handler_name, handler_config in config["handlers"].items():
            handler_class = logging.StreamHandler
            if "class" in handler_config:
                if handler_config["class"] == "logging.FileHandler":
                    handler_class = logging.FileHandler
                elif handler_config["class"] == "logging.StreamHandler":
                    handler_class = logging.StreamHandler

            handler_args = {}
            if "level" in handler_config:
                handler_args["level"] = logging.getLevelName(handler_config["level"])

            if handler_class == logging.FileHandler and "filename" in handler_config:
                handler_args["filename"] = handler_config["filename"]

            handlers[handler_name] = handler_class(**handler_args)

            if "formatter" in handler_config and handler_config["formatter"] in formatters:
                handlers[handler_name].setFormatter(formatters[handler_config["formatter"]])

    if "root" in config:
        if "level" in config["root"]:
            logger.setLevel(logging.getLevelName(config["root"]["level"]))

        if "handlers" in config["root"]:
            for handler_name in config["root"]["handlers"]:
                if handler_name in handlers:
                    logger.addHandler(handlers[handler_name])
