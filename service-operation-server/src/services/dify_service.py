"""
Dify API 服务
处理与Dify平台的交互，包括文件上传和护照识别
"""

import os
import httpx
import json
from typing import Optional, Dict, Any, List
from pathlib import Path
from loguru import logger

class DifyService:
    def __init__(self):
        self.base_url = os.getenv("DIFY_SERVER_URL", "https://difyapi.17usoft.com/v1")
        self.api_key = os.getenv("PASSPORT_REG_API_KEY","app-3uQKpowCseF3lY6tDUkXnOI5")
        
        if not self.api_key:
            logger.warning("PASSPORT_REG_API_KEY not found in environment variables")
        
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
        }
    
    async def upload_file(self, file_path: str, user_id: str) -> Optional[Dict[str, Any]]:
        """
        上传文件到Dify
        
        Args:
            file_path: 本地文件路径
            user_id: 用户标识
            
        Returns:
            上传结果，包含file_id等信息
        """
        try:
            file_path_obj = Path(file_path)
            if not file_path_obj.exists():
                logger.error(f"文件不存在: {file_path}")
                return None
            
            # 准备上传文件
            files = {
                'file': (
                    file_path_obj.name, 
                    open(file_path, 'rb'), 
                    'image/jpeg'
                ),
                'user': (None, user_id)
            }
            
            # 打印详细的请求信息
            logger.info(f"=== 📤 开始上传文件到Dify ===")
            logger.info(f"🌐 请求URL: {self.base_url}/files/upload")
            logger.info(f"🔑 请求Headers: {self.headers}")
            logger.info(f"📁 上传文件: '{file_path_obj.name}' (用户: {user_id})")
            logger.info(f"📏 文件大小: {file_path_obj.stat().st_size:,} bytes")
            logger.info(f"📂 本地路径: {file_path}")
            logger.info(f"⏰ 请求时间: {__import__('datetime').datetime.now().isoformat()}")
            
            async with httpx.AsyncClient(timeout=60.0) as client:
                response = await client.post(
                    f"{self.base_url}/files/upload",
                    headers=self.headers,
                    files=files
                )
                
                # 打印响应信息
                logger.info(f"📊 响应状态码: {response.status_code}")
                logger.info(f"📋 响应Headers: {dict(response.headers)}")
                logger.info(f"⏱️ 响应时间: {__import__('datetime').datetime.now().isoformat()}")
                
                try:
                    response_json = response.json()
                    logger.info(f"📄 响应内容 (JSON): {json.dumps(response_json, ensure_ascii=False, indent=2)}")
                except Exception as json_error:
                    logger.warning(f"⚠️ 响应内容不是JSON格式: {json_error}")
                    logger.info(f"📄 响应内容 (文本): {response.text}")
                
                if response.status_code == 201:
                    result = response.json()
                    file_id = result.get('id', 'Unknown ID')
                    logger.info(f"✅ 文件上传成功! 文件ID: {file_id}")
                    logger.info(f"📝 上传结果详情: {json.dumps(result, ensure_ascii=False, indent=2)}")
                    return result
                else:
                    error_text = response.text
                    logger.error(f"❌ 文件上传失败! 状态码: {response.status_code}")
                    logger.error(f"💥 错误详情: {error_text}")
                    return None
                    
        except Exception as e:
            logger.error(f"💥 文件上传异常: {e}")
            logger.error(f"📂 出错文件: {file_path}")
            logger.error(f"👤 用户ID: {user_id}")
            import traceback
            logger.error(f"🔍 异常堆栈: {traceback.format_exc()}")
            return None
        finally:
            # 确保文件句柄被关闭
            if 'files' in locals():
                try:
                    files['file'][1].close()
                except:
                    pass
    
    async def recognize_passport(self, upload_file_id: str, user_id: str, 
                               conversation_id: Optional[str] = None, max_retries: int = 2) -> Optional[Dict[str, Any]]:
        """
        调用Dify进行护照识别（支持重试机制）
        
        Args:
            upload_file_id: 上传文件ID
            user_id: 用户标识
            conversation_id: 可选的会话ID
            max_retries: 最大重试次数
            
        Returns:
            识别结果或None
        """
        # 重试机制
        for attempt in range(max_retries + 1):
            try:
                if attempt > 0:
                    logger.info(f"🔄 第 {attempt + 1} 次重试识别护照...")
                    # 重试前等待一段时间，递增等待时间
                    await __import__('asyncio').sleep(3 * attempt)
                
                logger.info(f"=== 🔍 开始调用Dify护照识别 (尝试 {attempt + 1}/{max_retries + 1}) ===")
                
                # 构建请求数据
                request_data = {
                    "inputs": {},
                    "query": "请识别这张护照图片中的所有信息，包括但不限于：护照号、姓名、国籍、出生日期、性别、签发日期、有效期等。请以结构化的方式返回这些信息。",
                    "response_mode": "blocking",  # 使用阻塞模式获取完整结果
                    "user": user_id,
                    "files": [
                        {
                            "type": "image",
                            "transfer_method": "local_file",
                            "upload_file_id": upload_file_id
                        }
                    ],
                    "auto_generate_name": True
                }
                
                logger.info(f"🌐 请求URL: {self.base_url}/chat-messages")
                logger.info(f"🔑 请求Headers: {self.headers}")
                logger.info(f"📤 请求数据: {json.dumps(request_data, ensure_ascii=False, indent=2)}")
                logger.info(f"👤 用户ID: {user_id}")
                logger.info(f"📁 文件ID: {upload_file_id}")
                logger.info(f"⏰ 请求时间: {__import__('datetime').datetime.now().isoformat()}")
                
                # 如果有会话ID，则添加到请求中
                if conversation_id:
                    request_data["conversation_id"] = conversation_id
                    logger.info(f"💬 会话ID: {conversation_id}")
                
                # 增加超时时间到180秒，并设置详细的超时配置
                timeout_config = httpx.Timeout(
                    timeout=180.0,  # 总超时时间
                    connect=30.0,   # 连接超时
                    read=180.0,     # 读取超时
                    write=30.0      # 写入超时
                )
                async with httpx.AsyncClient(timeout=timeout_config) as client:
                    response = await client.post(
                        f"{self.base_url}/chat-messages",
                        headers={
                            **self.headers,
                            "Content-Type": "application/json"
                        },
                        json=request_data
                    )
                    
                    logger.info(f"📊 响应状态码: {response.status_code}")
                    logger.info(f"📋 响应Headers: {dict(response.headers)}")
                    logger.info(f"⏱️ 响应时间: {__import__('datetime').datetime.now().isoformat()}")
                    
                    if response.status_code == 200:
                        result = response.json()
                        message_id = result.get('message_id', 'Unknown')
                        answer = result.get('answer', '')
                        logger.info(f"✅ 护照识别成功! 消息ID: {message_id}")
                        logger.info(f"🎯 识别答案预览: {answer[:200]}..." if len(answer) > 200 else f"🎯 识别答案: {answer}")
                        logger.info(f"📝 完整识别响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
                        
                        # 记录识别性能指标
                        metadata = result.get('metadata', {})
                        if metadata:
                            logger.info(f"📈 识别性能指标: {json.dumps(metadata, ensure_ascii=False, indent=2)}")
                        
                        return result
                    else:
                        error_text = response.text
                        logger.error(f"❌ 护照识别失败! 状态码: {response.status_code}")
                        logger.error(f"💥 错误详情: {error_text}")
                        
                        # 尝试解析错误响应
                        try:
                            error_json = response.json()
                            logger.error(f"🔍 错误响应 (JSON): {json.dumps(error_json, ensure_ascii=False, indent=2)}")
                        except:
                            logger.error(f"🔍 错误响应 (文本): {error_text}")
                        
                        # 如果是服务器错误且还有重试机会，继续重试
                        if response.status_code >= 500 and attempt < max_retries:
                            logger.warning(f"⚠️ 服务器错误，将在 {3 * (attempt + 1)} 秒后重试...")
                            continue
                        else:
                            return None
                        
            except (httpx.ReadTimeout, httpx.ConnectTimeout, httpx.TimeoutException) as e:
                logger.error(f"⏰ 请求超时 (尝试 {attempt + 1}/{max_retries + 1}): {e}")
                if attempt < max_retries:
                    wait_time = 5 * (attempt + 1)
                    logger.warning(f"⚠️ 将在 {wait_time} 秒后重试...")
                    await __import__('asyncio').sleep(wait_time)
                    continue
                else:
                    logger.error(f"💥 所有重试均超时，识别失败")
                    return None
                    
            except Exception as e:
                logger.error(f"💥 调用Dify识别护照异常 (尝试 {attempt + 1}/{max_retries + 1}): {e}")
                logger.error(f"📁 文件ID: {upload_file_id}")
                logger.error(f"👤 用户ID: {user_id}")
                logger.error(f"💬 会话ID: {conversation_id}")
                import traceback
                logger.error(f"🔍 异常堆栈: {traceback.format_exc()}")
                
                # 如果是网络相关错误且还有重试机会，继续重试
                if attempt < max_retries and ("connection" in str(e).lower() or "network" in str(e).lower()):
                    wait_time = 3 * (attempt + 1)
                    logger.warning(f"⚠️ 网络错误，将在 {wait_time} 秒后重试...")
                    await __import__('asyncio').sleep(wait_time)
                    continue
                else:
                    return None
        
        # 如果所有重试都失败了
        logger.error(f"💥 护照识别彻底失败，已尝试 {max_retries + 1} 次")
        return None
    
    async def parse_recognition_result(self, dify_response: Dict[str, Any]) -> Dict[str, Any]:
        """
        解析Dify返回的识别结果
        
        Args:
            dify_response: Dify API返回的响应
            
        Returns:
            解析后的护照信息字典
        """
        try:
            logger.info(f"=== 🔄 开始解析Dify识别结果 ===")
            logger.info(f"📥 输入参数: {json.dumps(dify_response, ensure_ascii=False, indent=2)}")
            
            # 获取识别的文本内容
            answer = dify_response.get("answer", "")
            logger.info(f"📝 提取的答案内容: {answer}")
            logger.info(f"📏 答案长度: {len(answer)} 字符")
            
            # 初始化结果字典
            parsed_data = {
                "certificate_type": None,
                "country_of_issue": None,
                "certificate_number": None,
                "surname": None,
                "given_names": None,
                "nationality": None,
                "date_of_birth": None,
                "sex": None,
                "date_of_issue": None,
                "date_of_expiry": None,
                "passenger_type": None,
                "viz_mrz_consistency": None,
                "ssr_code": None,
                "mrz_line1": None,
                "mrz_line2": None,
                "additional_info": {}
            }
            
            logger.info(f"🏗️ 初始化解析数据结构完成，包含 {len(parsed_data)} 个字段")
            
            # 从answer中提取JSON数据
            if answer:
                try:
                    # 查找JSON代码块
                    import re
                    json_pattern = r'```json\s*\n(.*?)\n```'
                    json_match = re.search(json_pattern, answer, re.DOTALL)
                    
                    if json_match:
                        json_str = json_match.group(1)
                        logger.info(f"🎯 从代码块中提取到JSON字符串: {json_str}")
                        
                        # 解析JSON
                        passport_data = json.loads(json_str)
                        logger.info(f"✅ JSON解析成功!")
                        logger.info(f"📊 解析后的JSON数据: {json.dumps(passport_data, ensure_ascii=False, indent=2)}")
                        logger.info(f"🔢 JSON包含字段数量: {len(passport_data) if isinstance(passport_data, dict) else 'Not a dict'}")
                        
                        # 映射字段
                        if isinstance(passport_data, dict):
                            # 新字段名映射
                            parsed_data["certificate_type"] = passport_data.get("certificate_type") or passport_data.get("document_type")
                            parsed_data["country_of_issue"] = passport_data.get("country_of_issue")
                            parsed_data["certificate_number"] = passport_data.get("certificate_number") or passport_data.get("passport_number")
                            parsed_data["surname"] = passport_data.get("surname")
                            parsed_data["given_names"] = passport_data.get("given_names")
                            parsed_data["nationality"] = passport_data.get("nationality")
                            parsed_data["date_of_birth"] = passport_data.get("date_of_birth")
                            parsed_data["sex"] = passport_data.get("sex")
                            parsed_data["date_of_issue"] = passport_data.get("date_of_issue")
                            parsed_data["date_of_expiry"] = passport_data.get("date_of_expiry")
                            parsed_data["passenger_type"] = passport_data.get("passenger_type")
                            parsed_data["viz_mrz_consistency"] = passport_data.get("viz_mrz_consistency")
                            parsed_data["ssr_code"] = passport_data.get("ssr_code")
                            parsed_data["mrz_line1"] = passport_data.get("mrz_line1")
                            parsed_data["mrz_line2"] = passport_data.get("mrz_line2")
                            
                            # 如果有其他字段，放入additional_info
                            for key, value in passport_data.items():
                                if key not in parsed_data and key not in ["document_type", "passport_number", "place_of_birth", "authority", "signature_present"]:
                                    parsed_data["additional_info"][key] = value
                    else:
                        # 如果没有找到JSON代码块，尝试直接解析整个answer作为JSON
                        try:
                            passport_data = json.loads(answer)
                            logger.info(f"✅ 直接解析answer为JSON成功!")
                            logger.info(f"📊 直接解析的JSON数据: {json.dumps(passport_data, ensure_ascii=False, indent=2)}")
                            logger.info(f"🔢 JSON包含字段数量: {len(passport_data) if isinstance(passport_data, dict) else 'Not a dict'}")
                            
                            if isinstance(passport_data, dict):
                                # 同样的映射逻辑
                                for field in ["certificate_type", "country_of_issue", "certificate_number", "surname", 
                                             "given_names", "nationality", "date_of_birth", "sex",
                                             "date_of_issue", "date_of_expiry", "passenger_type", "viz_mrz_consistency", 
                                             "ssr_code", "mrz_line1", "mrz_line2"]:
                                    if field in passport_data:
                                        parsed_data[field] = passport_data[field]
                                
                                # 处理旧字段名的兼容性
                                if "document_type" in passport_data and not parsed_data["certificate_type"]:
                                    parsed_data["certificate_type"] = passport_data["document_type"]
                                if "passport_number" in passport_data and not parsed_data["certificate_number"]:
                                    parsed_data["certificate_number"] = passport_data["passport_number"]
                                
                                # 其他字段放入additional_info（排除已删除的字段）
                                for key, value in passport_data.items():
                                    if key not in parsed_data and key not in ["document_type", "passport_number", "place_of_birth", "authority", "signature_present"]:
                                        parsed_data["additional_info"][key] = value
                        except json.JSONDecodeError as e:
                            logger.warning(f"⚠️ 无法将answer解析为JSON: {e}")
                            logger.info(f"🔄 切换到文本模式解析...")
                            # 如果JSON解析失败，尝试文本模式解析
                            parsed_data = await self._parse_text_answer(answer)
                
                except Exception as e:
                    logger.error(f"💥 JSON解析过程中出现异常: {e}")
                    logger.info(f"🔄 降级到文本解析模式...")
                    import traceback
                    logger.error(f"🔍 解析异常堆栈: {traceback.format_exc()}")
                    # 降级到文本解析
                    parsed_data = await self._parse_text_answer(answer)
            
            # 保存原始回答作为附加信息
            parsed_data["additional_info"]["original_answer"] = answer
            parsed_data["additional_info"]["dify_metadata"] = dify_response.get("metadata", {})
            
            # 统计解析结果
            non_empty_fields = {k: v for k, v in parsed_data.items() if v is not None and v != "" and k != "additional_info"}
            logger.info(f"✅ 解析完成! 共提取到 {len(non_empty_fields)} 个有效字段")
            logger.info(f"📋 有效字段列表: {list(non_empty_fields.keys())}")
            logger.info(f"📝 最终解析结果: {json.dumps(parsed_data, ensure_ascii=False, indent=2)}")
            return parsed_data
            
        except Exception as e:
            logger.error(f"💥 解析识别结果失败: {e}")
            import traceback
            logger.error(f"🔍 解析异常堆栈: {traceback.format_exc()}")
            logger.error(f"📥 出错的输入数据: {json.dumps(dify_response, ensure_ascii=False, indent=2)}")
            return {
                "certificate_type": None,
                "country_of_issue": None,
                "certificate_number": None,
                "surname": None,
                "given_names": None,
                "nationality": None,
                "date_of_birth": None,
                "sex": None,
                "date_of_issue": None,
                "date_of_expiry": None,
                "passenger_type": None,
                "viz_mrz_consistency": None,
                "ssr_code": None,
                "mrz_line1": None,
                "mrz_line2": None,
                "additional_info": {
                    "error": str(e),
                    "original_answer": dify_response.get("answer", ""),
                    "dify_metadata": dify_response.get("metadata", {})
                }
            }
    
    def _parse_text_answer(self, answer: str) -> Dict[str, Any]:
        """
        从文本回答中解析护照信息
        这是一个简单的文本解析示例，实际使用时可能需要更复杂的解析逻辑
        """
        parsed_data = {}
        
        # 定义关键词映射
        field_keywords = {
            "certificate_number": ["护照号", "passport number", "passport no", "document number", "证件号"],
            "certificate_type": ["证件类型", "document type", "certificate type"],
            "surname": ["姓氏", "surname", "family name", "last name"],
            "given_names": ["名字", "given names", "first name", "名"],
            "nationality": ["国籍", "nationality"],
            "date_of_birth": ["出生日期", "date of birth", "birth date"],
            "sex": ["性别", "sex", "gender"],
            "date_of_issue": ["签发日期", "date of issue", "issue date"],
            "date_of_expiry": ["有效期", "expiry date", "valid until", "到期日期"],
            "country_of_issue": ["签发国", "issuing country", "country of issue"],
            "passenger_type": ["旅客类型", "passenger type"],
            "viz_mrz_consistency": ["VIZ与MRZ一致性", "viz mrz consistency"],
            "ssr_code": ["SSR码", "ssr code"]
        }
        
        # 简单的文本匹配解析
        for field, keywords in field_keywords.items():
            for keyword in keywords:
                # 查找包含关键词的行
                lines = answer.split('\n')
                for line in lines:
                    if keyword.lower() in line.lower():
                        # 提取冒号后面的内容
                        if ':' in line:
                            value = line.split(':', 1)[1].strip()
                            if value and value != '-' and value.lower() != 'unknown':
                                parsed_data[field] = value
                                break
        
        return parsed_data

    async def send_chat_message(self, query: str, upload_file_id: str, user_id: str, 
                              conversation_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        发送聊天消息进行识别
        
        Args:
            query: 查询内容
            upload_file_id: 上传的文件ID
            user_id: 用户ID
            conversation_id: 会话ID（可选）
            
        Returns:
            识别结果
        """
        try:
            # 准备请求数据
            data = {
                "query": query,
                "response_mode": "blocking",
                "user": user_id,
                "files": [
                    {
                        "type": "image",
                        "transfer_method": "local_file",
                        "upload_file_id": upload_file_id
                    }
                ]
            }
            
            if conversation_id:
                data["conversation_id"] = conversation_id
            
            # 打印详细的请求信息
            logger.info(f"=== 发送聊天消息到Dify ===")
            logger.info(f"URL: {self.base_url}/chat-messages")
            logger.info(f"Headers: {self.headers}")
            logger.info(f"Request data: {data}")
            
            async with httpx.AsyncClient(timeout=120.0) as client:
                response = await client.post(
                    f"{self.base_url}/chat-messages",
                    headers={**self.headers, "Content-Type": "application/json"},
                    json=data
                )
                
                # 打印响应信息
                logger.info(f"Response status: {response.status_code}")
                logger.info(f"Response headers: {dict(response.headers)}")
                
                try:
                    response_json = response.json()
                    logger.info(f"Response body: {response_json}")
                except:
                    logger.info(f"Response text: {response.text}")
                
                if response.status_code == 200:
                    result = response.json()
                    logger.info(f"消息发送成功，返回答案: {result.get('answer', '无答案')[:100]}...")
                    logger.info(f"完整响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")
                    return result
                else:
                    error_text = response.text
                    logger.error(f"发送消息失败: {response.status_code} - {error_text}")
                    return None
                    
        except Exception as e:
            logger.error(f"发送消息异常: {e}")
            return None


# 创建全局实例
dify_service = DifyService() 