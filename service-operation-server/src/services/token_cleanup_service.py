"""
Token清理服务模块

定期清理过期的refresh token
"""

import asyncio
from datetime import datetime, timezone, timedelta
from loguru import logger
from src.db.models.refresh_token import RefreshToken


class TokenCleanupService:
    """Token清理服务"""
    
    def __init__(self, cleanup_interval_hours: int = 24):
        """
        初始化Token清理服务
        
        Args:
            cleanup_interval_hours: 清理间隔（小时）
        """
        self.cleanup_interval_hours = cleanup_interval_hours
        self.is_running = False
        self._task = None
    
    async def start(self):
        """启动清理服务"""
        if self.is_running:
            logger.warning("Token清理服务已经在运行")
            return
            
        self.is_running = True
        self._task = asyncio.create_task(self._cleanup_loop())
        logger.info(f"Token清理服务已启动，清理间隔: {self.cleanup_interval_hours}小时")
    
    async def stop(self):
        """停止清理服务"""
        if not self.is_running:
            return
            
        self.is_running = False
        if self._task:
            self._task.cancel()
            try:
                await self._task
            except asyncio.CancelledError:
                pass
        logger.info("Token清理服务已停止")
    
    async def _cleanup_loop(self):
        """清理循环"""
        while self.is_running:
            try:
                await self.cleanup_expired_tokens()
                # 等待下次清理
                await asyncio.sleep(self.cleanup_interval_hours * 3600)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Token清理过程中发生错误: {e}")
                # 发生错误时等待较短时间后重试
                await asyncio.sleep(300)  # 5分钟后重试
    
    async def cleanup_expired_tokens(self):
        """清理过期的tokens"""
        try:
            # 清理过期的refresh tokens
            expired_count = await RefreshToken.filter(
                expires_at__lt=datetime.now(timezone.utc)
            ).count()
            
            if expired_count > 0:
                await RefreshToken.cleanup_expired_tokens()
                logger.info(f"清理了 {expired_count} 个过期的refresh token")
            else:
                logger.debug("没有找到过期的refresh token")
                
        except Exception as e:
            logger.error(f"清理过期token时发生错误: {e}")
            raise


# 全局实例
token_cleanup_service = TokenCleanupService()


async def start_token_cleanup_service():
    """启动token清理服务"""
    await token_cleanup_service.start()


async def stop_token_cleanup_service():
    """停止token清理服务"""
    await token_cleanup_service.stop()