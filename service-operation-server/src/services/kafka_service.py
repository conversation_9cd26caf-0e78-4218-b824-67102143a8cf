"""
Kafka服务模块
用于向Kafka队列推送预订任务消息
"""
import json
import asyncio
from typing import Dict, Any, Optional
from kafka import KafkaProducer
from kafka.errors import KafkaError
from loguru import logger

from src.core.config import settings


class KafkaService:
    """Kafka服务类，负责消息推送"""
    
    def __init__(self):
        self.producer: Optional[KafkaProducer] = None
        self.is_enabled = settings.kafka_enabled
        
    def _create_producer(self) -> KafkaProducer:
        """创建Kafka生产者"""
        if not self.is_enabled:
            logger.warning("Kafka未启用，跳过创建生产者")
            return None
            
        try:
            # 尝试使用snappy压缩，如果失败则回退到gzip
            compression_type = settings.kafka_compression_type
            
            try:
                # 按照Java客户端参考代码的配置创建生产者
                producer = KafkaProducer(
                    bootstrap_servers=settings.kafka_bootstrap_servers.split(','),
                    acks=1,  # 简化acks设置
                    retries=3,  # 增加重试次数
                    request_timeout_ms=60000,  # 增加超时时间
                    compression_type=None,  # 禁用压缩解决超时问题
                    key_serializer=lambda x: x.encode('utf-8') if x else None,
                    value_serializer=lambda x: json.dumps(x, ensure_ascii=False).encode('utf-8'),
                    # 添加安全配置以确保连接稳定性
                    api_version=(0, 10, 1),
                )
                logger.info(f"Kafka生产者创建成功，连接到: {settings.kafka_bootstrap_servers}，压缩类型: 无压缩")
                return producer
                
            except Exception as compression_error:
                # 如果主配置失败，尝试备用配置
                logger.warning(f"主配置失败，尝试备用配置: {str(compression_error)}")
                producer = KafkaProducer(
                    bootstrap_servers=settings.kafka_bootstrap_servers.split(','),
                    acks=1,  # 简化acks设置
                    retries=3,  # 增加重试次数
                    request_timeout_ms=60000,  # 增加超时时间
                    compression_type=None,  # 禁用压缩解决超时问题
                    key_serializer=lambda x: x.encode('utf-8') if x else None,
                    value_serializer=lambda x: json.dumps(x, ensure_ascii=False).encode('utf-8'),
                    # 添加安全配置以确保连接稳定性
                    api_version=(0, 10, 1),
                )
                logger.info(f"Kafka生产者创建成功（备用配置），连接到: {settings.kafka_bootstrap_servers}")
                return producer
                    
        except Exception as e:
            logger.error(f"创建Kafka生产者失败: {str(e)}")
            return None
    
    def get_producer(self) -> Optional[KafkaProducer]:
        """获取Kafka生产者实例"""
        if not self.is_enabled:
            return None
            
        if self.producer is None:
            self.producer = self._create_producer()
        return self.producer
    
    async def send_booking_task_message(
        self,
        task_id: str,
        order_id: int,
        module: str,
        username: str,
        password: str,
        company_name: Optional[str] = None,
        send_sms: bool = False,
        has_agent: bool = False,
        agent_phone: Optional[str] = None,
        agent_name: Optional[str] = None,
        login_user_name: Optional[str] = None
    ) -> bool:
        """
        发送预订任务消息到Kafka

        Args:
            task_id: 任务ID
            order_id: 订单ID
            module: 预订模块类型 (train_ticket_booking 或 domestic_hotel_filling)
            username: 系统设置中的用户名
            password: 系统设置中的原始密文（数据库存储的加密值）
            company_name: 公司名称（从项目的client_name字段获取）
            send_sms: 是否发送短信
            has_agent: 是否有代订人
            agent_phone: 代订人手机号码，如果没有代订人则为空字符串
            agent_name: 代订人姓名，如果没有代订人则为空字符串
            login_user_name: 当前登录用户的姓名

        Returns:
            bool: 发送是否成功
        """
        if not self.is_enabled:
            logger.warning("Kafka未启用，跳过消息发送")
            return False
            
        producer = self.get_producer()
        if producer is None:
            logger.error("Kafka生产者未初始化，无法发送消息")
            return False
            
        # 构建消息内容
        message = {
            "task_id": task_id,
            "order_id": order_id,
            "module": module,
            "username": username,
            "password": password,
            "company_name": company_name or "",
            "send_sms": 1 if send_sms else 0,
            "has_agent": 1 if has_agent else 0,
            "agent_phone": agent_phone if has_agent else "",
            "agent_name": agent_name if has_agent else "",
            "login_user_name": login_user_name or ""
        }
        
        try:
            # 使用任务ID作为消息key，确保同一任务的消息有序
            future = producer.send(
                topic=settings.kafka_topic,
                key=task_id,
                value=message
            )
            
            # 等待发送结果
            record_metadata = future.get(timeout=10)
            
            logger.info(
                f"Kafka消息发送成功 - Topic: {record_metadata.topic}, "
                f"Partition: {record_metadata.partition}, "
                f"Offset: {record_metadata.offset}, "
                f"Task: {task_id}, Order: {order_id}"
            )
            return True
            
        except KafkaError as e:
            logger.error(f"Kafka消息发送失败: {str(e)}, Task: {task_id}, Order: {order_id}")
            return False
        except Exception as e:
            logger.error(f"发送Kafka消息时发生异常: {str(e)}, Task: {task_id}, Order: {order_id}")
            return False
    
    async def send_multiple_booking_messages(
        self,
        task_id: str,
        order_ids: list[int],
        module: str,
        username: str,
        password: str,
        company_name: Optional[str] = None,
        send_sms: bool = False,
        has_agent: bool = False,
        agent_phone: Optional[str] = None,
        agent_name: Optional[str] = None,
        login_user_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        批量发送预订任务消息到Kafka

        Args:
            task_id: 任务ID
            order_ids: 订单ID列表
            module: 预订模块类型
            username: 系统设置中的用户名
            password: 系统设置中的原始密文（数据库存储的加密值）
            company_name: 公司名称（从项目的client_name字段获取）
            send_sms: 是否发送短信
            has_agent: 是否有代订人
            agent_phone: 代订人手机号码，如果没有代订人则为空字符串
            agent_name: 代订人姓名，如果没有代订人则为空字符串
            login_user_name: 当前登录用户的姓名

        Returns:
            Dict: 包含成功、失败数量的统计结果
        """
        if not self.is_enabled:
            logger.warning("Kafka未启用，跳过批量消息发送")
            return {"success_count": 0, "failed_count": len(order_ids), "enabled": False}
            
        success_count = 0
        failed_count = 0
        
        logger.info(f"开始批量发送Kafka消息 - Task: {task_id}, Orders: {len(order_ids)}")
        
        # 并行发送消息以提高效率
        tasks = []
        for order_id in order_ids:
            task = self.send_booking_task_message(
                task_id=task_id,
                order_id=order_id,
                module=module,
                username=username,
                password=password,
                company_name=company_name,
                send_sms=send_sms,
                has_agent=has_agent,
                agent_phone=agent_phone,
                agent_name=agent_name,
                login_user_name=login_user_name
            )
            tasks.append(task)
        
        # 等待所有发送任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 统计结果
        for result in results:
            if isinstance(result, Exception):
                failed_count += 1
                logger.error(f"发送消息异常: {str(result)}")
            elif result:
                success_count += 1
            else:
                failed_count += 1
        
        logger.info(
            f"Kafka批量消息发送完成 - Task: {task_id}, "
            f"成功: {success_count}, 失败: {failed_count}"
        )
        
        return {
            "success_count": success_count,
            "failed_count": failed_count,
            "total_count": len(order_ids),
            "enabled": True
        }
    
    def close(self):
        """关闭Kafka生产者连接"""
        if self.producer:
            try:
                self.producer.close()
                logger.info("Kafka生产者连接已关闭")
            except Exception as e:
                logger.error(f"关闭Kafka生产者时发生错误: {str(e)}")
            finally:
                self.producer = None


# 全局Kafka服务实例
kafka_service = KafkaService()


# 便捷函数
async def send_train_booking_task(
    task_id: str,
    order_ids: list[int],
    username: str,
    password: str,
    company_name: Optional[str] = None,
    send_sms: bool = False,
    has_agent: bool = False,
    agent_phone: Optional[str] = None,
    agent_name: Optional[str] = None,
    login_user_name: Optional[str] = None
) -> Dict[str, Any]:
    """
    发送火车票预订任务消息

    Args:
        task_id: 任务ID
        order_ids: 订单ID列表
        username: 同程管家用户名
        password: 同程管家原始密文（数据库存储的加密值）
        company_name: 公司名称（从项目的client_name字段获取）
        send_sms: 是否发送短信
        has_agent: 是否有代订人
        agent_phone: 代订人手机号码，如果没有代订人则为空字符串
        agent_name: 代订人姓名，如果没有代订人则为空字符串
        login_user_name: 当前登录用户的姓名

    Returns:
        Dict: 发送结果统计
    """
    return await kafka_service.send_multiple_booking_messages(
        task_id=task_id,
        order_ids=order_ids,
        module="train_ticket_booking",  # 火车票预订固定为此值
        username=username,
        password=password,
        company_name=company_name,
        send_sms=send_sms,
        has_agent=has_agent,
        agent_phone=agent_phone,
        agent_name=agent_name,
        login_user_name=login_user_name
    )


async def send_hotel_booking_task(
    task_id: str,
    order_ids: list[int],
    username: str,
    password: str,
    company_name: Optional[str] = None,
    send_sms: bool = False,
    has_agent: bool = False,
    agent_phone: Optional[str] = None,
    agent_name: Optional[str] = None,
    login_user_name: Optional[str] = None
) -> Dict[str, Any]:
    """
    发送酒店预订任务消息

    Args:
        task_id: 任务ID
        order_ids: 订单ID列表
        username: 同程管家用户名
        password: 同程管家原始密文（数据库存储的加密值）
        company_name: 公司名称（从项目的client_name字段获取）
        send_sms: 是否发送短信
        has_agent: 是否有代订人
        agent_phone: 代订人手机号码，如果没有代订人则为空字符串
        agent_name: 代订人姓名，如果没有代订人则为空字符串
        login_user_name: 当前登录用户的姓名

    Returns:
        Dict: 发送结果统计
    """
    return await kafka_service.send_multiple_booking_messages(
        task_id=task_id,
        order_ids=order_ids,
        module="domestic_hotel_filling",  # 酒店预订固定为此值
        username=username,
        password=password,
        company_name=company_name,
        send_sms=send_sms,
        has_agent=has_agent,
        agent_phone=agent_phone,
        agent_name=agent_name,
        login_user_name=login_user_name
    )


async def send_flight_booking_task(
    task_id: str,
    order_ids: list[int],
    username: str,
    password: str,
    company_name: Optional[str] = None,
    send_sms: bool = False,
    has_agent: bool = False,
    agent_phone: Optional[str] = None,
    agent_name: Optional[str] = None,
    login_user_name: Optional[str] = None
) -> Dict[str, Any]:
    """
    发送飞机票预订任务消息

    Args:
        task_id: 任务ID
        order_ids: 订单ID列表
        username: 同程管家用户名
        password: 同程管家原始密文（数据库存储的加密值）
        company_name: 公司名称（从项目的client_name字段获取）
        send_sms: 是否发送短信
        has_agent: 是否有代订人
        agent_phone: 代订人手机号码，如果没有代订人则为空字符串
        agent_name: 代订人姓名，如果没有代订人则为空字符串
        login_user_name: 当前登录用户的姓名

    Returns:
        Dict: 发送结果统计
    """
    return await kafka_service.send_multiple_booking_messages(
        task_id=task_id,
        order_ids=order_ids,
        module="flight_ticket_booking",  # 飞机票预订固定为此值
        username=username,
        password=password,
        company_name=company_name,
        send_sms=send_sms,
        has_agent=has_agent,
        agent_phone=agent_phone,
        agent_name=agent_name,
        login_user_name=login_user_name
    )