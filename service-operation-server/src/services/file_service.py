"""
文件上传服务

处理文件上传、验证、存储等功能
"""

import os
import uuid
import zipfile
import rarfile
from pathlib import Path
from typing import List, Optional, Tuple
from datetime import datetime

from fastapi import UploadFile, HTTPException
from loguru import logger

from src.core.config import settings


class FileUploadService:
    """文件上传服务类"""
    
    def __init__(self):
        self.upload_path = settings.upload_path
        self.max_file_size = settings.max_file_size
        self.allowed_image_types = settings.allowed_image_types
        self.allowed_archive_types = settings.allowed_archive_types
        self.allowed_pdf_types = settings.allowed_pdf_types
    
    def _validate_file_size(self, file: UploadFile) -> None:
        """验证文件大小"""
        if hasattr(file.file, 'seek') and hasattr(file.file, 'tell'):
            # 获取文件大小
            file.file.seek(0, 2)  # 移动到文件末尾
            file_size = file.file.tell()
            file.file.seek(0)  # 重置到文件开头
            
            if file_size > self.max_file_size:
                raise HTTPException(
                    status_code=413,
                    detail=f"文件大小超过限制 ({self.max_file_size / 1024 / 1024:.1f}MB)"
                )
    
    def _validate_file_type(self, file: UploadFile, allowed_types: List[str]) -> None:
        """验证文件类型"""
        if file.content_type not in allowed_types:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的文件类型: {file.content_type}"
            )
    
    def _generate_unique_filename(self, original_filename: str) -> str:
        """生成唯一的文件名"""
        # 获取文件扩展名
        file_ext = Path(original_filename).suffix.lower()
        # 生成UUID作为文件名
        unique_name = f"{uuid.uuid4().hex}{file_ext}"
        return unique_name
    
    def _create_date_directory(self) -> Path:
        """创建按日期分组的目录"""
        today = datetime.now().strftime("%Y/%m/%d")
        date_dir = self.upload_path / today
        date_dir.mkdir(parents=True, exist_ok=True)
        return date_dir
    
    async def save_uploaded_file(self, file: UploadFile, subdir: str = "passport") -> Tuple[str, str]:
        """
        保存上传的文件
        
        Args:
            file: 上传的文件
            subdir: 子目录名称
            
        Returns:
            Tuple[相对路径, 绝对路径]
        """
        # 验证文件大小
        self._validate_file_size(file)
        
        # 验证文件类型
        all_allowed_types = self.allowed_image_types + self.allowed_archive_types + self.allowed_pdf_types
        self._validate_file_type(file, all_allowed_types)
        
        # 创建目录结构: uploads/passport/2024/01/15/
        subdir_path = self.upload_path / subdir
        date_dir = subdir_path / datetime.now().strftime("%Y/%m/%d")
        date_dir.mkdir(parents=True, exist_ok=True)
        
        # 生成唯一文件名
        unique_filename = self._generate_unique_filename(file.filename or "unknown")
        file_path = date_dir / unique_filename
        
        try:
            # 保存文件
            content = await file.read()
            with open(file_path, "wb") as f:
                f.write(content)
            
            # 返回相对路径和绝对路径
            relative_path = str(file_path.relative_to(self.upload_path))
            absolute_path = str(file_path)
            
            logger.info(f"文件上传成功: {relative_path}")
            return relative_path, absolute_path
            
        except Exception as e:
            logger.error(f"文件保存失败: {e}")
            # 如果保存失败，删除可能创建的文件
            if file_path.exists():
                file_path.unlink()
            raise HTTPException(status_code=500, detail="文件保存失败")
    
    async def extract_images_from_archive(self, archive_path: str) -> List[Tuple[str, str]]:
        """
        从压缩包中提取图片文件
        
        Args:
            archive_path: 压缩包的绝对路径
            
        Returns:
            List[Tuple[相对路径, 绝对路径]]
        """
        extracted_files = []
        archive_file = Path(archive_path)
        
        # 创建解压目录
        extract_dir = archive_file.parent / f"{archive_file.stem}_extracted"
        extract_dir.mkdir(exist_ok=True)
        
        try:
            # 根据文件类型选择解压方法
            if archive_file.suffix.lower() == '.zip':
                with zipfile.ZipFile(archive_path, 'r') as zip_ref:
                    zip_ref.extractall(extract_dir)
            elif archive_file.suffix.lower() == '.rar':
                with rarfile.RarFile(archive_path, 'r') as rar_ref:
                    rar_ref.extractall(extract_dir)
            else:
                raise HTTPException(status_code=400, detail="不支持的压缩包格式")
            
            # 遍历解压后的文件，找出图片文件
            for root, dirs, files in os.walk(extract_dir):
                for file in files:
                    file_path = Path(root) / file
                    
                    # 检查是否为图片文件（通过扩展名）
                    if file_path.suffix.lower() in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']:
                        # 生成新的唯一文件名
                        unique_filename = self._generate_unique_filename(file)
                        new_file_path = archive_file.parent / unique_filename
                        
                        # 移动文件到最终位置
                        file_path.rename(new_file_path)
                        
                        # 计算相对路径
                        relative_path = str(new_file_path.relative_to(self.upload_path))
                        absolute_path = str(new_file_path)
                        
                        extracted_files.append((relative_path, absolute_path))
                        logger.info(f"从压缩包提取图片: {relative_path}")
            
            # 清理解压目录
            import shutil
            shutil.rmtree(extract_dir, ignore_errors=True)
            
            # 删除原压缩包
            archive_file.unlink()
            
            return extracted_files
            
        except Exception as e:
            logger.error(f"压缩包解压失败: {e}")
            # 清理解压目录
            import shutil
            shutil.rmtree(extract_dir, ignore_errors=True)
            raise HTTPException(status_code=500, detail="压缩包解压失败")
    
    def get_file_url(self, relative_path: str) -> str:
        """
        获取文件的访问URL
        
        Args:
            relative_path: 文件的相对路径
            
        Returns:
            文件的访问URL
        """
        # 这里返回相对于服务器的URL路径
        # 实际部署时可能需要添加域名前缀
        return f"/uploads/{relative_path}"
    
    def get_absolute_path(self, relative_path: str) -> str:
        """
        获取文件的绝对路径
        
        Args:
            relative_path: 文件的相对路径
            
        Returns:
            文件的绝对路径
        """
        return str(self.upload_path / relative_path)
    
    def delete_file(self, relative_path: str) -> bool:
        """
        删除文件
        
        Args:
            relative_path: 文件的相对路径
            
        Returns:
            是否删除成功
        """
        try:
            file_path = self.upload_path / relative_path
            if file_path.exists():
                file_path.unlink()
                logger.info(f"文件删除成功: {relative_path}")
                return True
            else:
                logger.warning(f"文件不存在: {relative_path}")
                return False
        except Exception as e:
            logger.error(f"文件删除失败: {e}")
            return False


# 创建全局实例
file_service = FileUploadService() 