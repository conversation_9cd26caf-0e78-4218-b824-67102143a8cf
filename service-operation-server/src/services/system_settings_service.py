"""系统设置服务模块"""
from typing import List, Optional, Dict, Any
from tortoise.exceptions import DoesNotExist
from src.db.models.system_settings import SystemSettings
from src.core.encryption import encryption_service


class SystemSettingsService:
    """系统设置服务类"""
    
    # 预定义的配置项
    CONFIG_KEYS = {
        'tongcheng_username': '同程管家用户名',
        'tongcheng_password': '同程管家密码',
    }
    
    # 需要加密的配置项
    ENCRYPTED_KEYS = {'tongcheng_password'}
    
    @classmethod
    async def get_user_settings(cls, user_id: str) -> List[Dict[str, Any]]:
        """
        获取用户的所有系统设置
        
        Args:
            user_id: 用户ID
            
        Returns:
            设置列表，密码字段会被解密
        """
        settings = await SystemSettings.filter(user_id=user_id).all()
        
        result = []
        for setting in settings:
            config_value = setting.config_value
            
            # 如果是需要加密的字段，进行解密
            if setting.config_key in cls.ENCRYPTED_KEYS:
                try:
                    config_value = encryption_service.decrypt(config_value)
                except Exception:
                    # 解密失败时使用空字符串
                    config_value = ""
            
            result.append({
                'id': setting.id,
                'config_key': setting.config_key,
                'config_name': setting.config_name,
                'config_value': config_value,
                'created_at': setting.created_at,
                'updated_at': setting.updated_at
            })
        
        return result
    
    @classmethod
    async def get_setting(cls, user_id: str, config_key: str) -> Optional[str]:
        """
        获取特定的设置值
        
        Args:
            user_id: 用户ID
            config_key: 配置项key
            
        Returns:
            配置值（已解密），如果不存在返回None
        """
        try:
            setting = await SystemSettings.get(user_id=user_id, config_key=config_key)
            
            config_value = setting.config_value
            if config_key in cls.ENCRYPTED_KEYS:
                config_value = encryption_service.decrypt(config_value)
            
            return config_value
        except DoesNotExist:
            return None
    
    @classmethod
    async def set_setting(cls, user_id: str, config_key: str, config_value: str) -> Dict[str, Any]:
        """
        设置配置项
        
        Args:
            user_id: 用户ID
            config_key: 配置项key
            config_value: 配置项值
            
        Returns:
            更新后的设置信息
        """
        # 验证配置项是否有效
        if config_key not in cls.CONFIG_KEYS:
            raise ValueError(f"Invalid config key: {config_key}")
        
        config_name = cls.CONFIG_KEYS[config_key]
        
        # 如果是需要加密的字段，进行加密
        stored_value = config_value
        if config_key in cls.ENCRYPTED_KEYS and config_value:
            stored_value = encryption_service.encrypt(config_value)
        
        # 尝试更新现有设置，如果不存在则创建新设置
        setting, created = await SystemSettings.update_or_create(
            user_id=user_id,
            config_key=config_key,
            defaults={
                'config_name': config_name,
                'config_value': stored_value
            }
        )
        
        return {
            'id': setting.id,
            'config_key': setting.config_key,
            'config_name': setting.config_name,
            'config_value': config_value,  # 返回原始值（未加密）
            'created_at': setting.created_at,
            'updated_at': setting.updated_at
        }
    
    @classmethod
    async def delete_setting(cls, user_id: str, config_key: str) -> bool:
        """
        删除配置项
        
        Args:
            user_id: 用户ID
            config_key: 配置项key
            
        Returns:
            是否成功删除
        """
        try:
            setting = await SystemSettings.get(user_id=user_id, config_key=config_key)
            await setting.delete()
            return True
        except DoesNotExist:
            return False
    
    @classmethod
    async def get_tongcheng_credentials(cls, user_id: str) -> Dict[str, Optional[str]]:
        """
        获取同程管家的登录凭证
        
        Args:
            user_id: 用户ID
            
        Returns:
            包含用户名和密码的字典
        """
        username = await cls.get_setting(user_id, 'tongcheng_username')
        password = await cls.get_setting(user_id, 'tongcheng_password')
        
        return {
            'username': username,
            'password': password
        }
    
    @classmethod
    async def get_tongcheng_credentials_with_raw_password(cls, user_id: str) -> Dict[str, Optional[str]]:
        """
        获取同程管家的登录凭证，密码返回数据库原始值（加密状态）
        
        Args:
            user_id: 用户ID
            
        Returns:
            包含用户名和原始密码的字典
        """
        username = await cls.get_setting(user_id, 'tongcheng_username')
        
        # 获取密码的原始值（不解密）
        try:
            setting = await SystemSettings.get(user_id=user_id, config_key='tongcheng_password')
            raw_password = setting.config_value  # 直接使用数据库中的值
        except DoesNotExist:
            raw_password = None
        
        return {
            'username': username,
            'password': raw_password
        } 