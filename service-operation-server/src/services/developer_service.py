from typing import Optional, List, Dict, Any
from loguru import logger
from tortoise.exceptions import DoesNotExist, IntegrityError

from src.db.models.developer import Developer


async def create_developer(name: str, email: str) -> Dict[str, Any]:
    """
    创建一个新的开发者。
    
    :param name: 开发者名称
    :param email: 开发者邮箱
    :return: 包含新开发者信息的字典
    """
    try:
        # 检查邮箱是否已存在
        existing_developer = await Developer.filter(email=email).first()
        if existing_developer:
            return {"error": f"邮箱已存在: {email}"}
        
        # 创建新开发者
        developer = await Developer.create(
            name=name,
            email=email
        )
        
        return {
            "id": developer.id,
            "name": developer.name,
            "email": developer.email,
            "created_at": developer.created_at
        }
    except IntegrityError:
        logger.error(f"创建开发者时出现完整性错误，可能是邮箱重复: {email}")
        return {"error": "创建开发者失败，请检查数据是否重复"}
    except Exception as e:
        logger.error(f"创建开发者时出错: {e}")
        return {"error": f"创建开发者时出错: {e}"}


async def get_developer_by_id(developer_id: int) -> Optional[Developer]:
    """
    通过ID获取开发者。
    
    :param developer_id: 开发者ID
    :return: 开发者对象或None
    """
    try:
        return await Developer.get(id=developer_id)
    except DoesNotExist:
        return None
    except Exception as e:
        logger.error(f"获取开发者时出错 ID={developer_id}: {e}")
        return None


async def get_all_developers() -> List[Dict[str, Any]]:
    """
    获取所有开发者。
    
    :return: 开发者列表
    """
    try:
        developers = await Developer.all()
        return [
            {
                "id": dev.id,
                "name": dev.name,
                "email": dev.email,
                "created_at": dev.created_at
            }
            for dev in developers
        ]
    except Exception as e:
        logger.error(f"获取所有开发者时出错: {e}")
        return []


async def update_developer(developer_id: int, name: Optional[str] = None, email: Optional[str] = None) -> Dict[str, Any]:
    """
    更新开发者信息。
    
    :param developer_id: 开发者ID
    :param name: 新的开发者名称（可选）
    :param email: 新的开发者邮箱（可选）
    :return: 包含更新后的开发者信息的字典
    """
    try:
        developer = await Developer.get(id=developer_id)
        
        # 更新字段
        if name is not None:
            developer.name = name
        if email is not None:
            # 检查新邮箱是否已被其他开发者使用
            if email != developer.email:
                existing = await Developer.filter(email=email).first()
                if existing and existing.id != developer_id:
                    return {"error": f"邮箱已被其他开发者使用: {email}"}
            developer.email = email
        
        # 保存更改
        await developer.save()
        
        return {
            "id": developer.id,
            "name": developer.name,
            "email": developer.email,
            "created_at": developer.created_at,
            "updated_at": developer.updated_at
        }
    except DoesNotExist:
        return {"error": f"开发者不存在: {developer_id}"}
    except Exception as e:
        logger.error(f"更新开发者时出错 ID={developer_id}: {e}")
        return {"error": f"更新开发者时出错: {e}"}


async def delete_developer(developer_id: int) -> bool:
    """
    删除开发者。
    
    :param developer_id: 开发者ID
    :return: 操作是否成功
    """
    try:
        developer = await Developer.get(id=developer_id)
        await developer.delete()
        return True
    except DoesNotExist:
        logger.warning(f"尝试删除不存在的开发者: {developer_id}")
        return False
    except Exception as e:
        logger.error(f"删除开发者时出错 ID={developer_id}: {e}")
        return False
