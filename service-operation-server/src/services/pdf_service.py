"""PDF处理服务"""
import io
import tempfile
from pathlib import Path
from typing import List, Tuple
from uuid import uuid4

from fastapi import HTTPException, UploadFile
from loguru import logger
from PIL import Image
import fitz  # PyMuPDF


class PdfService:
    """PDF处理服务类"""
    
    def __init__(self):
        self.convert_first_page_only = True  # 只转换第一页
        self.image_quality = 95  # 图片质量
        self.image_format = 'JPEG'  # 输出格式
        self.dpi = 200  # DPI设置
    
    async def convert_pdf_to_images(self, pdf_file: UploadFile) -> List[Tuple[bytes, str]]:
        """
        将PDF文件转换为图片
        
        Args:
            pdf_file: PDF文件
            
        Returns:
            List[Tuple[bytes, str]]: 图片数据和文件名的列表
        """
        try:
            logger.info(f"开始处理PDF文件: {pdf_file.filename}")
            
            # 读取PDF文件内容
            pdf_content = await pdf_file.read()
            
            # 验证PDF文件
            await self._validate_pdf(pdf_content)
            
            # 获取PDF页数
            page_count = self._get_pdf_page_count(pdf_content)
            logger.info(f"📄 PDF文件 '{pdf_file.filename}' 总页数: {page_count}，将转换第一页")

            if page_count == 0:
                raise HTTPException(
                    status_code=400,
                    detail=f"PDF文件 '{pdf_file.filename}' 没有有效页面"
                )
            
            # 使用PyMuPDF转换PDF为图片
            pdf_document = fitz.open(stream=pdf_content, filetype="pdf")

            # 只处理第一页
            image_data_list = []
            base_filename = self._get_base_filename(pdf_file.filename)

            # 获取第一页
            page = pdf_document[0]
            logger.info(f"📄 开始转换PDF第一页: {pdf_file.filename}")

            # 设置渲染参数
            mat = fitz.Matrix(self.dpi / 72, self.dpi / 72)  # 缩放矩阵
            pix = page.get_pixmap(matrix=mat)

            # 转换为PIL Image
            img_data = pix.tobytes("ppm")
            image = Image.open(io.BytesIO(img_data))

            # 生成文件名（不需要页码，因为只有一页）
            image_filename = f"{base_filename}.jpg"

            # 转换为字节数据
            image_bytes = self._image_to_bytes(image)

            image_data_list.append((image_bytes, image_filename))
            logger.info(f"✅ PDF第一页转换完成: {image_filename}, 大小: {len(image_bytes)} bytes")

            # 关闭PDF文档
            pdf_document.close()
            
            return image_data_list
            
        except Exception as e:
            logger.error(f"PDF转换失败: {e}")
            if isinstance(e, HTTPException):
                raise e
            raise HTTPException(status_code=500, detail=f"PDF转换失败: {str(e)}")
    
    async def _validate_pdf(self, pdf_content: bytes) -> None:
        """验证PDF文件"""
        try:
            # 检查文件大小 (限制50MB)
            max_size = 50 * 1024 * 1024  # 50MB
            if len(pdf_content) > max_size:
                raise HTTPException(
                    status_code=400,
                    detail=f"PDF文件过大，最大支持50MB，当前{len(pdf_content) / 1024 / 1024:.1f}MB"
                )
            
            # 验证PDF格式
            pdf_document = fitz.open(stream=pdf_content, filetype="pdf")
            if len(pdf_document) == 0:
                pdf_document.close()
                raise HTTPException(status_code=400, detail="PDF文件为空或损坏")
            pdf_document.close()
                
        except Exception as e:
            if isinstance(e, HTTPException):
                raise e
            raise HTTPException(status_code=400, detail="无效的PDF文件")
    
    def _get_pdf_page_count(self, pdf_content: bytes) -> int:
        """获取PDF页数"""
        try:
            pdf_document = fitz.open(stream=pdf_content, filetype="pdf")
            page_count = len(pdf_document)
            pdf_document.close()
            return page_count
        except Exception as e:
            logger.error(f"获取PDF页数失败: {e}")
            raise HTTPException(status_code=400, detail="无法读取PDF文件")
    
    def _get_base_filename(self, original_filename: str) -> str:
        """获取基础文件名（去除扩展名）"""
        if not original_filename:
            return f"pdf_{uuid4().hex[:8]}"
        
        # 移除扩展名
        base_name = Path(original_filename).stem
        
        # 清理文件名，移除特殊字符
        import re
        clean_name = re.sub(r'[^\w\-_\.]', '_', base_name)
        
        return clean_name or f"pdf_{uuid4().hex[:8]}"
    
    def _image_to_bytes(self, image: Image.Image) -> bytes:
        """将PIL图片转换为字节数据"""
        try:
            # 如果是RGBA模式，转换为RGB
            if image.mode == 'RGBA':
                # 创建白色背景
                background = Image.new('RGB', image.size, (255, 255, 255))
                background.paste(image, mask=image.split()[-1])  # 使用alpha通道作为mask
                image = background
            elif image.mode != 'RGB':
                image = image.convert('RGB')
            
            # 转换为字节数据
            img_byte_arr = io.BytesIO()
            image.save(
                img_byte_arr, 
                format=self.image_format,
                quality=self.image_quality,
                optimize=True
            )
            
            return img_byte_arr.getvalue()
            
        except Exception as e:
            logger.error(f"图片转换失败: {e}")
            raise HTTPException(status_code=500, detail="图片转换失败")
    
    def is_pdf_file(self, filename: str, content_type: str) -> bool:
        """检查是否为PDF文件"""
        if not filename:
            return False

        # 检查文件扩展名
        if filename.lower().endswith('.pdf'):
            return True

        # 检查MIME类型
        if content_type == 'application/pdf':
            return True

        return False


    
    async def process_mixed_files(self, files: List[UploadFile]) -> List[Tuple[bytes, str, bool]]:
        """
        处理混合文件（PDF和图片）
        
        Args:
            files: 文件列表
            
        Returns:
            List[Tuple[bytes, str, bool]]: (文件数据, 文件名, 是否来自PDF转换)
        """
        processed_files = []
        
        for file in files:
            if self.is_pdf_file(file.filename or '', file.content_type or ''):
                logger.info(f"检测到PDF文件: {file.filename}")
                
                # 转换PDF为图片
                pdf_images = await self.convert_pdf_to_images(file)
                
                for image_data, image_filename in pdf_images:
                    processed_files.append((image_data, image_filename, True))
                    
            else:
                # 直接处理图片文件
                logger.info(f"处理图片文件: {file.filename}")
                
                file_content = await file.read()
                processed_files.append((file_content, file.filename or 'unknown', False))
        
        logger.info(f"文件处理完成，共处理{len(processed_files)}个文件")
        return processed_files


# 创建全局PDF服务实例
pdf_service = PdfService()
