import secrets
import uuid
from typing import Optional, List, Dict, Any

from loguru import logger
from tortoise.exceptions import DoesNotExist

from src.db.models.api_key import ApiKey
from src.db.models.developer import Developer
from src.core.security import get_password_hash


async def generate_api_key() -> str:
    """
    生成一个安全的 API 密钥字符串。
    
    :return: 新生成的 API 密钥
    """
    # 使用 UUID + 随机字符生成安全的 API 密钥
    return f"key_{uuid.uuid4().hex}{secrets.token_hex(8)}"


async def generate_api_secret() -> tuple[str, str]:
    """
    生成一个安全的 API 密钥秘钥和其哈希值。
    
    :return: 元组 (明文秘钥, 哈希秘钥)
    """
    # 生成明文秘钥
    plain_secret = f"secret_{uuid.uuid4().hex}{secrets.token_hex(8)}"
    # 对秘钥进行哈希
    hashed_secret = get_password_hash(plain_secret)
    return plain_secret, hashed_secret


async def create_api_key(developer_id: int) -> Dict[str, Any]:
    """
    为指定开发者创建一个新的 API 密钥。
    
    :param developer_id: 开发者ID
    :return: 包含新密钥信息的字典
    """
    try:
        # 验证开发者存在
        developer = await Developer.get(id=developer_id)
        
        # 生成 API 密钥和秘钥
        api_key = await generate_api_key()
        plain_secret, hashed_secret = await generate_api_secret()
        
        # 创建 API 密钥记录
        key_record = await ApiKey.create(
            api_key=api_key,
            secret=hashed_secret,
            developer=developer,
            status="active"
        )
        
        # 返回结果，包括明文秘钥（仅返回一次）
        return {
            "id": key_record.id,
            "api_key": api_key,
            "secret": plain_secret,  # 明文秘钥，仅在创建时返回一次
            "developer_id": developer_id,
            "status": "active",
            "created_at": key_record.created_at,
        }
    except DoesNotExist:
        logger.error(f"开发者不存在: {developer_id}")
        return {"error": "开发者不存在"}
    except Exception as e:
        logger.error(f"创建 API 密钥时出错: {e}")
        return {"error": f"创建 API 密钥时出错: {e}"}


async def get_api_key_by_key(api_key_value: str) -> Optional[ApiKey]:
    """
    通过密钥值从数据库中检索活跃的 API 密钥记录。
    
    :param api_key_value: 要搜索的 API 密钥字符串
    :return: 如果找到并且处于活跃状态，则返回 ApiKey 对象，否则返回 None
    """
    try:
        # 使用 Tortoise ORM 查询数据库
        # 确保密钥处于活跃状态
        api_key_record = await ApiKey.get_or_none(
            api_key=api_key_value,
            status="active"  # 假设 'active' 是有效密钥的状态
        ).prefetch_related('developer')  # 如果稍后需要，获取相关的开发者
        return api_key_record
    except Exception as e:
        # 适当地记录错误
        logger.error(f"获取 API 密钥 {api_key_value} 时出错: {e}")
        return None


async def get_developer_api_keys(developer_id: int) -> List[Dict[str, Any]]:
    """
    获取指定开发者的所有 API 密钥。
    
    :param developer_id: 开发者ID
    :return: API 密钥列表
    """
    try:
        # 查询开发者的所有 API 密钥
        keys = await ApiKey.filter(developer_id=developer_id).all()
        
        # 格式化结果
        return [
            {
                "id": key.id,
                "api_key": key.api_key,
                "status": key.status,
                "created_at": key.created_at,
                "last_used_at": key.last_used_at,
            }
            for key in keys
        ]
    except Exception as e:
        logger.error(f"获取开发者 {developer_id} 的 API 密钥时出错: {e}")
        return []


async def revoke_api_key(key_id: int) -> bool:
    """
    撤销指定的 API 密钥（将状态设置为 inactive）。
    
    :param key_id: API 密钥 ID
    :return: 操作是否成功
    """
    try:
        # 查找 API 密钥
        key = await ApiKey.get(id=key_id)
        
        # 更新状态
        key.status = "inactive"
        await key.save()
        
        logger.info(f"已撤销 API 密钥: {key_id}")
        return True
    except DoesNotExist:
        logger.error(f"API 密钥不存在: {key_id}")
        return False
    except Exception as e:
        logger.error(f"撤销 API 密钥 {key_id} 时出错: {e}")
        return False


async def update_api_key_last_used(api_key: ApiKey) -> bool:
    """
    更新 API 密钥的最后使用时间。
    
    :param api_key: API 密钥对象
    :return: 操作是否成功
    """
    try:
        # 更新最后使用时间为当前时间
        from datetime import datetime
        api_key.last_used_at = datetime.now()
        await api_key.save()
        
        return True
    except Exception as e:
        logger.error(f"更新 API 密钥 {api_key.id} 的最后使用时间时出错: {e}")
        return False