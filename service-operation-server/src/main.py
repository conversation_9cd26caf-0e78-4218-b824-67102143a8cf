import uvicorn

from src.gunicorn_runner import GunicornApplication
from src.core.config import settings


def main() -> None:
    """应用程序入口点。"""
    # 在开发模式下使用uvicorn的热重载
    if settings.reload:
        uvicorn.run(
            "src.app:get_app",  # 更新为新的应用路径
            workers=settings.workers_count,
            host=settings.host,
            port=settings.port,
            reload=settings.reload,
            log_level=settings.log_level.lower(),  # 使用枚举的值
            factory=True,
        )
    else:
        # 在非热重载模式下使用gunicorn
        # 因为gunicorn不支持reload功能
        GunicornApplication(
            "src.app:get_app",  # 更新为新的应用路径
            host=settings.host,
            port=settings.port,
            workers=settings.workers_count,
            factory=True,
            accesslog="-",
            loglevel=settings.log_level.lower(),  # 使用枚举的值
            access_log_format='%r "-" %s "-" %Tf',
        ).run()


if __name__ == "__main__":
    main()
