import jwt
import secrets
from datetime import datetime, timedelta, timezone
from passlib.context import Crypt<PERSON>ontext
from typing import Any, Dict, Optional, Tuple

from src.core.config import settings

# Password hashing context using bcrypt
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# 使用正确的配置项名称
ALGORITHM = settings.jwt_algorithm
JWT_SECRET_KEY = settings.jwt_secret
ACCESS_TOKEN_EXPIRE_MINUTES = settings.access_token_expire_minutes
REFRESH_TOKEN_EXPIRE_DAYS = settings.refresh_token_expire_days

def create_access_token(subject: Any, expires_delta: Optional[timedelta] = None) -> str:
    """
    Generates a JWT access token.

    :param subject: The subject of the token (e.g., user ID).
    :param expires_delta: Optional timedelta for token expiration. Uses setting if None.
    :return: The encoded JWT token.
    """
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)

    to_encode: Dict[str, Any] = {"exp": expire, "sub": str(subject)}
    encoded_jwt = jwt.encode(to_encode, JWT_SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_token(token: str) -> Optional[str]:
    """
    Verifies the JWT token and returns the subject (user ID).

    :param token: The JWT token string.
    :return: The subject (user ID) if the token is valid, None otherwise.
    """
    try:
        payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[ALGORITHM])
        # Optionally add more checks here (e.g., token type, issuer)
        subject = payload.get("sub")
        return subject
    except jwt.ExpiredSignatureError:
        # Token has expired
        return None
    except jwt.InvalidTokenError:
        # Any other invalid token error
        return None

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    Verifies a plain password against a hashed password.

    :param plain_password: The plain text password.
    :param hashed_password: The hashed password from the database.
    :return: True if the passwords match, False otherwise.
    """
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """
    Hashes a plain password using bcrypt.

    :param password: The plain text password.
    :return: The hashed password.
    """
    return pwd_context.hash(password)

def generate_refresh_token() -> str:
    """
    生成安全的refresh token
    
    :return: 随机生成的refresh token字符串
    """
    return secrets.token_urlsafe(32)

def create_refresh_token_expiry() -> datetime:
    """
    创建refresh token过期时间
    
    :return: refresh token过期时间
    """
    return datetime.now(timezone.utc) + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)

def create_token_pair(user_id: int) -> Tuple[str, str, datetime]:
    """
    创建access token和refresh token对
    
    :param user_id: 用户ID
    :return: (access_token, refresh_token, refresh_token_expiry)
    """
    access_token = create_access_token(subject=user_id)
    refresh_token = generate_refresh_token()
    refresh_token_expiry = create_refresh_token_expiry()
    
    return access_token, refresh_token, refresh_token_expiry

def verify_refresh_token_format(token: str) -> bool:
    """
    验证refresh token格式是否正确
    
    :param token: refresh token
    :return: True if valid format, False otherwise
    """
    try:
        # 简单的格式验证，确保是合法的URL安全base64字符串
        return len(token) >= 32 and token.replace('-', '').replace('_', '').isalnum()
    except:
        return False 