"""
基础Pydantic模型定义，为所有API模型提供统一的基类和工具函数。
"""
from datetime import datetime
from typing import Any, Dict, Optional, TypeVar, Generic, Type, List

from pydantic import BaseModel, ConfigDict, Field, model_validator
from pydantic import BaseModel


class CustomBaseModel(BaseModel):
    """
    自定义基础模型，为所有API模型提供统一配置和方法。
    """
    model_config = ConfigDict(
        # 使用驼峰式命名转换
        alias_generator=None,
        # 允许填充额外字段（但序列化时不会输出）
        extra='ignore',
        # 允许从ORM模型转换
        from_attributes=True,
        # 使用JSON兼容的编码器
        json_encoders={
            # 日期时间格式化为ISO格式
            datetime: lambda dt: dt.isoformat() if dt else None,
        },
        # 不允许protected字段的私有属性访问
        protected_namespaces=(),
        # 移除默认值为None的字段
        exclude_none=True,
    )

    def serializable_dict(self, **kwargs) -> Dict[str, Any]:
        """
        返回一个可以被JSON序列化的字典。
        
        :param kwargs: 额外参数传递给模型的dict()方法
        :return: 可序列化的字典
        """
        default_kwargs = {
            'exclude_none': True,
            'by_alias': True,
        }
        default_kwargs.update(kwargs)
        return self.model_dump(**default_kwargs)
    
    @classmethod
    def from_orm_optional(cls, obj: Any) -> Optional['CustomBaseModel']:
        """
        从ORM对象创建模型实例，如果对象为None则返回None。
        
        :param obj: ORM对象
        :return: 模型实例或None
        """
        if obj is None:
            return None
        return cls.model_validate(obj)


# 定义响应的通用模型
T = TypeVar('T')

class ResponseBase(CustomBaseModel):
    """基础响应模型，包含状态码和消息。"""
    status_code: int = Field(200, description="HTTP状态码")
    message: str = Field("Success", description="响应消息")


class DataResponse(ResponseBase, Generic[T]):
    """带数据的响应模型。"""
    data: Optional[T] = Field(None, description="响应数据")


class ListResponse(ResponseBase, Generic[T]):
    """列表数据响应模型。"""
    data: List[T] = Field([], description="响应数据列表")
    total: int = Field(0, description="总记录数")
    page: int = Field(1, description="当前页码")
    page_size: int = Field(10, description="每页记录数")
    

class ErrorResponse(ResponseBase):
    """错误响应模型。"""
    status_code: int = Field(400, description="HTTP状态码")
    message: str = Field("Error", description="错误消息")
    details: Optional[Any] = Field(None, description="详细错误信息")
