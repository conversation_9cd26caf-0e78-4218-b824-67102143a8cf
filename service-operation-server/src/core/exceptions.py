"""
自定义异常定义和全局异常处理器。
"""
from typing import Any, Dict, Optional, Union

from fastapi import FastAPI, Request, status
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from pydantic import ValidationError
from tortoise.exceptions import DoesNotExist, IntegrityError

from src.core.base_models import ErrorResponse


class APIError(Exception):
    """API错误基类。"""
    
    def __init__(
        self, 
        message: str = "服务器内部错误",
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
        details: Optional[Any] = None
    ):
        self.message = message
        self.status_code = status_code
        self.details = details
        super().__init__(self.message)


class NotFoundError(APIError):
    """资源未找到错误。"""
    
    def __init__(self, message: str = "请求的资源不存在", details: Optional[Any] = None):
        super().__init__(
            message=message,
            status_code=status.HTTP_404_NOT_FOUND,
            details=details
        )


class ValidationAPIError(APIError):
    """数据验证错误。"""
    
    def __init__(self, message: str = "数据验证失败", details: Optional[Any] = None):
        super().__init__(
            message=message,
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            details=details
        )


class AuthenticationError(APIError):
    """认证错误。"""
    
    def __init__(
        self, 
        message: str = "认证失败", 
        details: Optional[Any] = None,
        headers: Optional[Dict[str, str]] = None
    ):
        self.headers = headers
        super().__init__(
            message=message,
            status_code=status.HTTP_401_UNAUTHORIZED,
            details=details
        )


class AuthorizationError(APIError):
    """授权错误。"""
    
    def __init__(self, message: str = "没有操作权限", details: Optional[Any] = None):
        super().__init__(
            message=message,
            status_code=status.HTTP_403_FORBIDDEN,
            details=details
        )


class BadRequestError(APIError):
    """请求参数错误。"""
    
    def __init__(self, message: str = "请求参数错误", details: Optional[Any] = None):
        super().__init__(
            message=message,
            status_code=status.HTTP_400_BAD_REQUEST,
            details=details
        )


class ConflictError(APIError):
    """资源冲突错误。"""
    
    def __init__(self, message: str = "资源冲突", details: Optional[Any] = None):
        super().__init__(
            message=message,
            status_code=status.HTTP_409_CONFLICT,
            details=details
        )


def setup_exception_handlers(app: FastAPI) -> None:
    """
    设置全局异常处理器。
    
    :param app: FastAPI应用实例
    """
    
    @app.exception_handler(APIError)
    async def handle_api_error(request: Request, exc: APIError) -> JSONResponse:
        """处理自定义API错误。"""
        response = ErrorResponse(
            status_code=exc.status_code,
            message=exc.message,
            details=exc.details
        )
        headers = getattr(exc, "headers", None)
        return JSONResponse(
            status_code=exc.status_code,
            content=response.serializable_dict(),
            headers=headers
        )
    
    @app.exception_handler(RequestValidationError)
    async def handle_validation_error(
        request: Request, exc: RequestValidationError
    ) -> JSONResponse:
        """处理请求验证错误。"""
        response = ErrorResponse(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            message="请求数据验证失败",
            details=exc.errors()
        )
        return JSONResponse(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            content=response.serializable_dict()
        )
    
    @app.exception_handler(ValidationError)
    async def handle_pydantic_validation_error(
        request: Request, exc: ValidationError
    ) -> JSONResponse:
        """处理Pydantic验证错误。"""
        response = ErrorResponse(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            message="数据验证失败",
            details=exc.errors()
        )
        return JSONResponse(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            content=response.serializable_dict()
        )
    
    @app.exception_handler(DoesNotExist)
    async def handle_does_not_exist(
        request: Request, exc: DoesNotExist
    ) -> JSONResponse:
        """处理Tortoise ORM的记录不存在错误。"""
        response = ErrorResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            message="请求的资源不存在",
            details=str(exc)
        )
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content=response.serializable_dict()
        )
    
    @app.exception_handler(IntegrityError)
    async def handle_integrity_error(
        request: Request, exc: IntegrityError
    ) -> JSONResponse:
        """处理Tortoise ORM的完整性错误。"""
        response = ErrorResponse(
            status_code=status.HTTP_409_CONFLICT,
            message="数据完整性错误",
            details=str(exc)
        )
        return JSONResponse(
            status_code=status.HTTP_409_CONFLICT,
            content=response.serializable_dict()
        )
