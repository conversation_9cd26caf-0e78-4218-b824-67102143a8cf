import logging
import sys
from typing import Union

from loguru import logger

from src.core.config import settings


class InterceptHandler(logging.Handler):
    """
    Default handler from examples in loguru documentation.

    This handler intercepts all log requests and
    passes them to loguru.

    For more info see:
    https://loguru.readthedocs.io/en/stable/overview.html#entirely-compatible-with-standard-logging
    """

    def emit(self, record: logging.LogRecord) -> None:  # pragma: no cover
        """
        Propagates logs to loguru.

        :param record: record to log.
        """
        try:
            level: Union[str, int] = logger.level(record.levelname).name
        except ValueError:
            level = record.levelno

        # Find caller from where originated the logged message
        frame, depth = logging.currentframe(), 2
        while frame.f_code.co_filename == logging.__file__:
            frame = frame.f_back  # type: ignore
            depth += 1

        logger.opt(depth=depth, exception=record.exc_info).log(
            level,
            record.getMessage(),
        )


def configure_logging() -> None:  # pragma: no cover
    """Configures logging."""
    intercept_handler = InterceptHandler()

    logging.basicConfig(handlers=[intercept_handler], level=logging.NOTSET)

    for logger_name in logging.root.manager.loggerDict:
        if logger_name.startswith("uvicorn."):
            logging.getLogger(logger_name).handlers = []

    # change handler for default uvicorn logger
    logging.getLogger("uvicorn").handlers = [intercept_handler]
    logging.getLogger("uvicorn.access").handlers = [intercept_handler]

    # 设置日志输出、级别和格式
    logger.remove()

    # 控制台输出
    logger.add(
        sys.stdout,
        level=settings.log_level,
        format=settings.log_format,
    )

    # 文件输出（如果在生产环境）
    if settings.environment in ["production", "prod"]:
        # 应用日志
        logger.add(
            "logs/app.log",
            level=settings.log_level,
            format=settings.log_format,
            rotation="100 MB",  # 文件大小轮转
            retention="30 days",  # 保留30天
            compression="zip",  # 压缩旧日志
            enqueue=True,  # 异步写入
        )

        # 错误日志单独记录
        logger.add(
            "logs/error.log",
            level="ERROR",
            format=settings.log_format,
            rotation="50 MB",
            retention="60 days",  # 错误日志保留更久
            compression="zip",
            enqueue=True,
            filter=lambda record: record["level"].name == "ERROR"
        )
