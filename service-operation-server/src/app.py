from contextlib import asynccontextmanager
from importlib import metadata

from fastapi import FastAPI, Request, HTTPException
from fastapi.responses import UJSONResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from tortoise.contrib.fastapi import register_tortoise
from tortoise import Tortoise
from loguru import logger

from src.core.config import settings
from src.core.log import configure_logging
from src.api.router import api_router
from src.db.config import get_tortoise_config, TORTOISE_ORM_FASTAPI_CONFIG
# 显式导入所有模型，确保它们被正确注册
from src.db.models import User, Developer, ApiKey

@asynccontextmanager
async def lifespan(app: FastAPI):
    """管理应用程序生命周期事件。"""
    # 启动事件
    print(f"{settings.app_name} 应用程序启动中...")
    
    # 启动护照识别任务处理器
    try:
        from src.services.passport_task_service import passport_task_service
        await passport_task_service.start_task_processor()
        print("护照识别任务处理器已启动")
    except Exception as e:
        print(f"启动护照识别任务处理器失败: {e}")
    
    # 注意：我们不在这里初始化 Tortoise ORM
    # 而是使用 register_tortoise 在应用程序创建时进行初始化
    
    yield
    
    # 关闭事件
    print(f"{settings.app_name} 应用程序关闭中...")
    
    # 停止护照识别任务处理器
    try:
        from src.services.passport_task_service import passport_task_service
        await passport_task_service.stop_task_processor()
        print("护照识别任务处理器已停止")
    except Exception as e:
        print(f"停止护照识别任务处理器失败: {e}")
    
    # 注意：register_tortoise 会自动处理连接的关闭

def get_app() -> FastAPI:
    """
    获取 FastAPI 应用程序实例。

    这是应用程序的主要构造函数。

    :return: 应用程序实例。
    """
    configure_logging()
    
    # 创建 FastAPI 应用实例
    app = FastAPI(
        title=settings.app_name,
        description="FastAPI 基础开发框架",
        version=settings.version,  # 使用配置中的版本号
        docs_url="/api/docs" if settings.environment != "production" else None,
        redoc_url="/api/redoc" if settings.environment != "production" else None,
        openapi_url="/api/openapi.json" if settings.environment != "production" else None,
        default_response_class=UJSONResponse,
        lifespan=lifespan,
        # 添加 JWT Bearer 认证的 OpenAPI 配置
        swagger_ui_init_oauth={
            "usePkceWithAuthorizationCodeGrant": True,
            "useBasicAuthenticationWithAccessCodeGrant": True
        }
    )
    
    # CORS由nginx代理统一处理，后端不再配置CORS中间件

    # 全局异常处理器
    @app.exception_handler(Exception)
    async def global_exception_handler(request: Request, exc: Exception):
        """全局异常处理器，捕获所有未处理的异常并返回给前端"""
        
        # 获取请求体（如果是POST/PUT等请求）
        request_body = None
        try:
            if request.method in ["POST", "PUT", "PATCH"]:
                request_body = await request.body()
                if request_body:
                    request_body = request_body.decode('utf-8')[:500]  # 限制长度避免日志过长
        except:
            request_body = "无法读取请求体"
        
        # 记录详细的错误信息到日志
        logger.error(f"🚨 全局异常拦截 - URL: {request.url}")
        logger.error(f"请求方法: {request.method}")
        logger.error(f"查询参数: {dict(request.query_params)}")
        logger.error(f"请求头: {dict(request.headers)}")
        if request_body:
            logger.error(f"请求体: {request_body}")
        logger.error(f"异常类型: {type(exc).__name__}")
        logger.error(f"异常消息: {str(exc)}")
        logger.exception("完整异常堆栈:")
        
        # 根据异常类型返回不同的响应
        
        # 1. HTTPException（如404, 401等）
        if isinstance(exc, HTTPException):
            return JSONResponse(
                status_code=exc.status_code,
                content={
                    "success": False,
                    "error": {
                        "message": exc.detail,
                        "type": "HTTPException",
                        "code": exc.status_code
                    },
                    "timestamp": logger._core.formatters[0].datefmt if hasattr(logger, '_core') else None
                }
            )
        
        # 2. 验证异常（来自pydantic）
        from pydantic import ValidationError
        if isinstance(exc, ValidationError):
            # 处理Pydantic验证错误，将复杂对象转换为字符串
            error_details = "参数格式错误"
            if settings.environment != "production":
                # 将验证错误转换为用户友好的字符串格式
                error_list = []
                for error in exc.errors():
                    field = " -> ".join(str(loc) for loc in error.get('loc', []))
                    msg = error.get('msg', '验证失败')
                    error_list.append(f"{field}: {msg}")
                error_details = "; ".join(error_list) if error_list else "参数验证失败"
            
            return JSONResponse(
                status_code=422,
                content={
                    "success": False,
                    "error": {
                        "message": "请求参数验证失败",
                        "type": "ValidationError",
                        "code": 422,
                        "details": error_details
                    }
                }
            )
        
        # 3. 数据库异常
        from tortoise.exceptions import DoesNotExist, IntegrityError, OperationalError
        if isinstance(exc, DoesNotExist):
            return JSONResponse(
                status_code=404,
                content={
                    "success": False,
                    "error": {
                        "message": "请求的资源不存在",
                        "type": "DoesNotExist",
                        "code": 404
                    }
                }
            )
        elif isinstance(exc, IntegrityError):
            return JSONResponse(
                status_code=400,
                content={
                    "success": False,
                    "error": {
                        "message": "数据完整性错误",
                        "type": "IntegrityError", 
                        "code": 400,
                        "details": str(exc) if settings.environment != "production" else "数据约束冲突"
                    }
                }
            )
        elif isinstance(exc, OperationalError):
            return JSONResponse(
                status_code=503,
                content={
                    "success": False,
                    "error": {
                        "message": "数据库操作失败",
                        "type": "OperationalError",
                        "code": 503,
                        "details": str(exc) if settings.environment != "production" else "数据库暂时不可用"
                    }
                }
            )
        
        # 4. 自定义API异常
        from src.core.exceptions import APIError
        if hasattr(exc, 'status_code') and hasattr(exc, 'message'):
            return JSONResponse(
                status_code=getattr(exc, 'status_code', 500),
                content={
                    "success": False,
                    "error": {
                        "message": getattr(exc, 'message', str(exc)),
                        "type": type(exc).__name__,
                        "code": getattr(exc, 'status_code', 500),
                        "details": getattr(exc, 'details', None)
                    }
                }
            )
        
        # 5. 其他所有未捕获的异常
        error_message = "服务器内部错误，请稍后重试"
        error_details = None
        
        # 在非生产环境提供更详细的错误信息
        if settings.environment != "production":
            error_message = f"服务器内部错误: {str(exc)}"
            error_details = {
                "exception_type": type(exc).__name__,
                "exception_message": str(exc),
                "request_path": str(request.url.path),
                "request_method": request.method
            }
        
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "error": {
                    "message": error_message,
                    "type": "InternalServerError",
                    "code": 500,
                    "details": error_details
                }
            }
        )

    # 根级别健康检查端点
    @app.get("/health", summary="健康检查", description="返回服务器运行状态")
    async def health():
        return {"message": "the server is running"}

    # 包含主 API 路由
    app.include_router(router=api_router, prefix="/api")
    
    # 添加静态文件服务
    if settings.upload_path.exists():
        app.mount("/uploads", StaticFiles(directory=str(settings.upload_path)), name="uploads")

    # 注册 Tortoise ORM
    register_tortoise(
        app,
        config=get_tortoise_config(),
        **TORTOISE_ORM_FASTAPI_CONFIG
    )

    return app

app = get_app() # Create the app instance
