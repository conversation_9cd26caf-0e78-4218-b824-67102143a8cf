"""数据库模块初始化。"""

from tortoise import Tortoise
from src.core.config import settings

TORTOISE_ORM_CONFIG = {
    "connections": {"default": str(settings.db_url)},
    "apps": {
        "models": {
            "models": ["src.db.models", "aerich.models"],
            "default_connection": "default",
        },
    },
    "use_tz": False,
    "timezone": "UTC"
}

async def init_db():
    """初始化数据库连接。"""
    await Tortoise.init(config=TORTOISE_ORM_CONFIG)

async def close_db():
    """关闭数据库连接。"""
    await Tortoise.close_connections()

async def generate_schemas():
    """生成数据库架构（仅用于开发和测试环境）。"""
    if settings.environment != "production":
        await Tortoise.generate_schemas()
