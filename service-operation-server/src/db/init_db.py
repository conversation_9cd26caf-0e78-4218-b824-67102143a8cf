"""
数据库初始化模块
提供数据库初始化功能
"""

import asyncio
from tortoise import Tortoise
from .config import TORTOISE_CONFIG

async def init_db():
    """初始化数据库"""
    print("正在初始化数据库...")
    await Tortoise.init(config=TORTOISE_CONFIG)
    print("正在生成表结构...")
    await Tortoise.generate_schemas()
    print('数据库表创建成功')
    await Tortoise.close_connections()

# 保留可以直接运行的功能（仅用于测试）
if __name__ == "__main__":
    import sys
    import os
    # 添加项目根目录到Python路径以支持直接运行
    sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))
    from src.db.config import TORTOISE_CONFIG
    asyncio.run(init_db()) 