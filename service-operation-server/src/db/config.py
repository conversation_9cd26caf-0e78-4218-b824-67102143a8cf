"""Tortoise ORM 数据库配置模块。

此模块包含 Tortoise ORM 的配置，用于初始化数据库连接和注册模型。
它是应用程序数据库层的核心配置源。
"""

from typing import Dict, Any, List

from src.core.config import settings

# 列出所有模型模块
MODELS_MODULES: List[str] = [
    "src.db.models",  # 引用整个模型包，而不是单独的模块
]

# Tortoise ORM 主配置
# 此配置用于应用程序初始化和 Aerich 迁移工具
# 在 macOS 上，MySQL 的默认 socket 路径通常是 /tmp/mysql.sock
# 使用 socket 连接通常比 TCP/IP 连接更可靠
SOCKET_PATH = "/tmp/mysql.sock"

# 使用 Tortoise ORM 支持的连接字符串格式
# 注意：Tortoise ORM 不支持在 URL 中指定 driver 参数
DB_URL = f"mysql://{settings.db_user}:{settings.db_password}@{settings.db_host}:{settings.db_port}/{settings.db_base}"

TORTOISE_CONFIG: Dict[str, Any] = {
    "connections": {
        "default": DB_URL,
    },
    "apps": {
        "models": {
            "models": [*MODELS_MODULES, "aerich.models"],
            "default_connection": "default",
        },
    },
    "use_tz": False,
    "timezone": "UTC",
    # 在调试模式下记录所有SQL查询
    "log_queries": settings.db_echo
}

# 用于 register_tortoise 函数的额外参数
TORTOISE_ORM_FASTAPI_CONFIG = {
    "generate_schemas": False,  # 禁用自动模式生成，避免权限问题
    "add_exception_handlers": True,  # 添加异常处理器以处理 ORM 异常
}

# 获取完整的 FastAPI 配置函数
def get_tortoise_config() -> Dict[str, Any]:
    """获取用于 FastAPI 集成的完整 Tortoise ORM 配置。

    Returns:
        Dict[str, Any]: 包含所有 Tortoise ORM 配置的字典。
    """
    return TORTOISE_CONFIG
