from tortoise import fields
from datetime import datetime

from .base import AbstractBaseModel

class TaskToTrainOrder(AbstractBaseModel):
    """任务订单映射表模型"""
    
    # 项目ID
    project_id = fields.BigIntField(description="项目ID")
    
    # 任务ID（关联到ProjectTask表）
    task_id = fields.CharField(max_length=20, description="任务ID")
    
    # 订单ID（关联到TrainOrder表）
    order_id = fields.BigIntField(description="订单ID")
    
    # 订单状态
    order_status = fields.CharField(
        max_length=20, 
        default="initial", 
        description="订单状态",
        help_text="订单状态: initial(待预订), submitted(已提交), processing(处理中), completed(已完成), failed(失败)"
    )
    
    # 订单类型
    order_type = fields.CharField(
        max_length=20,
        default="book",
        description="订单类型",
        help_text="订单类型: book(预订), book_and_issue(预订且出票)"
    )
    
    # 时间信息
    start_time = fields.DatetimeField(description="开始时间", null=False, auto_now_add=True)
    end_time = fields.DatetimeField(description="结束时间", null=False, auto_now_add=True)
    
    # 处理消息
    message = fields.TextField(description="处理消息", null=True)

    class Meta:
        table = "task_to_train_orders"
        ordering = ["-created_at"]
        # 唯一约束：同一个任务下的同一个订单只能有一条记录
        unique_together = [("task_id", "order_id")]

    def __str__(self) -> str:
        return f"任务订单映射 - 任务:{self.task_id} - 订单:{self.order_id} - 状态:{self.order_status}"
    
    async def mark_started(self, message: str = None):
        """标记开始处理"""
        self.start_time = datetime.now()
        self.order_status = "processing"
        if message:
            self.message = message
        await self.save()
    
    async def mark_completed(self, message: str = None):
        """标记完成"""
        self.end_time = datetime.now()
        self.order_status = "completed"
        if message:
            self.message = message
        await self.save()
    
    async def mark_failed(self, message: str = None):
        """标记失败"""
        self.end_time = datetime.now()
        self.order_status = "failed"
        if message:
            self.message = message
        await self.save() 