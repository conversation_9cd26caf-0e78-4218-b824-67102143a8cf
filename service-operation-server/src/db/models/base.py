from tortoise import fields, models
from tortoise.contrib.pydantic import pydantic_model_creator

class AbstractBaseModel(models.Model):
    """Abstract base model with common fields like ID and timestamps."""
    id = fields.BigIntField(pk=True)
    created_at = fields.DatetimeField(auto_now_add=True)
    updated_at = fields.DatetimeField(auto_now=True)

    class Meta:
        abstract = True
    
    def _normalize_varchar_fields(self):
        """
        统一处理VARCHAR类型字段的空值，将None转换为空字符串
        这个方法会遍历所有CharField字段，确保它们不会保存None值
        """
        for field_name, field_obj in self._meta.fields_map.items():
            if isinstance(field_obj, fields.CharField):
                current_value = getattr(self, field_name, None)
                # 如果当前值为None，设置为空字符串
                if current_value is None:
                    setattr(self, field_name, "")
    
    async def save(self, *args, **kwargs):
        """重写save方法，在保存前处理VARCHAR字段的空值"""
        # 处理VARCHAR字段的空值
        self._normalize_varchar_fields()
        # 调用父类的save方法
        await super().save(*args, **kwargs) 