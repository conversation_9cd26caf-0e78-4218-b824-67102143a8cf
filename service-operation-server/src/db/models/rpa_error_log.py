"""RPA错误日志数据模型"""

from tortoise import fields
from .base import AbstractBaseModel


class RpaErrorLog(AbstractBaseModel):
    """RPA错误日志表，用于记录RPA自动化过程中的错误信息"""

    # 错误标识
    error_id = fields.CharField(max_length=50, unique=True, description="错误唯一标识")

    # 关联信息
    task_id = fields.CharField(max_length=100, description="关联任务ID")
    order_id = fields.CharField(max_length=100, description="关联订单ID")

    # 时间信息
    error_timestamp = fields.DatetimeField(description="错误发生时间")

    # 模块和函数信息
    module = fields.CharField(max_length=100, description="模块名称")
    function_name = fields.CharField(max_length=100, description="函数名称")

    # 错误信息
    error_type = fields.CharField(max_length=100, description="错误类型")
    error_message = fields.TextField(description="错误消息", null=True)
    severity = fields.CharField(max_length=10, default="medium", description="严重程度")

    # 页面信息
    page_url = fields.TextField(description="页面URL", null=True)
    page_title = fields.CharField(max_length=500, description="页面标题")
    action_attempted = fields.TextField(description="尝试的操作", null=True)

    # 浏览器信息
    user_agent = fields.CharField(max_length=500, description="用户代理")
    browser_info = fields.TextField(description="浏览器信息(JSON格式)", null=True)

    # 调试信息
    stack_trace = fields.TextField(description="堆栈跟踪", null=True)
    additional_context = fields.TextField(description="额外上下文信息(JSON格式)", null=True)

    class Meta:
        table = "rpa_error_logs"
        ordering = ["-error_timestamp"]

    def __str__(self) -> str:
        return f"RPA错误日志 - 任务:{self.task_id} - 订单:{self.order_id} - 类型:{self.error_type}"

    def __str__(self) -> str:
        return f"RPA错误日志 - 任务:{self.task_id} - 订单:{self.order_id} - 类型:{self.error_type}"
