from tortoise.models import Model
from tortoise import fields
from datetime import datetime, timezone


class RefreshToken(Model):
    id = fields.IntField(pk=True)
    user_id = fields.IntField()
    token = fields.CharField(max_length=255, unique=True)
    expires_at = fields.DatetimeField()
    created_at = fields.DatetimeField(auto_now_add=True)
    updated_at = fields.DatetimeField(auto_now=True)
    is_active = fields.BooleanField(default=True)
    device_info = fields.CharField(max_length=500, null=True)  # 存储设备信息
    
    class Meta:
        table = "refresh_tokens"
        
    @classmethod
    async def create_token(cls, user_id: int, token: str, expires_at: datetime, device_info: str = None):
        """创建refresh token记录"""
        return await cls.create(
            user_id=user_id,
            token=token,
            expires_at=expires_at,
            device_info=device_info
        )
    
    @classmethod
    async def get_active_token(cls, token: str):
        """获取有效的refresh token"""
        return await cls.filter(
            token=token,
            is_active=True,
            expires_at__gt=datetime.now(timezone.utc)
        ).first()
    
    @classmethod
    async def revoke_token(cls, token: str):
        """撤销token"""
        await cls.filter(token=token).update(is_active=False)
    
    @classmethod
    async def revoke_user_tokens(cls, user_id: int):
        """撤销用户所有token"""
        await cls.filter(user_id=user_id).update(is_active=False)
    
    @classmethod
    async def cleanup_expired_tokens(cls):
        """清理过期的token"""
        await cls.filter(expires_at__lt=datetime.now(timezone.utc)).delete()