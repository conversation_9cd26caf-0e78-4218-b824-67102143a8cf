from tortoise import fields
from tortoise.contrib.pydantic import pydantic_model_creator

from .base import AbstractBaseModel

class ApiKey(AbstractBaseModel):
    """Represents an API key associated with a developer."""
    api_key = fields.CharField(max_length=255, unique=True, description="The public API key string")
    secret = fields.CharField(max_length=255, description="The hashed API secret") # Store hashed secret
    status = fields.CharField(max_length=50, default="active", description="Status (e.g., active, inactive)")
    last_used_at = fields.DatetimeField(null=True)

    # Foreign Key relationship to Developer - 使用应用名称.模型名称格式
    # 在 Tortoise ORM 配置中，我们的应用名称是 "models"
    developer = fields.ForeignKeyField(
        "models.Developer", related_name="api_keys", on_delete=fields.CASCADE
    )

    class Meta:
        table = "api_keys"
        ordering = ["-created_at"]

    def __str__(self) -> str:
        return f"API Key for {self.developer_id}"

# 注意：Pydantic 模型创建器应该在所有模型定义完成后使用
# 这里先注释掉，等所有模型都正确加载后再使用
# ApiKey_Pydantic = pydantic_model_creator(ApiKey, name="ApiKey")
# ApiKeyIn_Pydantic = pydantic_model_creator(ApiKey, name="ApiKeyIn", exclude_readonly=True, exclude=["secret"])