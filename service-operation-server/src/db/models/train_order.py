from tortoise import fields
from decimal import Decimal

from .base import AbstractBaseModel

class TrainOrder(AbstractBaseModel):
    """火车票订单模型"""
    
    # 项目ID（外键关联到Project表）
    project_id = fields.BigIntField(description="项目ID")
    
    # 序号
    sequence_number = fields.IntField(description="序号")
    
    # 出行人信息
    traveler_full_name = fields.Char<PERSON>ield(max_length=100, description="出行人姓名")
    traveler_surname = fields.Cha<PERSON><PERSON><PERSON>(max_length=50, description="出行人姓", null=True)
    traveler_given_name = fields.CharField(max_length=50, description="出行人名", null=True)
    nationality = fields.CharField(max_length=50, description="国籍", null=True)
    gender = fields.Char<PERSON><PERSON>(max_length=10, description="性别", null=True)
    birth_date = fields.Char<PERSON><PERSON>(max_length=50, description="出生日期", default="")
    
    # 证件信息
    id_type = fields.Char<PERSON>ield(max_length=20, description="证件类型", null=True)
    id_number = fields.CharField(max_length=50, description="证件号码", null=True)
    id_expiry_date = fields.Char<PERSON>ield(max_length=50, description="证件有效期至", default="")
    
    # 联系信息
    mobile_phone = fields.CharField(max_length=20, description="手机号", null=True)
    mobile_phone_country_code = fields.CharField(max_length=10, description="手机号国际区号", null=True, default="+86")
    
    # 行程信息
    travel_date = fields.CharField(max_length=50, description="出行日期", default="")
    departure_station = fields.CharField(max_length=100, description="出发站名", null=True)
    arrival_station = fields.CharField(max_length=100, description="到达站名", null=True)
    train_number = fields.CharField(max_length=20, description="车次", null=True)
    seat_type = fields.CharField(max_length=50, description="座位类型", null=True)
    departure_time = fields.CharField(max_length=50, description="出发时间", default="")
    arrival_time = fields.CharField(max_length=50, description="到达时间", default="")
    
    # 管理信息
    cost_center = fields.CharField(max_length=100, description="成本中心", null=True)
    trip_submission_item = fields.CharField(max_length=200, description="行程提交项", null=True)
    
    # 联系人信息
    contact_person = fields.CharField(max_length=100, description="联系人", null=True)
    contact_phone = fields.CharField(max_length=20, description="联系人手机号", null=True)
    contact_email = fields.CharField(max_length=100, description="联系人邮箱", null=True)
    
    # 审批信息
    approval_reference = fields.CharField(max_length=100, description="审批参考人", null=True)
    company_name = fields.CharField(max_length=200, description="公司名称", null=True)
    booking_agent = fields.CharField(max_length=100, description="代订人", null=True)
    
    # 票务信息
    ticket_sms = fields.TextField(description="出票短信", null=True)
    amount = fields.DecimalField(max_digits=10, decimal_places=2, description="金额", default=0)
    order_number = fields.CharField(max_length=50, description="订单号", null=True)
    bill_number = fields.CharField(max_length=50, description="账单号", null=True)
    
    # 订单状态
    order_status = fields.CharField(
        max_length=20, 
        default="initial", 
        description="订单状态",
        help_text="订单状态: initial(待预订), submitted(已提交), processing(处理中), completed(已完成), failed(失败)"
    )
    
    # 失败原因
    fail_reason = fields.TextField(description="失败原因", null=True, help_text="记录订单处理过程中的各类失败原因")
    
    # 软删除标志位
    is_deleted = fields.BooleanField(default=False, description="是否已删除")

    class Meta:
        table = "train_orders"
        ordering = ["-created_at"]  # 按创建时间倒序排列

    def __str__(self) -> str:
        return f"火车票订单 - {self.traveler_full_name} - {self.train_number} - {self.travel_date}"

    async def soft_delete(self):
        """软删除订单"""
        self.is_deleted = True
        await self.save() 