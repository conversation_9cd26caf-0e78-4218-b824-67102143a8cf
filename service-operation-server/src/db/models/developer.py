from tortoise import fields
from tortoise.contrib.pydantic import pydantic_model_creator

from .base import AbstractBaseModel

class Developer(AbstractBaseModel):
    """Represents a developer using the API."""
    name = fields.CharField(max_length=100)
    email = fields.CharField(max_length=255, unique=True)
    # Add other relevant fields if needed (e.g., company, status)

    # Relationship back to ApiKey (One-to-Many)
    # This is defined by the related_name="api_keys" in ApiKey model
    # 不需要显式定义反向关系，Tortoise ORM 会自动处理

    class Meta:
        table = "developers"
        ordering = ["name"]

    def __str__(self) -> str:
        return self.name

# 注意：Pydantic 模型创建器应该在所有模型定义完成后使用
# 这里先注释掉，等所有模型都正确加载后再使用
# Developer_Pydantic = pydantic_model_creator(Developer, name="Developer")
# DeveloperIn_Pydantic = pydantic_model_creator(<PERSON><PERSON><PERSON>, name="DeveloperIn", exclude_readonly=True)