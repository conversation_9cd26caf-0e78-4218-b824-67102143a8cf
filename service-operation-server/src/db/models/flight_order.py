from tortoise import fields

from .base import AbstractBaseModel

class FlightOrder(AbstractBaseModel):
    """飞机票订单模型"""
    
    # 系统字段
    project_id = fields.BigIntField(description="项目ID")
    is_deleted = fields.BooleanField(default=False, description="是否删除")
    fail_reason = fields.TextField(null=True, description="失败原因")
    
    # 业务字段
    sequence_number = fields.IntField(description="序号")
    
    # 出行人基础信息
    traveler_full_name = fields.Char<PERSON>ield(max_length=100, default="", description="出行人姓名")
    traveler_surname = fields.Char<PERSON>ield(max_length=50, default="", description="出行人姓")
    traveler_given_name = fields.Char<PERSON>ield(max_length=50, default="", description="出行人名")
    nationality = fields.CharField(max_length=50, default="", description="国籍")
    gender = fields.CharField(max_length=10, default="", description="性别")
    birth_date = fields.Char<PERSON>ield(max_length=50, default="", description="出生日期")
    id_type = fields.CharField(max_length=30, default="", description="证件类型")
    id_number = fields.CharField(max_length=50, default="", description="证件号码")
    id_expiry_date = fields.CharField(max_length=50, default="", description="证件有效期至")
    mobile_country_code = fields.CharField(max_length=10, default="+86", description="手机号国际区号")
    mobile_phone = fields.CharField(max_length=20, default="", description="手机号")
    
    # 出行信息
    travel_date = fields.CharField(max_length=50, default="", description="出行日期")
    departure_airport = fields.CharField(max_length=100, default="", description="出发机场名")
    arrival_airport = fields.CharField(max_length=100, default="", description="到达机场名")
    flight_number = fields.CharField(max_length=20, default="", description="航班号")
    departure_time = fields.CharField(max_length=50, default="", description="出发时间")
    arrival_time = fields.CharField(max_length=50, default="", description="到达时间")
    trip_submission_item = fields.TextField(null=True, description="行程提交项")
    
    # 联系人信息
    contact_person = fields.CharField(max_length=50, default="", description="联系人")
    contact_mobile_phone = fields.CharField(max_length=20, default="", description="联系人手机号")
    contact_email = fields.CharField(max_length=100, default="", description="联系人邮箱")
    approver = fields.CharField(max_length=50, default="", description="审批参照人")
    
    # 对账单信息
    company_name = fields.CharField(max_length=100, default="", description="公司名称")
    booking_agent = fields.CharField(max_length=50, default="", description="代订人")
    ticket_sms = fields.TextField(null=True, description="出票短信")
    amount = fields.DecimalField(max_digits=10, decimal_places=2, default=0.00, description="金额")
    order_number = fields.CharField(max_length=100, default="", description="订单号")
    bill_number = fields.CharField(max_length=100, default="", description="账单号")

    # 保险信息
    insurance_name = fields.CharField(max_length=255, default="", description="保险名称")
    
    # 订单状态管理
    order_status = fields.CharField(
        max_length=20, 
        default="initial", 
        description="订单状态：initial=待提交, submitted=已提交, processing=处理中, completed=预定完成, failed=预定失败, check_failed=验证失败"
    )
    
    # 时间戳字段
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")

    class Meta:
        table = "flight_orders"
        table_description = "飞机票预订订单表"
        indexes = [
            "project_id",
            "order_status",
            "id_number",
            "flight_number",
            "travel_date",
            "created_at",
            "is_deleted"
        ]

    def __str__(self):
        return f"FlightOrder(id={self.id}, traveler_name={self.traveler_full_name}, flight_number={self.flight_number})"

    async def soft_delete(self):
        """软删除订单"""
        self.is_deleted = True
        await self.save()
