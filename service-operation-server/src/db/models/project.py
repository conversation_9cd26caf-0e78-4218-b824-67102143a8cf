from tortoise import fields
from datetime import datetime

from .base import AbstractBaseModel

class Project(AbstractBaseModel):
    """项目管理模型"""
    
    # 项目编号（递增序列号）
    project_number = fields.IntField(unique=True, description="项目编号")
    
    # 项目名称
    project_name = fields.CharField(max_length=200, description="项目名称")
    
    # 创建用户ID (暂时使用IntField，后续可以优化为外键)
    creator_user_id = fields.BigIntField(description="创建用户ID")
    
    # 创建人姓名
    creator_name = fields.CharField(max_length=100, description="创建人姓名")
    
    # 项目描述
    project_description = fields.TextField(null=True, description="项目描述")
    
    # 客户名称
    client_name = fields.CharField(max_length=200, description="客户名称")
    
    # 成本中心
    cost_center = fields.CharField(max_length=100, null=True, description="成本中心")
    
    # 代订人手机号码
    booking_agent_phone = fields.CharField(max_length=20, null=True, description="代订人手机号码")
    
    # 项目创建日期 (继承自AbstractBaseModel的created_at字段)
    # 如果需要单独的项目创建日期字段，可以添加：
    project_date = fields.DatetimeField(description="项目创建日期")
    
    # 软删除标志位
    is_deleted = fields.BooleanField(default=False, description="是否已删除")

    class Meta:
        table = "projects"
        ordering = ["-created_at"]  # 按创建时间倒序排列

    def __str__(self) -> str:
        return f"{self.project_number} - {self.project_name}"

    @classmethod
    async def get_next_project_number(cls) -> int:
        """生成下一个项目编号，从现有最大编号+1开始"""
        # 获取当前最大的项目编号
        max_project = await cls.all().order_by('-project_number').first()
        
        if max_project and max_project.project_number:
            # 如果存在项目，返回最大编号+1
            return max_project.project_number + 1
        else:
            # 如果没有项目，从25001开始（保持与现有数据一致）
            return 25001

    async def save(self, *args, **kwargs):
        """重写save方法，自动设置项目编号"""
        if not self.project_number:
            self.project_number = await self.get_next_project_number()
        # 调用父类save方法，会自动处理VARCHAR字段的空值
        await super().save(*args, **kwargs)
    
    async def soft_delete(self):
        """软删除项目"""
        self.is_deleted = True
        await self.save() 