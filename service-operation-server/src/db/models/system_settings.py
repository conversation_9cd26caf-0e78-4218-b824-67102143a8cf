from tortoise import fields

from .base import AbstractBaseModel

class SystemSettings(AbstractBaseModel):
    """系统设置表，用于存储用户的配置信息"""
    user_id = fields.CharField(max_length=50, description="用户ID")
    config_key = fields.CharField(max_length=100, description="配置项key")
    config_name = fields.CharField(max_length=200, description="配置项名称")
    config_value = fields.TextField(description="配置项值（加密存储）")
    
    class Meta:
        table = "system_settings"
        # 确保同一用户的同一配置项唯一
        unique_together = ("user_id", "config_key")
        ordering = ["user_id", "config_key"]

    def __str__(self) -> str:
        return f"{self.user_id}-{self.config_key}"

# 注意：我们暂时不使用 pydantic_model_creator，因为它与 Pydantic v2 有兼容性问题
# 当我们需要使用 Pydantic 模型时，将手动创建它们 