from tortoise import fields
from datetime import datetime

from .base import AbstractBaseModel

class ProjectTask(AbstractBaseModel):
    """项目任务模型"""
    
    # 项目ID（外键关联到Project表）
    project_id = fields.BigIntField(description="项目ID")
    
    # 任务ID（自动生成的唯一标识）
    task_id = fields.CharField(max_length=20, unique=True, description="任务ID")
    
    # 创建人ID
    creator_user_id = fields.BigIntField(description="创建用户ID")
    
    # 创建人姓名
    creator_name = fields.CharField(max_length=100, description="创建人姓名")
    
    # 任务类型
    task_type = fields.CharField(max_length=50, description="任务类型")
    
    # 任务标题
    task_title = fields.CharField(max_length=200, description="任务标题")
    
    # 任务描述
    task_description = fields.TextField(null=True, description="任务描述")
    
    # 任务状态
    task_status = fields.CharField(max_length=20, default="pending", description="任务状态")
    
    # 短信通知设置
    sms_notify = fields.BooleanField(default=False, description="是否短信通知")
    
    # 代订人设置
    has_agent = fields.BooleanField(default=False, description="是否有代订人")
    agent_phone = fields.CharField(max_length=20, null=True, description="代订人手机号码")
    agent_name = fields.CharField(max_length=100, default="", description="代订人姓名")
    
    # 软删除标志位
    is_deleted = fields.BooleanField(default=False, description="是否已删除")

    class Meta:
        table = "project_tasks"
        ordering = ["-created_at"]  # 按创建时间倒序排列

    def __str__(self) -> str:
        return f"{self.task_id} - {self.task_title}"

    @classmethod
    async def get_next_task_id(cls) -> str:
        """生成下一个任务ID，格式：TASK + YYMMDDHHMM + 序号"""
        now = datetime.now()
        # 生成基础时间戳：TASK + 年(2位)月日时分
        base_id = f"TASK{now.strftime('%y%m%d%H%M')}"
        
        # 查找当前时间戳开头的最大序号
        existing_tasks = await cls.filter(
            task_id__startswith=base_id
        ).values_list('task_id', flat=True)
        
        if not existing_tasks:
            # 没有相同时间戳的任务ID，返回基础ID + 01
            return f"{base_id}01"
        else:
            # 找到最大的序号，加1
            max_sequence = 0
            for task_id in existing_tasks:
                try:
                    sequence = int(task_id[-2:])  # 取最后两位数字
                    max_sequence = max(max_sequence, sequence)
                except ValueError:
                    continue
            
            next_sequence = max_sequence + 1
            if next_sequence > 99:
                # 如果序号超过99，等待下一分钟
                import asyncio
                await asyncio.sleep(1)
                return await cls.get_next_task_id()
            
            return f"{base_id}{next_sequence:02d}"

    async def save(self, *args, **kwargs):
        """重写save方法，自动设置任务ID"""
        if not self.task_id:
            self.task_id = await self.get_next_task_id()
        # 调用父类save方法，会自动处理VARCHAR字段的空值
        await super().save(*args, **kwargs)
    
    async def soft_delete(self):
        """软删除任务"""
        self.is_deleted = True
        await self.save() 