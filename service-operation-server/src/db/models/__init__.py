"""数据库模型模块。

此模块导出所有数据库模型，确保 Tortoise ORM 可以正确加载它们。
"""

# 首先导出基础模型
from .base import AbstractBaseModel

# 然后导出实体模型
from .user import User
from .passport import Passport
from .project import Project
from .project_task import ProjectTask
from .train_order import TrainOrder
from .task_to_train_order import TaskToTrainOrder
from .hotel_order import HotelOrder
from .task_to_hotel_order import TaskToHotelOrder
from .flight_order import FlightOrder
from .task_to_flight_order import TaskToFlightOrder
from .developer import Developer
from .api_key import ApiKey
from .refresh_token import RefreshToken
from .rpa_error_log import RpaErrorLog

# 注意：我们已在各个模型文件中注释了 Pydantic 模型创建器
# 当模型关系正确配置后，可以在这里创建 Pydantic 模型

# 注意：我们暂时不使用 pydantic_model_creator，因为它与 Pydantic v2 有兼容性问题
# 当我们需要使用 Pydantic 模型时，将手动创建它们

# 例如，我们可以在需要的模块中定义如下模型：
# from pydantic import BaseModel
# 
# class UserSchema(BaseModel):
#     id: int
#     name: str
#     email: str
#     created_at: datetime
#     updated_at: datetime
#     
#     class Config:
#         from_attributes = True
from .system_settings import SystemSettings
