-- 修改is_group_booking字段从布尔类型改为字符串类型
-- 执行日期：2025-01-15

USE TCSoapServer;

-- 步骤1：添加临时字段
ALTER TABLE hotel_orders 
ADD COLUMN is_group_booking_temp VARCHAR(10) DEFAULT '否';

-- 步骤2：将现有布尔值转换为字符串
UPDATE hotel_orders SET 
is_group_booking_temp = CASE 
    WHEN is_group_booking = TRUE THEN '是' 
    ELSE '否' 
END;

-- 步骤3：删除原字段
ALTER TABLE hotel_orders 
DROP COLUMN is_group_booking;

-- 步骤4：重命名临时字段
ALTER TABLE hotel_orders 
CHANGE COLUMN is_group_booking_temp is_group_booking VARCHAR(10) NOT NULL DEFAULT '否' COMMENT '是否为团房';
