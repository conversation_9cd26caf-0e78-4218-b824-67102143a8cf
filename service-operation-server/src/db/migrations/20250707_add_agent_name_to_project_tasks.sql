-- 为project_tasks表添加agent_name字段
-- 用于存储代订人姓名

-- 添加agent_name字段
ALTER TABLE `project_tasks` 
ADD COLUMN `agent_name` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '代订人姓名' AFTER `agent_phone`;

-- 创建索引以提高查询性能
CREATE INDEX `idx_project_tasks_agent_name` ON `project_tasks` (`agent_name`);

-- 验证字段添加成功
-- SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT 
-- FROM INFORMATION_SCHEMA.COLUMNS 
-- WHERE TABLE_NAME = 'project_tasks' AND COLUMN_NAME = 'agent_name';
