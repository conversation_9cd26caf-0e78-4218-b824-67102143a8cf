-- Create train_orders table
CREATE TABLE IF NOT EXISTS `train_orders` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `project_id` BIGINT NOT NULL COMMENT '项目ID',
    `task_id` VARCHAR(20) NOT NULL COMMENT '任务ID',
    `sequence_number` INT NOT NULL COMMENT '序号',
    `traveler_full_name` VARCHAR(100) NOT NULL COMMENT '出行人姓名',
    `traveler_surname` VARCHAR(50) NULL COMMENT '出行人姓',
    `traveler_given_name` VARCHAR(50) NULL COMMENT '出行人名',
    `nationality` VARCHAR(50) NULL COMMENT '国籍',
    `gender` VARCHAR(10) NULL COMMENT '性别',
    `birth_date` DATE NULL COMMENT '出生日期',
    `id_type` VARCHAR(20) NULL COMMENT '证件类型',
    `id_number` VARCHAR(50) NULL COMMENT '证件号码',
    `id_expiry_date` DATE NULL COMMENT '证件有效期至',
    `mobile_phone` VARCHAR(20) NULL COMMENT '手机号',
    `travel_date` DATE NULL COMMENT '出行日期',
    `departure_station` VARCHAR(100) NULL COMMENT '出发站名',
    `arrival_station` VARCHAR(100) NULL COMMENT '到达站名',
    `train_number` VARCHAR(20) NULL COMMENT '车次',
    `seat_type` VARCHAR(50) NULL COMMENT '座位类型',
    `departure_time` TIME NULL COMMENT '出发时间',
    `arrival_time` TIME NULL COMMENT '到达时间',
    `cost_center` VARCHAR(100) NULL COMMENT '成本中心',
    `trip_submission_item` VARCHAR(200) NULL COMMENT '行程提交项',
    `contact_person` VARCHAR(100) NULL COMMENT '联系人',
    `contact_phone` VARCHAR(20) NULL COMMENT '联系人手机号',
    `contact_email` VARCHAR(100) NULL COMMENT '联系人邮箱',
    `approval_reference` VARCHAR(100) NULL COMMENT '审批参考人',
    `company_name` VARCHAR(200) NULL COMMENT '公司名称',
    `booking_agent` VARCHAR(100) NULL COMMENT '代订人',
    `ticket_sms` TEXT NULL COMMENT '出票短信',
    `amount` DECIMAL(10,2) NULL COMMENT '金额',
    `order_number` VARCHAR(50) NULL COMMENT '订单号',
    `bill_number` VARCHAR(50) NULL COMMENT '账单号',
    `is_deleted` BOOL NOT NULL DEFAULT 0 COMMENT '是否已删除',
    `created_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
    `updated_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
    INDEX idx_project_id (`project_id`),
    INDEX idx_task_id (`task_id`),
    INDEX idx_traveler_name (`traveler_full_name`),
    INDEX idx_travel_date (`travel_date`),
    INDEX idx_train_number (`train_number`),
    INDEX idx_created_at (`created_at`)
) COMMENT='火车票订单表'; 