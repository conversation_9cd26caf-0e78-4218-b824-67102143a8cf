-- Create task_to_flight_orders table
CREATE TABLE IF NOT EXISTS `task_to_flight_orders` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `project_id` BIGINT NOT NULL COMMENT '项目ID',
    `task_id` VARCHAR(20) NOT NULL COMMENT '任务ID',
    `order_id` BIGINT NOT NULL COMMENT '飞机票订单ID',
    `order_status` VARCHAR(20) NOT NULL DEFAULT 'initial' COMMENT '订单状态: initial(待预订), submitted(已提交), processing(处理中), completed(已完成), failed(失败)',
    `order_type` VARCHAR(20) NOT NULL DEFAULT 'book' COMMENT '订单类型: book(预订), book_and_confirm(预订且确出票)',
    `start_time` DATETIME(6) NULL COMMENT '开始时间',
    `end_time` DATETIME(6) NULL COMMENT '结束时间',
    `message` TEXT NULL COMMENT '处理消息',
    `is_deleted` BOOL NOT NULL DEFAULT 0 COMMENT '是否已删除',
    `created_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
    `updated_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
    UNIQUE KEY unique_task_order (`task_id`, `order_id`),
    INDEX idx_project_id (`project_id`),
    INDEX idx_task_id (`task_id`),
    INDEX idx_order_id (`order_id`),
    INDEX idx_order_status (`order_status`),
    INDEX idx_created_at (`created_at`)
) COMMENT='任务飞机票订单关联表';
