-- upgrade --
CREATE TABLE IF NOT EXISTS `users` (
    `id` INT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `username` VA<PERSON><PERSON><PERSON>(100) NOT NULL,
    `department` VA<PERSON>HAR(100),
    `user_id` VARCHAR(50) NOT NULL UNIQUE,
    `work_id` VA<PERSON>HAR(50),
    `new_work_id` VARCHAR(50),
    `department_id` VARCHAR(50),
    `gender` VARCHAR(10),
    `email` VA<PERSON><PERSON><PERSON>(255),
    `dept_level_id` VARCHAR(255),
    `dept_level_name` VA<PERSON><PERSON><PERSON>(255),
    `phone_number` VA<PERSON><PERSON><PERSON>(50),
    `mtid` VARCHAR(50),
    `ctids` VARCHAR(50),
    `gid` VARCHAR(50),
    `mobile` VARCHAR(50),
    `member_id` VARCHAR(50),
    `is_virtual` INT,
    `tid` VARCHAR(50),
    `device_id` VARCHAR(100),
    `created_at` TIMESTAMP(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` TIMESTAMP(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
) CHARACTER SET utf8mb4;

