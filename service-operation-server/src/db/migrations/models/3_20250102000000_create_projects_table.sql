-- upgrade --
CREATE TABLE IF NOT EXISTS `projects` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `project_number` INT NOT NULL UNIQUE COMMENT '项目编号',
    `project_name` VARCHAR(200) NOT NULL COMMENT '项目名称',
    `creator_user_id` BIGINT NOT NULL COMMENT '创建用户ID',
    `creator_name` VARCHAR(100) NOT NULL COMMENT '创建人姓名',
    `project_description` LONGTEXT COMMENT '项目描述',
    `client_name` VARCHAR(200) NOT NULL COMMENT '客户名称',
    `project_date` DATE NOT NULL COMMENT '项目创建日期',
    `cost_center` VARCHAR(100) NOT NULL COMMENT '成本中心',
    `created_at` TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '记录创建时间',
    `updated_at` TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '记录更新时间'
) CHARACTER SET utf8mb4 COMMENT='项目管理表';

-- 创建索引
CREATE INDEX `idx_projects_project_number` ON `projects` (`project_number`);
CREATE INDEX `idx_projects_creator_user_id` ON `projects` (`creator_user_id`);
CREATE INDEX `idx_projects_client_name` ON `projects` (`client_name`);
CREATE INDEX `idx_projects_cost_center` ON `projects` (`cost_center`);
CREATE INDEX `idx_projects_created_at` ON `projects` (`created_at`);

-- downgrade --
DROP TABLE IF EXISTS `projects`; 