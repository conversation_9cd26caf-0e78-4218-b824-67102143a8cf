-- Add order_status column to train_orders table
-- Migration: 7_20250103000000_add_order_status_to_train_orders.sql

-- 添加order_status字段
ALTER TABLE `train_orders` 
ADD COLUMN `order_status` VARCHAR(20) NOT NULL DEFAULT 'initial' 
COMMENT '订单状态: initial(待预订), submitted(已提交), processing(处理中), completed(已完成), failed(失败)';

-- 为order_status字段添加索引以优化查询性能
ALTER TABLE `train_orders` 
ADD INDEX `idx_order_status` (`order_status`);

-- 更新现有记录的order_status为默认值(如果有必要)
-- UPDATE `train_orders` SET `order_status` = 'initial' WHERE `order_status` IS NULL;

-- 验证字段是否添加成功(可选的验证查询)
-- SELECT COLUMN_NAME, DATA_TYPE, COLUMN_DEFAULT, COLUMN_COMMENT
-- FROM INFORMATION_SCHEMA.COLUMNS 
-- WHERE TABLE_SCHEMA = DATABASE() 
-- AND TABLE_NAME = 'train_orders' 
-- AND COLUMN_NAME = 'order_status'; 