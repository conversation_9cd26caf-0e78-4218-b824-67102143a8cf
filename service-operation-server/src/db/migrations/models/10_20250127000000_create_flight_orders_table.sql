-- Create flight_orders table based on flight template fields
CREATE TABLE IF NOT EXISTS `flight_orders` (
    -- 系统字段
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT COMMENT '自增主键ID',
    `project_id` BIGINT NOT NULL COMMENT '项目ID',
    `is_deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除（0=否，1=是）',
    `fail_reason` TEXT NULL COMMENT '失败原因',
    
    -- 业务字段 - 基于飞机票导入模版的29个字段
    `sequence_number` INT NOT NULL COMMENT '序号',
    
    -- 出行人基础信息
    `traveler_full_name` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '出行人姓名',
    `traveler_surname` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '出行人姓',
    `traveler_given_name` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '出行人名',
    `nationality` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '国籍',
    `gender` VARCHAR(10) NOT NULL DEFAULT '' COMMENT '性别',
    `birth_date` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '出生日期',
    `id_type` VARCHAR(30) NOT NULL DEFAULT '' COMMENT '证件类型',
    `id_number` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '证件号码',
    `id_expiry_date` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '证件有效期至',
    `mobile_country_code` VARCHAR(10) NOT NULL DEFAULT '+86' COMMENT '手机号国际区号',
    `mobile_phone` VARCHAR(20) NOT NULL DEFAULT '' COMMENT '手机号',
    
    -- 出行信息
    `travel_date` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '出行日期',
    `departure_airport` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '出发机场名',
    `arrival_airport` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '到达机场名',
    `flight_number` VARCHAR(20) NOT NULL DEFAULT '' COMMENT '航班号',
    `departure_time` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '出发时间',
    `arrival_time` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '到达时间',
    `trip_submission_item` TEXT NULL COMMENT '行程提交项',
    
    -- 联系人信息
    `contact_person` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '联系人',
    `contact_mobile_phone` VARCHAR(20) NOT NULL DEFAULT '' COMMENT '联系人手机号',
    `contact_email` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '联系人邮箱',
    `approver` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '审批参照人',
    
    -- 对账单信息
    `company_name` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '公司名称',
    `booking_agent` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '代订人',
    `ticket_sms` TEXT NULL COMMENT '出票短信',
    `amount` DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '金额',
    `order_number` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '订单号',
    `bill_number` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '账单号',
    
    -- 订单状态管理
    `order_status` VARCHAR(20) NOT NULL DEFAULT 'initial' COMMENT '订单状态：initial=待提交, submitted=已提交, processing=处理中, completed=预定完成, failed=预定失败, check_failed=验证失败',
    
    -- 时间戳字段
    `created_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
    `updated_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
    
    -- 索引
    INDEX `idx_project_id` (`project_id`),
    INDEX `idx_order_status` (`order_status`),
    INDEX `idx_id_number` (`id_number`),
    INDEX `idx_flight_number` (`flight_number`),
    INDEX `idx_travel_date` (`travel_date`),
    INDEX `idx_created_at` (`created_at`),
    INDEX `idx_is_deleted` (`is_deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='飞机票预订订单表';
