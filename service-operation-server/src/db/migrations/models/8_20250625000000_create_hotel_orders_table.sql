-- Create hotel_orders table based on exact field requirements
CREATE TABLE IF NOT EXISTS `hotel_orders` (
    -- 系统字段
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT COMMENT '自增主键ID',
    `project_id` BIGINT NOT NULL COMMENT '项目ID',
    `is_deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除（0=否，1=是）',
    `fail_reason` TEXT NULL COMMENT '失败原因',
    
    -- 业务字段（根据Excel字段列表）
    `sequence_number` INT NOT NULL COMMENT '序号',
    `guest_full_name` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '入住人姓名',
    `guest_surname` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '入住人姓',
    `guest_given_name` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '入住人名',
    `guest_nationality` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '入住人国籍',
    `guest_gender` VARCHAR(10) NOT NULL DEFAULT '' COMMENT '入住人性别',
    `guest_birth_date` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '入住人出生日期',
    `guest_id_type` VARCHAR(30) NOT NULL DEFAULT '' COMMENT '入住人证件类型',
    `guest_id_number` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '入住人证件号码',
    `guest_id_expiry_date` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '入住人证件有效期至',
    `guest_mobile_country_code` VARCHAR(10) NOT NULL DEFAULT '+86' COMMENT '入住人手机号国际区号',
    `guest_mobile_phone` VARCHAR(20) NOT NULL DEFAULT '' COMMENT '入住人手机号',
    `guest_email` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '入住人邮箱',
    
    -- 酒店预订信息
    `destination` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '目的地',
    `hotel_id` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '酒店ID',
    `room_type` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '预订房型',
    `room_count` INT NOT NULL DEFAULT 1 COMMENT '预订房间数量',
    `policy_name` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '政策名称',
    `include_breakfast` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否含早',
    `is_half_day_room` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否为半日房',
    `check_in_time` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '入住时间',
    `check_out_time` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '离店时间',
    `is_group_booking` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否为团房',
    `group_booking_name` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '团房名称',
    `room_number` VARCHAR(20) NOT NULL DEFAULT '' COMMENT '房间号',
    
    -- 支付和财务信息
    `payment_method` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '支付方式',
    `invoice_type` VARCHAR(30) NOT NULL DEFAULT '' COMMENT '发票类型',
    `tax_rate` DECIMAL(5,4) NOT NULL DEFAULT 0.0000 COMMENT '税率',
    `agreement_type` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '协议类型',
    `supplier_name` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '供应商名称',
    `payment_channel` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '支付渠道',
    `payment_transaction_id` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '支付流水号',
    `cost_per_room` DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '对供成本（每间房成本）',
    `hidden_service_fee` DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '隐藏手续费',
    `cancellation_policy` TEXT NULL COMMENT '取消规则',
    `is_violation` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否违规',
    
    -- 联系人和管理信息
    `contact_person` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '联系人姓名',
    `contact_mobile_country_code` VARCHAR(10) NOT NULL DEFAULT '+86' COMMENT '联系人手机号国际区号',
    `contact_mobile_phone` VARCHAR(20) NOT NULL DEFAULT '' COMMENT '联系人手机号',
    `order_remarks` TEXT NULL COMMENT '订单备注',
    `cost_center` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '成本中心',
    `trip_submission_item` TEXT NULL COMMENT '行程提交项',
    `approver` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '审批人',
    
    -- 同住人信息
    `roommate_name` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '同住人姓名',
    `roommate_surname` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '同住人姓',
    `roommate_given_name` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '同住人名',
    `roommate_nationality` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '同住人国籍',
    `roommate_gender` VARCHAR(10) NOT NULL DEFAULT '' COMMENT '同住人性别',
    `roommate_birth_date` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '同住人出生日期',
    `roommate_id_type` VARCHAR(30) NOT NULL DEFAULT '' COMMENT '同住人证件类型',
    `roommate_id_number` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '同住人证件号码',
    `roommate_id_expiry_date` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '同住人证件有效期至',
    `roommate_mobile_country_code` VARCHAR(10) NOT NULL DEFAULT '+86' COMMENT '同住人手机号国际区号',
    `roommate_mobile_phone` VARCHAR(20) NOT NULL DEFAULT '' COMMENT '同住人手机号',
    `roommate_email` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '同住人邮箱',
    
    -- 对账单信息
    `company_name` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '公司名称',
    `hotel_name` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '酒店名称',
    `amount` DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '对客金额',
    `booking_agent` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '代订人',
    `order_number` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '订单号',
    `bill_number` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '账单号',
    
    -- 订单状态管理
    `order_status` VARCHAR(20) NOT NULL DEFAULT 'initial' COMMENT '订单状态：initial=待提交, submitted=已提交, processing=处理中, completed=预定完成, failed=预定失败, check_failed=验证失败',
    
    -- 时间戳字段
    `created_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
    `updated_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
    
    -- 索引
    INDEX `idx_project_id` (`project_id`),
    INDEX `idx_order_status` (`order_status`),
    INDEX `idx_guest_id_number` (`guest_id_number`),
    INDEX `idx_hotel_id` (`hotel_id`),
    INDEX `idx_check_in_time` (`check_in_time`),
    INDEX `idx_created_at` (`created_at`),
    INDEX `idx_is_deleted` (`is_deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='酒店预订订单表'; 
CREATE TABLE IF NOT EXISTS `hotel_orders` (
    -- 系统字段
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT COMMENT '自增主键ID',
    `project_id` BIGINT NOT NULL COMMENT '项目ID',
    `is_deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除（0=否，1=是）',
    `fail_reason` TEXT NULL COMMENT '失败原因',
    
    -- 业务字段（根据Excel字段列表）
    `sequence_number` INT NOT NULL COMMENT '序号',
    `guest_full_name` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '入住人姓名',
    `guest_surname` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '入住人姓',
    `guest_given_name` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '入住人名',
    `guest_nationality` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '入住人国籍',
    `guest_gender` VARCHAR(10) NOT NULL DEFAULT '' COMMENT '入住人性别',
    `guest_birth_date` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '入住人出生日期',
    `guest_id_type` VARCHAR(30) NOT NULL DEFAULT '' COMMENT '入住人证件类型',
    `guest_id_number` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '入住人证件号码',
    `guest_id_expiry_date` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '入住人证件有效期至',
    `guest_mobile_country_code` VARCHAR(10) NOT NULL DEFAULT '+86' COMMENT '入住人手机号国际区号',
    `guest_mobile_phone` VARCHAR(20) NOT NULL DEFAULT '' COMMENT '入住人手机号',
    `guest_email` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '入住人邮箱',
    
    -- 酒店预订信息
    `destination` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '目的地',
    `hotel_id` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '酒店ID',
    `room_type` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '预订房型',
    `room_count` INT NOT NULL DEFAULT 1 COMMENT '预订房间数量',
    `policy_name` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '政策名称',
    `include_breakfast` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否含早',
    `is_half_day_room` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否为半日房',
    `check_in_time` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '入住时间',
    `check_out_time` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '离店时间',
    `is_group_booking` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否为团房',
    `group_booking_name` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '团房名称',
    `room_number` VARCHAR(20) NOT NULL DEFAULT '' COMMENT '房间号',
    
    -- 支付和财务信息
    `payment_method` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '支付方式',
    `invoice_type` VARCHAR(30) NOT NULL DEFAULT '' COMMENT '发票类型',
    `tax_rate` DECIMAL(5,4) NOT NULL DEFAULT 0.0000 COMMENT '税率',
    `agreement_type` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '协议类型',
    `supplier_name` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '供应商名称',
    `payment_channel` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '支付渠道',
    `payment_transaction_id` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '支付流水号',
    `cost_per_room` DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '对供成本（每间房成本）',
    `hidden_service_fee` DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '隐藏手续费',
    `cancellation_policy` TEXT NULL COMMENT '取消规则',
    `is_violation` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否违规',
    
    -- 联系人和管理信息
    `contact_person` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '联系人姓名',
    `contact_mobile_country_code` VARCHAR(10) NOT NULL DEFAULT '+86' COMMENT '联系人手机号国际区号',
    `contact_mobile_phone` VARCHAR(20) NOT NULL DEFAULT '' COMMENT '联系人手机号',
    `order_remarks` TEXT NULL COMMENT '订单备注',
    `cost_center` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '成本中心',
    `trip_submission_item` TEXT NULL COMMENT '行程提交项',
    `approver` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '审批人',
    
    -- 同住人信息
    `roommate_name` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '同住人姓名',
    `roommate_surname` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '同住人姓',
    `roommate_given_name` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '同住人名',
    `roommate_nationality` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '同住人国籍',
    `roommate_gender` VARCHAR(10) NOT NULL DEFAULT '' COMMENT '同住人性别',
    `roommate_birth_date` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '同住人出生日期',
    `roommate_id_type` VARCHAR(30) NOT NULL DEFAULT '' COMMENT '同住人证件类型',
    `roommate_id_number` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '同住人证件号码',
    `roommate_id_expiry_date` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '同住人证件有效期至',
    `roommate_mobile_country_code` VARCHAR(10) NOT NULL DEFAULT '+86' COMMENT '同住人手机号国际区号',
    `roommate_mobile_phone` VARCHAR(20) NOT NULL DEFAULT '' COMMENT '同住人手机号',
    `roommate_email` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '同住人邮箱',
    
    -- 对账单信息
    `company_name` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '公司名称',
    `hotel_name` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '酒店名称',
    `amount` DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '对客金额',
    `booking_agent` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '代订人',
    `order_number` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '订单号',
    `bill_number` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '账单号',
    
    -- 订单状态管理
    `order_status` VARCHAR(20) NOT NULL DEFAULT 'initial' COMMENT '订单状态：initial=待提交, submitted=已提交, processing=处理中, completed=预定完成, failed=预定失败, check_failed=验证失败',
    
    -- 时间戳字段
    `created_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
    `updated_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
    
    -- 索引
    INDEX `idx_project_id` (`project_id`),
    INDEX `idx_order_status` (`order_status`),
    INDEX `idx_guest_id_number` (`guest_id_number`),
    INDEX `idx_hotel_id` (`hotel_id`),
    INDEX `idx_check_in_time` (`check_in_time`),
    INDEX `idx_created_at` (`created_at`),
    INDEX `idx_is_deleted` (`is_deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='酒店预订订单表'; 