-- 更新task_to_hotel_orders表结构
-- 添加缺失的字段以匹配TaskToTrainOrder模型

-- 1. 添加project_id字段
ALTER TABLE `task_to_hotel_orders` 
ADD COLUMN `project_id` BIGINT NOT NULL COMMENT '项目ID' AFTER `id`;

-- 2. 修改task_id字段类型
ALTER TABLE `task_to_hotel_orders` 
MODIFY COLUMN `task_id` VARCHAR(20) NOT NULL COMMENT '任务ID';

-- 3. 添加order_status字段
ALTER TABLE `task_to_hotel_orders` 
ADD COLUMN `order_status` VARCHAR(20) NOT NULL DEFAULT 'initial' COMMENT '订单状态' AFTER `order_id`;

-- 4. 添加order_type字段
ALTER TABLE `task_to_hotel_orders` 
ADD COLUMN `order_type` VARCHAR(20) NOT NULL DEFAULT 'book' COMMENT '订单类型' AFTER `order_status`;

-- 5. 添加时间字段
ALTER TABLE `task_to_hotel_orders` 
ADD COLUMN `start_time` DATETIME(6) NULL COMMENT '开始时间' AFTER `order_type`;

ALTER TABLE `task_to_hotel_orders` 
ADD COLUMN `end_time` DATETIME(6) NULL COMMENT '结束时间' AFTER `start_time`;

-- 6. 添加message字段
ALTER TABLE `task_to_hotel_orders` 
ADD COLUMN `message` TEXT NULL COMMENT '处理消息' AFTER `end_time`;

-- 7. 添加唯一约束
ALTER TABLE `task_to_hotel_orders` 
ADD UNIQUE KEY `uk_task_order` (`task_id`, `order_id`);

-- 8. 添加索引
CREATE INDEX `idx_project_id` ON `task_to_hotel_orders` (`project_id`);
CREATE INDEX `idx_order_status` ON `task_to_hotel_orders` (`order_status`);
CREATE INDEX `idx_order_type` ON `task_to_hotel_orders` (`order_type`); 