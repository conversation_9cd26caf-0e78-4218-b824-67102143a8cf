-- 修改include_breakfast和is_half_day_room字段从布尔类型改为字符串类型
-- 执行日期：2025-01-15

USE TCSoapServer;

-- 步骤1：添加临时字段
ALTER TABLE hotel_orders 
ADD COLUMN include_breakfast_temp VARCHAR(10) DEFAULT '否',
ADD COLUMN is_half_day_room_temp VARCHAR(10) DEFAULT '否';

-- 步骤2：将现有布尔值转换为字符串
UPDATE hotel_orders SET 
include_breakfast_temp = CASE 
    WHEN include_breakfast = TRUE THEN '是' 
    ELSE '否' 
END,
is_half_day_room_temp = CASE 
    WHEN is_half_day_room = TRUE THEN '是' 
    ELSE '否' 
END;

-- 步骤3：删除原字段
ALTER TABLE hotel_orders 
DROP COLUMN include_breakfast,
DROP COLUMN is_half_day_room;

-- 步骤4：重命名临时字段
ALTER TABLE hotel_orders 
CHANGE COLUMN include_breakfast_temp include_breakfast VARCHAR(10) NOT NULL DEFAULT '否' COMMENT '是否含早',
CHANGE COLUMN is_half_day_room_temp is_half_day_room VARCHAR(10) NOT NULL DEFAULT '否' COMMENT '是否为半日房'; 