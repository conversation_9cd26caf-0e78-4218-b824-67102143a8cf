-- 修改permissions表和user_permissions表的ENUM字段为VARCHAR字段
-- 创建时间: 2025-07-02
-- 说明: 生产数据库不支持ENUM类型，需要改为VARCHAR类型

-- 1. 修改permissions表的permission_type字段
-- 先添加新的VARCHAR字段
ALTER TABLE permissions ADD COLUMN permission_type_new VARCHAR(20) DEFAULT 'menu' COMMENT '权限类型(新字段)';

-- 将ENUM值转换为VARCHAR值
UPDATE permissions SET permission_type_new = 
    CASE permission_type
        WHEN 'menu' THEN 'menu'
        WHEN 'button' THEN 'button'
        WHEN 'api' THEN 'api'
        WHEN 'data' THEN 'data'
        ELSE 'menu'
    END;

-- 删除原ENUM字段
ALTER TABLE permissions DROP COLUMN permission_type;

-- 重命名新字段为原字段名
ALTER TABLE permissions CHANGE COLUMN permission_type_new permission_type VARCHAR(20) DEFAULT 'menu' COMMENT '权限类型';

-- 2. 修改user_permissions表的permission_type字段
-- 先添加新的VARCHAR字段
ALTER TABLE user_permissions ADD COLUMN permission_type_new VARCHAR(10) DEFAULT 'grant' COMMENT '权限类型(新字段)';

-- 将ENUM值转换为VARCHAR值
UPDATE user_permissions SET permission_type_new = 
    CASE permission_type
        WHEN 'grant' THEN 'grant'
        WHEN 'deny' THEN 'deny'
        ELSE 'grant'
    END;

-- 删除原ENUM字段
ALTER TABLE user_permissions DROP COLUMN permission_type;

-- 重命名新字段为原字段名
ALTER TABLE user_permissions CHANGE COLUMN permission_type_new permission_type VARCHAR(10) DEFAULT 'grant' COMMENT '权限类型';

-- 3. 更新相关索引（如果存在）
-- 重新创建permission_type索引
DROP INDEX IF EXISTS idx_permissions_type ON permissions;
CREATE INDEX idx_permissions_type ON permissions(permission_type);

-- 为user_permissions表的permission_type字段创建索引
CREATE INDEX idx_user_permissions_type ON user_permissions(permission_type);
