#!/usr/bin/env python3
"""
简单的S3连接测试脚本
"""

import os

# 设置环境变量 - 生产环境
os.environ['S3_ENDPOINT_URL'] = 'http://oss.17usoft.com'
os.environ['S3_REGION_NAME'] = 'us-east-1'
os.environ['S3_AWS_ACCESS_KEY_ID'] = 'yJqof1h3XvD15I11QCzi'
os.environ['S3_AWS_SECRET_ACCESS_KEY'] = 'N5yvz1WsJMPxKQQA9FhXckA7ifdGtFoWaxkJXBAo'
os.environ['S3_BUCKET_NAME'] = 'soap-files'
os.environ['S3_KEY_PREFIX'] = 'tmc.ai.soap.server'
os.environ['S3_USE_SSL'] = 'False'
os.environ['S3_VERIFY'] = 'False'

try:
    import boto3
    from botocore.exceptions import ClientError, NoCredentialsError
    
    print("🚀 开始S3连接测试\n")
    
    # 创建S3客户端
    client = boto3.client(
        's3',
        endpoint_url=os.environ['S3_ENDPOINT_URL'],
        region_name=os.environ['S3_REGION_NAME'],
        use_ssl=False,
        verify=False,
        aws_access_key_id=os.environ['S3_AWS_ACCESS_KEY_ID'],
        aws_secret_access_key=os.environ['S3_AWS_SECRET_ACCESS_KEY']
    )
    
    print("✅ S3客户端创建成功")
    print(f"Endpoint: {os.environ['S3_ENDPOINT_URL']}")
    print(f"Bucket: {os.environ['S3_BUCKET_NAME']}")
    print(f"Key Prefix: {os.environ['S3_KEY_PREFIX']}")
    
    # 测试连接
    print("\n=== 测试连接 ===")
    try:
        response = client.list_objects_v2(
            Bucket=os.environ['S3_BUCKET_NAME'],
            MaxKeys=1
        )
        print("✅ S3连接成功！")
        print(f"Bucket存在且可访问: {os.environ['S3_BUCKET_NAME']}")
        
        # 显示一些对象信息
        if 'Contents' in response:
            print(f"Bucket中有 {len(response['Contents'])} 个对象")
        else:
            print("Bucket为空或没有对象")
            
    except NoCredentialsError:
        print("❌ S3认证失败：缺少访问凭证")
    except ClientError as e:
        error_code = e.response['Error']['Code']
        if error_code == 'NoSuchBucket':
            print(f"❌ S3存储桶不存在: {os.environ['S3_BUCKET_NAME']}")
        elif error_code == 'AccessDenied':
            print("❌ S3访问被拒绝：权限不足")
        else:
            print(f"❌ S3连接失败: {e}")
    except Exception as e:
        print(f"❌ S3连接测试失败: {e}")

except ImportError:
    print("❌ boto3模块未安装，请先安装：pip install boto3")
except Exception as e:
    print(f"❌ 测试过程中发生异常: {e}")
    import traceback
    traceback.print_exc() 