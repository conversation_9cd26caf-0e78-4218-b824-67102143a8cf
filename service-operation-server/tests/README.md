# 测试目录说明

本目录包含项目的所有测试脚本，按功能模块组织。

## 目录结构

```
tests/
├── __init__.py                 # 测试模块初始化
├── README.md                   # 本文档
├── run_tests.py               # 测试运行器
├── test_problematic_upload.py # Excel上传问题测试
├── data/                      # 测试数据目录
│   ├── test_train_orders.xlsx # 标准测试Excel文件
│   └── problematic_train_orders.xlsx # 问题Excel文件（列名有空格）
├── auth/                      # 认证相关测试
│   ├── __init__.py
│   └── test_auth.py          # 认证功能测试
├── passport/                  # 护照相关测试
│   ├── __init__.py
│   ├── test_passport_api.py  # 护照API测试
│   └── test_passport_upload.py # 护照上传测试
└── sso/                      # SSO相关测试
    ├── __init__.py
    └── test_sso_logout.py    # SSO登出测试
```

## 测试脚本说明

### Excel上传测试

#### test_problematic_upload.py
- **功能**: 测试Excel上传功能，特别是列名空格问题的修复
- **测试内容**:
  - 上传包含列名空格的Excel文件
  - 验证列名清理功能
  - 检查订单数据解析正确性
- **测试数据**: 使用 `data/problematic_train_orders.xlsx`
- **使用方法**:
  ```bash
  cd service-operation-server
  poetry run python tests/test_problematic_upload.py
  ```

### 测试数据 (data/)

#### test_train_orders.xlsx
- **用途**: 标准的火车票订单Excel测试文件
- **内容**: 包含2条标准格式的火车票订单数据
- **特点**: 列名格式正确，无空格问题

#### problematic_train_orders.xlsx
- **用途**: 用于测试列名空格问题的Excel文件
- **内容**: 包含2条火车票订单数据，但列名故意添加了前导/尾随空格
- **特点**: 
  - `' 出行人姓名'` (前导空格)
  - `'出行人姓 '` (尾随空格)
  - `' 出行人名 '` (前后空格)
  - `' 成本中心'` (前导空格)

### 认证测试 (auth/)

#### test_auth.py
- **功能**: 全面的认证系统测试
- **测试内容**:
  - JWT token创建和验证
  - 用户数据检查
  - API认证状态
  - SSO登录流程
- **使用方法**:
  ```bash
  cd service-operation-server
  python tests/auth/test_auth.py
  ```

### 护照测试 (passport/)

#### test_passport_api.py
- **功能**: 护照API功能测试
- **测试内容**:
  - 护照文件上传
  - 护照列表获取
  - 任务护照查询
- **使用方法**:
  ```bash
  cd service-operation-server
  python tests/passport/test_passport_api.py
  ```

#### test_passport_upload.py
- **功能**: 护照上传功能测试
- **测试内容**:
  - 文件上传API
  - 护照列表API
- **使用方法**:
  ```bash
  cd service-operation-server
  python tests/passport/test_passport_upload.py
  ```

### SSO测试 (sso/)

#### test_sso_logout.py
- **功能**: SSO登出功能测试
- **测试内容**:
  - SSO登出服务
  - 直接HTTP请求测试
- **使用方法**:
  ```bash
  cd service-operation-server
  python tests/sso/test_sso_logout.py
  ```

## 运行所有测试

使用测试运行器可以一次性运行所有测试：

```bash
cd service-operation-server
python tests/run_tests.py
```

## 测试前准备

### 1. 环境要求
- Python 3.8+
- 已安装项目依赖 (`poetry install`)
- 后端服务正在运行 (`http://localhost:8000`)
- 数据库连接正常

### 2. 认证配置
某些测试需要有效的JWT token，可以通过以下方式获取：

#### 方法1: 运行认证测试获取token
```bash
python tests/auth/test_auth.py
```

#### 方法2: 通过SSO登录获取
```bash
curl "http://localhost:8000/api/auth/sso/login-url"
# 在浏览器中访问返回的URL登录，然后使用code换取token
```

#### 方法3: 在前端浏览器控制台获取
```javascript
localStorage.getItem('token')
```

### 3. 测试数据
- 确保数据库中存在测试用户 (user_id: 123497)
- 护照表已正确创建
- 上传目录权限正确

## 测试结果说明

### 成功标志
- ✅ 表示测试通过
- 📊 表示状态信息
- 📁 表示文件信息

### 失败标志
- ❌ 表示测试失败
- ⚠️ 表示警告信息

### 常见问题

#### 认证失败
```
{"detail":"Not authenticated"}
```
**解决方案**: 
1. 运行 `python tests/auth/test_auth.py` 获取有效token
2. 检查后端服务是否正常运行
3. 确认用户数据存在

#### 文件上传失败
```
{"detail":"File upload failed"}
```
**解决方案**:
1. 检查上传目录权限
2. 确认文件大小限制
3. 验证文件类型支持

#### 数据库连接失败
```
{"detail":"Database connection failed"}
```
**解决方案**:
1. 检查数据库服务状态
2. 验证连接配置
3. 确认数据库表结构

## 添加新测试

### 1. 创建测试文件
在相应的功能目录下创建新的测试文件：
```python
#!/usr/bin/env python3
"""
新功能测试脚本
"""

import asyncio
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# 导入项目模块
from src.your_module import your_function

async def test_your_function():
    """测试您的功能"""
    # 测试代码
    pass

if __name__ == "__main__":
    asyncio.run(test_your_function())
```

### 2. 更新测试运行器
在 `run_tests.py` 中添加新的测试脚本：
```python
test_scripts = [
    # ... 现有测试
    (tests_dir / "your_module" / "test_your_function.py", "您的功能测试"),
]
```

## 持续集成

这些测试脚本可以集成到CI/CD流水线中：

```yaml
# .github/workflows/test.yml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Python
        uses: actions/setup-python@v2
        with:
          python-version: 3.8
      - name: Install dependencies
        run: |
          cd service-operation-server
          pip install poetry
          poetry install
      - name: Run tests
        run: |
          cd service-operation-server
          python tests/run_tests.py
```

## 贡献指南

1. 为新功能编写对应的测试
2. 确保测试覆盖主要功能路径
3. 添加适当的错误处理测试
4. 更新相关文档
5. 在提交前运行所有测试 