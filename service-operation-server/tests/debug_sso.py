#!/usr/bin/env python3
"""
直接调试SSO服务的脚本
"""

import asyncio
import sys
import os

# 添加项目路径到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def debug_sso():
    """直接调试SSO令牌交换"""
    
    # 导入必要的模块
    from src.api.auth.sso_service import exchange_code_for_token
    from src.core.config import settings
    
    print("🔧 SSO调试工具")
    print("=" * 50)
    
    # 显示当前配置
    print(f"SSO域名: {settings.sso_domain}")
    print(f"客户端ID: {settings.sso_client_id}")
    print(f"客户端密钥: {settings.sso_client_secret[:8]}...")
    print(f"重定向URI: {settings.sso_redirect_uri}")
    
    # 使用真实的授权码
    code = "b07e7e521f9bbaf79c492bb4b89953af"
    state = "random_state"
    
    print(f"\n使用授权码: {code}")
    print(f"使用状态值: {state}")
    
    try:
        # 直接调用SSO服务函数
        print("\n🚀 开始调用SSO令牌交换...")
        access_token, user_info, app_token = await exchange_code_for_token(code, state)
        
        print("✅ SSO令牌交换成功!")
        print(f"访问令牌: {access_token[:20]}...")
        print(f"用户信息: {user_info}")
        print(f"应用令牌: {app_token[:20]}...")
        
    except Exception as e:
        print(f"❌ SSO令牌交换失败: {str(e)}")
        import traceback
        print("\n完整错误堆栈:")
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_sso()) 