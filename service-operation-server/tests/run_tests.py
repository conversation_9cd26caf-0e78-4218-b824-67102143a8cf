#!/usr/bin/env python3
"""
测试运行器 - 运行所有测试脚本
"""

import asyncio
import subprocess
import sys
from pathlib import Path

def run_script(script_path: Path, description: str):
    """运行单个测试脚本"""
    print(f"\n{'='*60}")
    print(f"🧪 运行测试: {description}")
    print(f"📁 脚本路径: {script_path}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(
            [sys.executable, str(script_path)],
            cwd=script_path.parent.parent,  # 在项目根目录运行
            capture_output=False,
            text=True
        )
        
        if result.returncode == 0:
            print(f"✅ {description} - 测试完成")
        else:
            print(f"❌ {description} - 测试失败 (退出码: {result.returncode})")
            
    except Exception as e:
        print(f"❌ {description} - 运行异常: {e}")

def main():
    """主函数"""
    print("🚀 开始运行所有测试...")
    
    # 获取测试目录
    tests_dir = Path(__file__).parent
    
    # 定义测试脚本
    test_scripts = [
        (tests_dir / "auth" / "test_auth.py", "认证功能测试"),
        (tests_dir / "passport" / "test_passport_api.py", "护照API测试"),
        (tests_dir / "passport" / "test_passport_upload.py", "护照上传测试"),
        (tests_dir / "sso" / "test_sso_logout.py", "SSO登出测试"),
    ]
    
    # 检查脚本是否存在
    missing_scripts = []
    for script_path, description in test_scripts:
        if not script_path.exists():
            missing_scripts.append((script_path, description))
    
    if missing_scripts:
        print("❌ 以下测试脚本不存在:")
        for script_path, description in missing_scripts:
            print(f"   - {description}: {script_path}")
        return
    
    # 运行所有测试
    for script_path, description in test_scripts:
        run_script(script_path, description)
    
    print(f"\n{'='*60}")
    print("🏁 所有测试运行完成!")
    print(f"{'='*60}")

if __name__ == "__main__":
    main() 