#!/usr/bin/env python3
"""
测试真实模板格式的Excel文件上传
"""

import asyncio
import os
import sys
import pandas as pd
from io import BytesIO

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_excel_parsing_logic():
    """测试Excel解析逻辑"""
    print("🧪 测试Excel解析逻辑...")
    
    # 测试数据目录
    data_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data')
    
    # 测试文件列表
    test_files = [
        ('real_template_train_orders.xlsx', '真实模板格式（正确）'),
        ('real_template_problematic.xlsx', '真实模板格式（列名有空格）'),
        ('test_train_orders.xlsx', '简化测试格式'),
        ('problematic_train_orders.xlsx', '简化测试格式（列名有空格）')
    ]
    
    for filename, description in test_files:
        file_path = os.path.join(data_dir, filename)
        
        if not os.path.exists(file_path):
            print(f"⚠️  文件不存在: {filename}")
            continue
            
        print(f"\n📄 测试文件: {filename} ({description})")
        print("=" * 60)
        
        try:
            # 读取文件内容
            with open(file_path, 'rb') as f:
                file_content = f.read()
            
            # 模拟解析逻辑
            # 首先尝试智能检测Excel格式
            df_preview = pd.read_excel(BytesIO(file_content), header=None, nrows=5)
            print(f"📊 预览数据: {len(df_preview)} 行 x {len(df_preview.columns)} 列")
            
            # 智能检测header位置和数据起始位置
            header_row = 0  # 默认第1行是header
            data_start_row = 1  # 默认第2行开始是数据
            
            # 检查是否是真实模板格式（第3行是列名，第5行开始是数据）
            if len(df_preview) >= 3:
                # 检查第3行（索引2）是否包含预期的列名
                potential_header = df_preview.iloc[2].dropna().astype(str).str.strip()
                expected_columns = ['序号', '出行人姓名', '出行人姓', '出行人名', '国籍', '性别']
                
                print(f"🔍 第3行内容: {potential_header.tolist()[:5]}...")
                
                # 如果第3行包含预期列名，则使用真实模板格式
                matched_columns = [col for col in expected_columns[:3] if col.strip() in potential_header.values]
                if len(matched_columns) >= 2:  # 至少匹配2个预期列名
                    header_row = 2  # 第3行是header（索引从0开始）
                    data_start_row = 4  # 第5行开始是数据（跳过第4行的单位说明）
                    print(f"✅ 检测到真实模板格式：匹配列名 {matched_columns}")
                else:
                    print("✅ 检测到简化测试格式")
            
            # 根据检测结果读取Excel文件
            if header_row == 2:
                # 真实模板格式：跳过前4行，从第5行开始读取数据
                df = pd.read_excel(BytesIO(file_content), header=header_row, skiprows=[3])
            else:
                # 简化测试格式：第1行是header，第2行开始是数据
                df = pd.read_excel(BytesIO(file_content), header=header_row)
            
            print(f"📋 解析结果: header在第{header_row+1}行，数据 {len(df)} 行")
            
            # 清理列名（移除空格和特殊字符）
            original_columns = df.columns.tolist()
            df.columns = [col.strip() if isinstance(col, str) else str(col).strip() for col in df.columns]
            cleaned_columns = df.columns.tolist()
            
            # 检查列名清理效果
            space_cleaned = []
            for i, (orig, clean) in enumerate(zip(original_columns[:5], cleaned_columns[:5])):
                if orig != clean:
                    space_cleaned.append(f"'{orig}' → '{clean}'")
            
            if space_cleaned:
                print(f"🧹 列名清理: {', '.join(space_cleaned)}")
            else:
                print("✅ 列名无需清理")
            
            # 检查关键列
            required_columns = ['出行人姓名']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                print(f"❌ 缺少必需列: {missing_columns}")
                print(f"   可用列名: {list(df.columns)[:10]}...")
            else:
                print("✅ 关键列检查通过")
            
            # 显示解析的数据样例
            if not df.empty:
                print(f"📝 数据样例:")
                for i, (_, row) in enumerate(df.iterrows()):
                    if i < 2:  # 只显示前2行
                        traveler_name = row.get('出行人姓名', '未知')
                        sequence = row.get('序号', i+1)
                        print(f"  第{i+1}条: 序号={sequence}, 姓名='{traveler_name}'")
            
        except Exception as e:
            print(f"❌ 解析失败: {str(e)}")

if __name__ == "__main__":
    test_excel_parsing_logic() 