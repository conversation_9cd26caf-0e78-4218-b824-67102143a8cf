#!/usr/bin/env python3
"""
认证测试脚本 - 用于诊断和解决认证问题
"""

import asyncio
import aiohttp
import json
from datetime import datetime
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.core.security import create_access_token, verify_token
from src.core.config import settings

BASE_URL = "http://localhost:8000"

async def test_jwt_creation_and_verification():
    """测试JWT token的创建和验证"""
    print("=== JWT Token 创建和验证测试 ===")
    
    # 测试用户ID
    test_user_id = "123497"
    
    # 创建token
    token = create_access_token(subject=test_user_id)
    print(f"创建的Token: {token}")
    
    # 验证token
    verified_user_id = verify_token(token)
    print(f"验证结果: {verified_user_id}")
    
    if verified_user_id == test_user_id:
        print("✅ JWT Token 创建和验证正常")
        return token
    else:
        print("❌ JWT Token 验证失败")
        return None

async def test_api_without_auth():
    """测试不需要认证的API"""
    print("\n=== 测试不需要认证的API ===")
    
    async with aiohttp.ClientSession() as session:
        try:
            # 测试健康检查
            async with session.get(f"{BASE_URL}/health") as response:
                if response.status == 200:
                    print("✅ 健康检查API正常")
                else:
                    print(f"❌ 健康检查API失败: {response.status}")
            
            # 测试SSO登录URL获取
            async with session.get(f"{BASE_URL}/api/auth/sso/login-url") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ SSO登录URL获取正常: {data.get('login_url', '')[:50]}...")
                else:
                    print(f"❌ SSO登录URL获取失败: {response.status}")
                    
        except Exception as e:
            print(f"❌ API测试失败: {e}")

async def test_api_with_auth(token: str):
    """测试需要认证的API"""
    print("\n=== 测试需要认证的API ===")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    async with aiohttp.ClientSession() as session:
        try:
            # 测试护照列表API
            async with session.get(f"{BASE_URL}/api/passport/list", headers=headers) as response:
                print(f"护照列表API响应状态: {response.status}")
                
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ 护照列表API正常，返回 {data.get('total', 0)} 条记录")
                elif response.status == 401:
                    error_data = await response.json()
                    print(f"❌ 认证失败: {error_data}")
                elif response.status == 404:
                    error_data = await response.json()
                    print(f"❌ 用户不存在: {error_data}")
                else:
                    error_data = await response.json()
                    print(f"❌ API调用失败: {response.status} - {error_data}")
                    
        except Exception as e:
            print(f"❌ 认证API测试失败: {e}")

async def test_user_exists():
    """测试用户是否存在"""
    print("\n=== 测试用户数据 ===")
    
    try:
        from src.db.models import User
        from tortoise import Tortoise
        
        # 初始化数据库连接
        await Tortoise.init(
            db_url=settings.db_url,
            modules={"models": ["src.db.models"]}
        )
        
        # 查找测试用户
        test_user = await User.filter(user_id="123497").first()
        if test_user:
            print(f"✅ 测试用户存在: {test_user.user_id} - {test_user.username}")
        else:
            print("❌ 测试用户不存在，正在创建...")
            
            # 创建测试用户
            test_user = await User.create(
                user_id="123497",
                username="test_user",
                email="<EMAIL>",
                is_active=True
            )
            print(f"✅ 测试用户已创建: {test_user.user_id} - {test_user.username}")
        
        await Tortoise.close_connections()
        
    except Exception as e:
        print(f"❌ 用户数据测试失败: {e}")

async def test_sso_login():
    """测试SSO登录流程"""
    print("\n=== SSO登录流程测试 ===")
    
    async with aiohttp.ClientSession() as session:
        try:
            # 1. 获取SSO登录URL
            async with session.get(f"{BASE_URL}/api/auth/sso/login-url") as response:
                if response.status == 200:
                    data = await response.json()
                    login_url = data.get('login_url')
                    print(f"✅ SSO登录URL: {login_url}")
                    print("请在浏览器中访问上述URL进行登录，然后输入获取到的code")
                    
                    # 等待用户输入code
                    code = input("请输入SSO返回的code（或按Enter跳过）: ").strip()
                    
                    if code:
                        # 2. 使用code换取token
                        callback_data = {"code": code}
                        async with session.post(
                            f"{BASE_URL}/api/auth/sso/callback",
                            json=callback_data
                        ) as callback_response:
                            if callback_response.status == 200:
                                token_data = await callback_response.json()
                                token = token_data.get('access_token')
                                print(f"✅ SSO登录成功，获取到token: {token[:50]}...")
                                return token
                            else:
                                error_data = await callback_response.json()
                                print(f"❌ SSO callback失败: {error_data}")
                    else:
                        print("跳过SSO登录测试")
                else:
                    print(f"❌ 获取SSO登录URL失败: {response.status}")
                    
        except Exception as e:
            print(f"❌ SSO登录测试失败: {e}")
    
    return None

def print_debug_info():
    """打印调试信息"""
    print("=== 调试信息 ===")
    print(f"JWT Secret: {settings.jwt_secret[:10]}...")
    print(f"JWT Algorithm: {settings.jwt_algorithm}")
    print(f"Token Expire Minutes: {settings.access_token_expire_minutes}")
    print(f"Database URL: {settings.db_url}")
    print(f"当前时间: {datetime.now()}")

async def main():
    """主测试函数"""
    print("🔍 开始认证问题诊断...")
    print_debug_info()
    
    # 1. 测试JWT创建和验证
    token = await test_jwt_creation_and_verification()
    
    # 2. 测试用户数据
    await test_user_exists()
    
    # 3. 测试不需要认证的API
    await test_api_without_auth()
    
    # 4. 如果有token，测试需要认证的API
    if token:
        await test_api_with_auth(token)
    
    # 5. 测试SSO登录（可选）
    print("\n是否要测试SSO登录流程？(y/n): ", end="")
    if input().lower() == 'y':
        sso_token = await test_sso_login()
        if sso_token:
            await test_api_with_auth(sso_token)
    
    print("\n🎯 诊断完成！")
    print("\n如果仍有问题，请检查：")
    print("1. 后端服务是否正常启动")
    print("2. 数据库连接是否正常")
    print("3. 前端是否正确存储和发送token")
    print("4. 浏览器控制台是否有错误信息")

if __name__ == "__main__":
    asyncio.run(main()) 