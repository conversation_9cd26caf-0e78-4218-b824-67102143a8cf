#!/usr/bin/env python3
"""
测试上传有问题的Excel文件
"""

import asyncio
import aiohttp
import json
import os

async def test_problematic_upload():
    print("🧪 开始测试有问题的Excel文件上传...")
    
    # API配置
    base_url = "http://localhost:8000"
    project_id = 10  # 使用项目ID 10
    
    # 获取Excel文件路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    excel_file_path = os.path.join(current_dir, 'data', 'problematic_train_orders.xlsx')
    
    if not os.path.exists(excel_file_path):
        print(f"❌ Excel文件不存在: {excel_file_path}")
        return
    
    # 获取JWT Token
    login_data = {
        "username": "admin",
        "password": "123456"
    }
    
    async with aiohttp.ClientSession() as session:
        # 登录获取token
        print("🔐 正在登录...")
        async with session.post(f"{base_url}/api/auth/login", data=login_data) as response:
            if response.status == 200:
                result = await response.json()
                token = result.get('access_token')
                print("✅ 登录成功")
            else:
                print(f"❌ 登录失败: {response.status}")
                return
        
        # 设置认证头
        headers = {"Authorization": f"Bearer {token}"}
        
        # 上传Excel文件
        print("📤 正在上传有问题的Excel文件...")
        print(f"📁 文件路径: {excel_file_path}")
        
        # 读取Excel文件
        with open(excel_file_path, 'rb') as f:
            data = aiohttp.FormData()
            data.add_field('project_id', str(project_id))
            data.add_field('file', f, filename='problematic_train_orders.xlsx', 
                          content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
            
            async with session.post(f"{base_url}/api/train-order/upload-excel", 
                                  data=data, headers=headers) as response:
                if response.status == 200:
                    result = await response.json()
                    print("✅ 上传成功!")
                    print(f"📋 任务ID: {result['task_id']}")
                    print(f"🎯 成功创建: {result['total_orders']} 条订单")
                    print(f"❌ 失败: {result['failed_orders']} 条订单")
                    print(f"💬 消息: {result['message']}")
                    if result.get('file_path'):
                        print(f"📁 文件路径: {result['file_path']}")
                    
                    # 查询创建的订单
                    task_id = result['task_id']
                    print(f"\n🔍 查询任务 {task_id} 的订单...")
                    async with session.get(f"{base_url}/api/train-order/task/{task_id}?page=1&size=10", 
                                         headers=headers) as query_response:
                        if query_response.status == 200:
                            orders = await query_response.json()
                            print(f"📊 查询结果: 总数 {orders['total']}, 当前页 {len(orders['items'])} 条")
                            for i, order in enumerate(orders['items']):
                                print(f"  {i+1}. 订单ID: {order['id']}, 姓名: '{order['traveler_full_name']}'")
                        else:
                            print(f"❌ 查询失败: {query_response.status}")
                else:
                    error_text = await response.text()
                    print(f"❌ 上传失败: {response.status}")
                    print(f"错误信息: {error_text}")

if __name__ == "__main__":
    asyncio.run(test_problematic_upload()) 