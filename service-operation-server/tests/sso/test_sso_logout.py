#!/usr/bin/env python3
"""
测试SSO登出功能的脚本
"""

import asyncio
import httpx
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.api.auth.sso_service import sso_logout

async def test_sso_logout():
    """测试SSO登出功能"""
    # 使用您提供的token进行测试
    test_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NDg0Mjc2OTYsInN1YiI6IjEyMzQ5NyJ9.cDHQ6wCg3-p6tCbHcZv4iIXi40NpO-SgFRaaCc01ElM"
    
    print(f"测试SSO登出，使用token: {test_token[:50]}...")
    
    try:
        result = await sso_logout(test_token)
        print(f"登出结果: {result}")
        
        if result:
            print("✅ SSO登出测试成功")
        else:
            print("❌ SSO登出测试失败")
            
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")

async def test_direct_request():
    """直接测试HTTP请求"""
    test_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NDg0Mjc2OTYsInN1YiI6IjEyMzQ5NyJ9.cDHQ6wCg3-p6tCbHcZv4iIXi40NpO-SgFRaaCc01ElM"
    sso_domain = "http://tccommon.qas.17usoft.com"
    
    async with httpx.AsyncClient() as client:
        print("\n测试POST请求...")
        try:
            response = await client.post(
                f"{sso_domain}/oauth/logout",
                headers={
                    "Content-Type": "application/x-www-form-urlencoded",
                    "Accept": "application/json"
                },
                data={
                    "access_token": test_token,
                    "client_id": "OpsFlow.local",
                    "client_secret": "6d0863be86c8ce517bb4df62de36b30b"
                },
                timeout=10.0
            )
            print(f"POST响应状态码: {response.status_code}")
            print(f"POST响应内容: {response.text}")
        except Exception as e:
            print(f"POST请求失败: {e}")
        
        print("\n测试GET请求...")
        try:
            response = await client.get(
                f"{sso_domain}/oauth/logout",
                params={
                    "access_token": test_token,
                    "client_id": "OpsFlow.local"
                },
                timeout=10.0
            )
            print(f"GET响应状态码: {response.status_code}")
            print(f"GET响应内容: {response.text}")
        except Exception as e:
            print(f"GET请求失败: {e}")

if __name__ == "__main__":
    print("开始测试SSO登出功能...")
    asyncio.run(test_sso_logout())
    asyncio.run(test_direct_request()) 