#!/usr/bin/env python3
"""
护照API测试脚本

用于测试护照上传和列表获取功能
"""

import asyncio
import aiohttp
import json
from pathlib import Path

# 测试配置
API_BASE_URL = "http://localhost:8000/api"
TEST_TOKEN = "your_test_token_here"  # 需要替换为真实的JWT token

async def test_passport_upload():
    """测试护照文件上传"""
    print("=== 测试护照文件上传 ===")
    
    # 创建测试图片文件（如果不存在）
    test_image_path = Path("test_passport.jpg")
    if not test_image_path.exists():
        # 创建一个简单的测试文件
        with open(test_image_path, "wb") as f:
            f.write(b"fake image content for testing")
        print(f"创建测试文件: {test_image_path}")
    
    async with aiohttp.ClientSession() as session:
        # 准备上传数据
        data = aiohttp.FormData()
        data.add_field('files', 
                      open(test_image_path, 'rb'), 
                      filename='test_passport.jpg',
                      content_type='image/jpeg')
        
        headers = {
            'Authorization': f'Bearer {TEST_TOKEN}'
        }
        
        try:
            async with session.post(
                f"{API_BASE_URL}/passport/upload",
                data=data,
                headers=headers
            ) as response:
                print(f"状态码: {response.status}")
                
                if response.status == 200:
                    result = await response.json()
                    print("上传成功!")
                    print(f"任务ID: {result.get('task_id')}")
                    print(f"上传文件数: {result.get('total_files')}")
                    print(f"响应消息: {result.get('message')}")
                    return result.get('task_id')
                else:
                    error_text = await response.text()
                    print(f"上传失败: {error_text}")
                    return None
                    
        except Exception as e:
            print(f"请求异常: {e}")
            return None

async def test_passport_list(task_id=None):
    """测试护照列表获取"""
    print("\n=== 测试护照列表获取 ===")
    
    async with aiohttp.ClientSession() as session:
        headers = {
            'Authorization': f'Bearer {TEST_TOKEN}'
        }
        
        # 构建URL
        url = f"{API_BASE_URL}/passport/list"
        params = {}
        if task_id:
            params['task_id'] = task_id
        
        try:
            async with session.get(url, headers=headers, params=params) as response:
                print(f"状态码: {response.status}")
                
                if response.status == 200:
                    result = await response.json()
                    print("获取列表成功!")
                    print(f"总记录数: {result.get('total')}")
                    print(f"当前页: {result.get('page')}")
                    print(f"每页大小: {result.get('size')}")
                    
                    items = result.get('items', [])
                    for i, item in enumerate(items):
                        print(f"\n记录 {i+1}:")
                        print(f"  ID: {item.get('id')}")
                        print(f"  任务ID: {item.get('task_id')}")
                        print(f"  用户ID: {item.get('user_id')}")
                        print(f"  图片URL: {item.get('uploaded_image_url')}")
                        print(f"  处理状态: {item.get('processing_status')}")
                        print(f"  创建时间: {item.get('created_at')}")
                else:
                    error_text = await response.text()
                    print(f"获取列表失败: {error_text}")
                    
        except Exception as e:
            print(f"请求异常: {e}")

async def test_task_passports(task_id):
    """测试根据任务ID获取护照列表"""
    if not task_id:
        print("\n=== 跳过任务护照列表测试（无任务ID）===")
        return
        
    print(f"\n=== 测试任务 {task_id} 的护照列表 ===")
    
    async with aiohttp.ClientSession() as session:
        headers = {
            'Authorization': f'Bearer {TEST_TOKEN}'
        }
        
        try:
            async with session.get(
                f"{API_BASE_URL}/passport/task/{task_id}",
                headers=headers
            ) as response:
                print(f"状态码: {response.status}")
                
                if response.status == 200:
                    result = await response.json()
                    print("获取任务护照列表成功!")
                    print(f"总记录数: {result.get('total')}")
                    
                    items = result.get('items', [])
                    for i, item in enumerate(items):
                        print(f"\n记录 {i+1}:")
                        print(f"  ID: {item.get('id')}")
                        print(f"  任务ID: {item.get('task_id')}")
                        print(f"  图片URL: {item.get('uploaded_image_url')}")
                        print(f"  处理状态: {item.get('processing_status')}")
                else:
                    error_text = await response.text()
                    print(f"获取任务护照列表失败: {error_text}")
                    
        except Exception as e:
            print(f"请求异常: {e}")

async def main():
    """主测试函数"""
    print("护照API测试开始...")
    print(f"API基础URL: {API_BASE_URL}")
    print(f"测试Token: {TEST_TOKEN[:20]}..." if len(TEST_TOKEN) > 20 else TEST_TOKEN)
    
    # 测试上传
    task_id = await test_passport_upload()
    
    # 测试列表获取
    await test_passport_list()
    
    # 测试任务护照列表
    await test_task_passports(task_id)
    
    print("\n护照API测试完成!")

if __name__ == "__main__":
    print("请确保:")
    print("1. 后端服务已启动 (http://localhost:8000)")
    print("2. 已设置正确的JWT token")
    print("3. 数据库连接正常")
    print()
    
    # 提示用户输入token
    token_input = input("请输入JWT token (或按回车使用默认值): ").strip()
    if token_input:
        TEST_TOKEN = token_input
    
    if TEST_TOKEN == "your_test_token_here":
        print("警告: 使用的是默认token，可能会认证失败")
    
    asyncio.run(main()) 