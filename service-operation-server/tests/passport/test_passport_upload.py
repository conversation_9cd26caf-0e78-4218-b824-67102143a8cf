#!/usr/bin/env python3
"""
护照上传API测试脚本
"""

import asyncio
import httpx
import json
from pathlib import Path

# API基础URL
BASE_URL = "http://localhost:8000"

async def test_passport_upload():
    """测试护照上传API"""
    
    # 创建一个测试图片文件
    test_image_path = Path("test_passport.jpg")
    if not test_image_path.exists():
        # 创建一个简单的测试文件
        with open(test_image_path, "wb") as f:
            f.write(b"fake image content for testing")
    
    try:
        async with httpx.AsyncClient() as client:
            # 准备文件上传
            files = {
                "files": ("test_passport.jpg", open(test_image_path, "rb"), "image/jpeg")
            }
            
            data = {
                "task_id": "test_task_001"
            }
            
            # 发送上传请求
            print("🚀 发送护照上传请求...")
            response = await client.post(
                f"{BASE_URL}/api/passport/upload",
                files=files,
                data=data,
                timeout=30.0
            )
            
            print(f"📊 响应状态码: {response.status_code}")
            print(f"📋 响应内容: {response.text}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ 上传成功!")
                print(f"📁 上传的文件数量: {result.get('total_files', 0)}")
                print(f"📝 任务ID: {result.get('task_id', 'N/A')}")
                
                # 显示上传的文件信息
                uploaded_files = result.get('uploaded_files', [])
                for i, file_info in enumerate(uploaded_files, 1):
                    print(f"📄 文件 {i}:")
                    print(f"   - 文件名: {file_info.get('filename', 'N/A')}")
                    print(f"   - 文件路径: {file_info.get('file_path', 'N/A')}")
                    print(f"   - 访问URL: {file_info.get('file_url', 'N/A')}")
                    print(f"   - 文件大小: {file_info.get('file_size', 0)} 字节")
            else:
                print("❌ 上传失败!")
                try:
                    error_detail = response.json()
                    print(f"错误详情: {error_detail}")
                except:
                    print(f"错误内容: {response.text}")
                    
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
    
    finally:
        # 清理测试文件
        if test_image_path.exists():
            test_image_path.unlink()

async def test_passport_list():
    """测试护照列表API"""
    try:
        async with httpx.AsyncClient() as client:
            print("\n🔍 测试护照列表API...")
            response = await client.get(
                f"{BASE_URL}/api/passport/",
                timeout=10.0
            )
            
            print(f"📊 响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ 获取列表成功!")
                print(f"📊 总记录数: {result.get('total', 0)}")
                print(f"📄 当前页: {result.get('page', 1)}")
                print(f"📏 每页大小: {result.get('size', 10)}")
                
                items = result.get('items', [])
                if items:
                    print(f"📋 护照记录:")
                    for i, item in enumerate(items, 1):
                        print(f"   {i}. ID: {item.get('id')}, 任务ID: {item.get('task_id')}")
                else:
                    print("📭 暂无护照记录")
            else:
                print("❌ 获取列表失败!")
                print(f"错误内容: {response.text}")
                
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")

async def main():
    """主测试函数"""
    print("🧪 开始护照API测试...")
    
    # 测试上传功能（需要认证，可能会失败）
    await test_passport_upload()
    
    # 测试列表功能（需要认证，可能会失败）
    await test_passport_list()
    
    print("\n🏁 测试完成!")

if __name__ == "__main__":
    asyncio.run(main()) 