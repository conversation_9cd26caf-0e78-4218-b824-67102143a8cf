"""
权限认证服务
"""
from typing import List, Optional, Dict, Any
from datetime import datetime
from tortoise.exceptions import DoesNotExist, IntegrityError
from tortoise.transactions import in_transaction

from app.models.auth import (
    User, Role, Permission, Application,
    UserRole, RolePermission, UserAppPermission, UserPermission,
    UserCreateRequest, UserUpdateRequest, RoleCreateRequest, RoleUpdateRequest,
    PermissionCreateRequest, PermissionUpdateRequest, UserPermissionAssignRequest
)


class AuthService:
    """权限认证服务类"""

    @staticmethod
    async def get_user_by_username(username: str) -> Optional[User]:
        """根据用户名获取用户"""
        try:
            return await User.get(username=username, status=True).prefetch_related("roles", "applications")
        except DoesNotExist:
            return None

    @staticmethod
    async def get_user_by_sso_id(sso_user_id: str) -> Optional[User]:
        """根据SSO用户ID获取用户"""
        try:
            return await User.get(sso_user_id=sso_user_id, status=True).prefetch_related("roles", "applications")
        except DoesNotExist:
            return None

    @staticmethod
    async def create_user_from_sso(sso_user_id: str, username: str, email: str = None, 
                                   full_name: str = None, department: str = None) -> User:
        """从SSO创建用户，默认分配普通用户角色"""
        async with in_transaction():
            # 创建用户
            user = await User.create(
                username=username,
                email=email,
                full_name=full_name,
                department=department,
                sso_user_id=sso_user_id,
                created_by="sso_system"
            )
            
            # 分配默认角色（普通用户）
            default_role = await Role.get(role_code="user", status=True)
            await UserRole.create(
                user=user,
                role=default_role,
                assigned_by="sso_system"
            )
            
            # 分配默认应用权限（DTTrip应用）
            default_app = await Application.get(app_code="dttrip", status=True)
            await UserAppPermission.create(
                user=user,
                app=default_app,
                granted_by="sso_system"
            )
            
            return await User.get(id=user.id).prefetch_related("roles", "applications")

    @staticmethod
    async def update_user_login(user: User) -> None:
        """更新用户登录信息"""
        user.last_login_at = datetime.now()
        user.login_count += 1
        await user.save(update_fields=["last_login_at", "login_count"])

    @staticmethod
    async def get_user_permissions(user_id: int) -> List[str]:
        """获取用户所有权限编码"""
        permissions = set()
        
        # 获取用户角色权限
        user_roles = await UserRole.filter(user_id=user_id, status=True).prefetch_related("role__role_permissions__permission")
        for user_role in user_roles:
            if user_role.role.status:
                for role_permission in user_role.role.role_permissions:
                    if role_permission.permission.status and role_permission.status:
                        permissions.add(role_permission.permission.permission_code)
        
        # 获取用户特殊权限
        user_permissions = await UserPermission.filter(user_id=user_id, status=True).prefetch_related("permission")
        for user_permission in user_permissions:
            if user_permission.permission.status:
                if user_permission.permission_type == "grant":
                    permissions.add(user_permission.permission.permission_code)
                elif user_permission.permission_type == "deny":
                    permissions.discard(user_permission.permission.permission_code)
        
        return list(permissions)

    @staticmethod
    async def get_user_applications(user_id: int) -> List[Application]:
        """获取用户有权限的应用列表"""
        user_app_permissions = await UserAppPermission.filter(
            user_id=user_id, 
            status=True
        ).prefetch_related("app")
        
        applications = []
        for permission in user_app_permissions:
            if permission.app.status:
                applications.append(permission.app)
        
        return applications

    @staticmethod
    async def check_user_permission(user_id: int, permission_code: str) -> bool:
        """检查用户是否有指定权限"""
        user_permissions = await AuthService.get_user_permissions(user_id)
        return permission_code in user_permissions

    @staticmethod
    async def check_user_app_access(user_id: int, app_code: str) -> bool:
        """检查用户是否有应用访问权限"""
        try:
            app = await Application.get(app_code=app_code, status=True)
            user_app_permission = await UserAppPermission.get(
                user_id=user_id,
                app_id=app.id,
                status=True
            )
            return True
        except DoesNotExist:
            return False

    # 用户管理相关方法
    @staticmethod
    async def get_users(page: int = 1, page_size: int = 20, search: str = None) -> Dict[str, Any]:
        """获取用户列表"""
        query = User.all().prefetch_related("roles", "applications")
        
        if search:
            query = query.filter(
                username__icontains=search
            ) | query.filter(
                full_name__icontains=search
            ) | query.filter(
                email__icontains=search
            )
        
        total = await query.count()
        users = await query.offset((page - 1) * page_size).limit(page_size)
        
        return {
            "items": users,
            "total": total,
            "page": page,
            "page_size": page_size,
            "pages": (total + page_size - 1) // page_size
        }

    @staticmethod
    async def create_user(request: UserCreateRequest, created_by: str) -> User:
        """创建用户"""
        async with in_transaction():
            # 创建用户
            user = await User.create(
                username=request.username,
                email=request.email,
                full_name=request.full_name,
                department=request.department,
                phone=request.phone,
                sso_user_id=request.sso_user_id,
                created_by=created_by
            )
            
            # 分配角色
            for role_id in request.role_ids:
                role = await Role.get(id=role_id)
                await UserRole.create(
                    user=user,
                    role=role,
                    assigned_by=created_by
                )
            
            # 分配应用权限
            for app_id in request.app_ids:
                app = await Application.get(id=app_id)
                await UserAppPermission.create(
                    user=user,
                    app=app,
                    granted_by=created_by
                )
            
            return await User.get(id=user.id).prefetch_related("roles", "applications")

    @staticmethod
    async def update_user(user_id: int, request: UserUpdateRequest, updated_by: str) -> User:
        """更新用户"""
        async with in_transaction():
            user = await User.get(id=user_id)
            
            # 更新基本信息
            if request.email is not None:
                user.email = request.email
            if request.full_name is not None:
                user.full_name = request.full_name
            if request.department is not None:
                user.department = request.department
            if request.phone is not None:
                user.phone = request.phone
            if request.status is not None:
                user.status = request.status
            
            user.updated_by = updated_by
            await user.save()
            
            # 更新角色
            if request.role_ids is not None:
                await UserRole.filter(user_id=user_id).delete()
                for role_id in request.role_ids:
                    role = await Role.get(id=role_id)
                    await UserRole.create(
                        user=user,
                        role=role,
                        assigned_by=updated_by
                    )
            
            # 更新应用权限
            if request.app_ids is not None:
                await UserAppPermission.filter(user_id=user_id).delete()
                for app_id in request.app_ids:
                    app = await Application.get(id=app_id)
                    await UserAppPermission.create(
                        user=user,
                        app=app,
                        granted_by=updated_by
                    )
            
            return await User.get(id=user.id).prefetch_related("roles", "applications")

    @staticmethod
    async def delete_user(user_id: int) -> bool:
        """删除用户（软删除）"""
        try:
            user = await User.get(id=user_id)
            user.status = False
            await user.save(update_fields=["status"])
            return True
        except DoesNotExist:
            return False

    # 角色管理相关方法
    @staticmethod
    async def get_roles(page: int = 1, page_size: int = 20, search: str = None) -> Dict[str, Any]:
        """获取角色列表"""
        query = Role.all()
        
        if search:
            query = query.filter(role_name__icontains=search)
        
        total = await query.count()
        roles = await query.offset((page - 1) * page_size).limit(page_size)
        
        return {
            "items": roles,
            "total": total,
            "page": page,
            "page_size": page_size,
            "pages": (total + page_size - 1) // page_size
        }

    @staticmethod
    async def create_role(request: RoleCreateRequest, created_by: str) -> Role:
        """创建角色"""
        async with in_transaction():
            role = await Role.create(
                role_name=request.role_name,
                role_code=request.role_code,
                description=request.description,
                created_by=created_by
            )
            
            # 分配权限
            for permission_id in request.permission_ids:
                permission = await Permission.get(id=permission_id)
                await RolePermission.create(
                    role=role,
                    permission=permission,
                    granted_by=created_by
                )
            
            return role

    @staticmethod
    async def update_role(role_id: int, request: RoleUpdateRequest, updated_by: str) -> Role:
        """更新角色"""
        async with in_transaction():
            role = await Role.get(id=role_id)
            
            if request.role_name is not None:
                role.role_name = request.role_name
            if request.description is not None:
                role.description = request.description
            if request.status is not None:
                role.status = request.status
            
            role.updated_by = updated_by
            await role.save()
            
            # 更新权限
            if request.permission_ids is not None:
                await RolePermission.filter(role_id=role_id).delete()
                for permission_id in request.permission_ids:
                    permission = await Permission.get(id=permission_id)
                    await RolePermission.create(
                        role=role,
                        permission=permission,
                        granted_by=updated_by
                    )
            
            return role

    @staticmethod
    async def delete_role(role_id: int) -> bool:
        """删除角色"""
        try:
            role = await Role.get(id=role_id)
            if role.is_system:
                return False  # 系统角色不能删除

            role.status = False
            await role.save(update_fields=["status"])
            return True
        except DoesNotExist:
            return False

    # 权限管理相关方法
    @staticmethod
    async def get_permissions(page: int = 1, page_size: int = 20, search: str = None,
                             app_id: int = None) -> Dict[str, Any]:
        """获取权限列表"""
        query = Permission.all().prefetch_related("app")

        if search:
            query = query.filter(permission_name__icontains=search)

        if app_id:
            query = query.filter(app_id=app_id)

        total = await query.count()
        permissions = await query.offset((page - 1) * page_size).limit(page_size)

        return {
            "items": permissions,
            "total": total,
            "page": page,
            "page_size": page_size,
            "pages": (total + page_size - 1) // page_size
        }

    @staticmethod
    async def create_permission(request: PermissionCreateRequest, created_by: str) -> Permission:
        """创建权限"""
        permission = await Permission.create(
            permission_name=request.permission_name,
            permission_code=request.permission_code,
            permission_type=request.permission_type,
            parent_id=request.parent_id,
            app_id=request.app_id,
            resource_path=request.resource_path,
            description=request.description,
            sort_order=request.sort_order,
            created_by=created_by
        )
        return permission

    @staticmethod
    async def update_permission(permission_id: int, request: PermissionUpdateRequest,
                               updated_by: str) -> Permission:
        """更新权限"""
        permission = await Permission.get(id=permission_id)

        if request.permission_name is not None:
            permission.permission_name = request.permission_name
        if request.permission_type is not None:
            permission.permission_type = request.permission_type
        if request.parent_id is not None:
            permission.parent_id = request.parent_id
        if request.app_id is not None:
            permission.app_id = request.app_id
        if request.resource_path is not None:
            permission.resource_path = request.resource_path
        if request.description is not None:
            permission.description = request.description
        if request.sort_order is not None:
            permission.sort_order = request.sort_order
        if request.status is not None:
            permission.status = request.status

        permission.updated_by = updated_by
        await permission.save()
        return permission

    @staticmethod
    async def delete_permission(permission_id: int) -> bool:
        """删除权限"""
        try:
            permission = await Permission.get(id=permission_id)
            permission.status = False
            await permission.save(update_fields=["status"])
            return True
        except DoesNotExist:
            return False

    # 应用管理相关方法
    @staticmethod
    async def get_applications(page: int = 1, page_size: int = 20, search: str = None) -> Dict[str, Any]:
        """获取应用列表"""
        query = Application.all()

        if search:
            query = query.filter(app_name__icontains=search)

        total = await query.count()
        applications = await query.offset((page - 1) * page_size).limit(page_size)

        return {
            "items": applications,
            "total": total,
            "page": page,
            "page_size": page_size,
            "pages": (total + page_size - 1) // page_size
        }

    # 用户权限分配相关方法
    @staticmethod
    async def assign_user_permissions(request: UserPermissionAssignRequest, assigned_by: str) -> bool:
        """分配用户权限"""
        async with in_transaction():
            user = await User.get(id=request.user_id)

            # 分配角色
            if request.role_ids is not None:
                await UserRole.filter(user_id=request.user_id).delete()
                for role_id in request.role_ids:
                    role = await Role.get(id=role_id)
                    await UserRole.create(
                        user=user,
                        role=role,
                        assigned_by=assigned_by
                    )

            # 分配应用权限
            if request.app_ids is not None:
                await UserAppPermission.filter(user_id=request.user_id).delete()
                for app_id in request.app_ids:
                    app = await Application.get(id=app_id)
                    await UserAppPermission.create(
                        user=user,
                        app=app,
                        granted_by=assigned_by
                    )

            # 分配特殊权限
            if request.permission_ids is not None:
                await UserPermission.filter(user_id=request.user_id).delete()
                for permission_id in request.permission_ids:
                    permission = await Permission.get(id=permission_id)
                    await UserPermission.create(
                        user=user,
                        permission=permission,
                        permission_type=request.permission_type,
                        granted_by=assigned_by
                    )

            return True

    @staticmethod
    async def get_user_permission_details(user_id: int) -> Dict[str, Any]:
        """获取用户权限详情"""
        user = await User.get(id=user_id).prefetch_related("roles", "applications")

        # 获取用户角色权限
        role_permissions = []
        user_roles = await UserRole.filter(user_id=user_id, status=True).prefetch_related("role__role_permissions__permission")
        for user_role in user_roles:
            if user_role.role.status:
                permissions = []
                for role_permission in user_role.role.role_permissions:
                    if role_permission.permission.status and role_permission.status:
                        permissions.append(role_permission.permission)
                role_permissions.append({
                    "role": user_role.role,
                    "permissions": permissions
                })

        # 获取用户特殊权限
        user_permissions = await UserPermission.filter(user_id=user_id, status=True).prefetch_related("permission")
        special_permissions = [up.permission for up in user_permissions if up.permission.status]

        # 获取用户应用权限
        user_apps = await UserAppPermission.filter(user_id=user_id, status=True).prefetch_related("app")
        applications = [ua.app for ua in user_apps if ua.app.status]

        return {
            "user": user,
            "role_permissions": role_permissions,
            "special_permissions": special_permissions,
            "applications": applications
        }
