"""
权限认证相关API路由
"""
from fastapi import APIRouter, HTTPException, Depends, Query
from typing import List, Optional
from tortoise.exceptions import DoesNotExist, IntegrityError

from app.services.auth_service import AuthService
from app.models.auth import (
    UserResponse, RoleResponse, PermissionResponse, ApplicationResponse,
    UserCreateRequest, UserUpdateRequest, RoleCreateRequest, RoleUpdateRequest,
    PermissionCreateRequest, PermissionUpdateRequest, UserPermissionAssignRequest
)
from app.core.auth import get_current_user
from app.models.auth import User

router = APIRouter(prefix="/auth", tags=["权限管理"])


# 用户管理相关接口
@router.get("/users", response_model=dict)
async def get_users(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    current_user: User = Depends(get_current_user)
):
    """获取用户列表"""
    # 检查权限
    has_permission = await AuthService.check_user_permission(current_user.id, "user_mgmt:user:view")
    if not has_permission:
        raise HTTPException(status_code=403, detail="没有权限访问用户管理")
    
    result = await AuthService.get_users(page, page_size, search)
    return result


@router.post("/users", response_model=UserResponse)
async def create_user(
    request: UserCreateRequest,
    current_user: User = Depends(get_current_user)
):
    """创建用户"""
    # 检查权限
    has_permission = await AuthService.check_user_permission(current_user.id, "user_mgmt:user:create")
    if not has_permission:
        raise HTTPException(status_code=403, detail="没有权限创建用户")
    
    try:
        user = await AuthService.create_user(request, current_user.username)
        return UserResponse.from_orm(user)
    except IntegrityError:
        raise HTTPException(status_code=400, detail="用户名或邮箱已存在")


@router.get("/users/{user_id}", response_model=UserResponse)
async def get_user(
    user_id: int,
    current_user: User = Depends(get_current_user)
):
    """获取用户详情"""
    # 检查权限
    has_permission = await AuthService.check_user_permission(current_user.id, "user_mgmt:user:view")
    if not has_permission:
        raise HTTPException(status_code=403, detail="没有权限查看用户详情")
    
    try:
        user = await User.get(id=user_id).prefetch_related("roles", "applications")
        return UserResponse.from_orm(user)
    except DoesNotExist:
        raise HTTPException(status_code=404, detail="用户不存在")


@router.put("/users/{user_id}", response_model=UserResponse)
async def update_user(
    user_id: int,
    request: UserUpdateRequest,
    current_user: User = Depends(get_current_user)
):
    """更新用户"""
    # 检查权限
    has_permission = await AuthService.check_user_permission(current_user.id, "user_mgmt:user:edit")
    if not has_permission:
        raise HTTPException(status_code=403, detail="没有权限编辑用户")
    
    try:
        user = await AuthService.update_user(user_id, request, current_user.username)
        return UserResponse.from_orm(user)
    except DoesNotExist:
        raise HTTPException(status_code=404, detail="用户不存在")
    except IntegrityError:
        raise HTTPException(status_code=400, detail="邮箱已存在")


@router.delete("/users/{user_id}")
async def delete_user(
    user_id: int,
    current_user: User = Depends(get_current_user)
):
    """删除用户"""
    # 检查权限
    has_permission = await AuthService.check_user_permission(current_user.id, "user_mgmt:user:delete")
    if not has_permission:
        raise HTTPException(status_code=403, detail="没有权限删除用户")
    
    success = await AuthService.delete_user(user_id)
    if not success:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    return {"message": "用户删除成功"}


# 角色管理相关接口
@router.get("/roles", response_model=dict)
async def get_roles(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    current_user: User = Depends(get_current_user)
):
    """获取角色列表"""
    # 检查权限
    has_permission = await AuthService.check_user_permission(current_user.id, "user_mgmt:role:view")
    if not has_permission:
        raise HTTPException(status_code=403, detail="没有权限访问角色管理")
    
    result = await AuthService.get_roles(page, page_size, search)
    return result


@router.post("/roles", response_model=RoleResponse)
async def create_role(
    request: RoleCreateRequest,
    current_user: User = Depends(get_current_user)
):
    """创建角色"""
    # 检查权限
    has_permission = await AuthService.check_user_permission(current_user.id, "user_mgmt:role:create")
    if not has_permission:
        raise HTTPException(status_code=403, detail="没有权限创建角色")
    
    try:
        role = await AuthService.create_role(request, current_user.username)
        return RoleResponse.from_orm(role)
    except IntegrityError:
        raise HTTPException(status_code=400, detail="角色名称或编码已存在")


@router.put("/roles/{role_id}", response_model=RoleResponse)
async def update_role(
    role_id: int,
    request: RoleUpdateRequest,
    current_user: User = Depends(get_current_user)
):
    """更新角色"""
    # 检查权限
    has_permission = await AuthService.check_user_permission(current_user.id, "user_mgmt:role:edit")
    if not has_permission:
        raise HTTPException(status_code=403, detail="没有权限编辑角色")
    
    try:
        role = await AuthService.update_role(role_id, request, current_user.username)
        return RoleResponse.from_orm(role)
    except DoesNotExist:
        raise HTTPException(status_code=404, detail="角色不存在")


@router.delete("/roles/{role_id}")
async def delete_role(
    role_id: int,
    current_user: User = Depends(get_current_user)
):
    """删除角色"""
    # 检查权限
    has_permission = await AuthService.check_user_permission(current_user.id, "user_mgmt:role:delete")
    if not has_permission:
        raise HTTPException(status_code=403, detail="没有权限删除角色")
    
    success = await AuthService.delete_role(role_id)
    if not success:
        raise HTTPException(status_code=404, detail="角色不存在或为系统角色")
    
    return {"message": "角色删除成功"}


# 权限管理相关接口
@router.get("/permissions", response_model=dict)
async def get_permissions(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    app_id: Optional[int] = Query(None, description="应用ID"),
    current_user: User = Depends(get_current_user)
):
    """获取权限列表"""
    # 检查权限
    has_permission = await AuthService.check_user_permission(current_user.id, "user_mgmt:permission:view")
    if not has_permission:
        raise HTTPException(status_code=403, detail="没有权限访问权限管理")
    
    result = await AuthService.get_permissions(page, page_size, search, app_id)
    return result


@router.post("/permissions", response_model=PermissionResponse)
async def create_permission(
    request: PermissionCreateRequest,
    current_user: User = Depends(get_current_user)
):
    """创建权限"""
    # 检查权限
    has_permission = await AuthService.check_user_permission(current_user.id, "user_mgmt:permission:create")
    if not has_permission:
        raise HTTPException(status_code=403, detail="没有权限创建权限")
    
    try:
        permission = await AuthService.create_permission(request, current_user.username)
        return PermissionResponse.from_orm(permission)
    except IntegrityError:
        raise HTTPException(status_code=400, detail="权限编码已存在")


@router.put("/permissions/{permission_id}", response_model=PermissionResponse)
async def update_permission(
    permission_id: int,
    request: PermissionUpdateRequest,
    current_user: User = Depends(get_current_user)
):
    """更新权限"""
    # 检查权限
    has_permission = await AuthService.check_user_permission(current_user.id, "user_mgmt:permission:edit")
    if not has_permission:
        raise HTTPException(status_code=403, detail="没有权限编辑权限")
    
    try:
        permission = await AuthService.update_permission(permission_id, request, current_user.username)
        return PermissionResponse.from_orm(permission)
    except DoesNotExist:
        raise HTTPException(status_code=404, detail="权限不存在")


@router.delete("/permissions/{permission_id}")
async def delete_permission(
    permission_id: int,
    current_user: User = Depends(get_current_user)
):
    """删除权限"""
    # 检查权限
    has_permission = await AuthService.check_user_permission(current_user.id, "user_mgmt:permission:delete")
    if not has_permission:
        raise HTTPException(status_code=403, detail="没有权限删除权限")
    
    success = await AuthService.delete_permission(permission_id)
    if not success:
        raise HTTPException(status_code=404, detail="权限不存在")
    
    return {"message": "权限删除成功"}


# 应用管理相关接口
@router.get("/applications", response_model=dict)
async def get_applications(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    current_user: User = Depends(get_current_user)
):
    """获取应用列表"""
    result = await AuthService.get_applications(page, page_size, search)
    return result


# 用户权限分配相关接口
@router.post("/user-permissions/assign")
async def assign_user_permissions(
    request: UserPermissionAssignRequest,
    current_user: User = Depends(get_current_user)
):
    """分配用户权限"""
    # 检查权限
    has_permission = await AuthService.check_user_permission(current_user.id, "user_mgmt:user_permission:assign")
    if not has_permission:
        raise HTTPException(status_code=403, detail="没有权限分配用户权限")
    
    try:
        success = await AuthService.assign_user_permissions(request, current_user.username)
        if success:
            return {"message": "用户权限分配成功"}
        else:
            raise HTTPException(status_code=400, detail="权限分配失败")
    except DoesNotExist:
        raise HTTPException(status_code=404, detail="用户、角色或权限不存在")


@router.get("/user-permissions/{user_id}")
async def get_user_permission_details(
    user_id: int,
    current_user: User = Depends(get_current_user)
):
    """获取用户权限详情"""
    # 检查权限
    has_permission = await AuthService.check_user_permission(current_user.id, "user_mgmt:user_permission:view")
    if not has_permission:
        raise HTTPException(status_code=403, detail="没有权限查看用户权限")
    
    try:
        result = await AuthService.get_user_permission_details(user_id)
        return result
    except DoesNotExist:
        raise HTTPException(status_code=404, detail="用户不存在")


# 当前用户权限相关接口
@router.get("/me/permissions")
async def get_my_permissions(current_user: User = Depends(get_current_user)):
    """获取当前用户权限"""
    permissions = await AuthService.get_user_permissions(current_user.id)
    applications = await AuthService.get_user_applications(current_user.id)
    
    return {
        "user": UserResponse.from_orm(current_user),
        "permissions": permissions,
        "applications": [ApplicationResponse.from_orm(app) for app in applications]
    }


@router.get("/me/applications")
async def get_my_applications(current_user: User = Depends(get_current_user)):
    """获取当前用户有权限的应用"""
    applications = await AuthService.get_user_applications(current_user.id)
    return [ApplicationResponse.from_orm(app) for app in applications]
