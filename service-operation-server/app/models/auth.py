"""
权限认证相关模型
"""
from tortoise.models import Model
from tortoise import fields
from typing import Optional, List
from datetime import datetime
from pydantic import BaseModel


class Role(Model):
    """角色模型"""
    id = fields.BigIntField(pk=True, description="角色ID")
    role_name = fields.CharField(max_length=50, unique=True, description="角色名称")
    role_code = fields.CharField(max_length=50, unique=True, description="角色编码")
    description = fields.TextField(null=True, description="角色描述")
    is_system = fields.BooleanField(default=False, description="是否系统角色")
    status = fields.BooleanField(default=True, description="状态")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    created_by = fields.CharField(max_length=100, null=True, description="创建人")
    updated_by = fields.Char<PERSON><PERSON>(max_length=100, null=True, description="更新人")

    class Meta:
        table = "roles"
        table_description = "角色表"

    def __str__(self):
        return f"Role(id={self.id}, name={self.role_name})"


class Application(Model):
    """应用模型"""
    id = fields.BigIntField(pk=True, description="应用ID")
    app_name = fields.CharField(max_length=100, description="应用名称")
    app_code = fields.CharField(max_length=50, unique=True, description="应用编码")
    app_url = fields.CharField(max_length=255, null=True, description="应用URL")
    description = fields.TextField(null=True, description="应用描述")
    icon = fields.CharField(max_length=100, null=True, description="应用图标")
    sort_order = fields.IntField(default=0, description="排序")
    status = fields.BooleanField(default=True, description="状态")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    created_by = fields.CharField(max_length=100, null=True, description="创建人")
    updated_by = fields.CharField(max_length=100, null=True, description="更新人")

    class Meta:
        table = "applications"
        table_description = "应用表"

    def __str__(self):
        return f"Application(id={self.id}, name={self.app_name})"


class Permission(Model):
    """权限模型"""
    id = fields.BigIntField(pk=True, description="权限ID")
    permission_name = fields.CharField(max_length=100, description="权限名称")
    permission_code = fields.CharField(max_length=100, unique=True, description="权限编码")
    permission_type = fields.CharField(
        max_length=20,
        default="menu",
        description="权限类型"
    )
    parent_id = fields.BigIntField(default=0, description="父权限ID")
    app = fields.ForeignKeyField("models.Application", related_name="permissions", null=True, description="所属应用")
    resource_path = fields.CharField(max_length=255, null=True, description="资源路径")
    description = fields.TextField(null=True, description="权限描述")
    sort_order = fields.IntField(default=0, description="排序")
    status = fields.BooleanField(default=True, description="状态")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    created_by = fields.CharField(max_length=100, null=True, description="创建人")
    updated_by = fields.CharField(max_length=100, null=True, description="更新人")

    class Meta:
        table = "permissions"
        table_description = "权限表"

    def __str__(self):
        return f"Permission(id={self.id}, name={self.permission_name})"


class User(Model):
    """用户模型"""
    id = fields.BigIntField(pk=True, description="用户ID")
    username = fields.CharField(max_length=100, unique=True, description="用户名")
    email = fields.CharField(max_length=255, unique=True, null=True, description="邮箱")
    full_name = fields.CharField(max_length=100, null=True, description="姓名")
    department = fields.CharField(max_length=100, null=True, description="部门")
    phone = fields.CharField(max_length=20, null=True, description="手机号")
    avatar = fields.CharField(max_length=255, null=True, description="头像")
    tc_user_id = fields.CharField(max_length=50, unique=True, null=True, description="同程用户ID")
    sso_user_id = fields.CharField(max_length=100, null=True, description="SSO用户ID")
    last_login_at = fields.DatetimeField(null=True, description="最后登录时间")
    login_count = fields.IntField(default=0, description="登录次数")
    status = fields.BooleanField(default=True, description="状态")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    created_by = fields.CharField(max_length=100, null=True, description="创建人")
    updated_by = fields.CharField(max_length=100, null=True, description="更新人")

    # 关联关系
    roles: fields.ManyToManyRelation[Role] = fields.ManyToManyField(
        "models.Role", 
        related_name="users",
        through="user_roles",
        description="用户角色"
    )
    
    applications: fields.ManyToManyRelation[Application] = fields.ManyToManyField(
        "models.Application",
        related_name="users", 
        through="user_app_permissions",
        description="用户应用权限"
    )

    class Meta:
        table = "users"
        table_description = "用户表"

    def __str__(self):
        return f"User(id={self.id}, username={self.username})"


class UserRole(Model):
    """用户角色关联模型"""
    id = fields.BigIntField(pk=True, description="关联ID")
    user = fields.ForeignKeyField("models.User", related_name="user_roles", description="用户")
    role = fields.ForeignKeyField("models.Role", related_name="user_roles", description="角色")
    assigned_at = fields.DatetimeField(auto_now_add=True, description="分配时间")
    assigned_by = fields.CharField(max_length=100, null=True, description="分配人")
    expires_at = fields.DatetimeField(null=True, description="过期时间")
    status = fields.BooleanField(default=True, description="状态")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")

    class Meta:
        table = "user_roles"
        table_description = "用户角色关联表"
        unique_together = (("user", "role"),)

    def __str__(self):
        return f"UserRole(user_id={self.user.id}, role_id={self.role.id})"


class RolePermission(Model):
    """角色权限关联模型"""
    id = fields.BigIntField(pk=True, description="关联ID")
    role = fields.ForeignKeyField("models.Role", related_name="role_permissions", description="角色")
    permission = fields.ForeignKeyField("models.Permission", related_name="role_permissions", description="权限")
    granted_at = fields.DatetimeField(auto_now_add=True, description="授权时间")
    granted_by = fields.CharField(max_length=100, null=True, description="授权人")
    status = fields.BooleanField(default=True, description="状态")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")

    class Meta:
        table = "role_permissions"
        table_description = "角色权限关联表"
        unique_together = (("role", "permission"),)

    def __str__(self):
        return f"RolePermission(role_id={self.role_id}, permission_id={self.permission_id})"


class UserAppPermission(Model):
    """用户应用权限模型"""
    id = fields.BigIntField(pk=True, description="权限ID")
    user = fields.ForeignKeyField("models.User", related_name="app_permissions", description="用户")
    app = fields.ForeignKeyField("models.Application", related_name="user_permissions", description="应用")
    granted_at = fields.DatetimeField(auto_now_add=True, description="授权时间")
    granted_by = fields.CharField(max_length=100, null=True, description="授权人")
    expires_at = fields.DatetimeField(null=True, description="过期时间")
    status = fields.BooleanField(default=True, description="状态")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")

    class Meta:
        table = "user_app_permissions"
        table_description = "用户应用权限表"
        unique_together = (("user", "app"),)

    def __str__(self):
        return f"UserAppPermission(user_id={self.user.id}, app_id={self.app.id})"


class UserPermission(Model):
    """用户权限模型"""
    id = fields.BigIntField(pk=True, description="权限ID")
    user = fields.ForeignKeyField("models.User", related_name="user_permissions", description="用户")
    permission = fields.ForeignKeyField("models.Permission", related_name="user_permissions", description="权限")
    permission_type = fields.CharField(
        max_length=10,
        default="grant",
        description="权限类型"
    )
    granted_at = fields.DatetimeField(auto_now_add=True, description="授权时间")
    granted_by = fields.CharField(max_length=100, null=True, description="授权人")
    expires_at = fields.DatetimeField(null=True, description="过期时间")
    status = fields.BooleanField(default=True, description="状态")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")

    class Meta:
        table = "user_permissions"
        table_description = "用户权限表"
        unique_together = (("user", "permission"),)

    def __str__(self):
        return f"UserPermission(user_id={self.user.id}, permission_id={self.permission.id})"


# Pydantic 模型用于API响应
class RoleResponse(BaseModel):
    """角色响应模型"""
    id: int
    role_name: str
    role_code: str
    description: Optional[str] = None
    is_system: bool
    status: bool
    created_at: datetime
    updated_at: datetime
    created_by: Optional[str] = None
    updated_by: Optional[str] = None

    class Config:
        from_attributes = True


class ApplicationResponse(BaseModel):
    """应用响应模型"""
    id: int
    app_name: str
    app_code: str
    app_url: Optional[str] = None
    description: Optional[str] = None
    icon: Optional[str] = None
    sort_order: int
    status: bool
    created_at: datetime
    updated_at: datetime
    created_by: Optional[str] = None
    updated_by: Optional[str] = None

    class Config:
        from_attributes = True


class PermissionResponse(BaseModel):
    """权限响应模型"""
    id: int
    permission_name: str
    permission_code: str
    permission_type: str
    parent_id: int
    app_id: Optional[int] = None
    resource_path: Optional[str] = None
    description: Optional[str] = None
    sort_order: int
    status: bool
    created_at: datetime
    updated_at: datetime
    created_by: Optional[str] = None
    updated_by: Optional[str] = None

    class Config:
        from_attributes = True


class UserResponse(BaseModel):
    """用户响应模型"""
    id: int
    username: str
    email: Optional[str] = None
    full_name: Optional[str] = None
    department: Optional[str] = None
    phone: Optional[str] = None
    avatar: Optional[str] = None
    sso_user_id: Optional[str] = None
    last_login_at: Optional[datetime] = None
    login_count: int
    status: bool
    created_at: datetime
    updated_at: datetime
    created_by: Optional[str] = None
    updated_by: Optional[str] = None
    roles: List[RoleResponse] = []
    applications: List[ApplicationResponse] = []

    class Config:
        from_attributes = True


class UserCreateRequest(BaseModel):
    """用户创建请求模型"""
    username: str
    email: Optional[str] = None
    full_name: Optional[str] = None
    department: Optional[str] = None
    phone: Optional[str] = None
    sso_user_id: Optional[str] = None
    role_ids: List[int] = []
    app_ids: List[int] = []


class UserUpdateRequest(BaseModel):
    """用户更新请求模型"""
    email: Optional[str] = None
    full_name: Optional[str] = None
    department: Optional[str] = None
    phone: Optional[str] = None
    status: Optional[bool] = None
    role_ids: Optional[List[int]] = None
    app_ids: Optional[List[int]] = None


class RoleCreateRequest(BaseModel):
    """角色创建请求模型"""
    role_name: str
    role_code: str
    description: Optional[str] = None
    permission_ids: List[int] = []


class RoleUpdateRequest(BaseModel):
    """角色更新请求模型"""
    role_name: Optional[str] = None
    description: Optional[str] = None
    status: Optional[bool] = None
    permission_ids: Optional[List[int]] = None


class PermissionCreateRequest(BaseModel):
    """权限创建请求模型"""
    permission_name: str
    permission_code: str
    permission_type: str = "menu"
    parent_id: int = 0
    app_id: Optional[int] = None
    resource_path: Optional[str] = None
    description: Optional[str] = None
    sort_order: int = 0


class PermissionUpdateRequest(BaseModel):
    """权限更新请求模型"""
    permission_name: Optional[str] = None
    permission_type: Optional[str] = None
    parent_id: Optional[int] = None
    app_id: Optional[int] = None
    resource_path: Optional[str] = None
    description: Optional[str] = None
    sort_order: Optional[int] = None
    status: Optional[bool] = None


class UserPermissionAssignRequest(BaseModel):
    """用户权限分配请求模型"""
    user_id: int
    role_ids: Optional[List[int]] = None
    app_ids: Optional[List[int]] = None
    permission_ids: Optional[List[int]] = None
    permission_type: str = "grant"  # grant 或 deny
