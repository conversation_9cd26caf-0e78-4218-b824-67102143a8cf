# Python 相关
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# 虚拟环境
venv/
env/
ENV/
.env
.venv
env.bak/
venv.bak/
.python-version

# 依赖管理
.poetry/
poetry.lock

# 测试相关
.coverage
htmlcov/
.pytest_cache/
.tox/
.nox/
.hypothesis/
.coverage.*
coverage.xml
*.cover
.hypothesis/

# 日志文件
*.log
logs/

# 数据库相关
*.sqlite3
*.db

# IDE 相关
.idea/
.vscode/
*.swp
*.swo
.DS_Store
.project
.pydevproject
.settings/
*.sublime-workspace
*.sublime-project
.windsurf/

# FastAPI 特定
.env
.env.*
!.env.example
!.env.prod

# 项目特定
aerich.ini.bak
migrations/models/old_models/

# Docker 相关
.docker/
docker-compose.override.yml

# 临时文件
.tmp/
tmp/
temp/
.temp/
*.tmp
*.bak
*.swp
*~

# 系统文件
.DS_Store
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
uploads/passport/*