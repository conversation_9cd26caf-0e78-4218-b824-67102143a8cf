# 基础镜像，使用国内镜像源
FROM hub.17usoft.com/base/python:3.11.6

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    default-libmysqlclient-dev \
    gcc \
    pkg-config \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# 复制requirements.txt（从pyproject.toml导出）
COPY requirements.txt ./

# 安装Python依赖
RUN pip install --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# 创建应用用户
RUN groupadd -r app && useradd -r -g app app

# 复制应用代码
COPY . .

# 设置权限
RUN mkdir -p logs uploads && \
    chown -R app:app /app

# 切换到非root用户
USER app

# 启动命令
CMD ["python", "-m", "src"] 