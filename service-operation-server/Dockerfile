# 基础镜像，使用国内镜像源
FROM hub.17usoft.com/base/python:3.11.6

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app

# 配置国内apt源（阿里云镜像）
RUN sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list.d/debian.sources || \
    sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list

# 更新包管理器并安装系统依赖
RUN apt-get update && apt-get install -y \
    default-libmysqlclient-dev \
    gcc \
    pkg-config \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# 配置pip国内源（可选，加速下载）
RUN pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/ && \
    pip config set global.trusted-host mirrors.aliyun.com

# 复制requirements.txt
COPY requirements.txt ./

# 安装Python依赖
RUN pip install --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# 清理构建工具（减小镜像大小）
RUN apt-get remove -y gcc pkg-config && \
    apt-get autoremove -y && \
    apt-get clean

# 创建应用用户
RUN groupadd -r app && useradd -r -g app app

# 复制应用代码
COPY . .

# 创建必要目录并设置权限
RUN mkdir -p logs uploads && \
    chown -R app:app /app

# 切换到非root用户
USER app

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 启动命令（兼容Linux部署）
ENTRYPOINT ["/usr/local/bin/python"]
CMD ["-m", "src"]