# 应用程序设置
APP_NAME=fastapi-base-framework
HOST=0.0.0.0
PORT=8000
WORKERS_COUNT=1
RELOAD=False
ENVIRONMENT=production
DEBUG=False
LOG_LEVEL=INFO

# 数据库设置
DB_HOST=database-mysql-qa.cdb.17usoft.com
DB_PORT=17782
DB_USER=TCSoapServer
DB_PASSWORD=x37zhCbgKKWKlXJO3CrGVP2y4RcZVBYH
DB_BASE=TCSoapServer
DB_ECHO=False

# JWT设置
SECRET_KEY=key_e71c0ccc89094bbd83c981422983a48dba22962e920a1ee3
JWT_SECRET=secret_16dd6801127749a3ac6b86c29b658a95f0d6d83dee88e53c
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=1440

# SSO设置
SSO_ENABLED=True
SSO_DOMAIN=https://tccommon.qas.17usoft.com
SSO_CLIENT_ID=OpsFlow.local
SSO_CLIENT_SECRET=6d0863be86c8ce517bb4df62de36b30b
SSO_RETURN_URL=http://localhost:5173
SSO_REDIRECT_URI=http://localhost:5173/auth/callback


# 系统设置加密key
SYSTEM_SETTINGS_ENCRYPTION_KEY=dttrip-system-settings-encryption-key-2024

# CORS设置
CORS_ORIGINS=http://localhost:3000,http://localhost:5173,http://localhost:8080,https://soa.qa.dttrip.cn,https://soa.dttrip.cn
CORS_ALLOW_CREDENTIALS=True
CORS_ALLOW_METHODS=GET,POST,PUT,DELETE,OPTIONS,PATCH
CORS_ALLOW_HEADERS=*

# Kafka设置 - 本地开发环境
KAFKA_ENABLED=True
KAFKA_BOOTSTRAP_SERVERS=kafka.ops.17usoft.com:9092
KAFKA_TOPIC=dttrip_soap_topic_tasks
KAFKA_ACKS=1
KAFKA_RETRIES=1
KAFKA_LINGER_MS=3
KAFKA_BATCH_SIZE=65536
KAFKA_BUFFER_MEMORY=67108864
KAFKA_RECEIVE_BUFFER=67108864
KAFKA_COMPRESSION_TYPE=snappy 


#DIFY API
DIFY_SERVER_URL=https://difyapi.17usoft.com/v1
PASSPORT_REG_API_KEY=app-3uQKpowCseF3lY6tDUkXnOI5

# S3 OSS设置 - QA环境
S3_ENDPOINT_URL=http://oss.qa.17usoft.com
S3_REGION_NAME=us-east-1
S3_AWS_ACCESS_KEY_ID=FhhO34TTwubHsi5AlYAQ
S3_AWS_SECRET_ACCESS_KEY=AbwMPCvDDWE66sb4dcMEDrPDmoaui1QRv54IKYfh
S3_BUCKET_NAME=soap-files
S3_KEY_PREFIX=tmc.ai.soap.server
S3_USE_SSL=False
S3_VERIFY=False