#!/usr/bin/env python3
"""
创建系统设置表的数据库迁移脚本
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from tortoise import Tortoise
from src.db.config import TORTOISE_ORM


async def create_system_settings_table():
    """创建系统设置表"""
    try:
        # 初始化数据库连接
        await Tortoise.init(config=TORTOISE_ORM)
        
        # 获取数据库连接
        db = Tortoise.get_connection("default")
        
        # 创建系统设置表的SQL
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS `system_settings` (
            `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
            `user_id` VARCHAR(50) NOT NULL COMMENT '用户ID',
            `config_key` VARCHAR(100) NOT NULL COMMENT '配置项key',
            `config_name` VARCHAR(200) NOT NULL COMMENT '配置项名称',
            `config_value` TEXT NOT NULL COMMENT '配置项值（加密存储）',
            `created_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
            `updated_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
            UNIQUE KEY `unique_user_config` (`user_id`, `config_key`),
            INDEX `idx_user_id` (`user_id`),
            INDEX `idx_config_key` (`config_key`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统设置表';
        """
        
        # 执行创建表的SQL
        await db.execute_query(create_table_sql)
        print("✅ 系统设置表创建成功！")
        
        # 检查表是否创建成功
        check_sql = "SHOW TABLES LIKE 'system_settings'"
        result = await db.execute_query(check_sql)
        if result:
            print("✅ 表结构验证成功")
            
            # 显示表结构
            desc_sql = "DESCRIBE system_settings"
            columns = await db.execute_query(desc_sql)
            print("\n📋 表结构:")
            for row in columns:
                print(f"  {row[0]}: {row[1]} {row[2]} {row[3]} {row[4]} {row[5]}")
        else:
            print("❌ 表创建验证失败")
            
    except Exception as e:
        print(f"❌ 创建系统设置表失败: {str(e)}")
        raise
    finally:
        # 关闭数据库连接
        await Tortoise.close_connections()


if __name__ == "__main__":
    print("🚀 开始创建系统设置表...")
    asyncio.run(create_system_settings_table())
    print("✨ 迁移完成！") 