#!/usr/bin/env python3
"""
护照表重构迁移脚本

此脚本将完成以下重构工作：
1. 删除字段：place_of_birth, authority, signature_present
2. 重命名字段：document_type → certificate_type, passport_number → certificate_number  
3. 修改日期字段类型：date_of_birth, date_of_issue, date_of_expiry 从DATETIME改为VARCHAR(50)
4. 新增字段：passenger_type, viz_mrz_consistency, ssr_code

运行方式：
cd service-operation-server
poetry run python scripts/migrate_passport_table_refactor.py
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from tortoise import Tortoise
from src.db.config import TORTOISE_CONFIG
from loguru import logger


async def migrate_passport_table():
    """执行护照表重构迁移"""
    try:
        # 初始化数据库连接
        await Tortoise.init(TORTOISE_CONFIG)
        db = Tortoise.get_connection('default')
        
        logger.info("开始护照表重构迁移...")
        
        # 步骤1: 新增新字段
        logger.info("步骤1: 新增新字段...")
        
        new_fields = [
            "passenger_type VARCHAR(20) DEFAULT '' COMMENT '旅客类型（成人/儿童）'",
            "viz_mrz_consistency VARCHAR(100) DEFAULT '' COMMENT 'VIZ与MRZ数据一致性检查结果'",
            "ssr_code VARCHAR(50) DEFAULT '' COMMENT 'SSR码'"
        ]
        
        for field_sql in new_fields:
            try:
                await db.execute_query(f"ALTER TABLE passports ADD COLUMN {field_sql}")
                logger.info(f"✅ 新增字段成功: {field_sql.split()[0]}")
            except Exception as e:
                if "Duplicate column name" in str(e):
                    logger.info(f"⚠️  字段已存在，跳过: {field_sql.split()[0]}")
                else:
                    raise e
        
        # 步骤2: 重命名字段
        logger.info("步骤2: 重命名字段...")
        
        rename_operations = [
            ("document_type", "certificate_type"),
            ("passport_number", "certificate_number")
        ]
        
        for old_name, new_name in rename_operations:
            try:
                await db.execute_query(f"""
                    ALTER TABLE passports 
                    CHANGE COLUMN {old_name} {new_name} VARCHAR(50) NOT NULL DEFAULT '' 
                    COMMENT '{new_name}'
                """)
                logger.info(f"✅ 字段重命名成功: {old_name} → {new_name}")
            except Exception as e:
                if "Unknown column" in str(e) and old_name in str(e):
                    logger.info(f"⚠️  字段 {old_name} 不存在，可能已重命名，跳过")
                else:
                    raise e
        
        # 步骤3: 修改日期字段类型（先转换数据再修改类型）
        logger.info("步骤3: 修改日期字段类型...")
        
        date_fields = ["date_of_birth", "date_of_issue", "date_of_expiry"]
        
        for field in date_fields:
            try:
                # 先添加临时字段
                temp_field = f"{field}_temp"
                await db.execute_query(f"""
                    ALTER TABLE passports 
                    ADD COLUMN {temp_field} VARCHAR(50) DEFAULT '' 
                    COMMENT '临时{field}字段'
                """)
                
                # 转换数据：将DATETIME转换为YYYY-MM-DD格式
                await db.execute_query(f"""
                    UPDATE passports 
                    SET {temp_field} = CASE 
                        WHEN {field} IS NULL OR {field} = '1900-01-01 00:00:00' THEN ''
                        ELSE DATE_FORMAT({field}, '%Y-%m-%d')
                    END
                """)
                
                # 删除原字段
                await db.execute_query(f"ALTER TABLE passports DROP COLUMN {field}")
                
                # 重命名临时字段为原字段名
                await db.execute_query(f"""
                    ALTER TABLE passports 
                    CHANGE COLUMN {temp_field} {field} VARCHAR(50) NOT NULL DEFAULT '' 
                    COMMENT '{field}字段（字符串格式）'
                """)
                
                logger.info(f"✅ 日期字段类型修改成功: {field} (DATETIME → VARCHAR)")
                
            except Exception as e:
                if "Unknown column" in str(e) and field in str(e):
                    logger.info(f"⚠️  字段 {field} 不存在，可能已修改，跳过")
                else:
                    logger.error(f"❌ 修改字段 {field} 失败: {e}")
                    # 清理可能的临时字段
                    try:
                        await db.execute_query(f"ALTER TABLE passports DROP COLUMN {field}_temp")
                    except:
                        pass
                    raise e
        
        # 步骤4: 删除不需要的字段
        logger.info("步骤4: 删除不需要的字段...")
        
        fields_to_delete = ["place_of_birth", "authority", "signature_present"]
        
        for field in fields_to_delete:
            try:
                await db.execute_query(f"ALTER TABLE passports DROP COLUMN {field}")
                logger.info(f"✅ 删除字段成功: {field}")
            except Exception as e:
                if "Unknown column" in str(e):
                    logger.info(f"⚠️  字段 {field} 不存在，可能已删除，跳过")
                else:
                    raise e
        
        # 步骤5: 验证最终表结构
        logger.info("步骤5: 验证最终表结构...")
        
        result = await db.execute_query('SHOW COLUMNS FROM passports')
        
        logger.info("=== 最终表结构 ===")
        expected_fields = {
            'id', 'created_at', 'updated_at', 'task_id', 'uploaded_image_url',
            'dify_image_url', 'dify_image_uuid', 'dify_image_filename', 'dify_filename',
            'certificate_type', 'country_of_issue', 'certificate_number', 'surname',
            'given_names', 'nationality', 'date_of_birth', 'sex', 'date_of_issue',
            'date_of_expiry', 'passenger_type', 'viz_mrz_consistency', 'ssr_code',
            'mrz_line1', 'mrz_line2', 'additional_info', 'processing_status', 'user_id'
        }
        
        actual_fields = set()
        
        for row in result[1]:
            field_name = row['Field']
            field_type = row['Type']
            null_allowed = row['Null']
            default_value = row['Default']
            
            actual_fields.add(field_name)
            
            # 检查关键字段的类型
            if field_name in ['date_of_birth', 'date_of_issue', 'date_of_expiry']:
                if not field_type.startswith('varchar'):
                    logger.warning(f"⚠️  字段 {field_name} 类型不正确: {field_type} (期望: varchar)")
                else:
                    logger.info(f"✅ 字段 {field_name}: {field_type}")
            elif field_name in ['certificate_type', 'certificate_number']:
                logger.info(f"✅ 重命名字段 {field_name}: {field_type}")
            elif field_name in ['passenger_type', 'viz_mrz_consistency', 'ssr_code']:
                logger.info(f"✅ 新增字段 {field_name}: {field_type}")
        
        # 检查字段完整性
        missing_fields = expected_fields - actual_fields
        extra_fields = actual_fields - expected_fields
        
        if missing_fields:
            logger.error(f"❌ 缺少字段: {missing_fields}")
        
        if extra_fields:
            logger.warning(f"⚠️  额外字段: {extra_fields}")
        
        if not missing_fields and not extra_fields:
            logger.info("✅ 表结构验证通过！所有字段都正确")
        
        logger.info("🎉 护照表重构迁移完成！")
        
    except Exception as e:
        logger.error(f"❌ 迁移失败: {e}")
        import traceback
        traceback.print_exc()
        raise e
    finally:
        await Tortoise.close_connections()


async def main():
    """主函数"""
    logger.info("护照表重构迁移脚本")
    logger.info("=" * 50)
    
    # 确认执行
    print("此脚本将对passports表进行重构，包括：")
    print("1. 删除字段：place_of_birth, authority, signature_present")
    print("2. 重命名字段：document_type → certificate_type, passport_number → certificate_number")
    print("3. 修改日期字段类型：DATETIME → VARCHAR(50)")
    print("4. 新增字段：passenger_type, viz_mrz_consistency, ssr_code")
    print()
    
    confirm = input("确认执行迁移？(y/N): ").strip().lower()
    if confirm != 'y':
        print("迁移已取消")
        return
    
    try:
        await migrate_passport_table()
        logger.info("✅ 迁移成功完成！")
    except Exception as e:
        logger.error(f"❌ 迁移失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main()) 