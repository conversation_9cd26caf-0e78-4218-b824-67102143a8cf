#!/usr/bin/env python3
"""
添加用户密码列的数据库迁移脚本
"""
import asyncio
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.config import settings
from tortoise import Tortoise

async def add_password_column():
    """添加用户密码列"""

    print("开始添加用户密码列...")

    # 初始化Tortoise ORM
    await Tortoise.init(
        db_url=settings.db_url,
        modules={'models': ['src.db.models']}
    )

    # 获取数据库连接
    connection = Tortoise.get_connection("default")
    
    try:
        # 添加密码列的SQL语句
        sql_statements = [
            """
            ALTER TABLE users
            ADD COLUMN password VARCHAR(500) DEFAULT '' COMMENT '用户密码(加密存储)'
            """,
            """
            ALTER TABLE users
            ADD COLUMN password_salt VARCHAR(100) DEFAULT '' COMMENT '密码盐值'
            """,
        ]
        
        # 执行SQL语句
        for i, sql in enumerate(sql_statements, 1):
            try:
                print(f"执行SQL语句 {i}/{len(sql_statements)}")
                result = await connection.execute_query(sql.strip())
                print(f"✓ 执行SQL语句 {i}/{len(sql_statements)}")
            except Exception as e:
                print(f"✗ 执行SQL语句 {i} 失败: {e}")
                # 如果是列已存在的错误，可以忽略
                if "Duplicate column name" in str(e):
                    print(f"  列已存在，跳过...")
                    continue
                else:
                    raise e
        
        print("用户密码列添加完成！")
        
    except Exception as e:
        print(f"添加用户密码列失败: {e}")
        raise
    finally:
        # 关闭Tortoise连接
        await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(add_password_column())
