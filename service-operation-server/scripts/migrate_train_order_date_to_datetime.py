#!/usr/bin/env python3
"""
迁移脚本：将train_orders表的date字段改为datetime字段
- birth_date: date -> datetime
- id_expiry_date: date -> datetime  
- travel_date: date -> datetime
"""

import asyncio
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tortoise import Tortoise
from src.core.config import settings

async def migrate_train_order_dates():
    """迁移train_orders表的date字段为datetime字段"""
    try:
        # 初始化数据库连接
        await Tortoise.init(
            db_url=str(settings.db_url),
            modules={'models': ['src.db.models']}
        )
        
        conn = Tortoise.get_connection('default')
        
        print("开始迁移train_orders表的date字段为datetime字段...")
        
        # 1. 检查当前表结构
        print("\n1. 检查当前表结构...")
        result = await conn.execute_query('DESCRIBE train_orders')
        date_fields = []
        for row in result[1]:
            field_name = row.get('Field')
            field_type = row.get('Type')
            if field_name in ['birth_date', 'id_expiry_date', 'travel_date']:
                date_fields.append((field_name, field_type))
                print(f"   {field_name}: {field_type}")
        
        if not date_fields:
            print("   没有找到需要迁移的date字段")
            return
            
        # 2. 检查数据量
        print("\n2. 检查数据量...")
        count_result = await conn.execute_query('SELECT COUNT(*) as count FROM train_orders')
        record_count = count_result[1][0]['count']
        print(f"   当前表中有 {record_count} 条记录")
        
        # 3. 执行字段类型修改
        print("\n3. 执行字段类型修改...")
        
        # 定义字段映射和对应的SQL语句
        field_modifications = {
            'birth_date': {
                'sql': """
                ALTER TABLE train_orders 
                MODIFY COLUMN birth_date DATETIME NULL 
                COMMENT '出生日期'
                """,
                'description': '出生日期'
            },
            'id_expiry_date': {
                'sql': """
                ALTER TABLE train_orders 
                MODIFY COLUMN id_expiry_date DATETIME NULL 
                COMMENT '证件有效期至'
                """,
                'description': '证件有效期至'
            },
            'travel_date': {
                'sql': """
                ALTER TABLE train_orders 
                MODIFY COLUMN travel_date DATETIME NULL 
                COMMENT '出行日期'
                """,
                'description': '出行日期'
            }
        }
        
        for field_name, current_type in date_fields:
            if 'datetime' not in current_type.lower() and field_name in field_modifications:
                print(f"   修改字段 {field_name}: {current_type} -> DATETIME")
                
                modification = field_modifications[field_name]
                
                try:
                    await conn.execute_query(modification['sql'])
                    print(f"   ✅ {field_name} 修改成功")
                except Exception as e:
                    print(f"   ❌ {field_name} 修改失败: {e}")
                    raise
            else:
                print(f"   ⏭️ {field_name} 已经是datetime类型，跳过")
        
        # 4. 验证修改结果
        print("\n4. 验证修改结果...")
        result = await conn.execute_query('DESCRIBE train_orders')
        for row in result[1]:
            field_name = row.get('Field')
            field_type = row.get('Type')
            field_default = row.get('Default')
            if field_name in ['birth_date', 'id_expiry_date', 'travel_date']:
                print(f"   {field_name}: {field_type} (默认值: {field_default})")
        
        # 5. 验证数据完整性
        print("\n5. 验证数据完整性...")
        final_count_result = await conn.execute_query('SELECT COUNT(*) as count FROM train_orders')
        final_record_count = final_count_result[1][0]['count']
        print(f"   迁移后记录数: {final_record_count}")
        
        if final_record_count == record_count:
            print("   ✅ 数据完整性验证通过，无数据丢失")
        else:
            print(f"   ❌ 数据完整性验证失败，预期 {record_count} 条，实际 {final_record_count} 条")
        
        print("\n✅ 迁移完成！")
        
    except Exception as e:
        print(f"\n❌ 迁移失败: {e}")
        raise
    finally:
        await Tortoise.close_connections()

if __name__ == "__main__":
    print("Train Orders表Date字段迁移为Datetime字段")
    print("=" * 60)
    
    # 确认操作
    confirm = input("此操作将修改数据库表结构，是否继续？(y/N): ").strip().lower()
    if confirm != 'y':
        print("操作已取消")
        sys.exit(0)
    
    asyncio.run(migrate_train_order_dates()) 