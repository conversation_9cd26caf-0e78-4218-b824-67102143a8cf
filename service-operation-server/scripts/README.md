# 脚本目录说明

本目录包含项目开发和维护过程中使用的各种脚本工具。

## 📁 目录结构

### 🗄️ 数据库相关脚本
- `init_db.py` - 初始化数据库和基础数据
- `init_database.py` - 简单的数据库初始化
- `init_passport_table.py` - 初始化护照识别表
- `add_is_deleted_columns.py` - 添加软删除字段
- `update_schema.py` - 更新数据库架构
- `migrate_train_orders.py` - 火车票订单数据迁移

### 👥 用户和认证脚本
- `create_test_user.py` - 创建测试用户
- `check_users.py` - 检查用户数据
- `generate_token.py` - 生成JWT认证令牌

### 📊 项目和数据管理
- `create_test_project.py` - 创建测试项目
- `check_project_data.py` - 检查项目数据
- `check_passport_data.py` - 检查护照识别数据
- `check_train_orders.py` - 检查火车票订单数据

### 🧪 Excel处理和测试
- `create_test_excel.py` - 创建标准测试Excel文件
- `create_problematic_excel.py` - 创建有问题的Excel文件（用于测试列名空格问题）
- `debug_excel_parsing.py` - 调试Excel解析过程

### 🔧 调试和维护工具
- `debug_frontend_navigation.py` - 前端导航调试
- `debug_tasks.py` - 任务调试
- `check_table_structure.py` - 检查数据库表结构
- `add_logs.py` - 添加日志功能

## 🚀 使用方法

所有脚本都应该在项目根目录下使用Poetry运行：

```bash
# 切换到项目根目录
cd service-operation-server

# 运行脚本
poetry run python scripts/脚本名.py
```

## 📝 脚本分类说明

### 初始化脚本
用于项目首次部署或重置环境时使用。

### 数据迁移脚本
用于数据库结构变更或数据迁移时使用。

### 测试数据脚本
用于创建测试数据和验证功能。

### 调试脚本
用于问题诊断和功能调试。

### 维护脚本
用于日常维护和数据检查。

## ⚠️ 注意事项

1. **生产环境谨慎使用**：某些脚本可能会修改数据库，在生产环境使用前请仔细阅读脚本内容。

2. **备份数据**：运行数据迁移脚本前，请确保已备份重要数据。

3. **环境配置**：确保已正确配置数据库连接和环境变量。

4. **依赖检查**：运行脚本前确保所有依赖已安装（`poetry install`）。

## 🔗 相关文档

- [项目README](../README.md)
- [测试文档](../tests/README.md)
- [部署文档](../deploy/README.md) 