#!/usr/bin/env python3
"""
重新加密系统设置中的密码数据
由于更换了加密密钥，需要重新加密现有的密码数据
"""
import asyncio
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.config import settings
from tortoise import Tortoise
from src.core.encryption import encryption_service

async def re_encrypt_system_settings():
    """重新加密系统设置中的密码"""
    
    print("开始重新加密系统设置中的密码...")
    
    # 初始化Tortoise ORM
    await Tortoise.init(
        db_url=settings.db_url,
        modules={'models': ['src.db.models']}
    )
    
    # 获取数据库连接
    connection = Tortoise.get_connection("default")
    
    try:
        # 查询所有加密的密码设置
        passwords_sql = """
        SELECT id, user_id, config_key, config_value 
        FROM system_settings 
        WHERE config_key = 'tongcheng_password'
        """
        
        result = await connection.execute_query(passwords_sql)
        if isinstance(result, tuple) and len(result) > 1:
            passwords_data = result[1]
        else:
            passwords_data = result if isinstance(result, list) else []
        
        if not passwords_data:
            print("没有找到需要重新加密的密码")
            return
        
        print(f"找到 {len(passwords_data)} 个密码需要重新加密")
        
        # 为每个密码重新设置默认值
        default_passwords = {
            "123497": "123456",  # wee.guo 的默认密码
            "121577": "123456",  # ryan.yuan 的默认密码  
            "121578": "123456",  # admin 的默认密码
        }
        
        success_count = 0
        for password_setting in passwords_data:
            setting_id = password_setting['id']
            user_id = password_setting['user_id']
            old_encrypted_value = password_setting['config_value']
            
            try:
                # 使用默认密码重新加密
                default_password = default_passwords.get(user_id, "123456")
                new_encrypted_value = encryption_service.encrypt(default_password)
                
                # 更新数据库
                update_sql = """
                UPDATE system_settings 
                SET config_value = %s, updated_at = NOW()
                WHERE id = %s
                """
                
                await connection.execute_query(update_sql, [new_encrypted_value, setting_id])
                
                print(f"✓ 用户 {user_id} 的密码已重新加密")
                success_count += 1
                
            except Exception as e:
                print(f"✗ 用户 {user_id} 的密码重新加密失败: {e}")
        
        print(f"\n重新加密完成: {success_count}/{len(passwords_data)} 个密码处理成功")
        
        if success_count == len(passwords_data):
            print("🎉 所有密码重新加密成功！")
            print("注意：所有用户的同程管家密码已重置为默认值 '123456'")
            print("建议用户重新设置正确的密码")
        else:
            print("❌ 部分密码重新加密失败，请检查错误信息")
        
        # 测试解密是否正常工作
        print(f"\n测试解密功能...")
        test_sql = """
        SELECT config_value 
        FROM system_settings 
        WHERE config_key = 'tongcheng_password' 
        LIMIT 1
        """
        
        test_result = await connection.execute_query(test_sql)
        if isinstance(test_result, tuple) and len(test_result) > 1:
            test_data = test_result[1]
        else:
            test_data = test_result if isinstance(test_result, list) else []
        
        if test_data:
            encrypted_value = test_data[0]['config_value']
            try:
                decrypted_value = encryption_service.decrypt(encrypted_value)
                print(f"✓ 解密测试成功，解密结果: {decrypted_value}")
            except Exception as e:
                print(f"✗ 解密测试失败: {e}")
        
    except Exception as e:
        print(f"重新加密系统设置失败: {e}")
        raise
    finally:
        # 关闭Tortoise连接
        await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(re_encrypt_system_settings())
