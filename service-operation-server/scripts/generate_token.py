#!/usr/bin/env python3
"""
临时脚本：为用户生成JWT token
用于测试护照识别功能
"""

from src.core.security import create_access_token

def generate_token_for_user():
    # 为用户2生成JWT token (郭伟, tc_user_id: 123497)
    user_id = "2"
    token = create_access_token(subject=user_id)
    
    print("=" * 60)
    print("护照识别系统 - JWT Token 生成器")
    print("=" * 60)
    print(f"用户ID: {user_id}")
    print(f"JWT Token: {token}")
    print("=" * 60)
    print("使用方法:")
    print("1. 打开浏览器开发者工具 (F12)")
    print("2. 切换到 Console 标签")
    print("3. 执行以下命令:")
    print(f'   localStorage.setItem("access_token", "{token}");')
    print("4. 刷新页面")
    print("=" * 60)
    
    return token

if __name__ == "__main__":
    generate_token_for_user() 