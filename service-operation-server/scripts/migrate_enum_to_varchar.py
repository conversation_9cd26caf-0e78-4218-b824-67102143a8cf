#!/usr/bin/env python3
"""
数据库迁移脚本：将ENUM字段改为VARCHAR字段
用于解决生产数据库不支持ENUM类型的问题

使用方法:
python scripts/migrate_enum_to_varchar.py
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.database import get_database_connection
from src.core.config import settings


async def execute_migration():
    """执行数据库迁移"""
    print("🚀 开始执行ENUM到VARCHAR的数据库迁移...")
    
    try:
        # 获取数据库连接
        db = get_database_connection()
        
        print("📊 连接数据库成功")
        
        # 读取迁移SQL文件
        migration_file = project_root / "src/db/migrations/20250702_update_enum_to_varchar.sql"
        
        if not migration_file.exists():
            print(f"❌ 迁移文件不存在: {migration_file}")
            return False
            
        with open(migration_file, 'r', encoding='utf-8') as f:
            migration_sql = f.read()
        
        print("📄 读取迁移SQL文件成功")
        
        # 分割SQL语句（按分号分割，忽略注释）
        sql_statements = []
        for line in migration_sql.split('\n'):
            line = line.strip()
            if line and not line.startswith('--'):
                sql_statements.append(line)
        
        # 合并多行语句
        current_statement = ""
        statements = []
        for line in sql_statements:
            current_statement += " " + line
            if line.endswith(';'):
                statements.append(current_statement.strip())
                current_statement = ""
        
        print(f"📝 准备执行 {len(statements)} 条SQL语句")
        
        # 执行每条SQL语句
        for i, statement in enumerate(statements, 1):
            if statement.strip():
                try:
                    print(f"⏳ 执行第 {i} 条语句...")
                    await db.execute_query(statement)
                    print(f"✅ 第 {i} 条语句执行成功")
                except Exception as e:
                    print(f"❌ 第 {i} 条语句执行失败: {e}")
                    print(f"SQL: {statement}")
                    # 继续执行其他语句，不中断整个迁移过程
                    continue
        
        print("🎉 数据库迁移完成！")
        return True
        
    except Exception as e:
        print(f"❌ 数据库迁移失败: {e}")
        return False


async def verify_migration():
    """验证迁移结果"""
    print("\n🔍 验证迁移结果...")
    
    try:
        db = get_database_connection()
        
        # 检查permissions表结构
        result = await db.fetch_all("DESCRIBE permissions")
        permission_type_field = None
        for row in result:
            if row['Field'] == 'permission_type':
                permission_type_field = row
                break
        
        if permission_type_field:
            print(f"✅ permissions.permission_type 字段类型: {permission_type_field['Type']}")
            if 'varchar' in permission_type_field['Type'].lower():
                print("✅ permissions表迁移成功")
            else:
                print("❌ permissions表迁移可能失败")
        
        # 检查user_permissions表结构
        result = await db.fetch_all("DESCRIBE user_permissions")
        permission_type_field = None
        for row in result:
            if row['Field'] == 'permission_type':
                permission_type_field = row
                break
        
        if permission_type_field:
            print(f"✅ user_permissions.permission_type 字段类型: {permission_type_field['Type']}")
            if 'varchar' in permission_type_field['Type'].lower():
                print("✅ user_permissions表迁移成功")
            else:
                print("❌ user_permissions表迁移可能失败")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证迁移结果失败: {e}")
        return False


async def main():
    """主函数"""
    print("=" * 60)
    print("数据库ENUM字段迁移工具")
    print("=" * 60)
    
    # 显示当前配置
    print(f"数据库主机: {settings.database_host}")
    print(f"数据库名称: {settings.database_name}")
    print(f"数据库用户: {settings.database_user}")
    
    # 确认执行
    confirm = input("\n是否继续执行迁移? (y/N): ").strip().lower()
    if confirm != 'y':
        print("❌ 用户取消操作")
        return
    
    # 执行迁移
    success = await execute_migration()
    
    if success:
        # 验证迁移结果
        await verify_migration()
        print("\n🎉 迁移完成！请重启应用程序以使用新的字段类型。")
    else:
        print("\n❌ 迁移失败，请检查错误信息并手动处理。")


if __name__ == "__main__":
    asyncio.run(main())
