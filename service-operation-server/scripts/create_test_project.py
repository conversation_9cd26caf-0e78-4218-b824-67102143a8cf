#!/usr/bin/env python3
"""
临时脚本：创建测试项目
"""

import asyncio
from datetime import date
from tortoise import Tortoise
from src.db.models.project import Project
from src.core.config import settings

async def create_test_project():
    await Tortoise.init(
        db_url=settings.db_url,
        modules={'models': ['src.db.models']}
    )
    
    # 检查项目是否存在
    project = await Project.filter(id=1).first()
    if not project:
        # 创建测试项目
        project = await Project.create(
            project_number="2501010001",
            project_name="测试项目",
            creator_user_id=123497,
            creator_name="测试用户",
            project_description="用于测试Excel上传功能的项目",
            client_name="测试客户",
            project_date=date.today()
        )
        print(f'已创建测试项目: {project.project_name} (ID: {project.id})')
    else:
        print(f'项目已存在: {project.project_name} (ID: {project.id})')
    
    await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(create_test_project()) 