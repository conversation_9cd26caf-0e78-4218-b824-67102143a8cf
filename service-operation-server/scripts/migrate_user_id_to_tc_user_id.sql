-- 用户ID字段重构迁移脚本
-- 将users表中的user_id字段重命名为tc_user_id
-- 系统中所有的user_id引用将改为使用users表的id字段

-- 开始事务
START TRANSACTION;

-- 1. 首先备份当前的users表结构和数据（可选，用于回滚）
-- CREATE TABLE users_backup AS SELECT * FROM users;

-- 2. 添加新的tc_user_id字段
ALTER TABLE users ADD COLUMN tc_user_id VARCHAR(50) COMMENT '同程用户ID（原user_id）';

-- 3. 将原user_id字段的数据复制到tc_user_id字段
UPDATE users SET tc_user_id = user_id;

-- 4. 为tc_user_id字段添加唯一索引
ALTER TABLE users ADD UNIQUE INDEX idx_users_tc_user_id (tc_user_id);

-- 5. 删除原user_id字段的唯一索引
ALTER TABLE users DROP INDEX user_id;

-- 6. 删除原user_id字段
ALTER TABLE users DROP COLUMN user_id;

-- 7. 验证数据完整性
-- 检查tc_user_id字段是否有空值
SELECT COUNT(*) as null_count FROM users WHERE tc_user_id IS NULL;

-- 检查tc_user_id字段是否有重复值
SELECT tc_user_id, COUNT(*) as count FROM users GROUP BY tc_user_id HAVING COUNT(*) > 1;

-- 8. 显示迁移后的表结构
DESCRIBE users;

-- 提交事务
COMMIT;

-- 如果需要回滚，可以执行以下语句：
-- ROLLBACK;
-- 或者从备份表恢复：
-- DROP TABLE users;
-- RENAME TABLE users_backup TO users;
