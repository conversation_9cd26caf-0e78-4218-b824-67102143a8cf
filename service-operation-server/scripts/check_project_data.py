#!/usr/bin/env python3
"""
检查数据库中的项目数据
"""

import asyncio
from tortoise import Tortoise
from src.db.models.project import Project
from src.core.config import settings

async def check_project_data():
    await Tortoise.init(
        db_url=settings.db_url,
        modules={'models': ['src.db.models']}
    )
    
    # 获取所有项目
    projects = await Project.all()
    print(f"总共有 {len(projects)} 个项目")
    
    for project in projects:
        print(f"\n项目 {project.id}:")
        print(f"  项目编号: {project.project_number} ({type(project.project_number)})")
        print(f"  项目名称: {project.project_name}")
        print(f"  客户名称: {project.client_name}")
        print(f"  创建人ID: {project.creator_user_id} ({type(project.creator_user_id)})")
        print(f"  创建人名称: {project.creator_name}")
        print(f"  项目日期: {project.project_date} ({type(project.project_date)})")
        print(f"  创建时间: {project.created_at} ({type(project.created_at)})")
        
        # 尝试转换为响应模型
        try:
            from src.api.project.schemas import ProjectResponse
            response = ProjectResponse.model_validate(project)
            print(f"  ✅ 转换成功")
        except Exception as e:
            print(f"  ❌ 转换失败: {e}")
    
    await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(check_project_data()) 