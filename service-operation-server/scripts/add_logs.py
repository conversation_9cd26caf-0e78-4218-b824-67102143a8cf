#!/usr/bin/env python3
import re

# 读取文件
with open('src/api/auth/sso_endpoints.py', 'r') as f:
    content = f.read()

# 在 handle_callback 函数中添加日志
pattern1 = r'(access_token, user_info, app_token = await exchange_code_for_token\(code, state\))\n\s+# 创建用户信息对象'
replacement1 = r'\1\n\n        # 打印用户信息详情\n        logger.info(f"SSO 返回的用户信息类型: {type(user_info)}")\n        logger.info(f"SSO 返回的用户信息字段: {list(user_info.keys()) if isinstance(user_info, dict) else \'Not a dict\'}")\n        logger.info(f"SSO 返回的用户信息内容: {user_info}")\n        \n        # 创建用户信息对象'
content = re.sub(pattern1, replacement1, content)

# 在 verify_code 函数中添加日志
pattern2 = r'(access_token, user_info, app_token = await exchange_code_for_token\(code, state\))\n\s+# 创建用户信息对象'
replacement2 = r'\1\n\n        # 打印用户信息详情\n        logger.info(f"SSO 返回的用户信息类型: {type(user_info)}")\n        logger.info(f"SSO 返回的用户信息字段: {list(user_info.keys()) if isinstance(user_info, dict) else \'Not a dict\'}")\n        logger.info(f"SSO 返回的用户信息内容: {user_info}")\n        \n        # 创建用户信息对象'
content = re.sub(pattern2, replacement2, content)

# 写回文件
with open('src/api/auth/sso_endpoints.py', 'w') as f:
    f.write(content)

print("日志添加完成！")
