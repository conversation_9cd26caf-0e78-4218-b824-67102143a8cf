#!/usr/bin/env python3
"""
数据库初始化脚本。

此脚本用于初始化数据库，创建所有表结构，并可选择地填充初始数据。
主要用于开发环境或首次部署时使用。
"""

import asyncio
import os
import sys
import typer
from pathlib import Path
from rich.console import Console
import copy

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from tortoise import Tortoise
from aerich import Command

from src.db.config import get_tortoise_config
from src.core.config import settings

app = typer.Typer()
console = Console()

async def get_command():
    """获取 Aerich 命令实例"""
    config_copy = copy.deepcopy(get_tortoise_config())
    
    # 打印配置，查看是否存在问题
    print("\nTortoise 配置:")
    print(f"Connections: {list(config_copy['connections'].keys())}")
    print(f"Apps: {list(config_copy['apps'].keys())}")
    for app_name, app_config in config_copy['apps'].items():
        print(f"  App '{app_name}' models: {app_config.get('models')}")
        print(f"  App '{app_name}' default_connection: {app_config.get('default_connection')}")
        
    # 确保应用名称与 Tortoise 配置中的名称匹配
    app_name = "models"
    if app_name not in config_copy["apps"]:
        raise ValueError(f"App '{app_name}' 不在 Tortoise 配置中的 'apps' 字典里。可用的应用名称: {list(config_copy['apps'].keys())}")
        
    return Command(
        tortoise_config=config_copy,
        app=app_name,
        location="./migrations"  # 简化迁移路径，使用相对于项目根目录的路径
    )

async def init_tortoise():
    """初始化 Tortoise ORM 连接"""
    config = get_tortoise_config()
    
    # 显示连接信息，隐藏密码
    connection_string = config['connections']['default']
    # 如果是字符串，尝试隐藏密码
    if isinstance(connection_string, str):
        # 简单替换密码部分，不完全准确但足够用于显示
        masked_connection = connection_string
        if ':' in connection_string and '@' in connection_string:
            parts = connection_string.split('@')
            auth_parts = parts[0].split(':')
            if len(auth_parts) > 1:
                auth_parts[-1] = '******'  # 替换密码部分
                parts[0] = ':'.join(auth_parts)
                masked_connection = '@'.join(parts)
        connection_info = masked_connection
    else:
        connection_info = connection_string
    
    console.print(f"[bold]正在连接到数据库：{connection_info}[/]")
    
    try:
        # 初始化 Tortoise ORM
        await Tortoise.init(config=config)
        console.print("[bold green]✓[/] 已连接到数据库")
        
        # 测试连接
        conn = Tortoise.get_connection("default")
        result = await conn.execute_query("SELECT 1")
        console.print(f"[bold green]✓[/] 数据库连接测试成功: {result}")
        return True
    except Exception as e:
        console.print(f"[bold red]✗[/] 数据库连接失败: {str(e)}")
        
        # 检查常见问题
        if "Access denied" in str(e):
            console.print("[yellow]可能是用户名或密码错误，请检查 .env 文件中的数据库配置[/]")
        elif "Unknown database" in str(e):
            console.print(f"[yellow]数据库 '{settings.db_base}' 不存在，需要先创建数据库[/]")
            console.print(f"[yellow]可以使用以下命令创建数据库：[/]")
            console.print(f"[cyan]mysql -u {settings.db_user} -p -e 'CREATE DATABASE {settings.db_base} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;'[/]")
        elif "Can't connect to MySQL server" in str(e):
            console.print("[yellow]无法连接到 MySQL 服务器，请确保 MySQL 服务正在运行且可以访问[/]")
            console.print("[yellow]可以使用 'brew services start mysql' 启动 MySQL 服务[/]")
        
        return False

@app.command()
def init_db(
    force: bool = typer.Option(False, "--force", "-f", help="强制重新初始化，会删除所有现有数据"),
    seed: bool = typer.Option(False, "--seed", "-s", help="初始化后填充种子数据")
):
    """初始化数据库并创建所有表结构"""
    async def run():
        try:
            # 初始化数据库连接
            connection_success = await init_tortoise()
            if not connection_success:
                console.print("[bold red]错误：[/] 数据库连接测试失败，无法继续初始化数据库")
                return
            
            # 获取 Aerich 命令实例
            try:
                console.print("[bold]正在初始化 Aerich 命令...[/]")
                command = await get_command()
                console.print("[bold green]✓[/] Aerich 命令初始化成功")
            except Exception as e:
                console.print(f"[bold red]错误：[/] 无法初始化 Aerich 命令: {str(e)}")
                return
            
            if force:
                console.print("[bold yellow]警告：[/] 正在强制重新初始化数据库，这将删除所有现有数据！")
                try:
                    # 尝试删除 aerich 表，如果存在的话
                    await Tortoise.get_connection("default").execute_script("DROP TABLE IF EXISTS aerich")
                    console.print("[bold green]✓[/] 已删除 aerich 表")
                except Exception as e:
                    console.print(f"[yellow]无法删除 aerich 表: {e}[/]")
            
            # 使用两阶段初始化方法：先init-db，再初始化迁移
            console.print("[bold]尝试使用init-db初始化数据库架构...[/]")
            try:
                await command.init_db()
                console.print("[bold green]✓[/] 数据库架构初始化成功")
            except Exception as e:
                # 如果已经初始化，可能会失败，这是意料之中
                console.print(f"[bold yellow]初始化数据库时出错（可能已存在）：{str(e)}[/]")
            
            # 测试模型加载
            try:
                console.print("[bold]正在测试模型加载...[/]")
                from src.db.models import ApiKey, Developer
                console.print(f"[bold green]✓[/] 模型加载成功：{ApiKey.__name__}, {Developer.__name__}")
            except Exception as e:
                console.print(f"[bold red]模型加载错误：{str(e)}[/]")

            # 生成迁移脚本
            console.print("[bold]尝试生成迁移脚本...[/]")
            try:
                # 初始化 Aerich
                console.print("[bold]初始化 Aerich 命令...[/]")
                await command.init()
                console.print("[bold green]✓[/] 已初始化迁移环境")
                
                # 使用 generate_schemas 直接生成表结构，避开迁移问题
                console.print("[bold]直接生成数据库表结构...[/]")
                from tortoise import Tortoise
                await Tortoise.generate_schemas()
                console.print("[bold green]✓[/] 数据库表结构生成成功")
                
                # 尝试生成第一个迁移脚本
                console.print("[bold]尝试创建第一个空白迁移...[/]")
                try:
                    migrations = await command.migrate("初始化")
                    if migrations:
                        console.print(f"[bold green]✓[/] 已创建初始迁移")
                        for migration in migrations:
                            console.print(f"  • {migration}")
                except Exception as e:
                    console.print(f"[bold yellow]创建初始迁移失败（可能没有变更）：{str(e)}[/]")
                    
            except Exception as e:
                console.print(f"[bold red]迁移初始化错误：{str(e)}[/]")
                import traceback
                console.print(traceback.format_exc())
            
            # 应用迁移
            await command.upgrade()
            console.print("[bold green]✓[/] 已应用所有迁移")
            
            if seed:
                console.print("[bold]正在填充种子数据...[/]")
                # 这里可以添加种子数据填充的代码
                # 例如创建默认管理员用户、测试数据等
                console.print("[bold green]✓[/] 种子数据填充完成")
            
            console.print("[bold green]数据库初始化完成！[/]")
        
        except Exception as e:
            console.print(f"[bold red]错误：[/] {str(e)}")
            raise typer.Exit(code=1)
        finally:
            # 关闭数据库连接
            if Tortoise._inited:
                try:
                    await Tortoise.close_connections()
                except Exception as e:
                    console.print(f"[yellow]关闭数据库连接时出错：{str(e)}[/]")
                    # 不抛出异常，继续执行
    
    console.print(f"[bold]正在初始化 {settings.app_name} 数据库...[/]")
    asyncio.run(run())

if __name__ == "__main__":
    app()
