#!/usr/bin/env python3
"""
迁移脚本：将passports表的date字段改为datetime字段
- date_of_birth: date -> datetime
- date_of_issue: date -> datetime  
- date_of_expiry: date -> datetime
"""

import asyncio
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tortoise import Tortoise
from src.core.config import settings

async def migrate_passport_dates():
    """迁移passports表的date字段为datetime字段"""
    try:
        # 初始化数据库连接
        await Tortoise.init(
            db_url=str(settings.db_url),
            modules={'models': ['src.db.models']}
        )
        
        conn = Tortoise.get_connection('default')
        
        print("开始迁移passports表的date字段为datetime字段...")
        
        # 1. 检查当前表结构
        print("\n1. 检查当前表结构...")
        result = await conn.execute_query('DESCRIBE passports')
        date_fields = []
        for row in result[1]:
            field_name = row.get('Field')
            field_type = row.get('Type')
            if field_name in ['date_of_birth', 'date_of_issue', 'date_of_expiry']:
                date_fields.append((field_name, field_type))
                print(f"   {field_name}: {field_type}")
        
        if not date_fields:
            print("   没有找到需要迁移的date字段")
            return
            
        # 2. 备份当前数据（可选，但推荐）
        print("\n2. 检查数据量...")
        count_result = await conn.execute_query('SELECT COUNT(*) as count FROM passports')
        record_count = count_result[1][0]['count']
        print(f"   当前表中有 {record_count} 条记录")
        
        # 3. 执行字段类型修改
        print("\n3. 执行字段类型修改...")
        
        for field_name, current_type in date_fields:
            if 'datetime' not in current_type.lower():
                print(f"   修改字段 {field_name}: {current_type} -> DATETIME")
                
                # 构建ALTER TABLE语句
                # 对于date字段，我们需要保持默认值和注释
                if field_name == 'date_of_birth':
                    sql = f"""
                    ALTER TABLE passports 
                    MODIFY COLUMN {field_name} DATETIME DEFAULT '1900-01-01 00:00:00' 
                    COMMENT '出生日期'
                    """
                elif field_name == 'date_of_issue':
                    sql = f"""
                    ALTER TABLE passports 
                    MODIFY COLUMN {field_name} DATETIME DEFAULT '1900-01-01 00:00:00' 
                    COMMENT '签发日期'
                    """
                elif field_name == 'date_of_expiry':
                    sql = f"""
                    ALTER TABLE passports 
                    MODIFY COLUMN {field_name} DATETIME DEFAULT '1900-01-01 00:00:00' 
                    COMMENT '有效期至'
                    """
                
                try:
                    await conn.execute_query(sql)
                    print(f"   ✅ {field_name} 修改成功")
                except Exception as e:
                    print(f"   ❌ {field_name} 修改失败: {e}")
                    raise
            else:
                print(f"   ⏭️ {field_name} 已经是datetime类型，跳过")
        
        # 4. 验证修改结果
        print("\n4. 验证修改结果...")
        result = await conn.execute_query('DESCRIBE passports')
        for row in result[1]:
            field_name = row.get('Field')
            field_type = row.get('Type')
            field_default = row.get('Default')
            if field_name in ['date_of_birth', 'date_of_issue', 'date_of_expiry']:
                print(f"   {field_name}: {field_type} (默认值: {field_default})")
        
        print("\n✅ 迁移完成！")
        
    except Exception as e:
        print(f"\n❌ 迁移失败: {e}")
        raise
    finally:
        await Tortoise.close_connections()

if __name__ == "__main__":
    print("Passports表Date字段迁移为Datetime字段")
    print("=" * 50)
    
    # 确认操作
    confirm = input("此操作将修改数据库表结构，是否继续？(y/N): ").strip().lower()
    if confirm != 'y':
        print("操作已取消")
        sys.exit(0)
    
    asyncio.run(migrate_passport_dates()) 