#!/usr/bin/env python3
"""
测试加密服务是否正常工作
"""
import asyncio
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.encryption import encryption_service
from src.api.user_management.password_service import password_service

def test_encryption_service():
    """测试加密服务"""
    print("测试加密服务...")
    
    # 测试数据
    test_data = "Hello, World! 这是一个测试字符串。"
    
    try:
        # 测试加密
        encrypted = encryption_service.encrypt(test_data)
        print(f"原文: {test_data}")
        print(f"加密后: {encrypted}")
        print(f"加密后长度: {len(encrypted)}")
        
        # 测试解密
        decrypted = encryption_service.decrypt(encrypted)
        print(f"解密后: {decrypted}")
        
        # 验证结果
        if test_data == decrypted:
            print("✓ 加密解密测试通过")
        else:
            print("✗ 加密解密测试失败")
            return False
            
    except Exception as e:
        print(f"✗ 加密服务测试失败: {e}")
        return False
    
    return True

def test_password_service():
    """测试密码服务"""
    print("\n测试密码服务...")
    
    # 测试密码
    test_password = "TestPassword123!"
    
    try:
        # 测试密码加密
        encrypted_password, salt = password_service.hash_password(test_password)
        print(f"原密码: {test_password}")
        print(f"加密后密码长度: {len(encrypted_password)}")
        print(f"盐值长度: {len(salt)}")
        
        # 测试密码验证
        is_valid = password_service.verify_password(test_password, encrypted_password, salt)
        print(f"密码验证结果: {is_valid}")
        
        # 测试错误密码
        wrong_password = "WrongPassword123!"
        is_invalid = password_service.verify_password(wrong_password, encrypted_password, salt)
        print(f"错误密码验证结果: {is_invalid}")
        
        # 验证结果
        if is_valid and not is_invalid:
            print("✓ 密码服务测试通过")
        else:
            print("✗ 密码服务测试失败")
            return False
            
    except Exception as e:
        print(f"✗ 密码服务测试失败: {e}")
        return False
    
    return True

def test_password_strength():
    """测试密码强度验证"""
    print("\n测试密码强度验证...")
    
    test_cases = [
        ("123456", False, "密码太短"),
        ("password", False, "缺少大写字母和数字"),
        ("Password", False, "缺少数字"),
        ("Password123", True, "符合要求"),
        ("Password123!", True, "符合要求（包含特殊字符）"),
        ("a" * 51, False, "密码太长"),
    ]
    
    all_passed = True
    
    for password, expected_valid, description in test_cases:
        try:
            is_valid, error_msg = password_service.validate_password_strength(password)
            if is_valid == expected_valid:
                print(f"✓ {description}: {password[:10]}... -> {is_valid}")
            else:
                print(f"✗ {description}: {password[:10]}... -> 期望{expected_valid}, 实际{is_valid}")
                all_passed = False
        except Exception as e:
            print(f"✗ 密码强度测试失败: {e}")
            all_passed = False
    
    if all_passed:
        print("✓ 密码强度验证测试通过")
    else:
        print("✗ 密码强度验证测试失败")
    
    return all_passed

def main():
    """主测试函数"""
    print("开始测试加密和密码服务...")
    print("=" * 50)
    
    # 测试加密服务
    encryption_ok = test_encryption_service()
    
    # 测试密码服务
    password_ok = test_password_service()
    
    # 测试密码强度
    strength_ok = test_password_strength()
    
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    print(f"加密服务: {'✓ 通过' if encryption_ok else '✗ 失败'}")
    print(f"密码服务: {'✓ 通过' if password_ok else '✗ 失败'}")
    print(f"密码强度: {'✓ 通过' if strength_ok else '✗ 失败'}")
    
    if encryption_ok and password_ok and strength_ok:
        print("\n🎉 所有测试通过！加密和密码服务工作正常。")
        return True
    else:
        print("\n❌ 部分测试失败，请检查配置和代码。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
