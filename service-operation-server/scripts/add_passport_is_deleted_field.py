#!/usr/bin/env python3
"""
添加护照表的 is_deleted 字段迁移脚本
"""

import asyncio
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from tortoise import Tortoise
from src.core.config import Settings

async def add_is_deleted_field():
    """添加 is_deleted 字段到 passports 表"""
    settings = Settings()
    
    # 初始化数据库连接
    await Tortoise.init(
        db_url=settings.db_url,
        modules={"models": ["src.db.models"]}
    )
    
    try:
        # 获取数据库连接
        conn = Tortoise.get_connection("default")
        
        # 检查字段是否已存在
        check_sql = """
        SELECT COUNT(*) as count 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'passports' 
        AND COLUMN_NAME = 'is_deleted'
        """
        
        result = await conn.execute_query(check_sql)
        field_exists = result[1][0]['count'] > 0
        
        if field_exists:
            print("字段 is_deleted 已存在，跳过添加")
            return
        
        # 添加 is_deleted 字段
        add_field_sql = """
        ALTER TABLE passports 
        ADD COLUMN is_deleted TINYINT(1) NOT NULL DEFAULT 0 
        COMMENT '是否已删除'
        """
        
        await conn.execute_query(add_field_sql)
        print("成功添加 is_deleted 字段到 passports 表")
        
        # 创建索引以优化查询性能
        create_index_sql = """
        CREATE INDEX idx_passports_is_deleted ON passports(is_deleted)
        """
        
        try:
            await conn.execute_query(create_index_sql)
            print("成功创建 is_deleted 字段索引")
        except Exception as e:
            if "Duplicate key name" in str(e):
                print("索引已存在，跳过创建")
            else:
                print(f"创建索引失败: {e}")
        
        # 验证字段添加成功
        verify_sql = """
        DESCRIBE passports
        """
        
        result = await conn.execute_query(verify_sql)
        fields = [row['Field'] for row in result[1]]
        
        if 'is_deleted' in fields:
            print("验证成功：is_deleted 字段已添加到 passports 表")
        else:
            print("验证失败：is_deleted 字段未找到")
            
    except Exception as e:
        print(f"添加字段失败: {e}")
        raise
    finally:
        await Tortoise.close_connections()

if __name__ == "__main__":
    print("开始添加 is_deleted 字段到 passports 表...")
    asyncio.run(add_is_deleted_field())
    print("迁移完成")
