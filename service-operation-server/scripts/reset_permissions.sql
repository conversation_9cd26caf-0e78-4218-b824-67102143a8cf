-- 重置权限数据脚本 - 仅保留导航栏菜单权限
-- 执行时间: 2025-06-28

-- 清理现有数据
DELETE FROM role_permissions;
DELETE FROM permissions;
DELETE FROM applications;
DELETE FROM roles;

-- 重置自增ID
ALTER TABLE roles AUTO_INCREMENT = 1;
ALTER TABLE applications AUTO_INCREMENT = 1;
ALTER TABLE permissions AUTO_INCREMENT = 1;

-- 1. 初始化角色数据
INSERT INTO roles (role_name, role_code, description, is_system, status, created_by) VALUES
('超级管理员', 'super_admin', '系统超级管理员，拥有所有权限', 1, 1, 'system'),
('管理员', 'admin', '系统管理员，拥有管理权限', 1, 1, 'system'),
('普通用户', 'user', '普通用户，基础权限', 1, 1, 'system');

-- 2. 初始化应用数据
INSERT INTO applications (app_name, app_code, app_url, description, icon, sort_order, status, created_by) VALUES
('DTTrip服务运营平台', 'dttrip', 'http://localhost:5173/dashboard', 'DTTrip服务运营自动化平台', 'building-office', 1, 1, 'system'),
('用户管理系统', 'user_management', 'http://localhost:5173/user-management', '用户权限管理系统', 'users', 2, 1, 'system');

-- 3. 初始化权限数据（仅导航栏菜单权限）
INSERT INTO permissions (permission_name, permission_code, permission_type, parent_id, app_id, resource_path, description, sort_order, status, created_by) VALUES
-- DTTrip应用导航栏菜单权限
('应用中心', 'dttrip:dashboard', 'menu', 0, 1, '/dashboard', '应用中心菜单', 1, 1, 'system'),
('团房团票', 'dttrip:projects', 'menu', 0, 1, '/projects', '团房团票菜单', 2, 1, 'system'),
('护照识别', 'dttrip:passport', 'menu', 0, 1, '/passport-recognition', '护照识别菜单', 3, 1, 'system'),
('系统设置', 'dttrip:settings', 'menu', 0, 1, '/settings', '系统设置菜单', 4, 1, 'system'),

-- 用户管理应用导航栏菜单权限
('用户管理', 'user_mgmt:access', 'menu', 0, 2, '/user-management', '用户管理菜单', 5, 1, 'system');

-- 4. 初始化角色权限关联（仅导航栏菜单权限）
-- 超级管理员拥有所有导航栏菜单权限
INSERT INTO role_permissions (role_id, permission_id, granted_by) VALUES
(1, 1, 'system'), -- 应用中心
(1, 2, 'system'), -- 团房团票
(1, 3, 'system'), -- 护照识别
(1, 4, 'system'), -- 系统设置
(1, 5, 'system'); -- 用户管理

-- 管理员拥有所有导航栏菜单权限
INSERT INTO role_permissions (role_id, permission_id, granted_by) VALUES
(2, 1, 'system'), -- 应用中心
(2, 2, 'system'), -- 团房团票
(2, 3, 'system'), -- 护照识别
(2, 4, 'system'), -- 系统设置
(2, 5, 'system'); -- 用户管理

-- 普通用户只有基础导航栏菜单权限（除用户管理外）
INSERT INTO role_permissions (role_id, permission_id, granted_by) VALUES
(3, 1, 'system'), -- 应用中心
(3, 2, 'system'), -- 团房团票
(3, 3, 'system'), -- 护照识别
(3, 4, 'system'); -- 系统设置

-- 验证数据
SELECT '=== 角色数据 ===' as info;
SELECT * FROM roles;

SELECT '=== 应用数据 ===' as info;
SELECT * FROM applications;

SELECT '=== 权限数据 ===' as info;
SELECT * FROM permissions ORDER BY sort_order;

SELECT '=== 角色权限关联 ===' as info;
SELECT r.role_name, p.permission_name, p.permission_code 
FROM role_permissions rp 
JOIN roles r ON rp.role_id = r.id 
JOIN permissions p ON rp.permission_id = p.id 
ORDER BY r.id, p.sort_order;
