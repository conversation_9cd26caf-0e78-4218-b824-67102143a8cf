#!/usr/bin/env python3
"""
优化脚本：为users表字段添加注释，将varchar字段改为非null并设置默认值为空字符串
"""

import asyncio
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tortoise import Tortoise
from src.core.config import settings

async def optimize_users_table():
    """优化users表字段"""
    try:
        # 初始化数据库连接
        await Tortoise.init(
            db_url=str(settings.db_url),
            modules={'models': ['src.db.models']}
        )
        
        conn = Tortoise.get_connection('default')
        
        print("开始优化users表字段...")
        
        # 1. 检查当前表结构
        print("\n1. 检查当前表结构...")
        result = await conn.execute_query('DESCRIBE users')
        current_fields = {}
        varchar_fields = []
        
        for row in result[1]:
            field_name = row.get('Field')
            field_type = row.get('Type')
            field_null = row.get('Null')
            field_default = row.get('Default')
            field_comment = row.get('Comment', '')
            
            current_fields[field_name] = {
                'type': field_type,
                'null': field_null,
                'default': field_default,
                'comment': field_comment
            }
            
            if 'varchar' in field_type.lower():
                varchar_fields.append(field_name)
                print(f"   {field_name}: {field_type}, NULL={field_null}, 默认值={field_default}")
        
        print(f"\n找到 {len(varchar_fields)} 个VARCHAR字段需要优化")
        
        # 2. 检查数据量
        print("\n2. 检查数据量...")
        count_result = await conn.execute_query('SELECT COUNT(*) as count FROM users')
        record_count = count_result[1][0]['count']
        print(f"   当前表中有 {record_count} 条记录")
        
        # 3. 定义字段注释和优化信息
        field_definitions = {
            'id': {
                'comment': '用户主键ID',
                'is_varchar': False
            },
            'username': {
                'comment': '用户名',
                'is_varchar': True,
                'not_null': True  # 保持非空
            },
            'department': {
                'comment': '部门名称',
                'is_varchar': True
            },
            'user_id': {
                'comment': '用户唯一标识符',
                'is_varchar': True,
                'not_null': True  # 保持非空
            },
            'work_id': {
                'comment': '工号',
                'is_varchar': True
            },
            'new_work_id': {
                'comment': '新工号',
                'is_varchar': True
            },
            'department_id': {
                'comment': '部门ID',
                'is_varchar': True
            },
            'gender': {
                'comment': '性别',
                'is_varchar': True
            },
            'email': {
                'comment': '邮箱地址',
                'is_varchar': True
            },
            'dept_level_id': {
                'comment': '部门级别ID',
                'is_varchar': True
            },
            'dept_level_name': {
                'comment': '部门级别名称',
                'is_varchar': True
            },
            'phone_number': {
                'comment': '电话号码',
                'is_varchar': True
            },
            'mtid': {
                'comment': 'MT ID标识',
                'is_varchar': True
            },
            'ctids': {
                'comment': 'CT IDs标识',
                'is_varchar': True
            },
            'gid': {
                'comment': 'G ID标识',
                'is_varchar': True
            },
            'mobile': {
                'comment': '手机号码',
                'is_varchar': True
            },
            'member_id': {
                'comment': '会员ID',
                'is_varchar': True
            },
            'is_virtual': {
                'comment': '是否虚拟用户（0否，1是）',
                'is_varchar': False
            },
            'tid': {
                'comment': 'T ID标识',
                'is_varchar': True
            },
            'device_id': {
                'comment': '设备ID',
                'is_varchar': True
            },
            'created_at': {
                'comment': '创建时间',
                'is_varchar': False
            },
            'updated_at': {
                'comment': '更新时间',
                'is_varchar': False
            }
        }
        
        # 4. 执行字段优化
        print("\n3. 执行字段优化...")
        
        for field_name, field_info in current_fields.items():
            if field_name in field_definitions:
                definition = field_definitions[field_name]
                
                # 构建ALTER语句
                if definition['is_varchar']:
                    # varchar字段优化
                    current_type = field_info['type']
                    is_currently_not_null = field_info['null'] == 'NO'
                    should_be_not_null = definition.get('not_null', False)
                    
                    # 决定是否需要修改
                    needs_update = (
                        field_info['comment'] != definition['comment'] or
                        (not should_be_not_null and is_currently_not_null) or  # 需要改为允许null
                        (should_be_not_null and not is_currently_not_null) or  # 需要改为不允许null
                        (not should_be_not_null and field_info['default'] != '')  # 需要设置默认值
                    )
                    
                    if needs_update:
                        if should_be_not_null:
                            # 保持非空（username, user_id）
                            sql = f"""
                            ALTER TABLE users 
                            MODIFY COLUMN {field_name} {current_type} NOT NULL 
                            COMMENT '{definition['comment']}'
                            """
                        else:
                            # 改为非null，默认值为空字符串
                            sql = f"""
                            ALTER TABLE users 
                            MODIFY COLUMN {field_name} {current_type} NOT NULL DEFAULT '' 
                            COMMENT '{definition['comment']}'
                            """
                        
                        try:
                            print(f"   修改字段 {field_name}: {current_type} -> 添加注释和优化默认值")
                            await conn.execute_query(sql)
                            print(f"   ✅ {field_name} 优化成功")
                        except Exception as e:
                            print(f"   ❌ {field_name} 优化失败: {e}")
                            raise
                else:
                    # 非varchar字段，只添加注释
                    if field_info['comment'] != definition['comment']:
                        current_type = field_info['type']
                        null_clause = "NOT NULL" if field_info['null'] == 'NO' else "NULL"
                        default_clause = f"DEFAULT {field_info['default']}" if field_info['default'] else ""
                        
                        sql = f"""
                        ALTER TABLE users 
                        MODIFY COLUMN {field_name} {current_type} {null_clause} {default_clause}
                        COMMENT '{definition['comment']}'
                        """
                        
                        try:
                            print(f"   为字段 {field_name} 添加注释")
                            await conn.execute_query(sql)
                            print(f"   ✅ {field_name} 注释添加成功")
                        except Exception as e:
                            print(f"   ❌ {field_name} 注释添加失败: {e}")
                            raise
            else:
                print(f"   ⚠️ 字段 {field_name} 未在定义中找到，跳过")
        
        # 5. 验证优化结果
        print("\n4. 验证优化结果...")
        result = await conn.execute_query('DESCRIBE users')
        print("优化后的表结构:")
        for row in result[1]:
            field_name = row.get('Field')
            field_type = row.get('Type')
            field_null = row.get('Null')
            field_default = row.get('Default')
            field_comment = row.get('Comment', '')
            
            if 'varchar' in field_type.lower():
                print(f"   {field_name}: {field_type}, NULL={field_null}, 默认值='{field_default}', 注释=\"{field_comment}\"")
        
        # 6. 验证数据完整性
        print("\n5. 验证数据完整性...")
        final_count_result = await conn.execute_query('SELECT COUNT(*) as count FROM users')
        final_record_count = final_count_result[1][0]['count']
        print(f"   优化后记录数: {final_record_count}")
        
        if final_record_count == record_count:
            print("   ✅ 数据完整性验证通过，无数据丢失")
        else:
            print(f"   ❌ 数据完整性验证失败，预期 {record_count} 条，实际 {final_record_count} 条")
        
        print("\n✅ 优化完成！")
        
    except Exception as e:
        print(f"\n❌ 优化失败: {e}")
        raise
    finally:
        await Tortoise.close_connections()

if __name__ == "__main__":
    print("Users表字段优化")
    print("=" * 50)
    print("操作内容：")
    print("1. 为所有字段添加业务注释")
    print("2. 将VARCHAR字段改为NOT NULL DEFAULT ''")
    print("3. 保持username和user_id为NOT NULL（无默认值）")
    print("=" * 50)
    
    # 确认操作
    confirm = input("此操作将修改数据库表结构，是否继续？(y/N): ").strip().lower()
    if confirm != 'y':
        print("操作已取消")
        sys.exit(0)
    
    asyncio.run(optimize_users_table()) 