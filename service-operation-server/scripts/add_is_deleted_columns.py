#!/usr/bin/env python3
"""
添加is_deleted字段到数据库表
"""

import asyncio
import aiomysql
from src.core.config import settings

async def add_is_deleted_columns():
    # 连接数据库
    connection = await aiomysql.connect(
        host=settings.db_host,
        port=settings.db_port,
        user=settings.db_user,
        password=settings.db_password,
        db=settings.db_base,
        autocommit=True
    )
    
    try:
        cursor = await connection.cursor()
        
        # 需要添加is_deleted字段的表
        tables = [
            'projects',
            'project_tasks', 
            'train_orders'
        ]
        
        for table in tables:
            try:
                # 检查字段是否已存在
                await cursor.execute(f"DESCRIBE {table}")
                columns = await cursor.fetchall()
                column_names = [col[0] for col in columns]
                
                if 'is_deleted' not in column_names:
                    # 添加is_deleted字段
                    sql = f"ALTER TABLE {table} ADD COLUMN is_deleted BOOLEAN DEFAULT FALSE"
                    await cursor.execute(sql)
                    print(f"已为表 {table} 添加 is_deleted 字段")
                else:
                    print(f"表 {table} 已有 is_deleted 字段")
                    
            except Exception as e:
                print(f"处理表 {table} 时出错: {e}")
                
    finally:
        await connection.ensure_closed()

if __name__ == "__main__":
    asyncio.run(add_is_deleted_columns()) 