#!/usr/bin/env python3
"""
初始化护照表的脚本
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from tortoise import Tortoise
from src.core.config import settings
from src.db.models import Passport, User
from loguru import logger

async def init_passport_table():
    """初始化护照表"""
    try:
        # 初始化数据库连接
        await Tortoise.init(
            db_url=settings.db_url,
            modules={"models": ["src.db.models"]}
        )
        
        # 生成数据库表结构
        await Tortoise.generate_schemas()
        
        logger.info("✅ 护照表初始化成功")
        
        # 验证表是否创建成功
        passport_count = await Passport.all().count()
        logger.info(f"📊 当前护照记录数量: {passport_count}")
        
        # 创建一些示例数据（可选）
        await create_sample_data()
        
    except Exception as e:
        logger.error(f"❌ 初始化护照表失败: {e}")
        raise
    finally:
        await Tortoise.close_connections()

async def create_sample_data():
    """创建示例数据"""
    try:
        # 检查是否已有用户数据
        user_count = await User.all().count()
        if user_count == 0:
            logger.warning("⚠️  没有找到用户数据，跳过创建示例护照数据")
            return
        
        # 获取第一个用户
        first_user = await User.first()
        if not first_user:
            logger.warning("⚠️  没有找到用户，跳过创建示例护照数据")
            return
        
        # 检查是否已有护照数据
        existing_passport = await Passport.filter(user=first_user).first()
        if existing_passport:
            logger.info("📋 已存在护照数据，跳过创建示例数据")
            return
        
        # 创建示例护照数据
        sample_recognition_data = {
            "document_type": "Passport",
            "country_of_issue": "China",
            "passport_number": "E12345678",
            "surname": "ZHANG",
            "given_names": "SAN",
            "nationality": "CHN",
            "date_of_birth": "1990-01-01",
            "sex": "M",
            "place_of_birth": "Beijing",
            "date_of_issue": "2020-01-01",
            "date_of_expiry": "2030-01-01",
            "authority": "Ministry of Public Security",
            "mrz_line1": "P<CHNZHANG<<SAN<<<<<<<<<<<<<<<<<<<<<<<<<<<<",
            "mrz_line2": "E123456781CHN9001011M3001011<<<<<<<<<<<<<<04",
            "signature_present": True,
            "additional_info": {
                "confidence": 0.95,
                "processing_time": "2.3s"
            }
        }
        
        sample_passport = await Passport.create_from_recognition_data(
            user_id=first_user.user_id,
            task_id="sample_task_001",
            uploaded_image_url="https://example.com/passport_images/sample.jpg",
            recognition_data=sample_recognition_data
        )
        
        logger.info(f"✅ 创建示例护照数据成功: {sample_passport.id}")
        
    except Exception as e:
        logger.error(f"❌ 创建示例数据失败: {e}")

async def verify_table_structure():
    """验证表结构"""
    try:
        # 获取第一个用户用于测试
        first_user = await User.first()
        if not first_user:
            logger.warning("⚠️  没有找到用户，跳过表结构验证")
            return
            
        # 执行一个简单的查询来验证表结构
        passport = await Passport.create(
            user=first_user,
            task_id="test_task",
            uploaded_image_url="https://example.com/test.jpg"
        )
        
        # 立即删除测试数据
        await passport.delete()
        
        logger.info("✅ 表结构验证成功")
        
    except Exception as e:
        logger.error(f"❌ 表结构验证失败: {e}")
        raise

if __name__ == "__main__":
    logger.info("🚀 开始初始化护照表...")
    asyncio.run(init_passport_table())
    logger.info("🎉 护照表初始化完成！") 