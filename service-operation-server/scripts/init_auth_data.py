#!/usr/bin/env python3
"""
初始化权限认证数据的脚本
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from tortoise import Tortoise
from src.core.config import settings


async def init_auth_data():
    """初始化权限认证数据"""
    
    # 构建数据库URL
    database_url = f"mysql://{settings.db_user}:{settings.db_password}@{settings.db_host}:{settings.db_port}/{settings.db_base}"

    # 初始化数据库连接
    await Tortoise.init(
        db_url=database_url,
        modules={'models': ['src.db.models']}
    )
    
    # 初始化数据的SQL语句
    init_data_sql = """
    -- 1. 初始化角色数据
    INSERT IGNORE INTO roles (role_name, role_code, description, is_system, status, created_by) VALUES
    ('超级管理员', 'super_admin', '系统超级管理员，拥有所有权限', 1, 1, 'system'),
    ('管理员', 'admin', '系统管理员，拥有管理权限', 1, 1, 'system'),
    ('普通用户', 'user', '普通用户，基础权限', 1, 1, 'system');

    -- 2. 初始化应用数据
    INSERT IGNORE INTO applications (app_name, app_code, app_url, description, icon, sort_order, status, created_by) VALUES
    ('DTTrip服务运营平台', 'dttrip', 'http://localhost:5173/dashboard', 'DTTrip服务运营自动化平台', 'building-office', 1, 1, 'system'),
    ('用户管理系统', 'user_management', 'http://localhost:5173/user-management', '用户权限管理系统', 'users', 2, 1, 'system');

    -- 3. 初始化权限数据
    -- DTTrip应用权限
    INSERT IGNORE INTO permissions (permission_name, permission_code, permission_type, parent_id, app_id, resource_path, description, sort_order, status, created_by) VALUES
    -- 应用访问权限
    ('DTTrip应用访问', 'dttrip:access', 'menu', 0, 1, '/dashboard', 'DTTrip应用访问权限', 1, 1, 'system'),

    -- 项目管理权限
    ('项目管理', 'dttrip:project', 'menu', 1, 1, '/projects', '项目管理模块', 10, 1, 'system'),
    ('项目查看', 'dttrip:project:view', 'button', 2, 1, '/projects', '查看项目列表', 11, 1, 'system'),
    ('项目创建', 'dttrip:project:create', 'button', 2, 1, '/projects/create', '创建新项目', 12, 1, 'system'),
    ('项目编辑', 'dttrip:project:edit', 'button', 2, 1, '/projects/edit', '编辑项目信息', 13, 1, 'system'),
    ('项目删除', 'dttrip:project:delete', 'button', 2, 1, '/projects/delete', '删除项目', 14, 1, 'system'),

    -- 火车票预订权限
    ('火车票预订', 'dttrip:train', 'menu', 1, 1, '/project-task-detail', '火车票预订模块', 20, 1, 'system'),
    ('火车票查看', 'dttrip:train:view', 'button', 7, 1, '/project-task-detail', '查看火车票订单', 21, 1, 'system'),
    ('火车票预订', 'dttrip:train:book', 'button', 7, 1, '/project-task-detail', '预订火车票', 22, 1, 'system'),
    ('火车票编辑', 'dttrip:train:edit', 'button', 7, 1, '/project-task-detail', '编辑火车票订单', 23, 1, 'system'),
    ('火车票删除', 'dttrip:train:delete', 'button', 7, 1, '/project-task-detail', '删除火车票订单', 24, 1, 'system'),
    ('火车票对账单', 'dttrip:train:reconciliation', 'button', 7, 1, '/project-task-detail', '火车票对账单管理', 25, 1, 'system'),

    -- 酒店预订权限
    ('酒店预订', 'dttrip:hotel', 'menu', 1, 1, '/hotel-booking', '酒店预订模块', 30, 1, 'system'),
    ('酒店查看', 'dttrip:hotel:view', 'button', 12, 1, '/hotel-booking', '查看酒店订单', 31, 1, 'system'),
    ('酒店预订', 'dttrip:hotel:book', 'button', 12, 1, '/hotel-booking', '预订酒店', 32, 1, 'system'),
    ('酒店编辑', 'dttrip:hotel:edit', 'button', 12, 1, '/hotel-booking', '编辑酒店订单', 33, 1, 'system'),
    ('酒店删除', 'dttrip:hotel:delete', 'button', 12, 1, '/hotel-booking', '删除酒店订单', 34, 1, 'system'),
    ('酒店对账单', 'dttrip:hotel:reconciliation', 'button', 12, 1, '/hotel-booking', '酒店对账单管理', 35, 1, 'system'),

    -- 护照识别权限
    ('护照识别', 'dttrip:passport', 'menu', 1, 1, '/passport-recognition', '护照识别模块', 40, 1, 'system'),
    ('护照识别查看', 'dttrip:passport:view', 'button', 17, 1, '/passport-recognition', '查看护照识别记录', 41, 1, 'system'),
    ('护照识别上传', 'dttrip:passport:upload', 'button', 17, 1, '/passport-recognition', '上传护照图片', 42, 1, 'system'),

    -- 系统设置权限
    ('系统设置', 'dttrip:settings', 'menu', 1, 1, '/settings', '系统设置模块', 50, 1, 'system'),
    ('系统设置查看', 'dttrip:settings:view', 'button', 20, 1, '/settings', '查看系统设置', 51, 1, 'system'),
    ('系统设置编辑', 'dttrip:settings:edit', 'button', 20, 1, '/settings', '编辑系统设置', 52, 1, 'system');

    -- 用户管理应用权限
    INSERT IGNORE INTO permissions (permission_name, permission_code, permission_type, parent_id, app_id, resource_path, description, sort_order, status, created_by) VALUES
    -- 用户管理应用访问权限
    ('用户管理应用访问', 'user_mgmt:access', 'menu', 0, 2, '/user-management', '用户管理应用访问权限', 1, 1, 'system'),

    -- 用户管理权限
    ('用户管理', 'user_mgmt:user', 'menu', 23, 2, '/user-management/users', '用户管理模块', 10, 1, 'system'),
    ('用户查看', 'user_mgmt:user:view', 'button', 24, 2, '/user-management/users', '查看用户列表', 11, 1, 'system'),
    ('用户创建', 'user_mgmt:user:create', 'button', 24, 2, '/user-management/users', '创建用户', 12, 1, 'system'),
    ('用户编辑', 'user_mgmt:user:edit', 'button', 24, 2, '/user-management/users', '编辑用户信息', 13, 1, 'system'),
    ('用户删除', 'user_mgmt:user:delete', 'button', 24, 2, '/user-management/users', '删除用户', 14, 1, 'system'),
    ('用户状态管理', 'user_mgmt:user:status', 'button', 24, 2, '/user-management/users', '管理用户状态', 15, 1, 'system'),

    -- 角色管理权限
    ('角色管理', 'user_mgmt:role', 'menu', 23, 2, '/user-management/roles', '角色管理模块', 20, 1, 'system'),
    ('角色查看', 'user_mgmt:role:view', 'button', 30, 2, '/user-management/roles', '查看角色列表', 21, 1, 'system'),
    ('角色创建', 'user_mgmt:role:create', 'button', 30, 2, '/user-management/roles', '创建角色', 22, 1, 'system'),
    ('角色编辑', 'user_mgmt:role:edit', 'button', 30, 2, '/user-management/roles', '编辑角色信息', 23, 1, 'system'),
    ('角色删除', 'user_mgmt:role:delete', 'button', 30, 2, '/user-management/roles', '删除角色', 24, 1, 'system'),

    -- 权限管理权限
    ('权限管理', 'user_mgmt:permission', 'menu', 23, 2, '/user-management/permissions', '权限管理模块', 30, 1, 'system'),
    ('权限查看', 'user_mgmt:permission:view', 'button', 35, 2, '/user-management/permissions', '查看权限列表', 31, 1, 'system'),
    ('权限创建', 'user_mgmt:permission:create', 'button', 35, 2, '/user-management/permissions', '创建权限', 32, 1, 'system'),
    ('权限编辑', 'user_mgmt:permission:edit', 'button', 35, 2, '/user-management/permissions', '编辑权限信息', 33, 1, 'system'),
    ('权限删除', 'user_mgmt:permission:delete', 'button', 35, 2, '/user-management/permissions', '删除权限', 34, 1, 'system'),

    -- 用户权限管理权限
    ('用户权限管理', 'user_mgmt:user_permission', 'menu', 23, 2, '/user-management/user-permissions', '用户权限管理模块', 40, 1, 'system'),
    ('用户权限查看', 'user_mgmt:user_permission:view', 'button', 40, 2, '/user-management/user-permissions', '查看用户权限', 41, 1, 'system'),
    ('用户权限分配', 'user_mgmt:user_permission:assign', 'button', 40, 2, '/user-management/user-permissions', '分配用户权限', 42, 1, 'system'),
    ('用户权限撤销', 'user_mgmt:user_permission:revoke', 'button', 40, 2, '/user-management/user-permissions', '撤销用户权限', 43, 1, 'system');

    -- 4. 初始化角色权限关联
    -- 超级管理员拥有所有权限
    INSERT IGNORE INTO role_permissions (role_id, permission_id, granted_by)
    SELECT 1, id, 'system' FROM permissions WHERE status = 1;

    -- 管理员拥有用户管理权限和DTTrip基础权限
    INSERT IGNORE INTO role_permissions (role_id, permission_id, granted_by) VALUES
    -- DTTrip应用访问权限
    (2, 1, 'system'),
    -- 项目管理权限（除删除外）
    (2, 2, 'system'), (2, 3, 'system'), (2, 4, 'system'), (2, 5, 'system'),
    -- 火车票预订权限（除删除外）
    (2, 7, 'system'), (2, 8, 'system'), (2, 9, 'system'), (2, 10, 'system'), (2, 12, 'system'),
    -- 酒店预订权限（除删除外）
    (2, 13, 'system'), (2, 14, 'system'), (2, 15, 'system'), (2, 16, 'system'), (2, 18, 'system'),
    -- 护照识别权限
    (2, 19, 'system'), (2, 20, 'system'), (2, 21, 'system'),
    -- 系统设置查看权限
    (2, 22, 'system'), (2, 23, 'system'),
    -- 用户管理应用访问权限
    (2, 24, 'system'),
    -- 用户管理权限
    (2, 25, 'system'), (2, 26, 'system'), (2, 27, 'system'), (2, 28, 'system'), (2, 30, 'system'),
    -- 角色管理权限
    (2, 31, 'system'), (2, 32, 'system'), (2, 33, 'system'), (2, 34, 'system'),
    -- 权限管理查看权限
    (2, 36, 'system'), (2, 37, 'system'),
    -- 用户权限管理权限
    (2, 41, 'system'), (2, 42, 'system'), (2, 43, 'system'), (2, 44, 'system');

    -- 普通用户只有DTTrip应用基础查看权限
    INSERT IGNORE INTO role_permissions (role_id, permission_id, granted_by) VALUES
    -- DTTrip应用访问权限
    (3, 1, 'system'),
    -- 项目查看权限
    (3, 2, 'system'), (3, 3, 'system'),
    -- 火车票查看权限
    (3, 7, 'system'), (3, 8, 'system'),
    -- 酒店查看权限
    (3, 13, 'system'), (3, 14, 'system'),
    -- 护照识别查看权限
    (3, 19, 'system'), (3, 20, 'system'),
    -- 系统设置查看权限
    (3, 22, 'system'), (3, 23, 'system');
    """
    
    try:
        # 获取数据库连接
        connection = Tortoise.get_connection("default")
        
        # 执行SQL语句
        print("开始初始化权限认证数据...")
        
        # 分割SQL语句并逐个执行
        sql_statements = [stmt.strip() for stmt in init_data_sql.split(';') if stmt.strip()]
        
        for i, sql in enumerate(sql_statements, 1):
            try:
                await connection.execute_query(sql)
                print(f"✓ 执行SQL语句 {i}/{len(sql_statements)}")
            except Exception as e:
                print(f"✗ 执行SQL语句 {i} 失败: {e}")
                # 继续执行其他语句
                continue
        
        print("权限认证数据初始化完成！")
        
    except Exception as e:
        print(f"初始化数据时发生错误: {e}")
        raise
    finally:
        await Tortoise.close_connections()


if __name__ == "__main__":
    asyncio.run(init_auth_data())
