#!/usr/bin/env python3
"""
调试Excel解析过程
"""

import pandas as pd
from io import BytesIO

def debug_excel_parsing():
    print("📋 开始调试Excel解析过程...")
    
    # 读取我们创建的测试Excel文件
    try:
        df = pd.read_excel('test_train_orders.xlsx')
        print(f"✅ Excel文件读取成功，共 {len(df)} 行数据")
        print(f"📊 列名: {list(df.columns)}")
        
        print("\n📝 原始数据预览:")
        for i, (index, row) in enumerate(df.iterrows()):
            if i < 3:  # 只显示前3行
                print(f"\n第 {index + 1} 行数据:")
                for col in df.columns:
                    print(f"  {col}: {repr(row[col])} (类型: {type(row[col])})")
        
        print("\n🔍 检查关键字段解析:")
        
        def safe_str(value):
            """安全转换为字符串"""
            if pd.isna(value) or value == "" or value is None:
                return None
            return str(value).strip()
        
        for index, row in df.iterrows():
            traveler_name = safe_str(row.get('出行人姓名'))
            traveler_surname = safe_str(row.get('出行人姓'))
            traveler_given_name = safe_str(row.get('出行人名'))
            
            print(f"\n第 {index + 1} 行解析结果:")
            print(f"  row.get('出行人姓名'): {repr(row.get('出行人姓名'))}")
            print(f"  safe_str结果: {repr(traveler_name)}")
            print(f"  最终姓名: {traveler_name or f'乘客{index + 1}'}")
            print(f"  姓: {traveler_surname}")
            print(f"  名: {traveler_given_name}")
        
        print("\n🎯 潜在问题检查:")
        
        # 检查列名是否完全匹配
        expected_columns = ['出行人姓名', '出行人姓', '出行人名']
        for col in expected_columns:
            if col in df.columns:
                print(f"  ✅ 列 '{col}' 存在")
            else:
                print(f"  ❌ 列 '{col}' 不存在")
                print(f"     相似列名: {[c for c in df.columns if col[:2] in c]}")
        
        # 检查数据是否为空
        for col in expected_columns:
            if col in df.columns:
                null_count = df[col].isna().sum()
                empty_count = (df[col] == "").sum()
                print(f"  列 '{col}': {null_count} 个NaN, {empty_count} 个空字符串")
        
        return df
        
    except Exception as e:
        print(f"❌ Excel文件读取失败: {e}")
        return None

if __name__ == "__main__":
    debug_excel_parsing() 