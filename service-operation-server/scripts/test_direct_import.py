#!/usr/bin/env python3
"""
测试直接导入Excel到train_orders表（不创建project_task）
"""

import os
import sys
import asyncio
from tortoise import Tortoise
from tortoise import connections

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(__file__))

from src.core.config import settings
from src.db.models.train_order import TrainOrder
from src.db.models.project_task import ProjectTask

async def test_direct_import():
    """测试直接导入功能"""
    
    # 初始化数据库连接
    await Tortoise.init(
        db_url=settings.db_url,
        modules={'models': ['src.db.models']}
    )
    
    try:
        print("🧪 测试直接导入功能...")
        
        # 1. 查看数据库表结构
        print("\n1. 检查train_orders表的task_id字段...")
        db = connections.get("default")
        result = await db.execute_query("DESCRIBE train_orders")
        
        task_id_field = None
        for field in result[1]:  # result[1]包含实际数据
            if field[0] == 'task_id':  # field[0]是字段名
                task_id_field = {
                    'Field': field[0],
                    'Type': field[1], 
                    'Null': field[2],
                    'Default': field[4]
                }
                break
        
        if task_id_field:
            print(f"✅ task_id字段信息:")
            print(f"   - Type: {task_id_field['Type']}")
            print(f"   - Null: {task_id_field['Null']}")
            print(f"   - Default: {task_id_field['Default']}")
        else:
            print("❌ 未找到task_id字段")
            return
        
        # 2. 测试创建订单（不设置task_id）
        print("\n2. 测试创建火车票订单（task_id=None）...")
        
        test_order_data = {
            'project_id': 1,  # 假设项目ID为1
            'task_id': None,
            'sequence_number': 999,
            'traveler_full_name': '测试乘客-直接导入',
            'mobile_phone': '13800138000',
            'mobile_phone_country_code': '+86',
            'order_status': 'initial'
        }
        
        order = await TrainOrder.create(**test_order_data)
        print(f"✅ 成功创建订单: ID={order.id}, 姓名={order.traveler_full_name}, task_id={order.task_id}")
        
        # 3. 验证订单创建
        print("\n3. 验证订单数据...")
        created_order = await TrainOrder.get(id=order.id)
        print(f"✅ 订单验证成功:")
        print(f"   - ID: {created_order.id}")
        print(f"   - 项目ID: {created_order.project_id}")
        print(f"   - 任务ID: {created_order.task_id}")
        print(f"   - 姓名: {created_order.traveler_full_name}")
        print(f"   - 订单状态: {created_order.order_status}")
        
        # 4. 查询没有task_id的订单
        print("\n4. 查询没有task_id的订单...")
        orders_without_task = await TrainOrder.filter(task_id__isnull=True).count()
        print(f"✅ 找到 {orders_without_task} 个没有task_id的订单")
        
        # 5. 清理测试数据
        print("\n5. 清理测试数据...")
        await created_order.delete()
        print("✅ 测试数据已清理")
        
        print("\n🎉 直接导入功能测试通过！")
        print("📋 总结:")
        print("   - task_id字段已设为可空")
        print("   - 可以创建没有task_id的订单")
        print("   - 不依赖project_tasks表")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(test_direct_import()) 