#!/usr/bin/env python3
"""
更新数据库表结构
"""

import asyncio
from tortoise import Tor<PERSON><PERSON>
from src.core.config import settings

async def update_schema():
    await Tortoise.init(
        db_url=settings.db_url,
        modules={'models': ['src.db.models']}
    )
    
    # 生成数据库模式
    await Tortoise.generate_schemas()
    print('数据库表结构已更新')
    
    await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(update_schema()) 