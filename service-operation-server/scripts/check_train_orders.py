#!/usr/bin/env python3
"""
检查数据库中的火车票订单数据
"""

import asyncio
from tortoise import Tortoise
from src.db.models.train_order import TrainOrder
from src.core.config import settings

async def check_train_orders():
    await Tortoise.init(
        db_url=settings.db_url,
        modules={'models': ['src.db.models']}
    )
    
    # 获取最近的火车票订单
    orders = await TrainOrder.all().order_by('-created_at').limit(10)
    print(f"📋 总共有 {len(orders)} 个最近的火车票订单:")
    
    for order in orders:
        print(f"\n🎫 订单 {order.id} (任务: {order.task_id}):")
        print(f"  序号: {order.sequence_number}")
        print(f"  出行人姓名: '{order.traveler_full_name}'")
        print(f"  出行人姓: '{order.traveler_surname}'")
        print(f"  出行人名: '{order.traveler_given_name}'")
        print(f"  出发站: {order.departure_station}")
        print(f"  到达站: {order.arrival_station}")
        print(f"  车次: {order.train_number}")
        print(f"  创建时间: {order.created_at}")
        
        # 检查是否使用了默认名称
        if order.traveler_full_name and order.traveler_full_name.startswith('乘客'):
            print(f"  ⚠️  使用了默认名称: {order.traveler_full_name}")
    
    # 获取特定任务的订单
    print(f"\n🔍 检查特定任务的订单:")
    task_orders = await TrainOrder.filter(task_id="TASK250601080401").all()
    print(f"任务 TASK250601080401 有 {len(task_orders)} 个订单:")
    
    for order in task_orders:
        print(f"  订单 {order.id}: {order.traveler_full_name}")
    
    await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(check_train_orders()) 