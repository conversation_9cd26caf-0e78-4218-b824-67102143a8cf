#!/usr/bin/env python3
"""
简化版数据库迁移脚本：将ENUM字段改为VARCHAR字段
直接使用MySQL连接执行迁移

使用方法:
python scripts/migrate_enum_simple.py
"""

import mysql.connector
import os
from pathlib import Path


def get_db_config():
    """获取数据库配置"""
    return {
        'host': os.getenv('DATABASE_HOST', 'localhost'),
        'port': int(os.getenv('DATABASE_PORT', 3306)),
        'user': os.getenv('DATABASE_USER', 'root'),
        'password': os.getenv('DATABASE_PASSWORD', ''),
        'database': os.getenv('DATABASE_NAME', 'dttrip'),
        'charset': 'utf8mb4'
    }


def execute_migration():
    """执行数据库迁移"""
    print("🚀 开始执行ENUM到VARCHAR的数据库迁移...")
    
    try:
        # 连接数据库
        config = get_db_config()
        print(f"📊 连接数据库: {config['host']}:{config['port']}/{config['database']}")
        
        conn = mysql.connector.connect(**config)
        cursor = conn.cursor()
        
        print("✅ 数据库连接成功")
        
        # 定义迁移SQL语句
        migration_statements = [
            # 1. 修改permissions表的permission_type字段
            "ALTER TABLE permissions ADD COLUMN permission_type_new VARCHAR(20) DEFAULT 'menu' COMMENT '权限类型(新字段)'",
            
            """UPDATE permissions SET permission_type_new = 
                CASE permission_type
                    WHEN 'menu' THEN 'menu'
                    WHEN 'button' THEN 'button'
                    WHEN 'api' THEN 'api'
                    WHEN 'data' THEN 'data'
                    ELSE 'menu'
                END""",
            
            "ALTER TABLE permissions DROP COLUMN permission_type",
            "ALTER TABLE permissions CHANGE COLUMN permission_type_new permission_type VARCHAR(20) DEFAULT 'menu' COMMENT '权限类型'",
            
            # 2. 修改user_permissions表的permission_type字段
            "ALTER TABLE user_permissions ADD COLUMN permission_type_new VARCHAR(10) DEFAULT 'grant' COMMENT '权限类型(新字段)'",
            
            """UPDATE user_permissions SET permission_type_new = 
                CASE permission_type
                    WHEN 'grant' THEN 'grant'
                    WHEN 'deny' THEN 'deny'
                    ELSE 'grant'
                END""",
            
            "ALTER TABLE user_permissions DROP COLUMN permission_type",
            "ALTER TABLE user_permissions CHANGE COLUMN permission_type_new permission_type VARCHAR(10) DEFAULT 'grant' COMMENT '权限类型'",
            
            # 3. 重建索引
            "DROP INDEX IF EXISTS idx_permissions_type ON permissions",
            "CREATE INDEX idx_permissions_type ON permissions(permission_type)",
            "CREATE INDEX IF NOT EXISTS idx_user_permissions_type ON user_permissions(permission_type)"
        ]
        
        print(f"📝 准备执行 {len(migration_statements)} 条SQL语句")
        
        # 执行每条SQL语句
        for i, statement in enumerate(migration_statements, 1):
            try:
                print(f"⏳ 执行第 {i} 条语句...")
                cursor.execute(statement)
                conn.commit()
                print(f"✅ 第 {i} 条语句执行成功")
            except mysql.connector.Error as e:
                print(f"❌ 第 {i} 条语句执行失败: {e}")
                print(f"SQL: {statement}")
                # 对于某些可能失败的语句（如DROP INDEX IF EXISTS），继续执行
                if "doesn't exist" in str(e).lower() or "duplicate" in str(e).lower():
                    print("⚠️  这是预期的错误，继续执行...")
                    continue
                else:
                    # 回滚并退出
                    conn.rollback()
                    raise e
        
        print("🎉 数据库迁移完成！")
        
        # 验证迁移结果
        verify_migration(cursor)
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库迁移失败: {e}")
        return False


def verify_migration(cursor):
    """验证迁移结果"""
    print("\n🔍 验证迁移结果...")
    
    try:
        # 检查permissions表结构
        cursor.execute("DESCRIBE permissions")
        permissions_fields = cursor.fetchall()
        
        permission_type_field = None
        for field in permissions_fields:
            if field[0] == 'permission_type':  # field[0] 是字段名
                permission_type_field = field
                break
        
        if permission_type_field:
            print(f"✅ permissions.permission_type 字段类型: {permission_type_field[1]}")
            if 'varchar' in permission_type_field[1].lower():
                print("✅ permissions表迁移成功")
            else:
                print("❌ permissions表迁移可能失败")
        
        # 检查user_permissions表结构
        cursor.execute("DESCRIBE user_permissions")
        user_permissions_fields = cursor.fetchall()
        
        permission_type_field = None
        for field in user_permissions_fields:
            if field[0] == 'permission_type':
                permission_type_field = field
                break
        
        if permission_type_field:
            print(f"✅ user_permissions.permission_type 字段类型: {permission_type_field[1]}")
            if 'varchar' in permission_type_field[1].lower():
                print("✅ user_permissions表迁移成功")
            else:
                print("❌ user_permissions表迁移可能失败")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证迁移结果失败: {e}")
        return False


def main():
    """主函数"""
    print("=" * 60)
    print("数据库ENUM字段迁移工具 (简化版)")
    print("=" * 60)
    
    # 显示当前配置
    config = get_db_config()
    print(f"数据库主机: {config['host']}:{config['port']}")
    print(f"数据库名称: {config['database']}")
    print(f"数据库用户: {config['user']}")
    
    # 确认执行
    confirm = input("\n是否继续执行迁移? (y/N): ").strip().lower()
    if confirm != 'y':
        print("❌ 用户取消操作")
        return
    
    # 执行迁移
    success = execute_migration()
    
    if success:
        print("\n🎉 迁移完成！请重启应用程序以使用新的字段类型。")
    else:
        print("\n❌ 迁移失败，请检查错误信息并手动处理。")


if __name__ == "__main__":
    main()
