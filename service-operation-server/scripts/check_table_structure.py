#!/usr/bin/env python3
"""
检查护照表结构的脚本
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from tortoise import Tortoise
from src.core.config import settings
from loguru import logger

async def check_table_structure():
    """检查护照表结构"""
    try:
        # 初始化数据库连接
        await Tortoise.init(
            db_url=settings.db_url,
            modules={"models": ["src.db.models"]}
        )
        
        conn = Tortoise.get_connection('default')
        
        # 检查表结构
        result = await conn.execute_query('DESCRIBE passports')
        logger.info("📋 护照表结构:")
        logger.info(f"{'字段名':<25} {'类型':<20} {'空值':<5} {'键':<5} {'默认值':<15} {'额外'}")
        logger.info("-" * 80)
        
        # result[1] 是行数据列表，每行是字典格式
        rows = result[1] if len(result) > 1 else []
        for i, row in enumerate(rows):
            try:
                if isinstance(row, dict):
                    field_name = row.get('Field', '')
                    field_type = row.get('Type', '')
                    null_allowed = row.get('Null', '')
                    key_type = row.get('Key', '')
                    default_value = row.get('Default') or ""
                    extra = row.get('Extra', '')
                    logger.info(f"{field_name:<25} {field_type:<20} {null_allowed:<5} {key_type:<5} {str(default_value):<15} {extra}")
                else:
                    logger.info(f"行数据格式异常: {row}")
            except Exception as e:
                logger.error(f"处理行数据时出错: {e}, 行数据: {row}")
        
        # 检查索引
        result = await conn.execute_query('SHOW INDEX FROM passports')
        logger.info("\n📋 护照表索引:")
        logger.info(f"{'表名':<15} {'唯一':<5} {'键名':<25} {'序号':<5} {'列名':<20}")
        logger.info("-" * 80)
        
        index_rows = result[1] if len(result) > 1 else []
        for row in index_rows:
            try:
                if isinstance(row, dict):
                    table_name = row.get('Table', '')
                    non_unique = "否" if row.get('Non_unique', 1) == 0 else "是"
                    key_name = row.get('Key_name', '')
                    seq_in_index = row.get('Seq_in_index', '')
                    column_name = row.get('Column_name', '')
                    logger.info(f"{table_name:<15} {non_unique:<5} {key_name:<25} {seq_in_index:<5} {column_name:<20}")
                else:
                    logger.info(f"索引数据格式异常: {row}")
            except Exception as e:
                logger.error(f"处理索引数据时出错: {e}, 行数据: {row}")
        
    except Exception as e:
        logger.error(f"❌ 检查表结构失败: {e}")
        raise
    finally:
        await Tortoise.close_connections()

if __name__ == "__main__":
    logger.info("🔍 开始检查护照表结构...")
    asyncio.run(check_table_structure())
    logger.info("✅ 护照表结构检查完成！") 