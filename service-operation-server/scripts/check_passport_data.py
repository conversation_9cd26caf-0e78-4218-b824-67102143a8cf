#!/usr/bin/env python3
"""
检查护照表数据的脚本
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from tortoise import Tortoise
from src.core.config import settings
from src.db.models import Passport, User
from loguru import logger

async def check_passport_data():
    """检查护照表数据"""
    try:
        # 初始化数据库连接
        await Tortoise.init(
            db_url=settings.db_url,
            modules={"models": ["src.db.models"]}
        )
        
        # 检查护照数据
        passports = await Passport.all().prefetch_related('user')
        logger.info(f"📊 护照记录总数: {len(passports)}")
        
        for passport in passports:
            logger.info(f"护照ID: {passport.id}")
            logger.info(f"用户ID: {passport.user.user_id}")
            logger.info(f"任务ID: {passport.task_id}")
            logger.info(f"护照号码: {passport.passport_number}")
            logger.info(f"姓名: {passport.surname} {passport.given_names}")
            logger.info(f"国籍: {passport.nationality}")
            logger.info(f"处理状态: {passport.processing_status}")
            logger.info("---")
        
        # 测试JSON转换
        if passports:
            first_passport = passports[0]
            recognition_json = first_passport.to_recognition_json()
            logger.info("📋 护照识别数据JSON格式:")
            import json
            logger.info(json.dumps(recognition_json, indent=2, ensure_ascii=False))
        
    except Exception as e:
        logger.error(f"❌ 检查护照数据失败: {e}")
        raise
    finally:
        await Tortoise.close_connections()

if __name__ == "__main__":
    logger.info("🔍 开始检查护照表数据...")
    asyncio.run(check_passport_data())
    logger.info("✅ 护照表数据检查完成！") 