-- 创建系统设置表
-- 需要管理员执行此SQL脚本

CREATE TABLE IF NOT EXISTS `system_settings` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `user_id` VARCHAR(50) NOT NULL COMMENT '用户ID',
    `config_key` VARCHAR(100) NOT NULL COMMENT '配置项key',
    `config_name` VARCHAR(200) NOT NULL COMMENT '配置项名称',
    `config_value` TEXT NOT NULL COMMENT '配置项值（加密存储）',
    `created_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
    `updated_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
    
    -- 确保同一用户的同一配置项唯一
    UNIQUE KEY `uk_user_config` (`user_id`, `config_key`),
    
    -- 添加索引以提高查询性能
    KEY `idx_user_id` (`user_id`),
    KEY `idx_config_key` (`config_key`)
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统设置表，用于存储用户的配置信息（如同程管家账号密码等）'; 