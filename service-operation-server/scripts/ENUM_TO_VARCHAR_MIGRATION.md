# ENUM字段迁移指南

## 背景

生产数据库不支持MySQL的ENUM类型，需要将以下表的ENUM字段改为VARCHAR字段：

1. `permissions` 表的 `permission_type` 字段：从 `ENUM('menu', 'button', 'api', 'data')` 改为 `VARCHAR(20)`
2. `user_permissions` 表的 `permission_type` 字段：从 `ENUM('grant', 'deny')` 改为 `VARCHAR(10)`

## 迁移方案

### 方案1：使用Python脚本自动迁移（推荐）

#### 前置条件
1. 确保已安装 `mysql-connector-python`：
   ```bash
   pip install mysql-connector-python
   ```

2. 设置数据库环境变量：
   ```bash
   export DATABASE_HOST=localhost
   export DATABASE_PORT=3306
   export DATABASE_USER=root
   export DATABASE_PASSWORD=your_password
   export DATABASE_NAME=dttrip
   ```

#### 执行迁移
```bash
cd service-operation-server
python scripts/migrate_enum_simple.py
```

### 方案2：手动执行SQL迁移

直接在数据库中执行以下SQL语句：

```sql
-- 1. 修改permissions表的permission_type字段
ALTER TABLE permissions ADD COLUMN permission_type_new VARCHAR(20) DEFAULT 'menu' COMMENT '权限类型(新字段)';

UPDATE permissions SET permission_type_new = 
    CASE permission_type
        WHEN 'menu' THEN 'menu'
        WHEN 'button' THEN 'button'
        WHEN 'api' THEN 'api'
        WHEN 'data' THEN 'data'
        ELSE 'menu'
    END;

ALTER TABLE permissions DROP COLUMN permission_type;
ALTER TABLE permissions CHANGE COLUMN permission_type_new permission_type VARCHAR(20) DEFAULT 'menu' COMMENT '权限类型';

-- 2. 修改user_permissions表的permission_type字段
ALTER TABLE user_permissions ADD COLUMN permission_type_new VARCHAR(10) DEFAULT 'grant' COMMENT '权限类型(新字段)';

UPDATE user_permissions SET permission_type_new = 
    CASE permission_type
        WHEN 'grant' THEN 'grant'
        WHEN 'deny' THEN 'deny'
        ELSE 'grant'
    END;

ALTER TABLE user_permissions DROP COLUMN permission_type;
ALTER TABLE user_permissions CHANGE COLUMN permission_type_new permission_type VARCHAR(10) DEFAULT 'grant' COMMENT '权限类型';

-- 3. 重建索引
DROP INDEX IF EXISTS idx_permissions_type ON permissions;
CREATE INDEX idx_permissions_type ON permissions(permission_type);
CREATE INDEX idx_user_permissions_type ON user_permissions(permission_type);
```

## 迁移步骤说明

### 对于每个ENUM字段，迁移过程如下：

1. **添加新的VARCHAR字段**：先添加一个新的VARCHAR字段作为临时字段
2. **数据转换**：将原ENUM字段的值复制到新VARCHAR字段
3. **删除原字段**：删除原来的ENUM字段
4. **重命名字段**：将新字段重命名为原字段名
5. **重建索引**：重新创建相关索引

### 为什么不直接ALTER COLUMN？

直接使用 `ALTER TABLE ... MODIFY COLUMN` 从ENUM改为VARCHAR可能会遇到兼容性问题，特别是在不同版本的MySQL中。使用添加新字段、数据迁移、删除旧字段的方式更加安全可靠。

## 验证迁移结果

迁移完成后，可以使用以下SQL验证结果：

```sql
-- 查看permissions表结构
DESCRIBE permissions;

-- 查看user_permissions表结构  
DESCRIBE user_permissions;

-- 检查数据是否正确迁移
SELECT DISTINCT permission_type FROM permissions;
SELECT DISTINCT permission_type FROM user_permissions;
```

## 注意事项

1. **备份数据**：在执行迁移前，请务必备份相关表的数据
2. **停止应用**：建议在迁移期间停止应用程序，避免数据不一致
3. **测试环境**：建议先在测试环境执行迁移，确认无误后再在生产环境执行
4. **权限检查**：确保数据库用户有足够的权限执行ALTER TABLE操作

## 回滚方案

如果需要回滚到ENUM类型（仅在支持ENUM的数据库中），可以执行：

```sql
-- 回滚permissions表
ALTER TABLE permissions ADD COLUMN permission_type_enum ENUM('menu', 'button', 'api', 'data') DEFAULT 'menu';
UPDATE permissions SET permission_type_enum = permission_type;
ALTER TABLE permissions DROP COLUMN permission_type;
ALTER TABLE permissions CHANGE COLUMN permission_type_enum permission_type ENUM('menu', 'button', 'api', 'data') DEFAULT 'menu';

-- 回滚user_permissions表
ALTER TABLE user_permissions ADD COLUMN permission_type_enum ENUM('grant', 'deny') DEFAULT 'grant';
UPDATE user_permissions SET permission_type_enum = permission_type;
ALTER TABLE user_permissions DROP COLUMN permission_type;
ALTER TABLE user_permissions CHANGE COLUMN permission_type_enum permission_type ENUM('grant', 'deny') DEFAULT 'grant';
```

## 相关文件修改

以下文件已经更新以支持VARCHAR类型：

1. `app/models/auth.py` - 模型定义
2. `scripts/database_schema_design.sql` - 数据库设计文档
3. `scripts/create_auth_tables.py` - 创建表脚本
4. `docs/PERMISSION_SYSTEM_IMPLEMENTATION.md` - 系统文档
