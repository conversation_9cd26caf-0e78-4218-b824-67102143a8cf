#!/usr/bin/env python3
"""
创建可能存在列名问题的Excel文件用于测试
"""

import pandas as pd
from datetime import date, time

def create_problematic_excel():
    # 创建模拟有问题的Excel数据（列名可能有空格）
    data = [
        {
            '序号': 1,
            ' 出行人姓名': '王小明',  # 故意在前面加空格
            '出行人姓 ': '王',        # 故意在后面加空格
            ' 出行人名 ': '小明',     # 前后都有空格
            '国籍': '中国',
            '性别': '男',
            '出生日期': '1992-03-15',
            '证件类型': '身份证',
            '证件号码': '110101199203152023',
            '证件有效期至': '2032-03-15',
            '手机号': '13800001111',
            '出行日期': '2025-03-01',
            '出发站名': '北京西',
            '到达站名': '西安北',
            '车次': 'G651',
            '座位类型': '二等座',
            '出发时间': '14:30',
            '到达时间': '19:45',
            ' 成本中心': '研发部',      # 故意添加空格
            '行程提交项': '技术交流',
            '联系人': '李经理',
            '联系人手机号': '13900001111',
            '联系人邮箱': '<EMAIL>',
            '审批参考人': '张总',
            '公司名称': '科技公司',
            '代订人': '小助手',
            '出票短信': '已发送',
            '金额': 515.50,
            '订单号': 'ORDER_TEST001',
            '账单号': 'BILL_TEST001'
        },
        {
            '序号': 2,
            ' 出行人姓名': '赵丽华',
            '出行人姓 ': '赵',
            ' 出行人名 ': '丽华',
            '国籍': '中国',
            '性别': '女',
            '出生日期': '1988-07-20',
            '证件类型': '护照',
            '证件号码': 'EF1234567',
            '证件有效期至': '2027-07-20',
            '手机号': '13700002222',
            '出行日期': '2025-03-02',
            '出发站名': '西安北',
            '到达站名': '成都东',
            '车次': 'G1711',
            '座位类型': '一等座',
            '出发时间': '08:20',
            '到达时间': '11:25',
            ' 成本中心': '市场部',
            '行程提交项': '市场调研',
            '联系人': '王助理',
            '联系人手机号': '13800002222',
            '联系人邮箱': '<EMAIL>',
            '审批参考人': '刘总',
            '公司名称': '科技公司',
            '代订人': '小助手',
            '出票短信': '已发送',
            '金额': 780.00,
            '订单号': 'ORDER_TEST002',
            '账单号': 'BILL_TEST002'
        }
    ]
    
    df = pd.DataFrame(data)
    
    # 保存到Excel文件
    df.to_excel('problematic_train_orders.xlsx', index=False)
    print("📄 已创建测试Excel文件: problematic_train_orders.xlsx")
    print(f"📊 文件包含 {len(df)} 行数据")
    print("🔍 列名情况:")
    for i, col in enumerate(df.columns):
        print(f"  {i+1}. '{col}' (长度: {len(col)})")
        if col.startswith(' ') or col.endswith(' '):
            print(f"      ⚠️  包含前导或尾随空格")

if __name__ == "__main__":
    create_problematic_excel() 