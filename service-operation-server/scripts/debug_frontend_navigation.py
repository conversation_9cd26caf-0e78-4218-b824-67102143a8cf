#!/usr/bin/env python3
"""
前端导航问题诊断脚本
"""

import asyncio
import aiohttp
import json

async def test_frontend_routes():
    """测试前端路由是否正常"""
    print("=== 前端路由诊断 ===")
    
    # 测试前端服务是否运行
    frontend_url = "http://localhost:5173"  # Vite默认端口
    
    async with aiohttp.ClientSession() as session:
        try:
            # 测试主页
            async with session.get(frontend_url) as response:
                print(f"前端主页状态: {response.status}")
                if response.status == 200:
                    print("✅ 前端服务正常运行")
                else:
                    print("❌ 前端服务异常")
            
            # 测试仪表盘页面
            async with session.get(f"{frontend_url}/dashboard") as response:
                print(f"仪表盘页面状态: {response.status}")
            
            # 测试护照识别页面
            async with session.get(f"{frontend_url}/passport-recognition") as response:
                print(f"护照识别页面状态: {response.status}")
                
        except Exception as e:
            print(f"❌ 前端服务连接失败: {e}")
            print("请确保前端服务正在运行: npm run dev")

async def test_backend_api():
    """测试后端API是否正常"""
    print("\n=== 后端API诊断 ===")
    
    backend_url = "http://localhost:8000"
    
    async with aiohttp.ClientSession() as session:
        try:
            # 测试健康检查
            async with session.get(f"{backend_url}/health") as response:
                print(f"后端健康检查状态: {response.status}")
                if response.status == 200:
                    print("✅ 后端服务正常运行")
                else:
                    print("❌ 后端服务异常")
            
            # 测试API文档
            async with session.get(f"{backend_url}/docs") as response:
                print(f"API文档状态: {response.status}")
                
        except Exception as e:
            print(f"❌ 后端服务连接失败: {e}")
            print("请确保后端服务正在运行")

def check_frontend_files():
    """检查前端关键文件"""
    print("\n=== 前端文件检查 ===")
    
    import os
    from pathlib import Path
    
    frontend_dir = Path("../service-operation-frontend")
    
    # 检查关键文件
    key_files = [
        "src/App.tsx",
        "src/pages/DashboardPage.tsx", 
        "src/pages/PassportRecognitionPage.tsx",
        "package.json",
        "vite.config.ts"
    ]
    
    for file_path in key_files:
        full_path = frontend_dir / file_path
        if full_path.exists():
            print(f"✅ {file_path} 存在")
        else:
            print(f"❌ {file_path} 不存在")
    
    # 检查node_modules
    node_modules = frontend_dir / "node_modules"
    if node_modules.exists():
        print("✅ node_modules 存在")
    else:
        print("❌ node_modules 不存在，请运行 npm install")

async def main():
    """主函数"""
    print("🔍 开始前端导航问题诊断...")
    
    # 检查文件
    check_frontend_files()
    
    # 测试服务
    await test_frontend_routes()
    await test_backend_api()
    
    print("\n📋 诊断建议:")
    print("1. 确保前端服务正在运行: cd service-operation-frontend && npm run dev")
    print("2. 确保后端服务正在运行: cd service-operation-server && python -m uvicorn src.main:app --reload")
    print("3. 检查浏览器控制台是否有JavaScript错误")
    print("4. 检查网络请求是否被阻止")
    print("5. 确保认证状态正常")

if __name__ == "__main__":
    asyncio.run(main()) 