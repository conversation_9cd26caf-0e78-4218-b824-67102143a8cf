#!/usr/bin/env python3
"""
数据库迁移脚本：为train_orders表添加手机号国际区号和失败原因字段
"""

import asyncio
import sys
import os

# 添加项目根目录到 Python 路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from tortoise import Tortoise
from src.core.config import settings


async def add_new_fields():
    """为train_orders表添加新字段"""
    try:
        # 初始化数据库连接
        await Tortoise.init(
            db_url=str(settings.db_url),
            modules={'models': ['src.db.models']}
        )
        
        # 获取数据库连接
        db = Tortoise.get_connection("default")
        
        print("🚀 开始为train_orders表添加新字段...")
        
        # 添加手机号国际区号字段
        try:
            await db.execute_query("""
                ALTER TABLE train_orders 
                ADD COLUMN mobile_phone_country_code VARCHAR(10) DEFAULT '+86' 
                COMMENT '手机号国际区号'
            """)
            print("✅ 成功添加 mobile_phone_country_code 字段")
        except Exception as e:
            if "Duplicate column name" in str(e):
                print("⚠️ mobile_phone_country_code 字段已存在，跳过")
            else:
                raise e
        
        # 添加失败原因字段
        try:
            await db.execute_query("""
                ALTER TABLE train_orders 
                ADD COLUMN fail_reason TEXT 
                COMMENT '失败原因'
            """)
            print("✅ 成功添加 fail_reason 字段")
        except Exception as e:
            if "Duplicate column name" in str(e):
                print("⚠️ fail_reason 字段已存在，跳过")
            else:
                raise e
        
        # 验证字段是否添加成功
        result = await db.execute_query("DESCRIBE train_orders")
        columns = [row[0] for row in result[1]] if result[1] else []
        
        if 'mobile_phone_country_code' in columns and 'fail_reason' in columns:
            print("🎉 所有字段添加成功！")
            print(f"📋 当前表结构包含字段: {len(columns)} 个")
            print("📋 新增字段:")
            print("   - mobile_phone_country_code: 手机号国际区号")
            print("   - fail_reason: 失败原因")
        else:
            print("❌ 字段添加可能不完整")
            
        print("\n📊 train_orders表当前字段列表:")
        for i, col in enumerate(columns, 1):
            mark = "🆕" if col in ['mobile_phone_country_code', 'fail_reason'] else "  "
            print(f"{mark} {i:2d}. {col}")
            
    except Exception as e:
        print(f"❌ 迁移失败: {str(e)}")
        return False
    finally:
        await Tortoise.close_connections()
    
    return True


async def main():
    """主函数"""
    print("=" * 60)
    print("🔧 Train Orders 表结构迁移工具")
    print("=" * 60)
    print("本脚本将为 train_orders 表添加以下字段:")
    print("1. mobile_phone_country_code - 手机号国际区号 (默认值: +86)")
    print("2. fail_reason - 失败原因 (用于记录订单处理过程中的各类失败原因)")
    print("=" * 60)
    
    success = await add_new_fields()
    
    if success:
        print("\n🎉 迁移完成！现在可以在代码中使用新字段了。")
        print("\n📝 使用示例:")
        print("# 设置手机号国际区号")
        print("order.mobile_phone_country_code = '+1'  # 美国")
        print("order.mobile_phone_country_code = '+86' # 中国")
        print("order.mobile_phone_country_code = '+81' # 日本")
        print("")
        print("# 记录失败原因")
        print("order.fail_reason = '身份证号码格式错误'")
        print("order.fail_reason = '手机号码无效'")
        print("order.fail_reason = '车次不存在'")
    else:
        print("\n❌ 迁移失败，请检查数据库连接和权限。")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main()) 