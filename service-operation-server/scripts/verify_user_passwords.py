#!/usr/bin/env python3
"""
验证现有用户密码是否正常工作
"""
import asyncio
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.config import settings
from tortoise import Tortoise
from src.api.user_management.password_service import password_service

async def verify_user_passwords():
    """验证现有用户密码"""
    
    print("开始验证现有用户密码...")
    
    # 初始化Tortoise ORM
    await Tortoise.init(
        db_url=settings.db_url,
        modules={'models': ['src.db.models']}
    )
    
    # 获取数据库连接
    connection = Tortoise.get_connection("default")
    
    try:
        # 查询所有有密码的用户
        users_sql = """
        SELECT user_id, username, member_id, password, password_salt
        FROM users 
        WHERE password IS NOT NULL AND password != '' AND status = 1
        LIMIT 5
        """
        
        result = await connection.execute_query(users_sql)
        if isinstance(result, tuple) and len(result) > 1:
            users_data = result[1]
        else:
            users_data = result if isinstance(result, list) else []
        
        if not users_data:
            print("没有找到有密码的用户")
            return
        
        print(f"找到 {len(users_data)} 个用户，开始验证密码...")
        
        # 默认密码
        default_password = "123456Aa"
        
        # 验证每个用户的密码
        success_count = 0
        for user in users_data:
            user_id = user['user_id']
            username = user['username']
            member_id = user['member_id']
            stored_password = user['password']
            salt = user['password_salt']
            
            try:
                # 验证默认密码
                is_valid = password_service.verify_password(default_password, stored_password, salt)
                
                if is_valid:
                    print(f"✓ 用户 {username} ({member_id}) 密码验证成功")
                    success_count += 1
                else:
                    print(f"✗ 用户 {username} ({member_id}) 密码验证失败")
                    
            except Exception as e:
                print(f"✗ 用户 {username} ({member_id}) 密码验证出错: {e}")
        
        print(f"\n验证结果: {success_count}/{len(users_data)} 个用户密码验证成功")
        
        if success_count == len(users_data):
            print("🎉 所有用户密码验证通过！")
        else:
            print("❌ 部分用户密码验证失败，可能需要重新初始化密码")
        
        # 测试密码加密是否使用了正确的密钥
        print(f"\n测试新密码加密...")
        test_password = "NewTestPassword123!"
        encrypted_password, new_salt = password_service.hash_password(test_password)
        is_valid = password_service.verify_password(test_password, encrypted_password, new_salt)
        
        if is_valid:
            print("✓ 新密码加密验证成功")
        else:
            print("✗ 新密码加密验证失败")
        
    except Exception as e:
        print(f"验证用户密码失败: {e}")
        raise
    finally:
        # 关闭Tortoise连接
        await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(verify_user_passwords())
