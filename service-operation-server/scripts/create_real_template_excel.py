#!/usr/bin/env python3
"""
创建符合真实模板格式的Excel文件
"""

import pandas as pd
from datetime import date, time

def create_real_template_excel():
    print("📄 创建符合真实模板格式的Excel文件...")
    
    # 创建包含完整格式的Excel数据
    # 第1行：标题
    # 第2行：空行
    # 第3行：列名
    # 第4行：单位或说明行
    # 第5行开始：数据
    
    # 准备数据：5行数据（标题行、空行、列名行、单位行、数据行1、数据行2）
    full_data = []
    
    # 第1行：标题
    title_row = ['火车票订单导入模板'] + [''] * 29
    full_data.append(title_row)
    
    # 第2行：空行
    empty_row = [''] * 30
    full_data.append(empty_row)
    
    # 第3行：列名（真正的列名行）
    header_row = [
        '序号', '出行人姓名', '出行人姓', '出行人名', '国籍', '性别', '出生日期', '证件类型', '证件号码', 
        '证件有效期至', '手机号', '出行日期', '出发站名', '到达站名', '车次', '座位类型', '出发时间', '到达时间',
        '成本中心', '行程提交项', '联系人', '联系人手机号', '联系人邮箱', '审批参考人', '公司名称', '代订人', 
        '出票短信', '金额', '订单号', '账单号'
    ]
    full_data.append(header_row)
    
    # 第4行：单位或说明行
    unit_row = [
        '自动递增', '必填', '可选', '可选', '可选', '男/女', 'YYYY-MM-DD', '身份证/护照', '必填',
        'YYYY-MM-DD', '11位数字', 'YYYY-MM-DD', '站名', '站名', '车次号', '座位类型', 'HH:MM', 'HH:MM',
        '部门', '出差原因', '联系人', '手机号', '邮箱', '审批人', '公司', '代订人',
        '状态', '金额(元)', '订单号', '账单号'
    ]
    full_data.append(unit_row)
    
    # 第5行：第一条数据
    data_row1 = [
        1, '张三', '张', '三', '中国', '男', '1990-01-01', '身份证', '123456789012345678',
        '2030-01-01', '13800138000', '2025-02-01', '北京南', '上海虹桥', 'G1', '二等座', '08:00', '12:30',
        '技术部', '出差', '王五', '13700137000', '<EMAIL>', '经理A', '测试公司', '助理A',
        '已发送', 553.5, 'ORDER001', 'BILL001'
    ]
    full_data.append(data_row1)
    
    # 第6行：第二条数据
    data_row2 = [
        2, '李四', '李', '四', '中国', '女', '1985-05-15', '护照', 'P12345678',
        '2025-05-15', '13900139000', '2025-02-02', '上海虹桥', '杭州东', 'G7001', '一等座', '09:30', '10:45',
        '市场部', '出差', '赵六', '13600136000', '<EMAIL>', '经理B', '测试公司', '助理B',
        '已发送', 660.0, 'ORDER002', 'BILL002'
    ]
    full_data.append(data_row2)
    
    # 创建DataFrame
    df = pd.DataFrame(full_data)
    
    # 保存到Excel文件
    output_file = 'tests/data/real_template_train_orders.xlsx'
    df.to_excel(output_file, index=False, header=False)
    
    print(f"✅ 已创建真实模板格式Excel文件: {output_file}")
    print("📋 文件结构:")
    print("  第1行: 标题行")
    print("  第2行: 空行") 
    print("  第3行: 列名行（真正的header）")
    print("  第4行: 单位/说明行")
    print("  第5行开始: 数据行")
    
    # 创建一个有问题的模板（列名有空格）
    problematic_data = []
    
    # 第1行：标题
    problematic_data.append(['火车票订单导入模板（包含列名空格问题）'] + [''] * 29)
    
    # 第2行：空行
    problematic_data.append([''] * 30)
    
    # 第3行：列名（故意添加空格）
    problematic_header = [
        '序号', ' 出行人姓名', '出行人姓 ', ' 出行人名 ', '国籍', '性别', '出生日期', '证件类型', '证件号码', 
        '证件有效期至', '手机号', '出行日期', '出发站名', '到达站名', '车次', '座位类型', '出发时间', '到达时间',
        ' 成本中心', '行程提交项', '联系人', '联系人手机号', '联系人邮箱', '审批参考人', '公司名称', '代订人', 
        '出票短信', '金额', '订单号', '账单号'
    ]
    problematic_data.append(problematic_header)
    
    # 第4行：单位或说明行
    problematic_data.append(unit_row)
    
    # 第5-6行：数据
    problematic_data.append([
        1, '王小明', '王', '小明', '中国', '男', '1992-03-15', '身份证', '110101199203152023',
        '2032-03-15', '13800001111', '2025-03-01', '北京西', '西安北', 'G651', '二等座', '14:30', '19:45',
        '研发部', '技术交流', '李经理', '13900001111', '<EMAIL>', '张总', '科技公司', '小助手',
        '已发送', 515.5, 'ORDER_TEST001', 'BILL_TEST001'
    ])
    
    problematic_data.append([
        2, '赵丽华', '赵', '丽华', '中国', '女', '1988-07-20', '护照', 'EF1234567',
        '2027-07-20', '13700002222', '2025-03-02', '西安北', '成都东', 'G1711', '一等座', '08:20', '11:25',
        '市场部', '市场调研', '王助理', '13800002222', '<EMAIL>', '刘总', '科技公司', '小助手',
        '已发送', 780.0, 'ORDER_TEST002', 'BILL_TEST002'
    ])
    
    # 创建问题模板DataFrame
    df_problematic = pd.DataFrame(problematic_data)
    
    # 保存问题模板
    problematic_output = 'tests/data/real_template_problematic.xlsx'
    df_problematic.to_excel(problematic_output, index=False, header=False)
    
    print(f"✅ 已创建有问题的真实模板: {problematic_output}")
    print("⚠️  该文件包含列名空格问题，用于测试列名清理功能")

if __name__ == "__main__":
    create_real_template_excel() 