#!/usr/bin/env python3
"""
检查Excel文件格式和结构
"""

import pandas as pd
import os

def check_excel_format():
    print("📋 检查Excel文件格式...")
    
    # 检查tests/data目录下的Excel文件
    data_dir = os.path.join('tests', 'data')
    excel_files = [f for f in os.listdir(data_dir) if f.endswith('.xlsx')]
    
    for excel_file in excel_files:
        file_path = os.path.join(data_dir, excel_file)
        print(f"\n📄 分析文件: {excel_file}")
        print("=" * 50)
        
        try:
            # 读取前10行，不指定header
            df_raw = pd.read_excel(file_path, header=None, nrows=10)
            print(f"📊 文件共有 {len(df_raw.columns)} 列")
            print(f"📝 前10行数据:")
            
            for i, (index, row) in enumerate(df_raw.iterrows()):
                print(f"\n第 {index + 1} 行:")
                for j, value in enumerate(row):
                    if pd.notna(value) and str(value).strip():
                        print(f"  列{j+1}: '{value}' (类型: {type(value).__name__})")
                
                if i >= 9:  # 只显示前10行
                    break
            
            # 检查不同header位置的可能性
            print(f"\n🔍 尝试不同的header位置:")
            
            for header_row in [0, 1, 2, 3]:
                try:
                    df_test = pd.read_excel(file_path, header=header_row)
                    print(f"  Header在第{header_row+1}行: 列名 = {list(df_test.columns)[:5]}..." if len(df_test.columns) > 5 else f"  Header在第{header_row+1}行: 列名 = {list(df_test.columns)}")
                    print(f"    数据行数: {len(df_test)}")
                except Exception as e:
                    print(f"  Header在第{header_row+1}行: 解析失败 - {e}")
            
        except Exception as e:
            print(f"❌ 读取文件失败: {e}")

if __name__ == "__main__":
    check_excel_format() 