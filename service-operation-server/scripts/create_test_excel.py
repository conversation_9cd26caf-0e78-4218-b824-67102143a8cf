#!/usr/bin/env python3
"""
创建测试Excel文件
"""

import pandas as pd
from datetime import date, time

# 创建测试数据
data = {
    '序号': [1, 2],
    '出行人姓名': ['张三', '李四'],
    '出行人姓': ['张', '李'],
    '出行人名': ['三', '四'],
    '国籍': ['中国', '中国'],
    '性别': ['男', '女'],
    '出生日期': ['1990-01-01', '1985-05-15'],
    '证件类型': ['身份证', '护照'],
    '证件号码': ['123456789012345678', 'P12345678'],
    '证件有效期至': ['2030-01-01', '2025-05-15'],
    '手机号': ['13800138000', '13900139000'],
    '出行日期': ['2025-02-01', '2025-02-02'],
    '出发站名': ['北京南', '上海虹桥'],
    '到达站名': ['上海虹桥', '杭州东'],
    '车次': ['G1', 'G7001'],
    '座位类型': ['二等座', '一等座'],
    '出发时间': ['08:00', '09:30'],
    '到达时间': ['12:30', '10:45'],
    '成本中心': ['技术部', '市场部'],
    '行程提交项': ['出差', '出差'],
    '联系人': ['王五', '赵六'],
    '联系人手机号': ['13700137000', '13600136000'],
    '联系人邮箱': ['<EMAIL>', '<EMAIL>'],
    '审批参考人': ['经理A', '经理B'],
    '公司名称': ['测试公司', '测试公司'],
    '代订人': ['助理A', '助理B'],
    '出票短信': ['已发送', '已发送'],
    '金额': [553.5, 660.0],
    '订单号': ['ORDER001', 'ORDER002'],
    '账单号': ['BILL001', 'BILL002']
}

# 创建DataFrame
df = pd.DataFrame(data)

# 保存为Excel文件
output_file = 'test_train_orders.xlsx'
df.to_excel(output_file, index=False, engine='openpyxl')

print(f"测试Excel文件已创建: {output_file}")
print(f"包含 {len(df)} 行测试数据")
print("可以使用此文件测试Excel上传功能") 