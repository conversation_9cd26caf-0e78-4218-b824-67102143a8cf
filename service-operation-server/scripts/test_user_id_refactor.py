#!/usr/bin/env python3
"""
用户ID重构测试脚本
测试重构后的用户管理功能是否正常工作
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tortoise import Tortoise
from src.api.user_management.service import UserManagementService
from src.api.user_management.schemas import UserCreateRequest, UserUpdateRequest
from src.api.auth.sso_service import get_or_create_user_from_sso


async def init_db():
    """初始化数据库连接"""
    await Tortoise.init(
        db_url="mysql://root:123456@localhost:3306/dttrip",
        modules={"models": ["src.db.models"]}
    )


async def test_user_creation():
    """测试用户创建功能"""
    print("=== 测试用户创建功能 ===")
    
    try:
        # 创建测试用户
        user_data = UserCreateRequest(
            username=f"test_user_{int(asyncio.get_event_loop().time())}",
            member_id="TEST001",
            work_id="W001",
            email="<EMAIL>",
            full_name="测试用户",
            department="测试部门",
            phone="13800138000",
            password="Test123456!",
            role_ids=[10]  # 普通用户角色
        )
        
        created_user = await UserManagementService.create_user(user_data)
        print(f"✓ 用户创建成功: ID={created_user.id}, 用户名={created_user.username}")
        
        return created_user.id
        
    except Exception as e:
        print(f"✗ 用户创建失败: {str(e)}")
        return None


async def test_user_query(user_id: str):
    """测试用户查询功能"""
    print("=== 测试用户查询功能 ===")
    
    try:
        # 根据ID查询用户
        user = await UserManagementService.get_user_by_id(user_id)
        if user:
            print(f"✓ 用户查询成功: ID={user.id}, 用户名={user.username}")
            print(f"  tc_user_id={user.tc_user_id}")
            return True
        else:
            print("✗ 用户查询失败: 用户不存在")
            return False
            
    except Exception as e:
        print(f"✗ 用户查询失败: {str(e)}")
        return False


async def test_user_update(user_id: str):
    """测试用户更新功能"""
    print("=== 测试用户更新功能 ===")
    
    try:
        # 更新用户信息
        update_data = UserUpdateRequest(
            full_name="更新后的测试用户",
            department="更新后的部门"
        )
        
        updated_user = await UserManagementService.update_user(user_id, update_data)
        if updated_user:
            print(f"✓ 用户更新成功: 姓名={updated_user.full_name}, 部门={updated_user.department}")
            return True
        else:
            print("✗ 用户更新失败")
            return False
            
    except Exception as e:
        print(f"✗ 用户更新失败: {str(e)}")
        return False


async def test_user_permissions(user_id: str):
    """测试用户权限查询功能"""
    print("=== 测试用户权限查询功能 ===")
    
    try:
        # 查询用户权限详情
        permission_details = await UserManagementService.get_user_permission_details(user_id)
        if permission_details:
            print(f"✓ 用户权限查询成功: 用户={permission_details.user.username}")
            print(f"  角色权限数量: {len(permission_details.role_permissions)}")
            print(f"  特殊权限数量: {len(permission_details.special_permissions)}")
            return True
        else:
            print("✗ 用户权限查询失败")
            return False
            
    except Exception as e:
        print(f"✗ 用户权限查询失败: {str(e)}")
        return False


async def test_sso_integration():
    """测试SSO集成功能"""
    print("=== 测试SSO集成功能 ===")
    
    try:
        # 模拟SSO用户数据
        sso_user_data = {
            "userId": f"SSO_{int(asyncio.get_event_loop().time())}",
            "username": "SSO测试用户",
            "email": "<EMAIL>",
            "department": "SSO部门",
            "workId": "SSO001"
        }
        
        user_info = await get_or_create_user_from_sso(sso_user_data)
        print(f"✓ SSO用户处理成功: ID={user_info.get('id')}, 用户名={user_info.get('username')}")
        return user_info.get('id')
        
    except Exception as e:
        print(f"✗ SSO用户处理失败: {str(e)}")
        return None


async def test_user_list():
    """测试用户列表查询功能"""
    print("=== 测试用户列表查询功能 ===")
    
    try:
        # 查询用户列表
        user_list = await UserManagementService.get_users(page=1, page_size=10)
        print(f"✓ 用户列表查询成功: 总数={user_list.total}, 当前页数量={len(user_list.items)}")
        
        # 显示前几个用户的信息
        for i, user in enumerate(user_list.items[:3]):
            print(f"  用户{i+1}: ID={user.id}, 用户名={user.username}, tc_user_id={user.tc_user_id}")
        
        return True
        
    except Exception as e:
        print(f"✗ 用户列表查询失败: {str(e)}")
        return False


async def cleanup_test_user(user_id: str):
    """清理测试用户"""
    print("=== 清理测试数据 ===")
    
    try:
        await UserManagementService.delete_user(user_id)
        print(f"✓ 测试用户删除成功: ID={user_id}")
    except Exception as e:
        print(f"✗ 测试用户删除失败: {str(e)}")


async def main():
    """主测试函数"""
    print("开始用户ID重构测试...")
    
    try:
        # 初始化数据库
        await init_db()
        print("✓ 数据库连接成功")
        
        # 测试用户列表查询
        await test_user_list()
        
        # 测试用户创建
        user_id = await test_user_creation()
        if not user_id:
            print("用户创建失败，跳过后续测试")
            return
        
        # 测试用户查询
        await test_user_query(user_id)
        
        # 测试用户更新
        await test_user_update(user_id)
        
        # 测试用户权限查询
        await test_user_permissions(user_id)
        
        # 测试SSO集成
        sso_user_id = await test_sso_integration()
        
        # 清理测试数据
        await cleanup_test_user(user_id)
        if sso_user_id:
            await cleanup_test_user(sso_user_id)
        
        print("\n=== 测试完成 ===")
        print("所有测试已完成，请检查上述结果")
        
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")
    finally:
        # 关闭数据库连接
        await Tortoise.close_connections()


if __name__ == "__main__":
    asyncio.run(main())
