#!/usr/bin/env python3
"""
添加order_status字段到train_orders表的迁移脚本
执行命令: poetry run python add_order_status_to_train_orders.py
"""

import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def add_order_status_column():
    """添加order_status字段到train_orders表"""
    
    # 数据库连接配置
    DATABASE_URL = os.getenv("DATABASE_URL") or "mysql://root:password@127.0.0.1:3306/dttrip"
    
    # 从MySQL连接字符串解析连接参数
    if DATABASE_URL.startswith("mysql://"):
        # 解析 mysql://user:password@host:port/database
        import re
        pattern = r'mysql://([^:]+):([^@]+)@([^:]+):(\d+)/(.+)'
        match = re.match(pattern, DATABASE_URL)
        if not match:
            raise ValueError("Invalid MySQL DATABASE_URL format")
        
        user, password, host, port, database = match.groups()
        
        # 使用pymysql连接MySQL
        import pymysql
        
        try:
            # 连接数据库
            connection = pymysql.connect(
                host=host,
                port=int(port),
                user=user,
                password=password,
                database=database,
                charset='utf8mb4'
            )
            
            print(f"✅ 成功连接到MySQL数据库: {host}:{port}/{database}")
            
            with connection.cursor() as cursor:
                # 检查order_status列是否已存在
                check_column_sql = """
                SELECT COUNT(*) as count
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = %s 
                AND TABLE_NAME = 'train_orders' 
                AND COLUMN_NAME = 'order_status'
                """
                
                cursor.execute(check_column_sql, (database,))
                result = cursor.fetchone()
                
                if result and result[0] > 0:
                    print("⚠️  order_status字段已存在，无需重复添加")
                    return
                
                # 添加order_status字段
                add_column_sql = """
                ALTER TABLE train_orders 
                ADD COLUMN order_status VARCHAR(20) NOT NULL DEFAULT 'initial' 
                COMMENT '订单状态: initial(待预订), submitted(已提交), processing(处理中), completed(已完成), failed(失败)'
                """
                
                print("🔄 正在添加order_status字段...")
                cursor.execute(add_column_sql)
                
                # 为order_status字段添加索引
                add_index_sql = """
                ALTER TABLE train_orders 
                ADD INDEX idx_order_status (order_status)
                """
                
                print("🔄 正在添加order_status索引...")
                cursor.execute(add_index_sql)
                
                # 提交事务
                connection.commit()
                
                print("✅ 成功添加order_status字段和索引到train_orders表")
                
                # 验证字段是否添加成功
                verify_sql = """
                SELECT COLUMN_NAME, DATA_TYPE, COLUMN_DEFAULT, COLUMN_COMMENT
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = %s 
                AND TABLE_NAME = 'train_orders' 
                AND COLUMN_NAME = 'order_status'
                """
                
                cursor.execute(verify_sql, (database,))
                result = cursor.fetchone()
                
                if result:
                    column_name, data_type, default_value, comment = result
                    print(f"📋 字段验证成功:")
                    print(f"   - 字段名: {column_name}")
                    print(f"   - 数据类型: {data_type}")
                    print(f"   - 默认值: {default_value}")
                    print(f"   - 注释: {comment}")
                else:
                    print("❌ 字段验证失败，请检查是否添加成功")
                
        except Exception as e:
            print(f"❌ 数据库操作失败: {e}")
            raise
        finally:
            connection.close()
            print("🔗 数据库连接已关闭")
    
    else:
        raise ValueError("不支持的数据库类型，请使用MySQL")

if __name__ == "__main__":
    print("🚀 开始执行train_orders表order_status字段添加迁移...")
    
    try:
        # 检查必要的依赖
        try:
            import pymysql
        except ImportError:
            print("❌ 缺少pymysql依赖，请安装: poetry add pymysql")
            exit(1)
        
        # 执行迁移
        add_order_status_column()
        
        print("🎉 迁移完成！")
        print("📝 接下来请记得:")
        print("   1. 更新API Schema中的TrainOrder模型")
        print("   2. 更新前端TypeScript接口")
        print("   3. 测试新字段的CRUD操作")
        
    except Exception as e:
        print(f"💥 迁移失败: {e}")
        exit(1) 