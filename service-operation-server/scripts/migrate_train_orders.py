#!/usr/bin/env python3
"""
火车票订单表数据库迁移脚本
执行火车票订单表的创建
"""

import asyncio
import os
import sys
from pathlib import Path

# 添加src目录到Python路径
current_dir = Path(__file__).parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))

from tortoise import Tortoise
from src.db.config import TORTOISE_CONFIG

async def run_migration():
    """执行火车票订单表迁移"""
    
    # 初始化数据库连接
    await Tortoise.init(config=TORTOISE_CONFIG)
    
    # 读取迁移SQL文件
    migration_file = current_dir / "src/db/migrations/models/6_20250102000000_create_train_orders_table.sql"
    
    if not migration_file.exists():
        print(f"❌ 迁移文件不存在: {migration_file}")
        return False
    
    try:
        # 读取SQL内容
        with open(migration_file, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        # 获取数据库连接
        connection = Tortoise.get_connection("default")
        
        # 执行SQL
        print("🚀 开始执行火车票订单表迁移...")
        await connection.execute_script(sql_content)
        print("✅ 火车票订单表创建成功!")
        
        # 验证表是否创建成功
        result = await connection.execute_query("SHOW TABLES LIKE 'train_orders'")
        if result[1]:  # 如果有结果
            print("✅ 表验证成功: train_orders 表已存在")
            
            # 显示表结构
            result = await connection.execute_query("DESCRIBE train_orders")
            print("\n📋 表结构:")
            print("字段名\t\t类型\t\t\t空值\t键\t默认值\t描述")
            print("-" * 80)
            for row in result[1]:
                field, field_type, null, key, default, extra = row
                print(f"{field:<20} {field_type:<20} {null:<8} {key:<8} {str(default):<10} {extra}")
        else:
            print("❌ 表验证失败: train_orders 表不存在")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ 迁移执行失败: {e}")
        return False
    
    finally:
        # 关闭数据库连接
        await Tortoise.close_connections()

async def main():
    """主函数"""
    print("=" * 60)
    print("🔄 火车票订单表数据库迁移")
    print("=" * 60)
    
    success = await run_migration()
    
    if success:
        print("\n🎉 迁移完成!")
        print("\n📚 火车票订单表字段说明:")
        print("• project_id: 项目ID")
        print("• task_id: 任务ID")
        print("• sequence_number: 序号")
        print("• traveler_full_name: 出行人姓名")
        print("• traveler_surname: 出行人姓")
        print("• traveler_given_name: 出行人名")
        print("• nationality: 国籍")
        print("• gender: 性别")
        print("• birth_date: 出生日期")
        print("• id_type: 证件类型")
        print("• id_number: 证件号码")
        print("• id_expiry_date: 证件有效期至")
        print("• mobile_phone: 手机号")
        print("• travel_date: 出行日期")
        print("• departure_station: 出发站名")
        print("• arrival_station: 到达站名")
        print("• train_number: 车次")
        print("• seat_type: 座位类型")
        print("• departure_time: 出发时间")
        print("• arrival_time: 到达时间")
        print("• cost_center: 成本中心")
        print("• trip_submission_item: 行程提交项")
        print("• contact_person: 联系人")
        print("• contact_phone: 联系人手机号")
        print("• contact_email: 联系人邮箱")
        print("• approval_reference: 审批参考人")
        print("• company_name: 公司名称")
        print("• booking_agent: 代订人")
        print("• ticket_sms: 出票短信")
        print("• amount: 金额")
        print("• order_number: 订单号")
        print("• bill_number: 账单号")
        print("• is_deleted: 软删除标志")
        print("• created_at: 创建时间")
        print("• updated_at: 更新时间")
    else:
        print("\n❌ 迁移失败!")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main()) 