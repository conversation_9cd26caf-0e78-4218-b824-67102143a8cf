#!/bin/bash

# DTTrip Backend Linux Deployment Script

set -e

PROJECT_NAME="dttrip-backend"

# 颜色
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 帮助信息
show_help() {
    cat << EOF
DTTrip Backend Linux Deployment Script

用法: $0 [命令]

命令:
    build       构建Linux镜像
    start       启动服务
    stop        停止服务
    restart     重启服务
    logs        查看日志
    status      查看状态
    clean       清理镜像
    help        显示帮助信息

示例:
    $0 build    # 构建镜像
    $0 start    # 启动服务
    $0 logs     # 查看日志
EOF
}

# 构建Linux镜像
build_image() {
    log_info "构建Linux amd64镜像..."
    docker build --platform linux/amd64 -t "${PROJECT_NAME}:latest" .
    log_success "镜像构建完成: ${PROJECT_NAME}:latest"
}

# 启动服务
start_service() {
    log_info "启动${PROJECT_NAME}服务..."
    docker-compose -f docker-compose.linux.yml up -d
    log_success "服务已启动"
}

# 停止服务
stop_service() {
    log_info "停止${PROJECT_NAME}服务..."
    docker-compose -f docker-compose.linux.yml down
    log_success "服务已停止"
}

# 重启服务
restart_service() {
    log_info "重启${PROJECT_NAME}服务..."
    stop_service
    start_service
}

# 查看日志
show_logs() {
    log_info "显示服务日志..."
    docker-compose -f docker-compose.linux.yml logs -f
}

# 查看状态
show_status() {
    log_info "服务状态:"
    docker-compose -f docker-compose.linux.yml ps
}

# 清理镜像
clean_images() {
    log_info "清理Docker镜像..."
    docker-compose -f docker-compose.linux.yml down
    docker rmi -f "${PROJECT_NAME}:latest" 2>/dev/null || true
    docker system prune -f
    log_success "清理完成"
}

# 主程序
case "${1:-help}" in
    build)
        build_image
        ;;
    start)
        start_service
        ;;
    stop)
        stop_service
        ;;
    restart)
        restart_service
        ;;
    logs)
        show_logs
        ;;
    status)
        show_status
        ;;
    clean)
        clean_images
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        log_error "未知命令: $1"
        show_help
        exit 1
        ;;
esac 