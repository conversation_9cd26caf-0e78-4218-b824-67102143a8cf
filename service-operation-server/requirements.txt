# ===== Core Web Framework =====
fastapi==0.115.12
uvicorn==0.34.2
gunicorn==23.0.0
starlette==0.46.2
python-multipart==0.0.20
httptools==0.6.4

# ===== Data Validation =====
pydantic==2.11.5
pydantic-settings==2.9.1
pydantic_core==2.33.2

# ===== Database =====
tortoise-orm==0.23.0
aerich==0.8.2
aiomysql==0.2.0
PyMySQL==1.1.1
aiosqlite==0.20.0
pypika-tortoise==0.3.2

# ===== Authentication & Security =====
python-jose==3.5.0
passlib==1.7.4
bcrypt==4.0.1
cryptography==44.0.3
PyJWT==2.9.0

# ===== Redis Cache =====
redis==5.3.0
hiredis==3.2.1

# ===== MongoDB =====
pymongo==4.13.0

# ===== Logging =====
loguru==0.7.3

# ===== CLI Tools =====
typer==0.9.4
rich==13.9.4

# ===== System Utilities =====
psutil==7.0.0

# ===== File Processing =====
rarfile==4.2
pillow==11.3.0
PyMuPDF==1.26.3

# ===== HTTP Client =====
httpx==0.27.2
requests==2.32.4

# ===== Data Processing =====
pandas==2.2.3
openpyxl==3.1.5
numpy==2.2.6

# ===== Message Queue =====
kafka-python==2.2.13
python-snappy==0.7.3

# ===== Cloud Storage =====
boto3==1.38.42
botocore==1.38.42
s3transfer==0.13.0

# ===== Core Dependencies =====
yarl==1.20.0
ujson==5.10.0
anyio==4.9.0
sniffio==1.3.1
h11==0.16.0
httpcore==1.0.9
idna==3.10
certifi==2025.4.26
urllib3==2.5.0
multidict==6.4.4
propcache==0.3.1
typing_extensions==4.13.2
annotated-types==0.7.0
packaging==25.0

# ===== Date/Time =====
python-dateutil==2.9.0.post0
pytz==2025.2
tzdata==2025.2
iso8601==2.1.0

# ===== Crypto & Encoding =====
cffi==1.17.1
pycparser==2.22
ecdsa==0.19.1
pyasn1==0.6.1
rsa==4.9.1
six==1.17.0

# ===== File Monitoring =====
watchfiles==1.0.5

# ===== WebSocket Support =====
websockets==15.0.1

# ===== Event Loop =====
uvloop==0.21.0

# ===== Excel Processing =====
et_xmlfile==2.0.0

# ===== Database Migration =====
asyncclick==8.1.8.0
click==8.1.8
dictdiffer==0.9.0

# ===== DNS =====
dnspython==2.7.0

# ===== Compression =====
cramjam==2.10.0

# ===== AWS SDK =====
jmespath==1.0.1

# ===== Environment =====
python-dotenv==1.1.0

# ===== YAML Support =====
PyYAML==6.0.2

# ===== Markdown =====
markdown-it-py==3.0.0
mdurl==0.1.2
Pygments==2.19.1

# ===== Type Checking =====
typing-inspection==0.4.1

# ===== Character Encoding =====
charset-normalizer==3.4.2
