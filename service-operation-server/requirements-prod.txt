# ===== Production Requirements for DTTrip Service Operation Server =====
# This file contains only the essential dependencies needed for production deployment

# ===== Core Web Framework =====
fastapi==0.115.12
uvicorn[standard]==0.34.2
gunicorn==23.0.0
python-multipart==0.0.20

# ===== Data Validation =====
pydantic==2.11.5
pydantic-settings==2.9.1

# ===== Database =====
tortoise-orm==0.23.0
aerich==0.8.2
aiomysql==0.2.0
PyMySQL==1.1.1

# ===== Authentication & Security =====
python-jose==3.5.0
passlib[bcrypt]==1.7.4
bcrypt==4.0.1
cryptography==44.0.3

# ===== Redis Cache =====
redis[hiredis]==5.3.0

# ===== MongoDB =====
pymongo==4.13.0

# ===== Logging =====
loguru==0.7.3

# ===== CLI Tools =====
typer==0.9.4
rich==13.9.4

# ===== System Utilities =====
psutil==7.0.0

# ===== File Processing =====
rarfile==4.2
pillow==11.3.0
PyMuPDF==1.26.3

# ===== HTTP Client =====
httpx==0.27.2
requests==2.32.4

# ===== Data Processing =====
pandas==2.2.3
openpyxl==3.1.5

# ===== Message Queue =====
kafka-python==2.2.13
python-snappy==0.7.3

# ===== Cloud Storage =====
boto3==1.38.42

# ===== Utilities =====
yarl==1.20.0
ujson==5.10.0
python-dotenv==1.1.0
