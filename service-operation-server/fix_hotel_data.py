#!/usr/bin/env python3\n\"\"\"\n修复酒店订单数据中的三个字段显示问题\n将 include_breakfast、is_half_day_room、is_group_booking 字段的值：\n- \"1\" -> \"是\"\n- \"0\" 或 其他值 -> \"否\"\n\"\"\"\n\nimport asyncio\nfrom tortoise import Tortoise\nfrom src.db.models.hotel_order import HotelOrder\n\nasync def fix_hotel_data():\n    \"\"\"修复酒店订单数据中的三个字段\"\"\"\n    \n    try:\n        # 初始化数据库连接\n        await Tortoise.init(\n            db_url=\"mysql://tcdbr:t&$@hfGdJ38%f@*************:3306/dttrip\",\n            modules={\"models\": [\"src.db.models\"]}\n        )\n        \n        print(\"开始修复酒店订单数据...\")\n        \n        # 查询所有酒店订单\n        orders = await HotelOrder.filter(is_deleted=False).all()\n        \n        print(f\"找到 {len(orders)} 条酒店订单数据\")\n        \n        fixed_count = 0\n        \n        for order in orders:\n            need_update = False\n            old_values = {\n                'include_breakfast': order.include_breakfast,\n                'is_half_day_room': order.is_half_day_room,\n                'is_group_booking': order.is_group_booking\n            }\n            \n            # 修复 include_breakfast\n            if order.include_breakfast == \"1\":\n                order.include_breakfast = \"是\"\n                need_update = True\n            elif order.include_breakfast != \"是\" and order.include_breakfast != \"否\":\n                order.include_breakfast = \"否\"\n                need_update = True\n                \n            # 修复 is_half_day_room  \n            if order.is_half_day_room == \"1\":\n                order.is_half_day_room = \"是\"\n                need_update = True\n            elif order.is_half_day_room != \"是\" and order.is_half_day_room != \"否\":\n                order.is_half_day_room = \"否\"\n                need_update = True\n                \n            # 修复 is_group_booking\n            if order.is_group_booking == \"1\":\n                order.is_group_booking = \"是\"\n                need_update = True\n            elif order.is_group_booking != \"是\" and order.is_group_booking != \"否\":\n                order.is_group_booking = \"否\"\n                need_update = True\n            \n            if need_update:\n                await order.save(update_fields=['include_breakfast', 'is_half_day_room', 'is_group_booking'])\n                fixed_count += 1\n                \n                print(f\"订单 {order.id} ({order.guest_full_name}):\")\n                if old_values['include_breakfast'] != order.include_breakfast:\n                    print(f\"  含早餐: '{old_values['include_breakfast']}' -> '{order.include_breakfast}'\")\n                if old_values['is_half_day_room'] != order.is_half_day_room:\n                    print(f\"  半日房: '{old_values['is_half_day_room']}' -> '{order.is_half_day_room}'\")\n                if old_values['is_group_booking'] != order.is_group_booking:\n                    print(f\"  团体预订: '{old_values['is_group_booking']}' -> '{order.is_group_booking}'\")\n                print()\n        \n        print(f\"修复完成！共修复了 {fixed_count} 条订单数据\")\n        \n        # 验证修复结果\n        print(\"\\n验证修复结果...\")\n        test_orders = await HotelOrder.filter(project_id=51, is_deleted=False).limit(5)\n        \n        for order in test_orders:\n            print(f\"订单 {order.id} ({order.guest_full_name}):\")\n            print(f\"  含早餐: '{order.include_breakfast}'\")\n            print(f\"  半日房: '{order.is_half_day_room}'\")\n            print(f\"  团体预订: '{order.is_group_booking}'\")\n            print()\n        \n    except Exception as e:\n        print(f\"错误: {e}\")\n        import traceback\n        traceback.print_exc()\n    finally:\n        await Tortoise.close_connections()\n\nif __name__ == \"__main__\":\n    asyncio.run(fix_hotel_data()) 