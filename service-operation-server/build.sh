#!/bin/bash

# DTTrip Backend Docker Build Script (简化版)

set -e

PROJECT_NAME="dttrip-backend"
VERSION="${VERSION:-latest}"

# 颜色
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 帮助信息
show_help() {
    cat << EOF
DTTrip Backend Docker Build Script

用法: $0 [命令]

命令:
    prod        构建生产镜像
    dev         构建开发镜像并运行
    build       构建生产镜像 (默认)
    test        运行测试
    clean       清理Docker镜像
    logs        查看容器日志
    stop        停止运行中的容器
    help        显示帮助信息

示例:
    $0 prod     # 构建生产镜像
    $0 dev      # 启动开发环境
    $0 test     # 运行测试
    $0 clean    # 清理镜像
    $0 logs     # 查看日志
EOF
}

# 检查Docker是否可用
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装或不在PATH中"
        exit 1
    fi
}

# 构建生产镜像
build_prod() {
    log_info "构建生产镜像..."
    docker build -t "${PROJECT_NAME}:${VERSION}" \
        --platform linux/amd64 \
        --build-arg INSTALL_DEV=false \
        .
    log_success "生产镜像构建完成: ${PROJECT_NAME}:${VERSION}"
}

# 构建并运行开发环境
build_dev() {
    log_info "构建开发镜像..."
    docker build -t "${PROJECT_NAME}:dev" \
        --build-arg INSTALL_DEV=true \
        .
    
    log_info "启动开发容器..."
    docker run -it --rm \
        -p 8000:8000 \
        -v "$(pwd):/app" \
        -v "$(pwd)/logs:/app/logs" \
        -e RELOAD=true \
        -e LOG_LEVEL=DEBUG \
        -e ENVIRONMENT=development \
        --name "${PROJECT_NAME}-dev" \
        "${PROJECT_NAME}:dev"
}

# 运行测试
run_tests() {
    log_info "构建测试镜像..."
    docker build -t "${PROJECT_NAME}:test" \
        --build-arg INSTALL_DEV=true \
        .
    
    log_info "运行测试..."
    docker run --rm \
        -v "$(pwd)/test-reports:/app/test-reports" \
        -e ENVIRONMENT=testing \
        -e LOG_LEVEL=WARNING \
        --name "${PROJECT_NAME}-test" \
        "${PROJECT_NAME}:test" \
        poetry run python -m pytest tests/ -v --cov=src --cov-report=html --cov-report=term-missing
}

# 停止容器
stop_containers() {
    log_info "停止运行中的容器..."
    docker ps --filter "name=${PROJECT_NAME}" --format "{{.ID}}" | xargs -r docker stop
    log_success "容器已停止"
}

# 查看日志
show_logs() {
    log_info "显示容器日志..."
    CONTAINER_ID=$(docker ps --filter "name=${PROJECT_NAME}" --format "{{.ID}}" | head -1)
    if [ -n "$CONTAINER_ID" ]; then
        docker logs -f "$CONTAINER_ID"
    else
        log_warning "没有找到运行中的${PROJECT_NAME}容器"
    fi
}

# 清理镜像
clean_images() {
    log_info "清理Docker镜像..."
    
    # 停止容器
    docker ps -a --filter "name=${PROJECT_NAME}" --format "{{.ID}}" | xargs -r docker rm -f
    
    # 删除镜像
    docker images "${PROJECT_NAME}" --format "{{.ID}}" | xargs -r docker rmi -f
    
    # 清理构建缓存
    docker builder prune -f
    
    log_success "清理完成"
}

# 检查Docker
check_docker

# 主程序
case "${1:-build}" in
    prod|build)
        build_prod
        ;;
    dev)
        build_dev
        ;;
    test)
        run_tests
        ;;
    stop)
        stop_containers
        ;;
    logs)
        show_logs
        ;;
    clean)
        clean_images
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        log_error "未知命令: $1"
        show_help
        exit 1
        ;;
esac 