#!/usr/bin/env python3

import asyncio
from tortoise import Tortoise
from src.db.models.hotel_order import HotelOrder

async def create_test_data():
    """创建测试酒店订单数据"""
    
    try:
        # 初始化数据库连接
        await Tortoise.init(
            db_url="mysql://tcdbr:t&$@hfGdJ38%f@*************:3306/dttrip",
            modules={"models": ["src.db.models"]}
        )
        
        # 创建测试订单
        test_order = await HotelOrder.create(
            project_id=51,
            guest_full_name="测试用户-团房展示",
            guest_id_type="身份证",
            guest_id_number="123456789012345678",
            guest_mobile_phone="13800138000",
            check_in_time="2024-01-20 14:00",
            check_out_time="2024-01-21 12:00",
            hotel_name="测试酒店-团房",
            room_type="标准间",
            room_count=2,
            cost_center="研发部",
            contact_person="联系人",
            contact_mobile_phone="13900139000",
            approver="审批人",
            company_name="测试公司",
            booking_agent="代订人",
            amount=500.00,
            # 重点：这三个字段设置为"是"/"否"字符串
            include_breakfast="是",
            is_half_day_room="否", 
            is_group_booking="是",
            group_booking_name="测试团体预订"
        )
        
        print(f"✅ 成功创建测试订单，ID: {test_order.id}")
        print(f"入住人姓名: {test_order.guest_full_name}")
        print(f"含早餐: '{test_order.include_breakfast}'")
        print(f"半日房: '{test_order.is_half_day_room}'")
        print(f"团体预订: '{test_order.is_group_booking}'")
        print(f"团体预订名称: '{test_order.group_booking_name}'")
        
    except Exception as e:
        print(f"❌ 创建测试数据时出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(create_test_data()) 