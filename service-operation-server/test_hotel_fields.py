#!/usr/bin/env python3
"""
测试酒店订单中三个字段的存储和返回：
include_breakfast, is_half_day_room, is_group_booking
"""

import asyncio
from tortoise import Tortoise
from src.db.models.hotel_order import HotelOrder

async def test_hotel_fields():
    # 初始化数据库连接
    await Tortoise.init(
        db_url="mysql://tcdbr:t&$@hfGdJ38%f@*************:3306/dttrip",
        modules={"models": ["src.db.models"]}
    )
    
    try:
        # 创建测试订单
        test_order = await HotelOrder.create(
            project_id=33,
            guest_full_name="测试用户",
            guest_id_type="身份证", 
            guest_id_number="123456789012345678",
            guest_mobile_phone="13800138000",
            check_in_time="2024-01-15 14:00",
            check_out_time="2024-01-16 12:00",
            hotel_name="测试酒店",
            room_type="标准间",
            cost_center="测试成本中心",
            contact_person="联系人",
            contact_mobile_phone="13800138001",
            approver="审批人",
            # 测试三个字段
            include_breakfast="是",
            is_half_day_room="否", 
            is_group_booking="是",
            amount=500.00
        )
        
        print(f"创建测试订单成功，ID: {test_order.id}")
        print(f"含早餐: {test_order.include_breakfast}")
        print(f"半日房: {test_order.is_half_day_room}")
        print(f"团体预订: {test_order.is_group_booking}")
        
        # 查询订单验证
        found_order = await HotelOrder.get(id=test_order.id)
        print(f"\n查询验证:")
        print(f"含早餐: {found_order.include_breakfast} (类型: {type(found_order.include_breakfast)})")
        print(f"半日房: {found_order.is_half_day_room} (类型: {type(found_order.is_half_day_room)})")
        print(f"团体预订: {found_order.is_group_booking} (类型: {type(found_order.is_group_booking)})")
        
        # 测试更新
        await found_order.update_from_dict({
            'include_breakfast': '否',
            'is_half_day_room': '是',
            'is_group_booking': '否'
        })
        await found_order.save()
        
        # 再次查询验证更新
        updated_order = await HotelOrder.get(id=test_order.id)
        print(f"\n更新后验证:")
        print(f"含早餐: {updated_order.include_breakfast}")
        print(f"半日房: {updated_order.is_half_day_room}")
        print(f"团体预订: {updated_order.is_group_booking}")
        
        # 清理测试数据
        await test_order.delete()
        print(f"\n测试数据已清理")
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(test_hotel_fields()) 