# Dify护照识别服务配置说明

## 环境变量配置

在 `.env` 文件中添加以下配置：

```env
# Dify API 配置
DIFY_SERVER_URL=https://difyapi.17usoft.com/v1
PASSPORT_REG_API_KEY=your-dify-api-key-here
```

## 功能说明

### 1. 自动识别流程

- 用户上传护照图片后，系统会自动启动识别任务
- 识别任务异步进行，不会阻塞用户操作
- 支持批量上传和批量识别

### 2. API 端点

#### 上传文件（自动启动识别）
```
POST /api/passport/upload
```

#### 手动启动识别任务
```
POST /api/passport/task/{task_id}/recognize
```

#### 查询任务状态
```
GET /api/passport/task/{task_id}/status
```

#### 停止识别任务
```
POST /api/passport/task/{task_id}/stop
```

### 3. 识别流程

1. **文件上传到Dify**：将本地图片上传到Dify平台获得upload_file_id
2. **调用识别API**：使用Dify的chat-messages接口进行护照识别
3. **解析结果**：解析Dify返回的识别结果
4. **更新数据库**：将识别结果保存到数据库

### 4. 支持的字段

- 护照号（passport_number）
- 姓氏（surname）
- 名字（given_names）
- 国籍（nationality）
- 出生日期（date_of_birth）
- 性别（sex）
- 签发日期（date_of_issue）
- 有效期（date_of_expiry）
- 签发国（country_of_issue）
- 出生地（place_of_birth）
- 签发机构（authority）
- MRZ码（mrz_line1, mrz_line2）
- 签名存在（signature_present）

### 5. 状态管理

- `pending`: 待处理
- `processing`: 处理中
- `completed`: 已完成
- `failed`: 失败

### 6. 错误处理

- 网络超时自动重试
- 识别失败记录错误信息
- 任务可以手动停止和重新启动

## 部署注意事项

1. 确保Dify API密钥正确配置
2. 检查网络连接到Dify服务器
3. 监控任务处理器状态
4. 定期检查错误日志 