# S3 OSS上传功能实现说明

## 概述

本功能将护照图片上传集成了S3兼容的OSS云存储服务，实现了图片的云端存储和管理。

## 功能特点

### 1. 智能上传策略
- **双重保障**: 先保存到本地，再上传到S3云存储
- **容错机制**: S3上传失败时自动回退到本地存储
- **优先云端**: 优先使用S3 URL，确保数据的云端可访问性

### 2. 高效文件处理
- **智能分段**: 文件大于50MB自动使用分段上传
- **并发上传**: 支持多文件并发上传，提升效率
- **文件去重**: 使用UUID生成唯一文件名，避免文件名冲突

### 3. 组织化存储
- **时间分区**: 按`YYYY/MM/DD`格式创建文件夹结构
- **前缀管理**: 支持自定义键前缀，便于文件分类
- **类型识别**: 自动识别文件类型，设置正确的Content-Type

## 技术架构

### 核心模块

#### 1. S3Service (`src/services/s3_service.py`)
```python
class S3Service:
    - upload_file(file_path, folder)          # 上传本地文件
    - upload_file_data(data, filename, folder) # 上传文件数据
    - delete_file(file_url)                   # 删除文件
    - test_connection()                       # 测试连接
```

#### 2. 护照上传端点优化 (`src/api/passport/endpoints.py`)
```python
async def _process_and_upload_file(file_path, filename, task_id, user):
    # 1. 生成本地URL作为备用
    # 2. 尝试上传到S3
    # 3. 选择最终URL（优先S3）
    # 4. 创建护照记录
```

### 配置管理

#### 环境变量配置
```env
# S3 OSS设置
S3_ENDPOINT_URL=http://oss.qa.17usoft.com
S3_REGION_NAME=us-east-1
S3_AWS_ACCESS_KEY_ID=FhhO34TTwubHsi5AlYAQ
S3_AWS_SECRET_ACCESS_KEY=AbwMPCvDDWE66sb4dcMEDrPDmoaui1QRv54IKYfh
S3_BUCKET_NAME=soap-files
S3_KEY_PREFIX=tmc.ai.soap.server
S3_USE_SSL=False
S3_VERIFY=False
```

#### 多环境支持
- `.local.env` - 本地开发环境
- `.qa.env` - QA测试环境  
- `.prod.env` - 生产环境

## API接口

### 1. 护照文件上传
```http
POST /passport/upload
Content-Type: multipart/form-data

# 自动处理：本地保存 -> S3上传 -> 数据库记录
```

### 2. S3连接测试
```http
GET /passport/s3/test
Authorization: Bearer <token>

# 响应示例
{
  "success": true,
  "message": "S3连接成功，bucket: soap-files",
  "data": {
    "s3_available": true,
    "bucket": "soap-files",
    "key_prefix": "tmc.ai.soap.server"
  }
}
```

## 文件存储结构

### S3存储路径
```
soap-files/
└── tmc.ai.soap.server/
    └── passport/
        └── 2025/
            └── 01/
                └── 14/
                    ├── a1b2c3d4e5f6...jpg
                    ├── f6e5d4c3b2a1...png
                    └── ...
```

### URL格式
```
https://oss.qa.17usoft.com/soap-files/tmc.ai.soap.server/passport/2025/01/14/a1b2c3d4e5f6...jpg
```

## 部署步骤

### 1. 安装依赖
```bash
# 添加boto3依赖
poetry add boto3

# 或更新现有依赖
poetry install
```

### 2. 配置环境变量
在对应环境的`.env`文件中添加S3配置：
```env
S3_ENDPOINT_URL=http://oss.qa.17usoft.com
S3_AWS_ACCESS_KEY_ID=FhhO34TTwubHsi5AlYAQ
S3_AWS_SECRET_ACCESS_KEY=AbwMPCvDDWE66sb4dcMEDrPDmoaui1QRv54IKYfh
S3_BUCKET_NAME=soap-files
S3_KEY_PREFIX=tmc.ai.soap.server
```

### 3. 测试S3连接
```bash
# 使用测试脚本
python simple_s3_test.py

# 或通过API
curl -H "Authorization: Bearer <token>" \
     http://localhost:8000/passport/s3/test
```

### 4. 验证上传功能
1. 访问护照识别页面
2. 上传测试图片
3. 检查日志中的S3上传信息
4. 验证数据库中的URL字段

## 监控和日志

### 关键日志信息
```
文件处理完成: test.jpg
  - 本地路径: /path/to/uploads/test.jpg
  - 本地URL: http://localhost:8000/uploads/test.jpg
  - S3 URL: https://oss.qa.17usoft.com/soap-files/...
  - 最终URL: https://oss.qa.17usoft.com/soap-files/...
```

### 错误处理
- S3配置不完整：自动禁用S3功能，使用本地存储
- S3上传失败：记录错误日志，回退到本地存储
- 网络问题：提供重试机制和详细错误信息

## 故障排除

### 常见问题

1. **S3连接失败**
   - 检查网络连接和防火墙设置
   - 验证访问凭证是否正确
   - 确认endpoint URL是否可访问

2. **权限问题**
   - 确认访问密钥有bucket的读写权限
   - 检查bucket策略设置

3. **上传失败**
   - 查看详细错误日志
   - 检查文件大小限制
   - 验证文件格式是否支持

### 调试命令
```bash
# 测试S3连接
poetry run python simple_s3_test.py

# 查看应用日志
tail -f soa.log | grep S3

# 检查环境变量
env | grep S3_
```

## 性能优化

### 上传性能
- 大文件自动分段上传，提升上传速度
- 支持并发上传多个文件
- 使用合适的分段大小（25MB）

### 存储优化
- 按时间分区存储，便于管理和清理
- 使用UUID避免文件名冲突
- 支持文件类型自动识别

## 安全考虑

### 访问控制
- 使用IAM访问密钥进行身份验证
- 支持SSL/TLS加密传输
- 可配置证书验证

### 数据保护
- 双重存储确保数据安全
- 详细的操作日志记录
- 支持文件删除和清理

## 扩展性

### 未来扩展
- 支持多个S3存储桶
- 添加文件压缩功能
- 实现文件生命周期管理
- 支持CDN加速访问

## 依赖管理

### 完整依赖列表

项目的`requirements.txt`已更新包含所有必要依赖（共79个包）：

**S3相关核心依赖：**
- `boto3==1.38.42` - AWS SDK
- `botocore==1.38.42` - AWS核心库
- `jmespath==1.0.1` - JSON查询语言
- `s3transfer==0.13.0` - S3传输库

**应用框架依赖：**
- `fastapi==0.115.12` - Web框架
- `uvicorn==0.34.2` - ASGI服务器
- `pydantic==2.11.5` - 数据验证
- `pydantic-settings==2.9.1` - 配置管理
- `loguru==0.7.3` - 日志库
- `yarl==1.20.0` - URL处理

### 使用国内镜像源安装

```bash
# 方法1：使用清华大学镜像源安装所有依赖
pip3 install -i https://pypi.tuna.tsinghua.edu.cn/simple/ \
  -r requirements.txt \
  --break-system-packages

# 方法2：只安装S3相关依赖
pip3 install -i https://pypi.tuna.tsinghua.edu.cn/simple/ \
  boto3==1.38.42 botocore==1.38.42 jmespath==1.0.1 s3transfer==0.13.0 \
  --break-system-packages

# 方法3：使用其他国内镜像源
pip3 install -i https://mirrors.aliyun.com/pypi/simple/ -r requirements.txt
```

### 验证安装

```bash
# 验证S3依赖
python3 -c "import boto3; print('boto3版本:', boto3.__version__)"

# 验证应用启动
python3 -c "from src.app import get_app; print('应用导入成功')"

# 测试S3功能
python3 simple_s3_test.py
```

详细的安装说明请参考：`INSTALL_DEPENDENCIES_CN.md` 