# 护照信息表 (Passports) 使用说明

## 概述

护照信息表用于存储用户上传的护照图片及其识别结果。该表支持完整的护照信息管理，包括图片存储、Dify集成和护照信息识别。

## 表结构

### 基础字段
- `id`: 主键，自增长整型
- `user_id`: 用户ID，关联用户表
- `task_id`: 任务ID，用于追踪处理流程
- `created_at`: 创建时间
- `updated_at`: 更新时间

### 图片相关字段
- `uploaded_image_url`: 上传后的护照图片地址
- `dify_image_url`: Dify处理后的图片地址
- `dify_image_uuid`: Dify图片UUID
- `dify_image_filename`: Dify图片文件名
- `dify_filename`: Dify文件名

### 护照识别信息字段
- `document_type`: 证件类型 (通常为"Passport")
- `country_of_issue`: 签发国代码
- `passport_number`: 护照号码
- `surname`: 姓氏
- `given_names`: 名字
- `nationality`: 国籍
- `date_of_birth`: 出生日期 (YYYY-MM-DD格式)
- `sex`: 性别 (M/F/X)
- `place_of_birth`: 出生地
- `date_of_issue`: 签发日期 (YYYY-MM-DD格式)
- `date_of_expiry`: 有效期至 (YYYY-MM-DD格式)
- `authority`: 签发机关
- `mrz_line1`: MRZ第一行
- `mrz_line2`: MRZ第二行
- `signature_present`: 持照人签名存在状态 (布尔值)
- `additional_info`: 额外信息 (JSON格式)
- `processing_status`: 处理状态 (pending/processing/completed/failed)

## 初始化步骤

### 1. 运行初始化脚本

```bash
cd service-operation-server
python scripts/init_passport_table.py
```

### 2. 手动执行迁移 (可选)

如果使用Aerich进行迁移管理：

```bash
cd service-operation-server
aerich upgrade
```

### 3. 验证表创建

```sql
-- 检查表是否创建成功
DESCRIBE passports;

-- 查看表结构
SHOW CREATE TABLE passports;
```

## 使用示例

### 1. 创建护照记录

```python
from src.db.models import Passport

# 使用识别数据创建护照记录
recognition_data = {
    "document_type": "Passport",
    "country_of_issue": "China",
    "passport_number": "E12345678",
    "surname": "ZHANG",
    "given_names": "SAN",
    "nationality": "CHN",
    "date_of_birth": "1990-01-01",
    "sex": "M",
    "place_of_birth": "Beijing",
    "date_of_issue": "2020-01-01",
    "date_of_expiry": "2030-01-01",
    "authority": "Ministry of Public Security",
    "mrz_line1": "P<CHNZHANG<<SAN<<<<<<<<<<<<<<<<<<<<<<<<<<<<",
    "mrz_line2": "E123456781CHN9001011M3001011<<<<<<<<<<<<<<04",
    "signature_present": True,
    "additional_info": {
        "confidence": 0.95,
        "processing_time": "2.3s"
    }
}

passport = await Passport.create_from_recognition_data(
    user_id="user123",
    task_id="task456",
    uploaded_image_url="https://example.com/passport.jpg",
    recognition_data=recognition_data
)
```

### 2. 查询护照记录

```python
# 根据用户ID查询
user_passports = await Passport.filter(user_id="user123").all()

# 根据护照号码查询
passport = await Passport.filter(passport_number="E12345678").first()

# 分页查询
passports = await Passport.all().offset(0).limit(10)
```

### 3. 更新护照记录

```python
passport = await Passport.get(id=1)
passport.processing_status = "completed"
passport.dify_image_url = "https://dify.example.com/processed.jpg"
await passport.save()
```

### 4. 转换为JSON格式

```python
passport = await Passport.get(id=1)
recognition_json = passport.to_recognition_json()
print(recognition_json)
```

## JSON数据格式

护照识别信息的标准JSON格式：

```json
{
  "document_type": "Passport",
  "country_of_issue": "China",
  "passport_number": "E12345678",
  "surname": "ZHANG",
  "given_names": "SAN",
  "nationality": "CHN",
  "date_of_birth": "1990-01-01",
  "sex": "M",
  "place_of_birth": "Beijing",
  "date_of_issue": "2020-01-01",
  "date_of_expiry": "2030-01-01",
  "authority": "Ministry of Public Security",
  "mrz_line1": "P<CHNZHANG<<SAN<<<<<<<<<<<<<<<<<<<<<<<<<<<<",
  "mrz_line2": "E123456781CHN9001011M3001011<<<<<<<<<<<<<<04",
  "signature_present": true,
  "additional_info": {
    "confidence": 0.95,
    "processing_time": "2.3s"
  }
}
```

## 索引说明

表中创建了以下索引以优化查询性能：

- `idx_user_id`: 用户ID索引
- `idx_task_id`: 任务ID索引
- `idx_passport_number`: 护照号码索引
- `idx_processing_status`: 处理状态索引
- `idx_created_at`: 创建时间索引

## 外键约束

- `fk_passports_user_id`: 关联到用户表的外键约束，级联删除

## 注意事项

1. **日期格式**: 所有日期字段统一使用 YYYY-MM-DD 格式
2. **JSON字段**: `additional_info` 字段使用MySQL的JSON类型，支持复杂数据结构
3. **字符集**: 表使用 utf8mb4 字符集，支持emoji和特殊字符
4. **外键约束**: 删除用户时会级联删除相关的护照记录
5. **处理状态**: 建议使用枚举值 (pending/processing/completed/failed)

## 扩展功能

### 1. 添加新字段

如果需要添加新字段，请：
1. 修改模型文件 `src/db/models/passport.py`
2. 创建新的迁移文件
3. 运行迁移更新数据库

### 2. 性能优化

- 根据查询模式添加复合索引
- 考虑对大文本字段进行分表存储
- 定期清理过期的护照记录

### 3. 数据备份

建议定期备份护照数据，特别是包含个人敏感信息的字段。 