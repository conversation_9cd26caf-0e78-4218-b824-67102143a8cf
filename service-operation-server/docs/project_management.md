# 项目管理功能说明

## 表结构设计

### projects 表结构

| 字段名 | 类型 | 说明 | 备注 |
|-------|------|------|------|
| id | BIGINT | 主键ID | 自增 |
| project_number | INT | 项目编号 | 从25001开始自增，唯一 |
| project_name | VARCHAR(200) | 项目名称 | 必填 |
| creator_user_id | BIGINT | 创建用户ID | 外键关联users表 |
| creator_name | VARCHAR(100) | 创建人姓名 | 必填 |
| project_description | LONGTEXT | 项目描述 | 可选 |
| client_name | VARCHAR(200) | 客户名称 | 必填 |
| project_date | DATE | 项目创建日期 | 必填 |
| cost_center | VARCHAR(100) | 成本中心 | 必填 |
| created_at | TIMESTAMP | 记录创建时间 | 自动生成 |
| updated_at | TIMESTAMP | 记录更新时间 | 自动更新 |

### 索引设计

- `idx_projects_project_number`: 项目编号索引（唯一）
- `idx_projects_creator_user_id`: 创建用户ID索引
- `idx_projects_client_name`: 客户名称索引
- `idx_projects_cost_center`: 成本中心索引
- `idx_projects_created_at`: 创建时间索引

## API 接口

### 基础CRUD操作

1. **创建项目** `POST /api/project/`
   - 自动分配项目编号（从25001开始）
   - 验证创建用户是否存在

2. **获取项目详情** `GET /api/project/{project_id}`
   - 根据项目ID获取详情

3. **获取项目列表** `GET /api/project/`
   - 支持分页查询
   - 支持按项目名称、客户名称、成本中心、创建人姓名筛选

4. **更新项目** `PUT /api/project/{project_id}`
   - 支持部分字段更新

5. **删除项目** `DELETE /api/project/{project_id}`

### 扩展接口

1. **根据项目编号查询** `GET /api/project/by-number/{project_number}`

2. **项目统计信息** `GET /api/project/stats/summary`

## 数据模型

### Pydantic 模型

- `ProjectCreate`: 创建项目请求模型
- `ProjectUpdate`: 更新项目请求模型  
- `ProjectResponse`: 项目响应模型
- `ProjectListResponse`: 项目列表响应模型
- `ProjectQuery`: 项目查询参数模型

## 使用说明

### 1. 创建项目

```python
import httpx

# 创建项目
project_data = {
    "project_name": "测试项目",
    "creator_user_id": 1,
    "creator_name": "张三",
    "project_description": "这是一个测试项目",
    "client_name": "测试客户",
    "project_date": "2025-01-02",
    "cost_center": "CC001"
}

response = httpx.post("/api/project/", json=project_data)
project = response.json()
print(f"创建的项目编号: {project['project_number']}")
```

### 2. 查询项目列表

```python
# 分页查询项目
params = {
    "page": 1,
    "page_size": 20,
    "client_name": "测试客户"  # 可选筛选条件
}

response = httpx.get("/api/project/", params=params)
result = response.json()
print(f"总项目数: {result['total']}")
for project in result['items']:
    print(f"项目: {project['project_name']} - 编号: {project['project_number']}")
```

## 数据库迁移

使用 Aerich 运行迁移：

```bash
# 应用迁移
aerich upgrade

# 如果需要回滚
aerich downgrade
```

## 注意事项

1. **项目编号自增**: 项目编号从25001开始，由应用层逻辑控制分配
2. **外键约束**: creator_user_id 必须是有效的用户ID
3. **数据完整性**: 删除用户时会级联删除相关项目（CASCADE）
4. **索引优化**: 已为常用查询字段创建索引，提高查询性能
5. **字符集**: 使用 utf8mb4 字符集，支持中文和特殊字符 