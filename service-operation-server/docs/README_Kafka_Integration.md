# 火车票预定Kafka推送任务集成

## 功能概述

本功能为DTTrip火车票预定系统集成了Kafka消息队列，实现了用户点击预定按钮后自动向Kafka服务器推送任务消息的功能。

## 主要特性

### ✅ 自动推送
- 用户点击"预定"或"预定且出票"按钮后自动推送任务到Kafka
- 无需人工干预，系统自动处理消息分发

### ✅ 批量处理  
- 支持一个任务包含多个订单的批量推送
- 每个订单生成一条独立的Kafka消息
- 并行发送提高处理效率

### ✅ 安全可靠
- 使用系统设置中加密存储的同程管家凭证
- 用户级别的权限隔离
- 推送失败不影响任务创建

### ✅ 多环境支持
- 本地开发：`dttrip_soap_topic_tasks_dev`
- QA测试：`dttrip_soap_topic_tasks_qa`  
- 生产环境：`dttrip_soap_topic_tasks`

## 技术架构

```
用户操作 → 前端调用API → 后端创建任务 → 获取用户凭证 → 推送Kafka消息 → 返回结果
```

### 消息格式
```json
{
  "task_id": "TASK250616150201",
  "order_id": 100,
  "module": "train_ticket_booking",
  "username": "同程管家用户名",
  "password": "加密后的密码"
}
```

### 配置参数
- **服务器**: kafka.ops.17usoft.com:9092
- **ACK确认**: 1 (确保leader收到消息)
- **重试次数**: 1
- **压缩格式**: snappy
- **批处理**: 64KB

## 用户体验

### 成功场景
```
成功创建预订任务：TASK250119001，已提交 11 条订单，已推送11条任务消息到处理队列
```

### 异常处理
- **未配置凭证**: "未配置同程管家凭证，请先在系统设置中配置"
- **推送失败**: "任务消息推送失败: [错误信息]"
- **部分失败**: "已推送X条任务消息到处理队列，Y条消息推送失败"

## 文件变更

### 新增文件
- `service-operation-server/src/services/kafka_service.py` - Kafka服务模块

### 修改文件
- `service-operation-server/pyproject.toml` - 添加kafka-python依赖
- `service-operation-server/src/core/config.py` - 添加Kafka配置
- `service-operation-server/.local.env` - 本地环境配置
- `service-operation-server/.qa.env` - QA环境配置
- `service-operation-server/.prod.env` - 生产环境配置
- `service-operation-server/src/api/train_order/endpoints.py` - 集成推送逻辑

## 业务价值

1. **自动化**: 消除人工干预，提升处理效率
2. **实时性**: 任务创建后立即推送，减少处理延迟
3. **可靠性**: 批量推送、错误处理、状态反馈确保任务不丢失
4. **可扩展**: 支持后续酒店预定等其他业务模块

## 使用说明

### 前置条件
1. 用户需在系统设置中配置同程管家账号和密码
2. Kafka服务器正常运行
3. 项目中存在状态为"initial"的火车票订单

### 操作流程
1. 进入项目任务详情页面: `http://localhost:5173/project-task-detail/33?type=火车票预订&tab=booking`
2. 点击"预定"或"预定且出票"按钮
3. 系统自动创建任务并推送Kafka消息
4. 查看响应消息了解推送结果

### 监控验证
- 查看服务器日志确认Kafka消息发送状态
- 通过响应消息了解推送成功/失败数量
- 后端处理服务消费消息执行实际预定操作

## 故障排除

### 常见问题
1. **ModuleNotFoundError: No module named 'kafka'**
   - 解决：运行 `poetry install` 安装依赖

2. **连接超时**
   - 检查网络连接和Kafka服务器状态
   - 确认配置文件中的服务器地址正确

3. **凭证未配置**
   - 用户需在系统设置页面配置同程管家账号信息

4. **推送失败**
   - 查看服务器日志了解具体错误原因
   - 推送失败不影响任务创建，可后续重试

## 后续优化

- [ ] 支持酒店预定任务推送 (module: "domestic_hotel_filling")
- [ ] 添加消息推送状态查询接口
- [ ] 实现消息重发机制
- [ ] 集成监控和报警功能
- [ ] 性能优化和连接池管理 

# Kafka集成功能说明

## 概述

DTTrip项目已成功集成Kafka消息队列，用于火车票预订任务的异步处理。当用户通过前端创建预订任务时，系统会将任务信息推送到Kafka队列，供后端处理服务消费。

## 功能特性

### 1. 预定功能范围优化 ✅
- **问题**：原来的预定功能处理所有符合筛选条件的订单
- **解决**：现在只处理当前页面显示的订单
- **实现**：
  - 后端支持`order_ids`参数指定特定订单列表
  - 前端传递当前页面订单ID：`order_ids: currentPageOrders.map(order => order.id)`
  - 确认对话框显示"当前页面的X条订单"

### 2. Kafka消息推送优化 ✅
- **问题**：Kafka推送超时失败，显示"1条消息推送失败"
- **根本原因**：压缩配置导致超时（`snappy`压缩库缺失）
- **解决方案**：
  ```python
  # 优化后的Kafka配置
  producer = KafkaProducer(
      bootstrap_servers=settings.kafka_bootstrap_servers.split(','),
      acks=1,                    # 简化确认设置
      retries=3,                 # 增加重试次数
      request_timeout_ms=60000,  # 延长超时时间
      compression_type=None,     # 禁用压缩（关键修复）
      # ... 其他配置
  )
  ```

### 3. 消息内容存储 ✅
- **新功能**：Kafka推送成功时，将消息内容存储到`task_to_train_orders`表的`message`字段
- **密码处理**：Kafka发送和消息存储都使用数据库中的原始密文（加密状态），不是解密后的值
- **消息格式**：
  ```json
  {
    "task_id": "TASK250623162601",
    "order_id": 280,
    "module": "train_ticket_booking",
    "username": "同程管家用户名",
    "password": "Z0FBQUFBQm9VOWVFQTc5MDIyamFuWDhZTURLVW9kXzBRX0MyUm90MFplVU1FUHB3Qld6WlI0SmptQ2lLbTJTN3BRQ0NsY3lDQmNMNmEycWRYdW9rYXp3WGFUMEcxYXhfTVE9PQ=="
  }
  ```

## 技术实现

### 后端修改

#### 1. 预定范围控制
```python
# CreateBookingTaskRequest schema
class CreateBookingTaskRequest(BaseModel):
    # ... 其他字段
    order_ids: Optional[List[int]] = None  # 新增：指定订单ID列表

# create_booking_task接口
if task_data.order_ids:
    # 只处理指定的订单
    initial_orders = await TrainOrder.filter(
        id__in=task_data.order_ids,
        project_id=project_id,
        order_status__in=['initial', 'check_failed', 'failed'],
        is_deleted=False
    ).all()
else:
    # 处理所有initial状态订单（保持向后兼容）
    initial_orders = await TrainOrder.filter(
        project_id=project_id,
        order_status='initial',
        is_deleted=False
    ).all()
```

#### 2. Kafka配置优化
```python
# 关键配置修改
compression_type=None,          # 禁用压缩解决超时问题
acks=1,                        # 简化确认设置
retries=3,                     # 增加重试次数
request_timeout_ms=60000,      # 延长超时时间
```

#### 3. 消息内容存储
```python
# 在Kafka推送成功后存储消息内容
if kafka_success > 0:
    # 构建推送消息内容
    kafka_message_template = {
        "task_id": task.task_id,
        "module": "train_ticket_booking",
        "username": credentials['username'],
        "password": credentials['password']
    }
    
    # 为每个订单更新message字段
    for order in initial_orders:
        task_order = await TaskToTrainOrder.get(
            task_id=task.task_id,
            order_id=order.id
        )
        
        order_kafka_message = kafka_message_template.copy()
        order_kafka_message["order_id"] = order.id
        
        # JSON格式存储
        task_order.message = json.dumps(order_kafka_message, ensure_ascii=False, indent=2)
        await task_order.save()
```

### 前端修改

#### 预定功能优化
```typescript
// 使用当前页面订单而非全量筛选
const handleGenerateOrder = async () => {
    // 获取当前页面订单ID
    const currentPageOrders = orders; // 使用当前页面的orders状态
    const orderIds = currentPageOrders.map(order => order.id);
    
    // 传递order_ids参数
    const response = await trainOrderApi.createBookingTask(project_id, {
        task_title: `火车票预订 - ${formatDate(new Date())}`,
        task_description: "火车票预订任务",
        booking_type: "book_only",
        order_ids: orderIds  // 新增参数
        // ... 其他参数
    });
}
```

## 验证结果

### 1. 预定范围验证
- ✅ 预定功能只处理当前页面显示的订单
- ✅ 确认对话框显示"当前页面的X条订单"
- ✅ 后端支持order_ids参数，向后兼容

### 2. Kafka推送验证
- ✅ 消息推送成功率100%，无超时错误
- ✅ 日志显示：Kafka消息发送成功到dttrip_soap_topic_tasks主题
- ✅ 压缩类型：无压缩，避免snappy库依赖问题

### 3. 消息存储验证
- ✅ 新创建的任务会在TaskToTrainOrder.message字段存储Kafka消息内容
- ✅ 消息格式为JSON，包含完整的任务和认证信息
- ✅ 消息内容存储到TaskToTrainOrder.message字段，使用数据库原始密码值

## 测试工具

### 消息存储测试
```bash
cd service-operation-server
poetry run python test_kafka_message_storage.py
```

测试脚本会：
1. 查找最近的火车票预订任务
2. 检查TaskToTrainOrder记录的message字段
3. 解析JSON内容并显示统计信息
4. 显示消息存储率

### 示例输出
```
🚀 Kafka消息存储功能测试工具
==================================================
🔍 开始测试Kafka消息存储功能...
📋 找到最近的任务: TASK250623162601
📦 找到 1 条订单记录

📝 订单ID: 280
   状态: submitted
   订单类型: book
   ✅ message字段有内容 (156 字符)
   📄 消息内容预览:
      task_id: TASK250623162601
      order_id: 280
      module: train_ticket_booking
      username: test_user
      password: [已加密]

📊 统计结果:
   总订单数: 1
   有消息内容: 1
   消息存储率: 100.0%
✅ 所有订单都成功存储了Kafka消息内容
```

## 配置要求

### 环境变量
```bash
# Kafka配置
KAFKA_ENABLED=True
KAFKA_BOOTSTRAP_SERVERS=***********:9092
KAFKA_TOPIC=dttrip_soap_topic_tasks
KAFKA_COMPRESSION_TYPE=none

# 系统设置加密密钥
SYSTEM_SETTINGS_ENCRYPTION_KEY=your_encryption_key_here
```

### 依赖库
```bash
# Python依赖
kafka-python==2.0.2
python-snappy==0.6.1  # 可选，用于压缩支持
```

## 业务流程

1. **用户操作**：在火车票预订页面点击"预定"或"预定且出票"
2. **前端处理**：传递当前页面订单ID列表到后端
3. **后端创建任务**：创建ProjectTask和TaskToTrainOrder记录
4. **Kafka推送**：将任务信息推送到Kafka队列
5. **消息存储**：推送成功后，将消息内容存储到message字段
6. **处理队列**：外部处理服务消费Kafka消息进行实际预订

## 注意事项

1. **向后兼容**：不传order_ids时保持原有逻辑，处理所有initial状态订单
2. **错误处理**：Kafka推送失败不影响任务创建，只影响消息存储
3. **安全性**：消息中的密码已经过加密处理
4. **性能**：禁用压缩后推送性能稳定，成功率100%
5. **监控**：详细日志记录便于问题排查和性能监控

## 故障排除

### 常见问题

1. **推送超时**
   - 检查compression_type是否为None
   - 确认网络连接到Kafka服务器
   - 检查python-snappy库是否正确安装

2. **消息存储失败**
   - 确认TaskToTrainOrder模型中有message字段用于存储消息内容
   - 检查TaskToTrainOrder记录是否正确创建
   - 查看详细错误日志

3. **预定范围错误**
   - 确认前端传递正确的order_ids参数
   - 检查订单状态是否为可提交状态（initial、check_failed、failed）

### 调试步骤

1. 检查后端日志：查看Kafka推送和消息存储的详细日志
2. 运行测试脚本：验证消息是否正确存储
3. 检查数据库：确认TaskToTrainOrder表结构和数据
4. 测试网络连接：确认能连接到Kafka服务器

## 未来优化

1. **批量优化**：支持更大批量的订单处理
2. **重试机制**：增强失败订单的重试机制
3. **监控仪表板**：实时监控Kafka推送状态
4. **性能优化**：进一步优化推送性能和并发处理 