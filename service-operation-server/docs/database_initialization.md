# 数据库初始化说明

## 文件结构

```
service-operation-server/
├── src/
│   └── db/
│       ├── init_db.py          # 数据库初始化模块
│       └── config.py           # 数据库配置
└── init_database.py           # 数据库初始化启动脚本（推荐使用）
```

## 使用方法

### 方法1：使用根目录启动脚本（推荐）

```bash
# 在项目根目录下
source venv/bin/activate
python init_database.py
```

### 方法2：直接导入模块

```python
from src.db.init_db import init_db
import asyncio

# 在代码中使用
await init_db()

# 或者同步方式
asyncio.run(init_db())
```

### 方法3：从src/db目录直接运行（仅用于测试）

```bash
cd src/db
python init_db.py
```

## 为什么要移动到db目录？

1. **逻辑分组**: 数据库相关的脚本放在db目录下更符合项目结构
2. **模块化**: 作为模块函数可以在其他地方被导入使用
3. **维护性**: 与其他数据库相关文件（config.py, models等）放在一起便于维护

## 注意事项

- 建议使用方法1（根目录启动脚本），这样可以避免路径问题
- 确保已经激活虚拟环境并安装了所有依赖
- 数据库配置需要正确设置（用户名、密码、数据库名等） 