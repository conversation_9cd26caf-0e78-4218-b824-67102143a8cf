# 护照识别功能实现说明

## 功能概述

本系统实现了基于Dify API的护照识别功能，支持：

- 📁 批量上传护照图片
- 🤖 自动异步识别护照信息
- 📊 实时显示识别进度
- 💾 自动保存识别结果到数据库
- 🔄 任务状态实时更新

## 环境配置

### 1. 在 `.env` 文件中添加配置

```env
# Dify API 配置
DIFY_SERVER_URL=https://difyapi.17usoft.com/v1
PASSPORT_REG_API_KEY=your-dify-api-key-here
```

### 2. 获取Dify API Key

1. 登录Dify平台
2. 进入应用管理页面
3. 找到护照识别应用
4. 复制API Key到环境变量

## 系统架构

### 后端组件

1. **DifyService** (`src/services/dify_service.py`)
   - 处理文件上传到Dify
   - 调用Dify识别API
   - 解析识别结果

2. **PassportTaskService** (`src/services/passport_task_service.py`)
   - 异步任务队列管理
   - 识别任务调度
   - 状态跟踪和更新

3. **API端点** (`src/api/passport/endpoints.py`)
   - 文件上传接口
   - 任务管理接口
   - 状态查询接口

### 前端功能

1. **文件上传** - 支持批量上传护照图片
2. **实时进度** - 显示识别任务进度和状态
3. **结果展示** - 图文对照显示识别结果
4. **任务历史** - 查看历史识别任务

## API接口说明

### 上传文件并启动识别

```http
POST /api/passport/upload
Content-Type: multipart/form-data
Authorization: Bearer <token>

files: [文件1, 文件2, ...]
```

### 手动启动识别任务

```http
POST /api/passport/task/{task_id}/recognize
Authorization: Bearer <token>
```

### 查询任务状态

```http
GET /api/passport/task/{task_id}/status
Authorization: Bearer <token>
```

### 停止识别任务

```http
POST /api/passport/task/{task_id}/stop
Authorization: Bearer <token>
```

## 识别流程

### 1. 文件上传阶段
```
用户上传 -> 文件验证 -> 保存到本地 -> 创建数据库记录
```

### 2. 识别处理阶段
```
启动任务 -> 上传到Dify -> 调用识别API -> 解析结果 -> 更新数据库
```

### 3. 结果展示阶段
```
实时轮询 -> 获取状态 -> 更新界面 -> 显示结果
```

## 支持的护照字段

- `passport_number` - 护照号
- `surname` - 姓氏
- `given_names` - 名字
- `nationality` - 国籍
- `date_of_birth` - 出生日期
- `sex` - 性别
- `place_of_birth` - 出生地
- `date_of_issue` - 签发日期
- `date_of_expiry` - 有效期
- `country_of_issue` - 签发国
- `authority` - 签发机构

## 任务状态说明

- `pending` - 等待处理
- `processing` - 识别中
- `completed` - 识别完成
- `failed` - 识别失败

## 错误处理

### 常见错误及解决方案

1. **API Key 未配置**
   ```
   错误：PASSPORT_REG_API_KEY not found
   解决：在 .env 文件中配置正确的API Key
   ```

2. **网络连接问题**
   ```
   错误：Failed to upload file to Dify
   解决：检查网络连接和Dify服务状态
   ```

3. **文件格式不支持**
   ```
   错误：Unsupported file type
   解决：使用支持的图片格式（JPG, PNG, etc.）
   ```

## 性能优化

### 并发处理
- 任务队列支持并发处理多个识别任务
- 可以配置最大并发数来控制系统负载

### 缓存策略
- 识别结果自动缓存到数据库
- 避免重复识别相同文件

### 错误重试
- 网络异常自动重试
- 识别失败可手动重新识别

## 测试验证

运行测试脚本验证系统功能：

```bash
cd service-operation-server
python test_passport_recognition.py
```

## 部署注意事项

1. **环境变量配置**
   - 确保所有必要的环境变量都已配置
   - API Key 需要保密，不要提交到代码库

2. **依赖安装**
   ```bash
   pip install httpx loguru pydantic-settings tortoise-orm
   ```

3. **服务启动顺序**
   - 先启动数据库服务
   - 再启动后端API服务
   - 最后启动前端服务

4. **监控和日志**
   - 使用loguru记录详细日志
   - 监控任务处理器状态
   - 定期检查识别准确率

## 故障排除

### 服务无法启动
1. 检查端口是否被占用
2. 确认所有依赖都已安装
3. 验证数据库连接

### 识别准确率低
1. 确保图片质量清晰
2. 检查Dify模型配置
3. 优化图片预处理

### 前端连接失败
1. 确认后端服务正常运行
2. 检查CORS配置
3. 验证API路径配置

## 更新日志

- **v1.0.0** - 基础护照识别功能
- **v1.1.0** - 添加异步任务处理
- **v1.2.0** - 前端实时进度显示
- **v1.3.0** - 错误处理和重试机制 