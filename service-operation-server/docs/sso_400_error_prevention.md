# SSO 400错误预防和诊断指南

## 📋 官方400错误说明

根据同程SSO官方提供的400错误说明：

### 🔑 关键要求
1. **redirect_uri不要urlencode** - 回调URI必须保持原始格式
2. **一个code只能换一次token** - 授权码只能使用一次
3. **grant_type固定的值：authorization_code** - 授权类型必须正确

### 🚨 循环登录问题
如果出现大量的code换取token出现400，基本是因为：
- 系统iframe嵌入了别的系统
- 开了同程管家的情况下循环登录
- 由于HTTP/HTTPS混合使用，HTTP获取不到HTTPS中的cookie导致

## 🛠️ 系统改进

### 1. 预防性检查
系统现在会在每次token交换前进行以下检查：

```
🔍 SSO官方400错误预防检查
- ✅ redirect_uri未被URL编码
- ✅ grant_type正确: authorization_code
- ❌ 检测到授权码重复使用（防止循环登录）
```

### 2. 详细错误诊断
当出现400错误时，系统会输出详细的诊断信息：

```
🚨 SSO认证失败 - 详细错误信息
📍 请求信息: SSO域名、状态码、响应时间
🔑 认证参数: 客户端ID、密钥、回调URI
📝 授权码信息: 长度、前缀、后缀
❌ 错误详情: 错误类型、描述、完整响应
🔍 可能原因: 针对性分析
🛠️ 建议解决方案: 具体操作步骤
📊 授权码使用统计: 循环登录检测
```

### 3. 循环登录检测
- 记录所有授权码使用时间
- 检测短时间内的重复使用
- 提供风险评估和建议

## 🔧 新增诊断端点

### 1. 配置检查
```
GET /api/auth/sso/config-check
```
检查SSO配置的完整性和有效性。

### 2. 连接诊断
```
GET /api/auth/sso/diagnose
```
测试SSO服务器连接状态和各个端点的可达性。

### 3. 授权码统计
```
GET /api/auth/sso/code-stats
```
查看授权码使用统计，用于排查循环登录问题。

返回示例：
```json
{
  "timestamp": "2024-01-15 10:30:00",
  "total_codes": 15,
  "time_periods": {
    "last_1_minute": {"count": 0, "codes": []},
    "last_5_minutes": {"count": 2, "codes": [...]}
  },
  "risk_assessment": {
    "level": "low",
    "message": "正常使用",
    "recommendations": []
  }
}
```

### 4. 清理缓存
```
POST /api/auth/sso/clear-code-cache
```
清理授权码使用缓存，重置循环登录检测。

## 🎯 排查步骤

### 当出现400错误时：

1. **查看详细日志** - 系统会输出完整的错误诊断信息
2. **检查授权码统计** - 访问 `/api/auth/sso/code-stats` 查看使用频率
3. **验证配置** - 访问 `/api/auth/sso/config-check` 检查配置
4. **测试连接** - 访问 `/api/auth/sso/diagnose` 测试网络连接

### 循环登录问题排查：

1. **检查iframe嵌入** - 确认页面是否被其他系统嵌入
2. **同程管家插件** - 检查是否开启了相关插件
3. **HTTP/HTTPS混合** - 确认协议使用的一致性
4. **Cookie设置** - 检查cookie的domain和secure设置

## 📊 风险级别说明

- **Low**: 正常使用，偶尔的重复请求
- **Medium**: 5分钟内多次使用授权码，需要关注
- **High**: 1分钟内多次使用，可能存在严重循环登录问题

## 🔒 安全说明

- 所有诊断端点仅在非生产环境可用
- 敏感信息（如完整授权码、密钥）会被脱敏处理
- 授权码缓存会自动清理24小时前的记录
