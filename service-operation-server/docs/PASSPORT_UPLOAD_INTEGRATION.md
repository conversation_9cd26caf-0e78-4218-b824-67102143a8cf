# 护照上传前后端联调说明

## 概述

本文档说明如何进行护照上传功能的前后端联调，包括环境准备、API测试和前端集成。

## 功能特性

- ✅ 支持单张/多张图片上传
- ✅ 支持ZIP/RAR压缩包上传并自动解压
- ✅ 自动生成任务ID（格式：AN_当前时间_序号）
- ✅ 文件存储和URL生成
- ✅ 数据库记录创建
- ✅ 前端列表展示和管理

## 后端API接口

### 1. 护照文件上传
```
POST /api/passport/upload
Content-Type: multipart/form-data
Authorization: Bearer <JWT_TOKEN>

参数:
- files: 文件列表（支持图片和压缩包）
- task_id: 可选，任务ID（不提供则自动生成）

响应:
{
  "task_id": "AN_20241230143022_001",
  "uploaded_files": [
    {
      "filename": "passport1.jpg",
      "file_path": "passport/2024/12/30/abc123.jpg",
      "file_url": "/uploads/passport/2024/12/30/abc123.jpg",
      "file_size": 1024000,
      "content_type": "image/jpeg"
    }
  ],
  "total_files": 1,
  "message": "成功上传 1 个文件"
}
```

### 2. 获取护照列表
```
GET /api/passport/list?page=1&size=20&task_id=AN_20241230143022_001
Authorization: Bearer <JWT_TOKEN>

响应:
{
  "total": 1,
  "items": [
    {
      "id": 1,
      "user_id": "123497",
      "task_id": "AN_20241230143022_001",
      "uploaded_image_url": "/uploads/passport/2024/12/30/abc123.jpg",
      "processing_status": "pending",
      "created_at": "2024-12-30T14:30:22",
      "updated_at": "2024-12-30T14:30:22",
      // ... 其他护照识别字段
    }
  ],
  "page": 1,
  "size": 20
}
```

### 3. 根据任务ID获取护照列表
```
GET /api/passport/task/{task_id}
Authorization: Bearer <JWT_TOKEN>
```

### 4. 删除护照记录
```
DELETE /api/passport/{passport_id}
Authorization: Bearer <JWT_TOKEN>
```

## 环境准备

### 后端启动

1. 确保数据库连接正常
2. 安装依赖：
   ```bash
   cd service-operation-server
   poetry install
   ```

3. 启动服务：
   ```bash
   poetry run uvicorn src.app:get_app --host 0.0.0.0 --port 8000 --reload
   ```

4. 验证服务：访问 http://localhost:8000/api/docs

### 前端启动

1. 安装依赖：
   ```bash
   cd service-operation-frontend
   npm install
   ```

2. 配置环境变量（.env.local）：
   ```
   VITE_API_BASE_URL=http://localhost:8000
   ```

3. 启动开发服务器：
   ```bash
   npm run dev
   ```

4. 访问：http://localhost:5173

## 测试步骤

### 1. 后端API测试

使用提供的测试脚本：
```bash
cd service-operation-server
python test_passport_api.py
```

或使用curl命令：
```bash
# 获取JWT token（需要先通过SSO登录或API认证）
TOKEN="your_jwt_token_here"

# 测试文件上传
curl -X POST "http://localhost:8000/api/passport/upload" \
  -H "Authorization: Bearer $TOKEN" \
  -F "files=@test_passport.jpg"

# 测试获取列表
curl -X GET "http://localhost:8000/api/passport/list" \
  -H "Authorization: Bearer $TOKEN"
```

### 2. 前端功能测试

1. 登录系统（通过SSO或其他认证方式）
2. 访问护照识别页面：http://localhost:5173/passport-recognition
3. 测试文件上传：
   - 单张图片上传
   - 多张图片上传
   - 压缩包上传
4. 验证列表显示：
   - 查看上传的文件记录
   - 检查任务ID格式
   - 验证图片预览
   - 测试删除功能

## 任务ID格式

任务ID采用格式：`AN_YYYYMMDDHHMMSS_XXX`

- `AN`: 固定前缀
- `YYYYMMDDHHMMSS`: 当前时间戳
- `XXX`: 当天的序号（3位数，从001开始）

示例：`AN_20241230143022_001`

## 文件存储结构

```
uploads/
└── passport/
    └── 2024/
        └── 12/
            └── 30/
                ├── abc123def456.jpg
                ├── def789ghi012.png
                └── ...
```

## 数据库表结构

护照记录存储在 `passports` 表中，主要字段：

- `id`: 主键
- `user_id`: 用户ID（外键）
- `task_id`: 任务ID
- `uploaded_image_url`: 上传图片URL
- `processing_status`: 处理状态（pending/processing/completed/failed）
- `created_at`: 创建时间
- `updated_at`: 更新时间
- 其他护照识别字段...

## 前端组件说明

### PassportRecognitionPage
主要护照识别页面，包含：
- 文件上传区域
- 上传进度显示
- 结果列表展示
- 操作按钮（刷新、导出、删除）

### passportService
前端API服务，提供：
- `uploadFiles()`: 文件上传
- `getPassportList()`: 获取列表
- `getPassportsByTask()`: 根据任务获取列表
- `deletePassport()`: 删除记录

## 常见问题

### 1. 认证问题
- 确保JWT token有效
- 检查token是否正确设置在请求头中
- 验证用户权限

### 2. 文件上传问题
- 检查文件大小限制（默认10MB）
- 验证文件类型是否支持
- 确保上传目录权限正确

### 3. 跨域问题
- 后端已配置CORS允许所有来源
- 生产环境需要限制允许的域名

### 4. 数据库连接问题
- 检查数据库配置
- 确保passports表已创建
- 验证外键关系

## 下一步开发

1. **护照识别集成**：集成Dify或其他OCR服务
2. **批量处理**：支持批量识别和导出
3. **状态更新**：实时更新处理状态
4. **错误处理**：完善错误处理和重试机制
5. **性能优化**：文件压缩、缓存等

## 联调检查清单

- [ ] 后端服务正常启动
- [ ] 数据库连接正常
- [ ] 护照表已创建
- [ ] 文件上传目录权限正确
- [ ] 前端服务正常启动
- [ ] API基础URL配置正确
- [ ] JWT认证正常工作
- [ ] 文件上传功能正常
- [ ] 列表获取功能正常
- [ ] 图片预览正常显示
- [ ] 删除功能正常工作
- [ ] 任务ID格式正确
- [ ] 错误处理正常 