# 使用国内镜像源安装依赖说明

为了提高依赖包的下载速度和避免网络问题，建议使用国内镜像源安装S3相关依赖。

## 方法一：使用pip安装（推荐）

### 1. 安装S3相关依赖

```bash
# 使用清华大学镜像源安装S3依赖
pip3 install -i https://pypi.tuna.tsinghua.edu.cn/simple/ \
  boto3==1.38.42 \
  botocore==1.38.42 \
  jmespath==1.0.1 \
  s3transfer==0.13.0 \
  --break-system-packages
```

### 2. 安装完整项目依赖

```bash
# 使用清华大学镜像源安装所有依赖
pip3 install -i https://pypi.tuna.tsinghua.edu.cn/simple/ \
  -r requirements.txt \
  --break-system-packages
```

## 方法二：使用Poetry安装

### 1. 配置Poetry使用国内镜像源

```bash
# 配置清华大学镜像源
poetry source add tsinghua https://pypi.tuna.tsinghua.edu.cn/simple/

# 设置为主要源
poetry config repositories.tsinghua https://pypi.tuna.tsinghua.edu.cn/simple/
```

### 2. 安装依赖

```bash
# 使用国内镜像源安装
poetry install --source tsinghua
```

## 常用国内镜像源

### 清华大学镜像源（推荐）
```
https://pypi.tuna.tsinghua.edu.cn/simple/
```

### 阿里云镜像源
```
https://mirrors.aliyun.com/pypi/simple/
```

### 中科大镜像源
```
https://pypi.mirrors.ustc.edu.cn/simple/
```

### 豆瓣镜像源
```
https://pypi.douban.com/simple/
```

## 验证安装

### 1. 验证所有依赖安装

```bash
# 验证boto3安装
python3 -c "import boto3; print('boto3版本:', boto3.__version__)"

# 验证其他关键依赖
python3 -c "import loguru, pydantic_settings, yarl; print('关键依赖已安装')"

# 验证应用导入
python3 -c "from src.app import get_app; print('应用导入成功')"
```

### 2. 测试S3功能

```bash
# 运行S3连接测试
python3 simple_s3_test.py
```

预期输出：
```
🚀 开始S3连接测试

✅ S3客户端创建成功
Endpoint: http://oss.qa.17usoft.com
Bucket: soap-files
Key Prefix: tmc.ai.soap.server

=== 测试连接 ===
❌ S3访问被拒绝：权限不足  # 这是正常的，需要正确的访问凭证
```

### 3. 测试后端服务启动

```bash
# 启动后端服务
python3 -m src

# 在另一个终端测试健康检查
curl -s http://127.0.0.1:8000/api/health
```

预期输出：
```json
{
  "status_code": 200,
  "message": "系统状态检查",
  "data": null,
  "status": "healthy",
  "version": "0.1.0",
  "environment": "Environment.DEVELOPMENT"
}
```

## 故障排除

### 1. 网络连接问题

如果遇到网络连接问题，可以尝试其他镜像源：

```bash
# 尝试阿里云镜像源
pip3 install -i https://mirrors.aliyun.com/pypi/simple/ boto3

# 尝试中科大镜像源
pip3 install -i https://pypi.mirrors.ustc.edu.cn/simple/ boto3
```

### 2. 权限问题

如果遇到权限问题，可以使用用户安装：

```bash
# 用户级安装
pip3 install --user -i https://pypi.tuna.tsinghua.edu.cn/simple/ boto3
```

### 3. 版本冲突

如果遇到版本冲突，可以强制重新安装：

```bash
# 强制重新安装
pip3 install --force-reinstall -i https://pypi.tuna.tsinghua.edu.cn/simple/ boto3==1.38.42
```

## 生产环境部署

### Docker环境

在Dockerfile中使用国内镜像源：

```dockerfile
# 使用国内镜像源安装依赖
RUN pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ \
    -r requirements.txt \
    --no-cache-dir
```

### CI/CD环境

在CI/CD配置中使用国内镜像源：

```yaml
# GitHub Actions示例
- name: Install dependencies
  run: |
    pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ \
      -r requirements.txt
```

## 性能对比

使用国内镜像源的优势：

- **下载速度**: 提升3-10倍下载速度
- **稳定性**: 避免网络超时和连接中断
- **可靠性**: 减少因网络问题导致的部署失败

## 安全注意事项

1. **镜像源可信度**: 建议使用知名大学和大公司的镜像源
2. **HTTPS连接**: 确保使用HTTPS协议下载包
3. **包完整性**: 安装后验证包的完整性和功能

## 相关文件

- `requirements.txt` - 包含完整的依赖列表
- `pyproject.toml` - Poetry项目配置
- `simple_s3_test.py` - S3功能测试脚本
- `S3_UPLOAD_IMPLEMENTATION.md` - S3功能详细说明 