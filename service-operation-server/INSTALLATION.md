# DTTrip Service Operation Server - 安装指南

## 概述

本文档提供了 DTTrip Service Operation Server 的完整安装指南，包括依赖安装、环境配置和部署说明。

## 系统要求

- Python 3.9.1+
- MySQL 5.7+ 或 8.0+
- Redis 6.0+
- MongoDB 4.4+ (可选)

## 依赖安装

### 方式一：使用 Poetry (推荐开发环境)

```bash
# 安装 Poetry
curl -sSL https://install.python-poetry.org | python3 -

# 安装项目依赖
poetry install

# 激活虚拟环境
poetry shell

# 运行服务
poetry run uvicorn src.app:app --host 0.0.0.0 --port 8000 --reload
```

### 方式二：使用 pip (生产环境)

```bash
# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate  # Windows

# 安装生产依赖
pip install -r requirements-prod.txt

# 或安装完整依赖（包含开发工具）
pip install -r requirements.txt
```

## 核心依赖说明

### PDF 处理功能
- **PyMuPDF (1.26.3)**: PDF转图片转换，纯Python实现，无需系统依赖
- **Pillow (11.3.0)**: 图像处理库

### Web 框架
- **FastAPI (0.115.12)**: 现代高性能Web框架
- **Uvicorn (0.34.2)**: ASGI服务器
- **Gunicorn (23.0.0)**: WSGI服务器（生产环境）

### 数据库
- **Tortoise ORM (0.23.0)**: 异步ORM
- **aiomysql (0.2.0)**: 异步MySQL驱动
- **PyMySQL (1.1.1)**: MySQL连接器

### 认证安全
- **python-jose (3.5.0)**: JWT处理
- **passlib (1.7.4)**: 密码哈希
- **bcrypt (4.0.1)**: 密码加密

### 文件存储
- **boto3 (1.38.42)**: AWS S3 SDK
- **rarfile (4.2)**: RAR文件解压

### 数据处理
- **pandas (2.2.3)**: 数据分析
- **openpyxl (3.1.5)**: Excel文件处理

## 环境配置

创建 `.env` 文件：

```bash
# 数据库配置
DATABASE_URL=mysql://username:password@host:port/database

# Redis配置
REDIS_URL=redis://localhost:6379/0

# S3配置
S3_ENDPOINT_URL=http://your-s3-endpoint
S3_AWS_ACCESS_KEY_ID=your-access-key
S3_AWS_SECRET_ACCESS_KEY=your-secret-key
S3_BUCKET_NAME=your-bucket

# JWT配置
SECRET_KEY=your-secret-key
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 应用配置
APP_NAME=DTTrip Service Operation Server
DEBUG=false
LOG_LEVEL=info
```

## 数据库迁移

```bash
# 初始化数据库
poetry run aerich init -t src.db.config.TORTOISE_ORM

# 生成迁移文件
poetry run aerich init-db

# 应用迁移
poetry run aerich upgrade
```

## 运行服务

### 开发环境
```bash
poetry run uvicorn src.app:app --host 0.0.0.0 --port 8000 --reload
```

### 生产环境
```bash
poetry run gunicorn src.app:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```

## 功能特性

- ✅ PDF转图片处理（无需系统依赖）
- ✅ 护照识别上传
- ✅ 火车票订单管理
- ✅ 酒店订单管理
- ✅ 用户认证授权
- ✅ S3文件存储
- ✅ Excel数据导入导出
- ✅ 压缩包文件处理

## 故障排除

### PDF处理问题
如果遇到PDF处理错误，确保已安装 PyMuPDF：
```bash
poetry add PyMuPDF
# 或
pip install PyMuPDF==1.26.3
```

### 数据库连接问题
检查数据库配置和网络连接：
```bash
# 测试MySQL连接
mysql -h host -P port -u username -p database

# 检查Redis连接
redis-cli -h host -p port ping
```

### 依赖冲突
如果遇到依赖冲突，建议使用虚拟环境：
```bash
# 删除现有环境
rm -rf venv

# 重新创建
python3 -m venv venv
source venv/bin/activate
pip install -r requirements-prod.txt
```

## 更多信息

- 项目文档：`docs/`
- API文档：`http://localhost:8000/docs`
- 健康检查：`http://localhost:8000/health`
