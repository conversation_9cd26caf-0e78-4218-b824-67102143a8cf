version: '3.8'

services:
  backend:
    build:
      context: .
      dockerfile: Dockerfile
      platform: linux/amd64
    image: dttrip-backend:latest
    container_name: dttrip-backend
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      - PYTHONPATH=/app
      - LOG_LEVEL=INFO
      - ENVIRONMENT=production
      - RELOAD=false
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - dttrip-network

networks:
  dttrip-network:
    driver: bridge

volumes:
  logs:
  uploads: 