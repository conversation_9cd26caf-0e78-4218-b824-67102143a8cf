# DTTrip 项目偏好设置

## 编码规范和偏好

### 日期格式规范
- **标准格式**: 所有日期字段统一使用 YYYY-MM-DD 格式
- **应用范围**: 出生日期、证件有效期、入住时间、退房时间等所有日期字段
- **前后端一致性**: 前端显示和后端存储都使用相同的YYYY-MM-DD格式
- **兼容性处理**: 支持多种输入格式但统一转换为标准格式

### 技术栈和工具
- **前端**: React + TypeScript + Vite
- **UI库**: Tailwind CSS + Shadcn/ui
- **状态管理**: React Hooks (useState, useEffect, useCallback)
- **构建工具**: npm (不是yarn)
- **代码风格**: TypeScript严格模式, ESLint规范
- **性能优化**: 使用React.memo和useCallback避免不必要的重新渲染，特别是在复杂表单组件中

### React编辑组件最佳实践 - 防止焦点丢失
- **组件定义位置**: 可编辑组件必须在主组件外部独立定义，避免父组件重新渲染时重新创建子组件
- **组件优化策略**: 
  - 使用React.memo包装可编辑组件
  - 组件内部事件处理函数使用useCallback优化
  - 字段变更处理函数使用空依赖数组[]和函数式更新
- **状态更新模式**: 
  ```typescript
  const handleFieldChange = useCallback((field, value) => {
    setEditingOrder(prev => {
      if (!prev) return prev;
      const updated = { ...prev, [field]: value };
      // 验证逻辑在函数式更新内部处理
      return updated;
    });
  }, []); // 关键：空依赖数组
  ```
- **验证逻辑集成**: 在状态更新函数内部处理实时验证，避免额外的useEffect依赖
- **成功案例参考**: 项目任务详情页面的EditableInput组件实现为标准模板

### 数据处理规范
- **类型安全**: 所有API调用都要有完整的TypeScript类型定义
- **错误处理**: 前端要有友好的错误提示，后端要有详细的日志记录
- **验证规则**: 前后端都要有数据验证，前端实时验证，后端服务端验证
- **格式一致性**: 日期、金额、电话号码等都有统一的格式规范

## 用户界面设计偏好

### 表格和列表设计
- **详情弹窗**: 使用表格式布局，参考火车票订单的设计风格
- **字段分组**: 按功能逻辑分组显示（基础信息、预订信息、联系信息、财务信息）
- **编辑体验**: 查看模式和编辑模式无缝切换，EditableCell组件统一处理

### 操作交互规范
- **确认对话框**: 危险操作（删除）要有确认对话框
- **状态反馈**: 操作要有明确的loading状态和结果提示
- **错误显示**: 验证错误要在字段下方显示，颜色使用红色

## API设计规范
- **RESTful**: 遵循REST API设计原则
- **错误处理**: 统一的错误响应格式
- **分页**: 列表接口支持分页和搜索
- **状态管理**: 订单状态要有明确的状态流转

# 项目偏好和自定义规则

## 设计偏好
- **设计风格**: 简洁、专业的企业级设计，避免过度装饰
- **颜色方案**: 
  - 主色调: 蓝色系 (#2563eb) 
  - 文字层次: 灰色系 (gray-900, gray-600, gray-500)
  - 状态色: 绿色(成功)、黄色(警告)、红色(错误)
  - 背景: 白色为主，浅灰色(gray-50)为辅
- **UI设计原则**:
  - 简洁性：避免渐变、装饰元素
  - 一致性：与项目管理界面风格保持一致
  - 专业性：企业级应用标准
  - 实用性：功能优于形式
- **UI组件库**: 基于Shadcn/ui的组件系统
- **图标库**: Lucide React图标

## 技术栈偏好
- **前端**: React + TypeScript
- **样式**: Tailwind CSS（使用标准颜色系统，避免自定义渐变）
- **状态管理**: React Hooks
- **路由**: React Router
- **API**: RESTful API with error handling

## 代码规范
- **组件结构**: 功能组件优于类组件
- **类型安全**: 严格使用TypeScript类型定义
- **错误处理**: 完善的错误边界和用户友好的错误提示
- **响应式设计**: 支持多种屏幕尺寸

## 业务逻辑偏好
- **用户体验**: 加载状态、空状态、错误状态都要有良好的界面反馈
- **数据展示**: 使用白色卡片式布局，信息层次清晰
- **操作反馈**: 所有用户操作都要有明确的反馈

## 项目特定规则
- **任务类型配色**: 
  - 酒店预订: 蓝色 (blue-600, blue-50)
  - 机票预订: 绿色 (green-600, green-50)
  - 火车票预订: 紫色 (purple-600, purple-50)
  - 交通安排: 橙色 (orange-600, orange-50)
- **状态指示**: 使用简洁的进度条和状态标签
- **卡片设计**: 白色背景，灰色边框，轻微阴影
- **金额显示**: 使用千分位分隔符格式化，突出显示重要金额信息

## 容器化部署偏好
- **部署方式**: Docker容器化部署，支持生产和开发环境分离
- **启动命令**: 
  - 本地开发: `poetry run python -m src` (在service-operation-server目录)
  - Docker生产: `poetry run python -m src` (保持一致性)
  - Docker开发: `poetry run python -m src` (保持一致性)
- **环境配置**:
  - 生产环境: docker-compose.yml
  - 开发环境: docker-compose.dev.yml
  - 端口分配: 前端3000(生产)/5173(开发), 后端8000, 数据库3306/3307, Redis6379/6380
- **自动化脚本**:
  - 部署管理: `./deploy.sh [start|start-dev|stop|restart|status|logs|cleanup|backup]`
  - 镜像构建: `./build.sh [all|frontend|backend|push|cleanup]`
- **健康检查**: 所有服务配置健康检查，确保服务可用性
- **数据持久化**: MySQL和Redis数据通过命名卷持久化
- **网络隔离**: 使用自定义Docker网络，服务间通信安全

## 后端Docker镜像偏好
- **多阶段构建**: 使用base -> dependencies -> production/development/testing的分层结构
- **安全配置**: 
  - 非root用户运行 (app:app, uid=1000, gid=1000)
  - 生产环境移除编译工具
  - 使用dumb-init处理信号
- **镜像优化**:
  - Python环境变量优化 (PYTHONDONTWRITEBYTECODE, PYTHONUNBUFFERED)
  - Poetry缓存挂载和并行安装
  - 分层缓存策略优化构建时间
- **环境区分**:
  - 生产镜像: 精简依赖，增强健康检查，60s启动时间
  - 开发镜像: 包含开发依赖，热重载支持，源码挂载
  - 测试镜像: 预配置测试环境，包含测试工具
- **构建管理**: 
  - `./build.sh prod` - 构建生产镜像
  - `./build.sh dev` - 启动开发环境 
  - `./build.sh test` - 运行容器化测试
  - `./build.sh all` - 构建所有环境镜像
- **文件管理**: 完善的.dockerignore排除构建无关文件，减小构建上下文

## 订单管理功能偏好
- **操作确认**: 所有删除操作需要用户确认，显示具体影响内容
- **状态反馈**: 操作进行中显示加载状态，防止重复操作
- **批量操作**: 支持批量删除功能，提供一键清空选项
- **编辑跳转**: 编辑操作通过URL参数跳转，支持深链接
- **颜色语义**:
  - 编辑操作: 蓝色主题 (text-blue-600, hover:bg-blue-50)
  - 删除操作: 红色主题 (text-red-600, hover:bg-red-50)
  - 查看操作: 默认灰色主题
- **操作列设计**: 使用紧凑的多按钮布局，每个按钮都有图标和提示

## 系统设置功能
- **加密存储**: 使用Fernet对称加密算法保护敏感信息（如同程管家密码）
- **配置管理**: 支持用户级别的配置项管理，每个用户都有独立的设置
- **数据库设计**: 
  - 表名: `system_settings`
  - 唯一约束: `(user_id, config_key)` 确保同一用户的配置项唯一
  - 加密字段: `config_value` 存储加密后的配置值
- **API接口**:
  - GET `/api/v1/system-settings/tongcheng-credentials` - 获取同程管家账号信息  
  - POST `/api/v1/system-settings/tongcheng-credentials` - 保存同程管家账号信息
  - 其他通用CRUD接口支持扩展配置项
- **前端界面**:
  - 路径: `/settings` 
  - 功能: 同程管家用户名/密码管理
  - 特性: 密码显示/隐藏切换、表单验证、操作反馈
- **安全特性**:
  - 环境变量配置加密密钥: `SYSTEM_SETTINGS_ENCRYPTION_KEY`  
  - 密码在传输和存储过程中全程加密
  - 支持用户级别的权限隔离

## 数据库表结构
需要管理员手动执行SQL脚本 `create_system_settings_table.sql` 创建表：
```sql
CREATE TABLE IF NOT EXISTS `system_settings` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `user_id` VARCHAR(50) NOT NULL,
    `config_key` VARCHAR(100) NOT NULL,
    `config_name` VARCHAR(200) NOT NULL,
    `config_value` TEXT NOT NULL,
    `created_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    UNIQUE KEY `uk_user_config` (`user_id`, `config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

## 酒店预订任务功能偏好
- **功能一致性**: 酒店预订任务相关功能完全参考火车票预订任务的设计和功能

### 酒店预订页面details Tab任务概览功能
- **页面结构**: 与火车票预订任务详情页面保持完全一致的布局设计
- **任务概览标题**: 使用FileText图标 + "任务概览"标题 + 刷新按钮
- **任务卡片网格**: 响应式4列网格布局，支持自适应屏幕尺寸
- **任务类型配色**: 
  - 酒店预订: 紫色主题 (🏨 icon, purple-600, bg-purple-50, border-purple-200)
  - 火车票预订: 蓝色主题 (Building icon, blue-600, bg-blue-50, border-blue-200)
  - 机票预订: 绿色主题 (✈️ icon, green-600, bg-green-50, border-green-200)
- **统计数据展示**: 4列显示已提交、处理中、预定完成、预定失败订单数量
- **状态标签系统**: 
  - 基础状态: 待处理(黄色)、已提交(蓝色)、处理中(蓝色)、已完成(绿色)、失败(红色)
  - 智能状态: 结合统计数据显示"部分失败"、"全部完成"等细化状态
- **交互设计**: 
  - 悬停效果: hover:shadow-md hover:scale-105
  - 查看详情按钮: 根据任务类型跳转到对应详情页面
  - 刷新按钮: 支持重新加载任务和统计数据

### 酒店预订任务详情功能
- **功能一致性**: 酒店预订任务详情完全参考火车票预订任务详情的设计和功能
- **页面结构**:
  - 任务信息卡片: 显示任务ID、类型、状态、订单总数
  - 搜索筛选区域: 入住人姓名、手机号码、联系人手机
  - 订单列表表格: 核心字段展示，支持分页浏览
  - 导出功能: Excel格式导出任务下所有订单
- **路由设计**: `/hotel-task-detail/:taskId` 支持直接跳转到任务详情
- **数据展示**: 表格显示核心字段，失败原因高亮显示红色
- **状态显示**: 
  - 任务状态: 已提交(蓝色)、处理中(黄色)、已完成(绿色)、失败(红色)
  - 订单状态: 待预订(灰色)、已提交(蓝色)、处理中(黄色)、已完成(绿色)、失败(红色)、验证失败(琥珀色)
- **用户交互**: 
  - 从酒店预订页面details Tab的任务列表点击"查看详情"进入
  - 支持返回上一页导航
  - 搜索功能实时生效，支持重置搜索条件
- **技术实现**:
  - 使用`hotelOrderApi.getTaskOrders`获取任务订单
  - 支持分页、搜索、导出等完整功能
  - 完全复用酒店订单的数据结构和API接口

## 火车票预订功能偏好
- **任务创建**: 自动生成任务标题格式为"火车票预订 - 日期"或"火车票预订且出票 - 日期"
- **状态管理**: 
  - initial(待提交): 灰色背景 
  - submitted(已提交): 蓝色背景
  - processing(处理中): 黄色背景
  - completed(已完成): 绿色背景
  - failed(失败): 红色背景
- **业务流程**:

## 酒店预订功能偏好
- **任务创建**: 
  - 自动生成任务标题格式为"酒店预订任务 - YYYYMMDD_HHMMSS"
  - 任务描述包含订单数量信息："批量预订 X 条酒店订单"
- **任务关联**: 
  - 预订操作必须创建或关联酒店预订任务（task_type="酒店预订"）
  - 在task_to_hotel_orders表中创建订单与任务的关联记录
  - 支持预订类型区分（book_only vs book_and_issue）
- **状态管理**: 
  - 订单状态更新：预订时设为"submitted"，预订且出票时设为"processing"
  - 关联记录状态与订单状态保持同步
  - 支持重复预订时的关联记录更新
- **任务管理**:
  - 一个项目可以有多个酒店预订任务
  - 任务包含短信通知和代订人设置
  - 自动记录任务创建时间和更新时间
- **数据完整性**:
  - 预订操作同时更新hotel_orders和task_to_hotel_orders两个表
  - 错误处理确保数据一致性
  - 详细的日志记录便于问题排查 
  - 预订按钮只处理状态为initial的订单
  - 一次预订操作创建一个任务并提交所有待提交订单
  - 同时更新train_orders和task_to_train_orders表的状态
- **用户体验**:
  - 点击预订前检查是否有待提交订单
  - 提供清晰的成功/失败反馈信息
  - 订单状态变化实时显示在表格中
- **数据完整性**:
  - 使用数据库事务确保任务创建和订单提交的原子性
  - 代订人信息验证：有代订人必须提供手机号
  - 详细的错误日志记录便于问题排查
- **项目任务详情页面**:
  - **Tab结构**: 按"继续预订" -> "所有订单" -> "任务详情"的顺序排列，提供完整的订单管理流程
  - **所有订单功能**: 
    - 使用Eye图标标识，提供完整的订单列表查看功能
    - 支持分页浏览，每页20条记录
    - 表格显示：序号、状态、出行人、证件号、出行日期、出发站、到达站、车次、座位类型、金额、操作
    - 操作按钮：查看详情、编辑订单、删除订单，颜色语义化
    - 自动数据加载：切换到所有订单tab时自动加载数据
    - 刷新功能：提供手动刷新按钮
    - 空状态显示：无订单时友好提示界面
  - **响应式设计**: 表格支持横向滚动，适配不同屏幕尺寸
  - **状态反馈**: 统一使用toast通知进行错误提示和操作反馈 

## 项目详情页面订单管理功能 (2025-01-12)

### 功能描述
为项目详情页面的"所有订单"部分实现了完整的订单管理功能，包括查看、编辑、删除单个订单的操作。

### 技术实现要点
1. **查看订单详情**：使用模态框显示订单完整信息，分6个分组展示30个字段
2. **编辑订单功能**：支持在线编辑所有业务字段，使用EditableCell组件
3. **删除订单功能**：安全删除机制，包含确认对话框和操作反馈
4. **状态管理**：selectedOrder, isViewModalOpen, isEditModalOpen, isDeleteDialogOpen等状态变量
5. **API集成**：DELETE和PUT接口用于订单删除和更新操作

### API路径
- 删除订单：`DELETE /train-order/{order_id}`
- 更新订单：`PUT /train-order/{order_id}`
- 订单查询：`GET /train-order/project/{project_id}`

### 用户体验设计
- 操作按钮颜色区分：查看(灰色)、编辑(蓝色)、删除(红色)
- 模态框支持ESC键和背景点击关闭
- 操作加载状态显示，防止重复提交
- 删除前明确确认，编辑时可取消恢复

### 实现组件
- ProjectDetailPage.tsx：主页面组件，包含订单表格和操作按钮
- EditOrderModal.tsx：独立的编辑订单模态框组件
- 状态显示函数：getOrderStatusDisplay和getOrderStatusInfo

## 火车票预订成功后自动跳转功能 (2025-01-12)

### 功能描述
在火车票预订页面（待预定标签页）实现预定和预定且出票成功后自动跳转到所有订单标签页的功能。

### 实现方案
1. **组件通信**：通过props在父子组件间传递跳转函数
2. **延迟跳转**：预定成功后延迟1秒跳转，让用户看到成功提示
3. **用户体验**：无需手动切换标签页，自动进入订单查看界面

## 酒店预订Excel导入功能偏好 (2025-01-15)

### 数据格式要求
- **Excel格式**：第二行为列名（header），第四行开始为正式数据
- **向后兼容**：同时支持第一行为列名的传统格式
- **字段数量**：61个完整的酒店业务字段映射

### 字段分类
1. **入住人信息**：姓名、国籍、性别、证件、联系方式等
2. **酒店预订信息**：目的地、酒店、房型、入住离店时间等
3. **支付财务信息**：支付方式、发票、税率、成本、手续费等
4. **联系人信息**：联系人姓名、手机号、国际区号
5. **同住人信息**：同住人完整信息（如有）
6. **对账单信息**：公司、金额、代订人、订单号等

### 重复检查规则
- **检查字段**：证件号码 + 入住时间 + 酒店名称
- **业务逻辑**：同一项目内，相同客人在同一酒店同一时间的预订视为重复

### 数据类型处理
- **字符串字段**：默认空字符串处理
- **整数字段**：房间数量、序号等使用safe_int转换
- **小数字段**：金额、税率等使用safe_decimal转换
- **布尔字段**：是否含早、是否团房等使用parse_boolean转换
- **日期字段**：入住时间、证件有效期等使用parse_excel_date处理

### 错误处理策略
- **必填字段验证**：入住人姓名、证件、入住时间、酒店名称等必填
- **格式验证**：证件号码、手机号、邮箱格式检查
- **证件类型特殊处理**：非身份证需要额外字段（姓、名、国籍、性别、出生日期、有效期）
- **统计反馈**：提供详细的导入成功、失败、跳过统计信息

## 酒店预订功能完整实现 (2025-01-12)

### 功能描述
成功为DTTrip项目实现了完整的酒店预订功能，与火车票预订功能完全对等，提供相同的用户体验和功能完整性。

### 前端实现要点
1. **API服务层**：创建了完整的`hotel.ts` API文件，包含61个业务字段的接口定义和13个API方法
2. **类型安全**：正确处理`ApiResponse<T>`类型，所有API调用都提取`response.data`
3. **酒店预订页面**：`/hotel-booking/${projectId}` 完全重构，包含4个Tab（待预订、所有订单、异常订单、预订任务）

### Tab功能详情
- **待预订Tab**：Excel文件上传、订单统计展示（总订单、已完成、失败订单、总金额）、清空订单功能
- **所有订单Tab**：完整的订单列表、搜索筛选（姓名、手机号）、分页导航、状态显示、操作按钮（查看、编辑）
- **异常订单Tab**：异常订单展示、失败原因显示、异常优先排序、卡片式布局
- **预订任务Tab**：预订任务管理界面（预留扩展）

### 技术特点
- **颜色主题**：酒店预订统一使用蓝色主题 (`text-blue-600`, `bg-blue-50`, `border-blue-500`)
- **状态管理**：完整的状态管理系统，包括加载状态、分页、搜索、筛选、错误处理
- **响应式设计**：支持不同屏幕尺寸，表格横向滚动，移动端友好
- **用户反馈**：完善的Toast通知系统，操作加载状态，空状态和错误状态显示

### API集成验证
- 后端酒店订单API完全正常工作
- 统计API返回正确数据格式
- 订单查询、创建、更新、删除功能完整
- Excel上传和验证功能就绪

### 页面路由配置
项目详情页面的"开始预定酒店"和"查看详情"按钮正确链接到酒店预订专用页面 `/hotel-booking/${projectId}`，实现了完整的业务流程闭环。

## 酒店预订按钮链接配置 (2025-06-25)

### 功能描述
在项目详情页面的任务类型卡片中，为酒店预订配置专用的链接跳转，直接导航到酒店预订页面。

### 技术实现
1. **路由配置**：酒店预订使用专门的路由`/hotel-booking/:projectId`
2. **导航逻辑**：修改`handleStartTaskType`和`handleViewTaskType`函数
   - 酒店预订类型：直接跳转到`/hotel-booking/${projectId}`
   - 其他类型：跳转到通用的项目任务详情页面
3. **颜色主题**：按照项目偏好配置正确的颜色方案
   - 酒店预订：蓝色主题 (blue-600, blue-50, border-blue-500)
   - 火车票预订：紫色主题 (purple-600, purple-50, border-purple-500)
   - 机票预订：绿色主题 (green-600, green-50, border-green-500)

### 用户体验
- 点击"开始酒店预订"按钮直接进入酒店订单管理界面
- 点击"查看详情"按钮也进入酒店预订页面
- 保持与其他预订类型一致的操作体验
- 正确的颜色区分，便于用户识别不同预订类型

### 相关文件
- `service-operation-frontend/src/pages/project/ProjectDetailPage.tsx`: 修改导航逻辑和颜色配置

## S3 OSS云存储集成 (2025-06-24)

### 功能描述
为DTTrip项目的护照识别功能集成了S3 OSS云存储，将护照图片上传到S3服务器并将返回的URL存储到数据库的uploaded_image_url字段中。

### 技术实现
1. **依赖管理**：
   - 在pyproject.toml和requirements.txt中添加boto3相关依赖
   - 支持Poetry和pip两种依赖管理方式
   - 使用国内镜像源提高安装速度

2. **环境配置**：
   - S3配置项：endpoint、access_key、secret_key、bucket_name等
   - 支持多环境配置：.local.env、.qa.env、.prod.env

3. **S3服务模块**：
   - 创建`src/services/s3_service.py`封装所有S3操作
   - 支持文件上传、删除、连接测试等功能
   - 智能上传：大于50MB使用分段上传
   - 时间分区存储：按YYYY/MM/DD格式组织文件

4. **护照上传端点优化**：
   - 双重保障策略：先保存本地，再上传S3
   - 优先使用S3 URL，失败时回退本地URL
   - 支持压缩包和普通文件两种上传方式

### 容错机制
- S3配置不完整时自动禁用S3功能
- S3上传失败时自动回退到本地存储
- 详细的错误日志和用户友好提示

### 文件路径处理修复
修复了护照识别任务中文件路径重复的问题：
- **问题**：路径中出现重复的uploads目录
- **原因**：路径处理逻辑假设URL格式错误
- **修复**：支持多种URL格式（绝对路径、相对路径、S3 URL）
- **增强**：添加详细的路径处理调试日志

### 相关文件
- `src/services/s3_service.py`：S3服务封装
- `src/api/passport/endpoints.py`：护照上传端点
- `src/services/passport_task_service.py`：路径处理修复
- `PASSPORT_PATH_FIX_SUMMARY.md`：路径修复详细文档
- `S3_UPLOAD_IMPLEMENTATION.md`：S3集成详细文档

### 技术实现
- **接口定义**：`TrainBookingContentProps`包含可选的跳转回调函数
- **父组件传递**：`ProjectTaskDetailPage`通过props传递`handleTabChange('all-orders')`
- **子组件调用**：`TrainBookingContent`在预定成功后调用跳转函数
- **时序控制**：使用`setTimeout`延迟1秒执行跳转

### 用户流程
1. 用户在待预定标签页点击"预定"或"预定且出票"
2. 操作成功后显示成功提示
3. 1秒后自动跳转到所有订单标签页
4. 用户可以立即查看刚提交的订单状态

## 项目任务详情页面导出Excel功能 (2025-01-12)

### 导出功能统一化
为项目任务详情页面的三个标签页（待预定、所有订单、异常订单）的导出Excel功能添加了统一的标题行设计：

1. **标题行设计**：
   - 第一行：项目名称 + 订单类型标识（如"项目名称 - 所有订单明细"）
   - 合并单元格，浅蓝色背景(E3F2FD)，14号加粗字体，居中对齐
   - 第二行：列名，灰色背景(F3F4F6)，10号加粗字体，居中对齐
   - 第三行开始：数据行，白色背景，10号字体，左对齐

2. **技术实现**：
   - 使用XLSX.utils.aoa_to_sheet创建空工作表
   - 使用sheet_add_aoa分别添加标题行、列名行、数据行
   - 使用!merges合并标题行单元格
   - 统一的边框和字体样式设置

3. **文件命名规则**：
   - 待预定："火车票订单_项目ID_筛选条件_时间戳.xlsx"
   - 所有订单："火车票订单_项目ID_筛选条件_时间戳.xlsx" 
   - 异常订单："异常订单_项目名称_日期.xlsx"

4. **导出功能全面优化**（2025-01-12补充）：
   - **标题优化**：待预定导出使用项目名称而非项目编号：`${project?.project_name || '项目' + projectId} - 待预定订单明细`
   - **数据简化**：所有导出功能删除"是否删除"列，包括：
     - TrainBookingContent.tsx（待预定导出）
     - ProjectTaskDetailPage.tsx（所有订单、异常订单导出）
     - TaskOrderDetailPage.tsx（任务订单导出）
   - **列宽调整**：移除对应的列宽配置，优化表格布局

5. **用户体验**：
   - Excel文件更加专业，标题清晰，使用实际项目名称
   - 表格结构层次分明，便于阅读和打印
   - 导出数据更简洁实用，所有导出功能都删除了用户不需要的"是否删除"列
   - 向后兼容，支持无标题的传统格式

### 修改文件
- `service-operation-frontend/src/pages/ProjectTaskDetailPage.tsx`
- `service-operation-frontend/src/components/booking/TrainBookingContent.tsx`

## 项目任务详情页面订单管理功能 (2025-01-12) - 修正版

### 功能描述
在项目任务详情页面（ProjectTaskDetailPage.tsx）的"所有订单"tab中实现了正确的订单管理功能，包括查看、编辑、删除单个订单的操作。

### 实现位置修正
- ❌ 错误：最初在ProjectDetailPage.tsx中实现
- ✅ 正确：实际在ProjectTaskDetailPage.tsx中实现
- 说明：project-detail页面不需要订单列表，订单操作功能应在project-task-detail页面

### 技术实现要点
1. **查看订单详情**：使用模态框显示订单完整信息，分4个分组展示：基本信息、出行人信息、行程信息、车次信息
2. **编辑订单功能**：使用独立的EditOrderForm组件，支持在线编辑所有业务字段，包含座位类型下拉选择
3. **删除订单功能**：安全删除机制，包含确认对话框显示订单关键信息，防止误删
4. **状态管理**：selectedOrder, isViewModalOpen, isEditModalOpen, isDeleteDialogOpen等状态变量
5. **API集成**：正确的API路径为/train-order/{order_id}，DELETE和PUT接口用于删除和更新操作

### 组件结构
- ProjectTaskDetailPage.tsx：主页面组件，包含"所有订单"tab和订单操作逻辑
- EditOrderForm.tsx：独立的编辑订单表单组件，支持完整的字段编辑
- 状态显示函数：getOrderStatusDisplay和getOrderStatusInfo

### 用户体验设计
- 操作按钮颜色区分：查看(灰色)、编辑(蓝色)、删除(红色)
- 模态框支持X按钮关闭
- 操作加载状态显示，防止重复提交
- 删除前显示订单详细信息进行确认
- 操作成功后自动刷新订单列表并提供toast反馈

### 关键设计点
- 订单管理功能集成到任务详情页面，符合业务流程
- 编辑表单包含座位类型下拉选择（硬座、硬卧、软卧、二等座、一等座、商务座、特等座）
- 删除确认对话框显示关键订单信息，确保用户明确知道要删除的内容
- 所有操作都有完善的错误处理和用户反馈机制

### 查看订单详情界面优化 (2025-01-12)
- **展示方式**: 使用表格形式展示，每行显示两个字段，信息密度更高
- **分组结构**: 按照客户基本信息、行程信息、业务信息三个分组组织
- **视觉设计**: 
  - 分组标题使用下边框分隔，层次清晰
  - 表格行使用浅灰色边框分隔，易于阅读
  - 字段标签使用深灰色，内容使用黑色，对比明确
- **内容组织**:
  - **客户基本信息**: 出行人姓名、证件号码、手机号、订单状态
  - **行程信息**: 出行日期、车次、出发站、到达站、出发时间、到达时间、座位类型
  - **业务信息**: 订单号、序号、金额、成本中心
- **重点信息突出**: 车次使用蓝色显示，金额使用绿色显示，状态标签保持原有颜色设计 

### 订单详情完整字段显示功能 (2025-01-12)

**功能描述**：
实现了查看订单详情时显示数据库中所有业务字段的功能，提供完整的订单信息视图。

**设计偏好**：
1. **分组显示逻辑**：
   - 客户基本信息：旅客信息、证件信息、联系方式、订单状态
   - 行程信息：出行日期、车次、站点、时间、座位类型
   - 业务信息：订单号、金额、成本中心、联系人、审批等业务字段

2. **字段完整性**：
   - 显示TrainOrder模型的所有业务字段（共约30个字段）
   - 排除系统自动字段：id, created_at, updated_at, is_deleted
   - 特殊字段处理：fail_reason和ticket_sms仅在有值时显示

3. **视觉设计偏好**：
   - 表格形式：两列数据展示，清晰的字段标签对齐
   - 颜色强调：车次号蓝色、金额绿色、状态彩色标签
   - 分组标题：明确的业务分组，使用边框分隔
   - 空值处理：显示"-"而不是空白，保持视觉一致性

4. **交互设计**：
   - 响应式布局：适配不同屏幕尺寸
   - 特殊内容：出票短信使用滚动区域，失败原因红色警示
   - 字体选择：订单号、账单号使用等宽字体（font-mono）

5. **数据完整性**：
   - 前端接口定义与后端模型完全对应
   - 支持所有可选字段的显示
   - 提供完整的订单数据追溯能力 

### 订单状态显示UI偏好 (2025-01-12)

**状态显示设计偏好**：
1. **置顶原则**: 重要状态信息始终在详情页面顶部显示，优先级最高
2. **颜色语义化**: 
   - 灰色 - 初始/待处理状态
   - 蓝色 - 已提交/进行中状态  
   - 黄色 - 处理中/警告状态
   - 绿色 - 成功/完成状态
   - 红色 - 失败/错误状态

3. **视觉增强元素**:
   - 圆点状态指示器 (2x2像素) 提供快速视觉识别
   - 圆形徽章背景增加边框效果
   - 左边框色彩引导 (4px宽度)
   - 浅色背景区域突出重要信息

4. **布局设计**: 
   - 状态信息左对齐，序号/ID右对齐
   - 灰色背景卡片式设计
   - 合理的内边距 (16px) 确保舒适阅读
   - 与整体界面风格保持一致

5. **状态标签规范**:
   - 圆形背景 + 对应颜色文字 + 相同颜色边框
   - 前置圆点指示器增强视觉识别
   - 中文状态名称清晰易懂
   - 统一的字体大小和间距 

### 表格样式设计偏好 (2025-01-12)

**详情表格设计偏好**：
1. **边框和分隔线**: 
   - 外边框增强: `border-2 border-gray-300 shadow-sm` (2px宽度，中灰色，带阴影)
   - 圆角效果: `rounded-lg overflow-hidden`
   - 行分隔线: `border-b border-gray-200`
   - 列分隔线: `border-r border-gray-200`

2. **背景色差异化**:
   - 字段标签列: 浅灰色背景 (`bg-gray-50`)
   - 数据内容列: 白色背景 (默认)
   - 创建清晰的视觉层次和对比度

3. **内边距规范**:
   - 水平内边距: `px-4` (16px)
   - 垂直内边距: `py-3` (12px)
   - 确保内容舒适间距和整齐对齐

4. **表格结构**:
   - 四列布局: 字段-内容-字段-内容 (1:1:1:1)
   - 响应式宽度: `w-1/4` 等比例分配
   - 交替的背景颜色提升可读性

5. **立体效果**:
   - 外边框使用2px宽度突出表格边界
   - 轻微阴影效果增强层次感
   - 与页面背景形成明确的视觉分离

6. **特殊处理**:
   - 最后一行动态移除底边框
   - 跨列内容保持字段列样式
   - 特殊字段(失败原因、短信)红色和滚动区域 

## 国内网络环境部署偏好 (2025-01-12)
- **部署策略**: 优先使用简化部署方案，避免Docker镜像拉取问题
- **镜像源配置**: 
  - 中科大镜像源: `https://docker.mirrors.ustc.edu.cn`
  - DockerProxy: `https://dockerproxy.com`
  - 南京大学: `https://docker.nju.edu.cn`
  - 上海交大: `https://docker.mirrors.sjtug.sjtu.edu.cn`
- **部署脚本**:
  - 简化部署: `./deploy-simple.sh` (推荐，无需Docker)
  - Docker部署: `./deploy-china.sh` (使用国内镜像源)
  - 镜像源配置: `./configure-docker-mirror.sh`
- **服务管理**:
  - 启动服务: `./deploy-simple.sh`
  - 停止服务: `./stop-services.sh`
  - 前端地址: http://localhost:3000
  - 后端地址: http://localhost:8000
- **故障排除**:
  - 网络不稳定时优先使用简化部署
  - Docker镜像拉取失败时配置国内镜像源
  - 端口冲突时检查并停止占用进程
- **文档**:
  - 快速启动: `QUICK_START.md`
  - 国内部署指南: `CHINA_DEPLOYMENT_GUIDE.md`
  - 详细部署说明: `DEPLOYMENT_INSTRUCTIONS.md` 

## HTTPS安全配置偏好 (2025-01-12)
- **SSL/TLS协议**: 支持TLS 1.2和1.3，禁用不安全的旧协议
- **加密套件**: 使用现代ECDHE加密套件，支持前向保密
- **安全头部**: 
  - HSTS: 强制HTTPS访问，防止降级攻击
  - X-Frame-Options: 防止点击劫持
  - X-Content-Type-Options: 防止MIME类型嗅探
  - X-XSS-Protection: XSS保护
- **端口配置**:
  - HTTP: 80端口（自动重定向到HTTPS）
  - HTTPS: 443端口
  - Docker映射: 3000:80, 3443:443
- **证书管理**:
  - 开发/测试: 使用自签名证书 `./generate-ssl-cert.sh`
  - 生产环境: 使用Let's Encrypt或商业证书
  - 证书路径: `/etc/nginx/ssl/server.crt` 和 `/etc/nginx/ssl/server.key`
- **HTTP/2支持**: 启用HTTP/2协议提升性能
- **会话管理**: 配置SSL会话缓存和超时
- **Docker挂载**: SSL证书通过docker-ssl目录挂载到容器
- **访问地址**:
  - HTTPS: https://localhost:3443
  - HTTP重定向: http://localhost:3000 -> https://localhost:3443 

## 前端代理配置偏好 (2025-01-16)
- **开发环境代理**: 使用Vite代理将`/api/*`请求转发到`http://localhost:8000`
- **环境变量管理**:
  - 开发环境：不设置`VITE_API_BASE_URL`，让constants.ts自动使用`/api`代理路径
  - 生产环境：设置`VITE_API_BASE_URL`为完整的后端URL
  - QA环境：在`.qa.env`中设置`VITE_API_BASE_URL=https://soa.qa.dttrip.cn/api`
- **代理配置**: vite.config.ts中配置详细的代理日志和错误处理
- **API基础URL逻辑**:
```typescript
export const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 
  (import.meta.env.DEV ? '/api' : 'http://localhost:8000/api');
```
- **调试工具**: 提供`test-sso.html`、`test-api-paths.html`等测试页面验证代理配置
- **API路径规范**: 所有API路径末尾必须带斜杠，避免FastAPI重定向导致CORS预检失败
- **启动顺序**: 先启动后端服务，再启动前端服务，确保代理目标可用 

## 项目结构
- 前端项目位于 `service-operation-frontend` 目录下
- 后端项目位于 `service-operation-server` 目录下
- 这是一个前后端分离的项目结构

## 开发规范
- 始终使用中文简体回复
- 删除未使用的变量和函数，保持代码整洁
- 使用TypeScript进行类型检查
- 遵循React函数组件和Hooks的最佳实践

## 功能特性
- 项目管理和任务详情页面
- 火车票订单管理系统
- Excel导入导出功能
- 搜索和过滤功能
- 分页显示
- 订单状态管理

## 最新功能实现
### 搜索功能（2024年最新）
- 支持按出行人姓名、手机号、联系人手机号进行搜索
- 实现位置：
  - ProjectTaskDetailPage.tsx 的"所有订单"列表
  - TrainBookingContent.tsx 的订单列表
- 搜索界面包含三个输入框和搜索/清空按钮
- 支持回车键快速搜索，响应式布局 

## 搜索功能设计偏好 (2025-01-16)
- **空状态处理**: 根据搜索条件动态显示不同的空状态消息
  - 有搜索条件且无结果: "没有找到符合搜索条件的订单"
  - 无搜索条件且无数据: 显示相应的引导操作提示
- **搜索界面持久性**: 搜索无结果时保持搜索条件和界面显示，方便用户调整
- **用户反馈**: 明确区分"无数据"和"搜索无结果"两种场景
- **搜索条件**: 支持多字段组合搜索（出行人姓名、手机号、联系人手机号）
- **搜索交互**: 支持回车键快速搜索，提供清空按钮重置条件 

## Excel导入字段映射规范 (2025-01-13)

### 字段名称一致性要求
为避免字段映射不一致导致的验证错误，制定以下规范：

1. **统一字段名称**：
   - Excel模板、验证函数、上传函数中的字段名必须完全一致
   - 优先使用完整的字段名称（如"联系人手机号码"而非"联系人手机号"）
   - 如有历史兼容性需求，使用 `or` 逻辑处理多种字段名

2. **字段映射检查清单**：
   - ✅ Excel创建脚本 (`create_real_template_excel.py`)
   - ✅ 验证函数 (`validate_excel_data`)
   - ✅ 上传函数 (`upload_excel`)
   - ✅ 数据库模型定义 (`train_order.py`)

3. **兼容性处理原则**：
   ```python
   # 推荐的兼容性处理方式
   'field_name': safe_str(row.get('标准字段名')) or safe_str(row.get('历史字段名'))
   ```

4. **预防措施**：
   - 新增字段时必须同时更新所有相关文件
   - 定期进行字段名一致性检查
   - 建立自动化测试覆盖字段映射验证

### 已修复的字段映射问题
- **联系人手机号码**: 兼容 `'联系人手机号码'` 和 `'联系人手机号'` 两种字段名
- **解决方案**: 在验证和上传函数中使用双重查找逻辑确保兼容性

## 项目任务详情页面异常订单功能 (2025-01-13)

### 功能描述
在项目任务详情页面（ProjectTaskDetailPage.tsx）的"所有订单"tab后增加了"异常订单"tab，专门显示状态为异常（failed）的订单。

### 实现内容
1. **Tab结构扩展**：
   - 更新TabType类型定义：`'booking' | 'all-orders' | 'exception-orders' | 'details'`
   - 在"所有订单"后添加"异常订单"tab，使用AlertTriangle图标
   - Tab显示异常订单数量标记（红色徽章）

2. **异常订单数据管理**：
   - 添加异常订单专用状态变量：exceptionOrders, exceptionOrdersLoading, exceptionOrdersPage等
   - 专用搜索状态：exceptionSearchTravelerName, exceptionSearchMobilePhone, exceptionSearchContactPhone
   - loadExceptionOrders函数：通过API参数`status: 'failed'`只获取失败状态的订单

3. **界面设计特色**：
   - 统计卡片使用红色主题（border-red-200, bg-red-100, text-red-600）强调异常状态
   - 表格头部使用红色背景（bg-red-50, text-red-900）区别于正常订单
   - 搜索框聚焦时使用红色边框（focus:ring-red-500）保持主题一致性
   - 搜索按钮使用红色主题（bg-red-600 hover:bg-red-700）

4. **功能完整性**：
   - 支持按出行人姓名、手机号、联系人手机号搜索异常订单
   - 表格重点显示失败原因列，支持长文本截断和tooltip显示
   - 支持导出异常订单Excel文件，文件名包含"异常订单"前缀
   - 分页功能支持大量异常订单数据的浏览
   - 查看、编辑、删除操作与所有订单功能保持一致

5. **空状态设计**：
   - 当无异常订单时显示绿色CheckCircle图标和积极提示信息
   - 文案："暂无异常订单，该项目没有失败的订单，所有订单处理正常"

### 技术实现特点
- 复用现有的订单操作函数（handleViewOrder, handleEditOrder, handleDeleteOrder）
- 通过API查询参数过滤状态，避免前端数据过滤的性能问题
- 自动数据加载：切换到异常订单tab时通过useEffect自动加载数据
- 统计信息专注于异常订单相关指标：异常订单数、失败状态标识、涉及金额

### 用户体验优化
- 异常订单表格突出显示失败原因，帮助快速定位问题
- 使用一致的红色主题贯穿整个异常订单功能
- 保持与所有订单tab相同的操作逻辑，降低学习成本
- 支持按Enter键快速搜索，提升操作效率 

## 预订任务卡片展示优化 (2025-01-13)

### 功能描述
将项目任务详情页面的"预订任务"tab改为类似于任务概览的卡片形式展示，模仿图片中的设计风格，包含统计信息和进度条。

### 设计改进
1. **卡片式布局**：
   - 采用3列网格布局（响应式：大屏幕3列，中等屏幕2列，小屏幕1列）
   - 每个任务类型独立卡片：火车票预订（蓝色）、飞机票预订（绿色）、酒店预订（紫色）
   - 卡片具有阴影效果和hover动画过渡

2. **统计信息展示**：
   - 订单数、总人数、总金额三个核心指标
   - 金额格式化显示（Y47K形式）
   - 动态获取真实统计数据（通过API）

3. **进度可视化**：
   - 横向进度条显示预订完成进度
   - 进度百分比数值显示
   - 基于completed_orders/total_orders计算

4. **状态智能识别**：
   - 暂无数据（灰色）：没有订单
   - 待确认（黄色）：有订单但未完成
   - 进行中（蓝色）：部分订单已完成
   - 已完成（绿色）：所有订单已完成
   - 部分失败（红色）：存在失败订单

5. **操作按钮优化**：
   - 继续预订（次要按钮）：用于已有任务的操作
   - 查看详情/开始预订（主要按钮）：根据状态动态变化
   - 颜色主题与任务类型保持一致

### 技术实现
1. **数据获取**：
   - getTaskStatistics()函数获取每个任务的统计数据
   - 支持并行获取多个任务的统计信息
   - 错误处理机制确保稳定性

2. **格式化函数**：
   - formatAmountDisplay()：金额格式化（Y47K）
   - getBookingProgress()：进度计算
   - getTaskStatusLabel()：状态标签生成

3. **响应式设计**：
   - 使用Tailwind CSS网格系统
   - 支持移动端、平板、桌面三种布局
   - 统一的卡片内边距和间距

### 用户体验提升
- 一目了然的任务概览界面
- 直观的进度和状态可视化
- 统一的设计语言和交互模式
- 快速访问相关功能的入口

这个改进大大提升了任务管理的用户体验，让用户能够快速了解项目中各类预订任务的整体情况和进度状态。 

## DTTrip 项目设置和偏好

## 项目结构
- 前端代码位于 service-operation-frontend 目录
- 后端代码位于 service-operation-server 目录
- 项目是前后端分离架构

## 技术栈
- 前端：React + TypeScript + Vite + Tailwind CSS + shadcn/ui
- 后端：Python + FastAPI + Tortoise ORM
- 数据库：MySQL

## 项目任务详情页面功能更新 (2025-01-12)

### 完善了基于project_tasks表的任务展示
**功能描述**:
更新了项目任务详情页面的"预定任务"tab，使其完全基于project_tasks表的内容进行展示。

**实现内容**:
1. **数据源统一**: 展示所有任务类型，不只是火车票预订，包括：
   - 火车票预订
   - 飞机票预订/机票预订
   - 酒店预订
   - 交通安排
   - 会议安排
   - 其他类型

2. **project_tasks表字段完整展示**:
   - 任务ID (task_id)
   - 任务类型 (task_type)
   - 任务标题 (task_title)
   - 任务描述 (task_description)
   - 任务状态 (task_status) - 基于数据库状态显示
   - 创建人 (creator_name)
   - 创建时间 (created_at)
   - 短信通知 (sms_notify)
   - 代订人设置 (has_agent, agent_phone)

3. **状态映射系统**:
   - pending: 待处理
   - submitted: 已提交
   - processing/in_progress: 进行中
   - completed: 已完成
   - failed: 失败
   - cancelled: 已取消

4. **多任务类型支持**:
   - 每种任务类型有独特的颜色主题和图标
   - 支持任务类型的动态配置
   - 统一的卡片布局适配所有任务类型

5. **统计数据集成**:
   - 结合task_to_train_orders表提供统计信息
   - 显示订单数、总人数、总金额
   - 动态进度条展示完成情况
   - 智能状态判断（结合数据库状态和统计信息）

6. **交互优化**:
   - 根据任务类型提供不同的操作按钮
   - 火车票预订可直接跳转到相关tab
   - 其他任务类型保留扩展接口

**技术实现细节**:
- loadBookingTasks函数现在获取所有项目任务
- renderProjectTaskCard函数支持多种任务类型
- getTaskTypeConfig函数提供任务类型配置
- getProjectTaskStatusDisplay函数映射数据库状态

**用户体验提升**:
- 完整显示project_tasks表的所有重要字段
- 直观的视觉区分不同任务类型
- 状态信息准确反映数据库实际状态
- 支持project_tasks表的扩展字段展示

这次更新确保了任务展示完全基于project_tasks表的真实数据，为后续添加更多任务类型奠定了基础。

## 异常订单Tab实现 (2025-01-12)
**功能描述**:
在项目任务详情页面添加了"异常订单"Tab，专门展示状态为'failed'的订单。

**实现内容**:
1. **独立的异常订单状态管理**:
   - exceptionOrders、exceptionOrdersLoading等状态变量
   - 独立的分页系统 (exceptionOrdersPage, exceptionOrdersTotal)
   - 专门的搜索功能 (exceptionSearchTravelerName等)

2. **视觉设计强调异常**:
   - 红色主题配色突出异常状态
   - AlertTriangle图标表示警告
   - 红色徽章显示异常订单数量

3. **功能特性**:
   - 只显示order_status='failed'的订单
   - 支持按出行人姓名、手机号、联系人手机号搜索
   - 独立的Excel导出功能
   - 与全部订单Tab完全独立的状态管理

4. **自动加载机制**:
   - 切换到异常订单Tab时自动加载数据
   - 初始页面加载时计算异常订单数量用于徽章显示

## 预订任务卡片化展示优化 (2025-01-12)
**功能描述**:
将项目详情页面的预订任务展示改为卡片格式，按时间倒序显示所有已提交的火车票预订任务。

**实现内容**:
1. **数据获取方式**:
   - 使用ProjectTaskService.getTasksByProject API
   - 过滤task_type为'火车票预订'的任务
   - 按created_at字段倒序排列

2. **卡片展示内容**:
   - 任务基本信息：任务名称、描述、状态
   - 统计数据：订单数量、总人数、总金额
   - 进度条：显示预订完成百分比
   - 操作按钮：继续预订、查看详情

3. **状态逻辑**:
   - 通过getTaskStatistics获取统计数据
   - 智能判断任务状态（无数据、进行中、已完成、部分失败）
   - 金额格式化显示（超过1000显示为K格式）

4. **自动加载机制**:
   - 切换到详情Tab时自动调用loadBookingTasks
   - 支持手动刷新功能

## 卡片布局优化 (2025-01-12)
**功能描述**:
根据提供的设计图重新设计了任务卡片布局，采用3列响应式网格布局，包含丰富的统计信息和进度可视化。

**技术实现**:
1. **响应式网格布局**:
   - 桌面端：grid-cols-3（3列）
   - 平板端：md:grid-cols-2（2列）
   - 移动端：grid-cols-1（1列）

2. **多任务类型支持**:
   - 火车票预订（蓝色主题）
   - 飞机票预订（绿色主题）
   - 酒店预订（紫色主题）
   - 每种类型有独特的图标和配色方案

3. **数据统计与格式化**:
   - getTaskStatistics函数获取真实统计数据
   - formatAmountDisplay函数格式化金额（Y47K格式）
   - getBookingProgress函数计算完成百分比

4. **智能状态系统**:
   - 无数据：灰色 "暂无数据"
   - 待确认：黄色 "待确认"  
   - 进行中：蓝色 "进行中"
   - 已完成：绿色 "已完成"
   - 部分失败：红色 "部分失败"

5. **进度可视化**:
   - 水平进度条显示完成百分比
   - 不同任务类型使用对应主题色
   - 平滑的CSS过渡动画效果

6. **操作按钮设计**:
   - "继续预订"按钮（浅色主题）
   - "查看详情"/"开始预订"按钮（主题色）
   - 根据任务状态动态调整按钮文本和行为

**视觉设计原则**:
- 卡片阴影效果增强层次感
- 悬停效果提升交互体验
- 色彩区分不同任务类型
- 统计数据突出显示
- 状态标签醒目易识别

这次优化使任务概览更加直观和信息丰富，用户可以快速了解各项任务的进展情况。

## 卡片现代化设计优化 (2025-01-12)
**功能描述**:
对任务卡片进行现代化设计改造，使其更紧凑、简洁，提升视觉体验和信息密度。

**设计改进**:
1. **尺寸优化**:
   - 内边距从 p-6 减少到 p-4
   - 间距统一缩小 (mb-4 → mb-3, gap-4 → gap-3)
   - 图标尺寸优化 (h-5 w-5 → h-4 w-4)
   - 字体大小适当缩小 (text-2xl → text-lg)

2. **布局密度提升**:
   - 网格从 3列 改为 4列 (lg:grid-cols-4)
   - 卡片间距缩小 (gap-6 → gap-4)
   - 移除任务描述显示，减少视觉噪音
   - 任务ID信息简化

3. **交互体验优化**:
   - 删除"继续处理"按钮，简化操作
   - 保留单一"查看详情"按钮，带Eye图标
   - 增加hover缩放效果 (hover:scale-105)
   - 状态标签改为圆角设计 (rounded-full)

4. **信息展示精简**:
   - 标签文字简化 ("订单数" → "订单", "总人数" → "人数", "总金额" → "金额")
   - 创建时间格式简化 (只显示月日)
   - 特殊标记紧凑化 (短信通知/代订人显示为小标签)
   - 进度条高度减少 (h-2 → h-1.5)

5. **视觉层次优化**:
   - 状态标签使用 whitespace-nowrap 防止换行
   - 任务标题使用 truncate 防止溢出
   - 特殊功能标记使用彩色背景小标签显示
   - 按钮增加透明度hover效果

**技术细节**:
- 响应式布局：移动端1列，平板3列，桌面4列
- 卡片阴影等级调整 (hover:shadow-lg → hover:shadow-md)
- 添加 overflow-hidden 确保缩放效果不超出边界
- 图标尺寸统一调整，保持视觉一致性

**用户体验提升**:
- 更高的信息密度，一屏显示更多任务
- 简化的操作流程，减少决策负担
- 现代化的视觉效果，提升产品质感
- 保持功能完整性的同时优化空间利用

这次现代化改造使任务卡片更加紧凑、清晰，适合大量任务的快速浏览和管理。 

## 任务订单详情页面功能 (2025-01-12)

### 功能描述
创建了专门的任务订单详情页面（TaskOrderDetailPage.tsx），用于展示特定任务的所有关联订单，提供完整的订单管理功能。

### 核心功能实现
1. **任务信息展示**：
   - 任务ID、任务类型、任务状态、订单总数
   - 使用ProjectTaskService.getTaskByTaskId()获取任务详细信息
   - 任务信息卡片布局，4列网格展示关键信息

2. **订单列表管理**：
   - 基于task_to_train_orders表关联查询订单
   - 支持分页显示，每页20条记录
   - 完整的搜索功能：出行人姓名、手机号、联系人手机号
   - 表格展示13个关键字段：序号、状态、出行人、手机号、出行日期、出发站、到达站、车次、座位类型、联系人、金额、创建时间、操作

3. **订单操作功能**：
   - 查看详情：模态框展示订单基本信息和行程信息
   - 编辑订单：集成EditOrderForm组件进行在线编辑
   - 删除订单：安全删除机制，包含确认对话框
   - 导出Excel：支持将订单数据导出为Excel文件

### 技术实现要点
1. **后端API扩展**：
   - 新增`GET /train-order/task/{task_id}`端点
   - 通过task_to_train_orders表关联查询
   - 支持状态筛选、姓名搜索、手机号搜索等参数

2. **前端API集成**：
   - trainOrderApi.getOrdersByTask()方法
   - 支持分页、搜索、筛选参数
   - 返回TrainOrderListResponse格式数据

3. **路由配置**：
   - 路由路径：`/task-orders/:taskId`
   - 在App.tsx中添加ProtectedRoute保护
   - 从项目任务详情页面的"查看详情"按钮跳转

4. **状态管理**：
   - task, orders, loading, error等核心状态
   - 搜索状态：searchTravelerName, searchMobilePhone, searchContactPhone
   - 操作状态：selectedOrder, isViewModalOpen, isEditModalOpen, isDeleteDialogOpen

### 用户体验设计
1. **页面导航**：
   - 面包屑导航，支持返回上一页
   - 页面标题显示任务名称和任务ID
   - 刷新按钮和导出Excel按钮

2. **搜索和过滤**：
   - 3个搜索字段的卡片布局
   - 搜索按钮和清空按钮
   - 实时搜索结果更新

3. **状态反馈**：
   - 加载状态显示旋转动画
   - 空状态显示友好提示界面
   - 错误状态显示错误信息和返回按钮
   - 操作成功/失败的toast提示

4. **模态框设计**：
   - 查看详情：只读模式，展示关键信息
   - 编辑订单：集成EditOrderForm，支持保存和取消
   - 删除确认：显示具体删除内容，防止误操作

### 数据流程
1. **页面加载**：获取任务信息 → 加载订单列表 → 渲染界面
2. **搜索操作**：输入搜索条件 → 重新加载订单列表 → 更新显示
3. **订单操作**：选择订单 → 打开模态框 → 执行操作 → 刷新列表
4. **导出功能**：格式化订单数据 → 生成Excel文件 → 下载保存

### 文件结构
- `TaskOrderDetailPage.tsx`：主页面组件
- `App.tsx`：路由配置更新
- `trainOrder.ts`：API接口扩展
- `train_order/endpoints.py`：后端API实现

### 跳转集成
- 从ProjectTaskDetailPage的任务卡片"查看详情"按钮跳转
- 使用`navigate(\`/task-orders/\${task.task_id}\`)`进行路由跳转
- 支持深链接，可直接通过URL访问特定任务的订单详情

### 样式统一优化 (2025-01-12)
为了保持与项目其他页面的视觉一致性，对TaskOrderDetailPage进行了样式统一：

1. **主容器样式**：
   - 使用 `p-4 bg-gray-50 min-h-screen` 与ProjectTaskDetailPage保持一致
   - 移除了 `container mx-auto px-4 py-6` 的居中容器样式

2. **页面头部布局**：
   - 返回按钮移至右上角，与刷新、导出按钮并列显示
   - 使用 `flex items-center justify-between mb-3` 的布局结构
   - 左侧放置空div，右侧放置操作按钮组

3. **任务信息卡片样式**：
   - 采用与ProjectTaskDetailPage相同的卡片设计
   - 添加 `border-l-4 border-l-blue-500` 的左边框强调
   - 使用 `bg-white shadow-sm border border-gray-200` 的卡片样式
   - 图标和标题布局与项目详情页保持一致

4. **卡片统一样式**：
   - 所有Card组件都添加 `bg-white shadow-sm border border-gray-200` 样式
   - 保持统一的阴影效果和边框样式

5. **按钮尺寸统一**：
   - 头部操作按钮使用 `size="sm"` 保持紧凑布局
   - 与其他页面的按钮尺寸保持一致

这些修改确保了TaskOrderDetailPage与项目整体设计语言的一致性，提供了统一的用户体验。

### 完整字段显示优化 (2025-01-12)
为了满足用户查看订单完整信息的需求，对TaskOrderDetailPage进行了全面的字段显示优化：

1. **表格字段扩展**：
   - 从原来的13个字段扩展到35个字段，覆盖TrainOrder接口的所有关键字段
   - 新增字段包括：出行人姓、出行人名、国籍、性别、出生日期、证件类型、证件号码、证件有效期、手机号国家码、出发时间、到达时间、成本中心、行程提交项、联系人手机、联系人邮箱、审批参考人、公司名称、预订代理、票务短信、订单号、账单号、失败原因、更新时间

2. **表格列标题优化**：
   - 使用清晰的中文标题，便于用户理解
   - 按照逻辑分组排列：基本信息 → 证件信息 → 联系信息 → 行程信息 → 业务信息 → 系统信息

3. **数据显示处理**：
   - 所有空值统一显示为 "-"，保持界面整洁
   - 失败原因字段使用红色文字突出显示，便于快速识别问题
   - 保持金额格式化显示，时间字段统一格式

4. **Excel导出优化**：
   - 导出功能同步更新，包含所有35个字段
   - 保持中文列标题，便于业务人员查看和处理
   - 金额字段带货币符号，时间字段格式化显示

5. **响应式设计考虑**：
   - 使用`overflow-x-auto`确保大量列在小屏幕上可以横向滚动
   - 保持表格的可读性和操作性

6. **字段映射完整性**：
   - 确保前端显示字段与后端API返回的TrainOrder接口完全对应
   - 包含所有业务必需的字段：旅客信息、证件信息、行程信息、联系信息、订单状态、时间信息等

这次优化使得用户可以在一个页面中查看订单的所有详细信息，无需频繁点击查看详情，大大提升了工作效率。同时完整的Excel导出功能支持离线数据分析和报表制作。 

## UI/UX Design Principles

### Modal Design Consistency
- 所有相关页面的模态框设计必须保持一致
- 查看详情和编辑功能的模态框样式要统一
- 表格布局使用4列结构，字段分组要清晰

### Edit Modal Requirements
- **订单状态显示**：编辑模态框顶部必须显示订单状态区域
- **验证错误信息**：失败订单要显示详细的失败原因
- **滚动条支持**：内容区域必须支持垂直滚动，高度限制为 `max-h-[calc(90vh-180px)]`
- **内联编辑**：使用表格形式的内联编辑，而非独立组件
- **状态管理**：需要 `editingOrder` 和 `validationErrors` 状态变量
- **字段变更**：实现 `handleFieldChange` 函数处理实时编辑

### Order Detail Display Structure
订单详情必须按照以下三个分组显示：
1. **出行人基础信息**：姓名、姓、名、国籍、性别、出生日期、证件类型、证件号码、证件有效期、手机国际区号、手机号
2. **出行信息**：差旅单号、出行日期、出发站名、到达站名、车次、座位类型、出发时间、到达时间、成本中心  
3. **对账单信息**：公司名称、代订人、出票短信、金额、订单号、账单号

### Page Functionality Requirements
- 异常订单页面只显示验证失败(check_failed)和预定失败(failed)的订单
- 异常订单的查看详情和编辑功能必须与火车票预订页面保持完全一致
- 模态框设计要统一：相同的头部样式、表格布局、按钮位置

## Technical Preferences

### API Integration
- 后端接口参数命名要准确，特别是状态筛选使用 `order_status` 而不是 `status`
- 前后端数据验证要协同工作

### Code Organization
- 相同功能的组件要复用，避免重复实现
- UI组件的样式类要保持一致性
- 编辑功能使用内联表格编辑，提供更好的用户体验 

## 火车票订单验证规则偏好 (2025-06-23)

### 证件类型管理
- **支持证件类型**: 12种标准证件类型
  - 身份证、公务护照、普通护照、港澳通行证、台胞证、回乡证
  - 军人证、海员证、台湾通行证、外国永久居留证、港澳台居民居住证、其他
- **前端展示**: 所有页面的证件类型下拉框都使用统一的12种选项
- **后端验证**: 严格枚举验证，不在列表内的证件类型会被拒绝

### 证件号码验证策略
- **条件验证**: 根据证件类型动态调整验证规则
  - 身份证：严格验证15-18位数字或字母X的格式
  - 其他证件：不进行格式验证，允许各种长度和格式
- **字段长度**: 证件号码字段支持最长50个字符，满足各种证件类型需求
- **错误提示**: 针对不同证件类型提供具体的错误信息

### 验证逻辑统一性
- **前后端一致**: 前端和后端使用相同的验证规则和错误信息
- **多页面统一**: 所有相关页面（预订页面、项目任务详情页面、编辑表单）都应用相同规则
- **动态验证**: 证件类型变更时，相关字段的验证规则自动调整

### 用户体验优化
- **Excel导入**: 非身份证证件不会因为号码格式问题导致导入失败
- **实时验证**: 前端编辑时根据证件类型实时调整验证提示
- **错误信息**: 证件类型错误时明确列出所有允许的选项
- **向下兼容**: 现有身份证数据的验证标准保持不变

### 业务规则
- **必填字段**: 证件类型始终为必填项
- **格式验证**: 只对身份证进行严格格式验证
- **数据质量**: 通过枚举限制确保证件类型数据的标准化
- **扩展性**: "其他"选项为特殊证件类型提供兜底支持 

## 预定类型字段映射规范 (2025-06-23)

### 数据存储设计
- **主表**: `train_orders` 存储订单基础信息
- **关联表**: `task_to_train_orders` 存储任务与订单的映射关系，包含预定类型
- **字段名**: `order_type` 存储预定类型信息

### 预定类型枚举值
- **book**: 仅预定 - 只进行火车票预订，不出票
- **book_and_issue**: 预定且出票 - 既预订又出票的完整服务

### 前端显示规范
- **仅预定**: 蓝色标签 `bg-blue-100 text-blue-700` \n- **预定且出票**: 绿色标签 `bg-green-100 text-green-700`\n- **空值**: 灰色文本 `-` 表示未设置或未关联任务\n\n### API设计原则\n- **关联查询**: 所有返回订单列表的API都应包含order_type字段\n- **性能考虑**: 使用单独查询而非JOIN，避免复杂SQL\n- **向下兼容**: 未关联任务的订单order_type为null，前端显示为\"-\"\n- **数据完整性**: 通过task_id过滤确保关联数据的准确性\n\n### 业务流程集成\n- **任务创建**: 根据用户选择的预订类型设置order_type\n- **订单显示**: 所有订单列表都显示预定类型信息\n- **数据导出**: Excel导出包含预定类型列\n- **统计分析**: 支持按预定类型进行订单统计\n\n### 技术实现要点\n- **Schema扩展**: TrainOrderResponse包含optional的order_type字段\n- **关联查询**: 通过TaskToTrainOrder.filter(order_id=order.id)获取类型\n- **错误处理**: 关联查询失败时order_type设为None\n- **类型安全**: 使用TypeScript接口确保前端类型一致性\n\n### 用户体验优化\n- **视觉区分**: 不同预定类型使用不同颜色标签\n- **信息完整**: 订单详情页面显示完整的预定类型信息\n- **操作便利**: 用户可以快速识别订单的服务类型\n- **数据追踪**: 提供完整的订单处理流程可视化" 

## 任务概览卡片设计规范 (2025-06-23)

### 显示内容原则
- **核心信息优先**: 任务概览卡片应突出最重要的信息
- **简洁明了**: 避免信息过载，只显示用户最关心的数据
- **统一标准**: 所有任务类型的卡片都使用相同的信息结构

### 任务概览卡片结构
1. **卡片头部**:
   - 任务类型图标和名称
   - 任务标题
   - 任务状态标签（右上角）

2. **核心统计**:
   - **订单总数**: 使用大字体(text-2xl)突出显示
   - 居中布局，清晰醒目
   - 标签使用"订单总数"而非简单的"订单"

3. **任务信息**:
   - 创建人信息
   - 创建时间（简化显示，只显示月日）
   - 特殊标记：短信通知、代订等

4. **操作区域**:
   - 查看详情按钮
   - 按钮颜色根据任务类型动态配置

### 删除的信息
- **人数统计**: 不在概览卡片中显示
- **金额信息**: 移至详细页面查看
- **完成进度**: 不显示进度条和百分比
- **复杂统计**: 避免多列数据展示

### 设计理念
- **信息层次**: 订单数量 > 任务信息 > 操作按钮
- **视觉重点**: 订单总数是唯一的数字指标
- **空间利用**: 简化设计减少卡片高度，提高页面信息密度
- **用户体验**: 快速扫描获取关键信息，减少认知负担

### 响应式设计
- **网格布局**: md:grid-cols-3 lg:grid-cols-4
- **卡片间距**: gap-4 提供适当的视觉分离
- **悬停效果**: hover:shadow-md hover:scale-105 提供交互反馈

### 任务类型配置
- **图标系统**: 每种任务类型都有专属图标和颜色
- **状态管理**: 根据任务状态和统计数据动态显示状态标签
- **按钮主题**: 查看详情按钮颜色与任务类型保持一致

这种简化设计让用户能够快速了解每个任务的核心情况，提高了界面的可用性和效率。 

## 任务订单详情页面导出功能优化 (2025-01-18)

### 功能描述
优化了任务订单详情页面(TaskOrderDetailPage.tsx)的导出Excel功能，将按钮文本改为"导出"，并参考所有订单列表的导出样式进行了全面升级。

### 主要改进
1. **按钮文本简化**：将"导出Excel"改为"导出"，保持界面简洁
2. **导出样式专业化**：
   - 添加详细的列宽设置，每列都有合适的宽度
   - 设置完整的单元格边框样式（细线边框）
   - 为标题行添加特殊样式：灰色背景、居中对齐、加粗字体
   - 数据行使用左对齐和自动换行
   - 使用微软雅黑字体，字号10，提升可读性

3. **字段名称统一**：
   - "证件有效期" → "证件有效期至"
   - "手机号国家码" → "手机号国际区号"
   - "预订代理" → "代订人"
   - "票务短信" → "出票短信"
   - 添加"是否删除"字段

4. **数据处理优化**：
   - 序号使用连续编号(index + 1)而不是数据库的sequence_number
   - 金额字段直接使用数值类型而不是格式化字符串
   - 包含所有数据库字段的完整导出

### 技术实现
- 参考ProjectTaskDetailPage.tsx中的导出函数实现
- 使用XLSX.write的cellStyles: true选项确保样式正确应用
- 设置36列的详细列宽配置，优化显示效果
- 工作表名称保持为"火车票订单"
- 文件命名格式：`任务订单_${任务标题}_${日期}.xlsx`

### 样式配置
```typescript
// 列宽配置示例
const colWidths = [
  { wch: 6 },   // 序号
  { wch: 10 },  // 订单状态
  { wch: 12 },  // 出行人姓名
  { wch: 18 },  // 证件号码
  { wch: 20 },  // 失败原因
  // ... 其他字段
];

// 单元格样式
ws[cellAddress].s = {
  border: { top: borderAll, bottom: borderAll, left: borderAll, right: borderAll },
  alignment: { horizontal: 'left', vertical: 'center', wrapText: true },
  font: { name: '微软雅黑', sz: 10 }
};
```

### 用户体验提升
- 导出的Excel文件具有专业的表格样式，适合商务使用
- 列宽自动调整，便于查看完整数据
- 标题行突出显示，数据层次清晰
- 按钮文本简洁明了，符合用户操作习惯
- 与系统其他导出功能保持一致的视觉风格

### 文件修改
- `service-operation-frontend/src/pages/TaskOrderDetailPage.tsx`：导出功能完整优化

## Kafka消息队列集成 (2025-01-19)

### 功能配置
系统集成Kafka消息队列，支持火车票预定任务的自动化处理。

### 环境配置
- **本地环境**: `dttrip_soap_topic_tasks_dev`
- **QA环境**: `dttrip_soap_topic_tasks_qa`
- **生产环境**: `dttrip_soap_topic_tasks`
- **Kafka服务器**: `kafka.ops.17usoft.com:9092`

### 技术栈
- **Python依赖**: `kafka-python = "^2.0.2"`
- **配置管理**: 环境变量统一管理
- **服务模块**: 独立的Kafka服务封装

### 消息格式标准
```json
{
  "task_id": "任务ID",
  "order_id": "订单ID", 
  "module": "train_ticket_booking" | "domestic_hotel_filling",
  "username": "同程管家用户名",
  "password": "加密后的密码"
}
```

### 业务集成点
- **触发时机**: 用户点击预定或预定且出票按钮
- **集成位置**: `create_booking_task`接口 
- **凭证来源**: 系统设置中的同程管家账号
- **错误处理**: 推送失败不影响任务创建，在响应中体现状态

### 安全特性
- 用户级别的凭证隔离
- 密码加密存储和传输  
- Kafka连接超时和重试机制
- 详细的操作日志记录

### 扩展规划
- 支持酒店预定任务推送
- 消息状态查询接口
- 重发机制和监控报警

### 消息内容存储 (2025-01-19)
- **存储位置**: TaskToTrainOrder表的message字段
- **存储时机**: Kafka推送成功后自动存储
- **存储格式**: JSON格式，包含完整的Kafka消息内容
- **密码处理**: Kafka发送和消息存储都使用数据库原始密文（加密状态），不是解密后的值
- **业务价值**: 便于问题排查、审计和后续功能扩展
- **技术实现**: 新增get_tongcheng_credentials_with_raw_password方法获取原始密码
- **字段配置**: 
  - message字段：存储消息内容，已从模型中删除kafka_message字段定义
  - 修复问题：解决了"Unknown column 'kafka_message' in 'field list'"的数据库错误
- **完全统一**: Kafka发送逻辑也修改为使用原始密文，确保发送和存储完全一致

# 项目记忆 - DTTrip项目技术规范和配置

## 项目结构
项目根目录是/Users/<USER>/Projects/dttrip。前端项目位于service-operation-frontend目录下，后端位于service-operation-server目录下。这是一个前后端分离的项目结构。

## 同程SSO登出功能规范
根据同程SSO官方文档，正确的登出实现方式：

### 官方接口规范
- **推荐方式**：POST /oauth/logoutapi?access_token=xxx（退出后重定向到业务系统登录页）
- **备用方式**：GET /oauth/logout?access_token=xxx（退出到统一登录页面）
- **响应格式**：{"success_code": "1"}正常，{"success_code": "0"}异常

### 环境配置
- **线下**：http://tccommon.qas.17usoft.com/oauth/logoutapi
- **预发**：http://tccommon.t.17usoft.com/oauth/logoutapi  
- **正式**：http://tccommon.17usoft.com/oauth/logoutapi

### 实现位置
- **后端实现**：service-operation-server/src/api/auth/sso_service.py
- **前端实现**：service-operation-frontend/src/services/sso-service.ts

### 关键技术点
1. 必须使用POST方式调用/oauth/logoutapi接口
2. 正确解析success_code响应字段判断登出结果
3. 实现多重备用策略确保登出成功率
4. 前端简化为基础清理+后端调用+重定向的可靠模式

### 解决的业务问题
解决了用户在同程管家切换用户后，DTTrip系统登出再登录仍然显示原来用户的问题。通过正确实现SSO登出功能，确保服务器端会话彻底清除。

## 左侧导航菜单
- 应用中心 (/dashboard)
- 团房团票 (/projects) - 原"项目管理"已更名
- 护照识别 (/passport-recognition)  
- 系统设置 (/settings)

菜单配置位于service-operation-frontend/src/components/layout/SideNav.tsx文件中。

## 火车票订单验证规则
1. **基础必填字段（所有情况都必填）**：出行人姓名、证件类型、证件号码、手机号、出行日期、出发站名、到达站名、车次、座位类型、出发时间、到达时间、成本中心、联系人、联系人手机号、审批参考人
2. **动态必填字段（仅证件类型不为身份证时必填）**：出行人姓、出行人名、国籍、性别、出生日期、证件有效期至  
3. **非必填字段**：手机号国际区号（自动补齐为86）、联系人邮箱（如填写需验证格式）、金额等

## 枚举字段验证规则
1. **性别字段**：只能选择"男"或"女"
2. **证件类型字段**：支持12种证件类型
   - 身份证、公务护照、普通护照、港澳通行证、台胞证、回乡证
   - 军人证、海员证、台湾通行证、外国永久居留证、港澳台居民居住证、其他

## 证件号码验证规则（已优化）
- 验证规则：`/^[0-9Xx]{15,18}$/` - 支持15-18位数字和大小写X
- 错误信息：证件号码格式不正确（15-18位数字或字母X）
- 支持范围：15位老身份证、16-17位特殊证件、18位标准身份证
- 校验位：支持大写X和小写x

## 验证逻辑
- 身份证类型：只验证15个基础必填字段
- 其他证件类型（护照、港澳通行证等）：验证15个基础必填字段 + 6个动态必填字段
- 联系人邮箱：非必填，但如果填写需要验证邮箱格式
- 手机号国际区号：非必填，系统自动补齐为86
- 性别和证件类型：必须在预定义的枚举值范围内，否则验证失败

## 订单状态自动更新
- 编辑订单保存时，如果前端验证通过，自动将订单状态设置为'initial'（待提交）
- 确保验证通过的订单能够参与预订流程
- 保存成功提示明确显示状态变化

## 编辑界面
- 性别和证件类型字段在编辑模式下显示为下拉框选择器
- 其他字段保持输入框形式
- 下拉框包含"请选择"默认选项，引导用户选择
- 实时验证确保数据符合规范

# DTTrip Project Memory

## User Preferences and Requirements

### 护照识别功能要求
- 护照列表字段按照指定的15个字段顺序显示
- 日期格式统一使用 YYYY-MM-DD 格式
- 所有字段不做截断，完整显示内容
- 一致性检查：只有完全一致才显示绿色，其他情况都显示红色
- SSR DOCS码使用水平滚动条显示，控件高度要足够（min-h-[3rem]）
- 任务详情页面的统计卡片要显示准确的全任务统计数据，不能基于当前页面数据计算
- **S3云存储**: 护照图片必须上传到S3 OSS服务器，将S3 URL存储到数据库uploaded_image_url字段

### S3 OSS配置要求
- **服务端点**: http://oss.qa.17usoft.com
- **访问凭证**: FhhO34TTwubHsi5AlYAQ / AbwMPCvDDWE66sb4dcMEDrPDmoaui1QRv54IKYfh
- **存储桶**: soap-files
- **键前缀**: tmc.ai.soap.server
- **文件组织**: 按日期分层存储 (YYYY/MM/DD)
- **容错机制**: S3上传失败时回退到本地存储
- **大文件支持**: 超过50MB使用分段上传

### 技术实现偏好
- 前端使用 TypeScript + React
- 保持向后兼容性，新字段为可选类型
- 使用专门的统计接口获取准确数据，避免基于部分数据计算统计
- 长文本字段使用合适的显示方式（滚动条或换行）
- 用户体验优先：双击复制、悬停提示、颜色区分等交互细节
- **云存储优先**: 优先使用S3存储，本地存储作为备份方案
- **环境配置**: 所有S3配置通过环境变量管理，支持多环境部署

## 项目技术栈偏好
- 前端：React + TypeScript + Tailwind CSS
- 状态管理：useState + useEffect hooks
- 路由：React Router
- UI组件：shadcn/ui组件库
- API请求：axios封装的统一API服务
- 图标：Lucide React
- 代码风格：TypeScript严格模式，函数式组件

## 酒店预订功能实现详情

### 功能完整性
✅ **完全对等的酒店预订功能**：与火车票预订功能完全对等，包括：
- Excel上传和文本输入两种数据录入方式
- 完整的订单管理和展示功能
- 异常订单专门处理
- 预订任务管理
- 统计数据展示

### 前端实现要点

#### 1. 酒店预订页面设计风格
**重要：完全遵循火车票预订页面设计风格**
- 页面路径：`/hotel-booking/:projectId`（专用酒店预订页面）
- URL示例：`http://localhost:5173/hotel-booking/45`
- 设计风格：完全参照 `http://localhost:5173/project-task-detail/45?type=%E7%81%AB%E8%BD%A6%E7%A5%A8%E9%A2%84%E8%AE%A2&tab=booking`

#### 2. 核心组件结构
- **主页面**：`service-operation-frontend/src/pages/hotel_order/HotelBookingPage.tsx`
  - 完全遵循火车票预订页面的布局和风格
  - 页面头部：白色背景，阴影效果，返回按钮，项目信息展示
  - Tab导航：4个标签页（待预订、所有订单、异常订单、预订任务）
  - 内容区域：最小高度96，居中布局

- **预订内容组件**：`service-operation-frontend/src/components/booking/HotelBookingContent.tsx`
  - 实现与 `TrainBookingContent` 完全一致的功能结构
  - Tab导航：Excel文件上传、文本内容两个子标签
  - 订单列表：完整的表格展示，分页，搜索筛选功能
  - 操作功能：预订、预订且出票、清空订单等

#### 3. Tab功能详情
**待预订Tab（booking）**：
- Excel文件上传功能（支持.xlsx和.xls）
- 文本内容解析功能
- 订单列表展示（表格形式，支持分页）
- 搜索和筛选功能
- 预订设置（短信通知、代订人信息）
- 操作按钮：预订、预订且出票、清空订单

**所有订单Tab（all-orders）**：
- 完整的订单列表展示
- 搜索功能：入住人姓名、手机号
- 状态筛选：验证失败、待提交、已提交、处理中、已完成、失败
- 分页导航
- 订单操作：查看详情、编辑

**异常订单Tab（exception-orders）**：
- 异常订单专门展示（验证失败和预订失败）
- 卡片式布局，突出显示错误信息
- 失败原因详细展示

**预订任务Tab（details）**：
- 预订任务管理界面
- 任务列表展示和详情查看

#### 4. API集成
- **API服务文件**：`service-operation-frontend/src/api/hotel.ts`
- **接口数量**：13个完整的API方法
- **数据类型**：61个业务字段的完整酒店订单接口
- **类型安全**：所有API调用都通过 `response.data` 提取数据

#### 5. 颜色主题规范
- **酒店预订主色调**：蓝色系（`text-blue-600`, `bg-blue-50`, `border-blue-500`）
- **状态颜色**：
  - 验证失败：琥珀色（`text-amber-600`, `bg-amber-100`）
  - 待提交：灰色（`text-gray-600`, `bg-gray-100`）
  - 已提交：蓝色（`text-blue-600`, `bg-blue-100`）
  - 处理中：青色（`text-cyan-600`, `bg-cyan-100`）
  - 已完成：绿色（`text-green-600`, `bg-green-100`）
  - 失败：红色（`text-red-600`, `bg-red-100`）

### 页面路由配置
- **项目详情页面集成**：项目详情页面中的酒店预订按钮链接到 `/hotel-booking/${projectId}`
- **导航路径**：项目详情页 → 酒店预订页 → 订单管理
- **业务流程闭环**：完整的从项目管理到酒店预订的业务流程

### 技术特点
- **响应式设计**：支持不同屏幕尺寸，表格支持横向滚动
- **状态管理**：完整的加载状态、分页状态、搜索状态、错误状态管理
- **用户体验**：Toast通知、加载动画、空状态提示、错误处理
- **类型安全**：TypeScript严格类型检查，API响应类型安全处理

### 最新更新（2024）
- **设计风格完全对齐**：酒店预订页面现在完全遵循火车票预订页面的设计风格
- **页面结构统一**：头部布局、Tab导航、内容区域与火车票页面保持一致
- **组件复用**：`HotelBookingContent` 组件实现与 `TrainBookingContent` 相同的功能结构
- **用户体验一致**：确保用户在使用不同预订类型时有一致的操作体验

## 前端开发规范
- 组件命名：使用PascalCase，如 `HotelBookingPage`
- 文件组织：按功能模块分组，如 `pages/hotel_order/`、`components/booking/`
- 状态管理：优先使用React Hooks，避免复杂的状态管理库
- 错误处理：统一使用toast通知，提供用户友好的错误信息
- 加载状态：所有异步操作都要有加载状态指示
- 响应式设计：所有页面都要支持移动端访问

## API开发规范
- 接口路径：使用RESTful风格，如 `/api/hotel-order/project/{projectId}`
- 数据格式：统一使用JSON，响应格式包含 `ApiResponse<T>` 包装
- 错误处理：标准化错误码和错误信息
- 分页支持：列表接口必须支持分页参数
- 搜索筛选：列表接口支持常用字段的搜索和状态筛选

# DTTrip 项目记忆 - 用户偏好和自定义规则

## 核心技术栈和偏好设置

### 后端 API 开发规范
- **Framework**: FastAPI (Python)
- **数据库**: Tortoise ORM + MySQL
- **文件上传处理**: 使用 `Form(...)` 参数处理 multipart/form-data
- **错误处理**: 统一返回 HTTP 状态码 + detail 消息
- **认证**: JWT Token + SSO 集成

### 前端开发规范  
- **Framework**: React + TypeScript + Vite
- **UI组件**: shadcn/ui + Tailwind CSS
- **API请求**: 自定义 request.ts 封装
- **文件上传**: FormData + isFormData 标志，**禁止手动设置Content-Type**

### 重要修复记录

#### multipart/form-data 文件上传标准
**问题**: 手动设置 `'Content-Type': 'multipart/form-data'` 导致 "Missing boundary in multipart" 错误
**正确方式**: 使用 `isFormData: true` 标志，让浏览器自动设置包含 boundary 的正确 Content-Type
```typescript
// ❌ 错误方式
const response = await api.post('/upload', formData, {
  headers: {
    'Content-Type': 'multipart/form-data',  // 缺少boundary参数
  },
});

// ✅ 正确方式
const response = await api.post('/upload', formData, {
  isFormData: true,  // 让浏览器自动设置正确的Content-Type
});
```

**技术原理**: 浏览器发送FormData时会自动生成随机boundary并设置为 `Content-Type: multipart/form-data; boundary=----WebKitFormBoundary...`，手动设置会覆盖这个自动生成的完整头部。

#### FastAPI Form 参数要求
后端接收 multipart/form-data 必须使用 `Form(...)` 参数：
```python
# ✅ 正确方式
async def upload_file(
    project_id: int = Form(...),
    file: UploadFile = File(...)
):
    pass

# ❌ 错误方式  
async def upload_file(project_id: int, file: UploadFile = File(...)):
    pass  # project_id 无法从表单数据中获取
```

## 项目特定配置和偏好

### 开发环境设置
- **后端端口**: 8000 
- **前端端口**: 5173
- **代理配置**: 前端 /api/* 代理到后端
- **开发调试**: 优先使用浏览器 Network 面板分析请求

### 错误处理偏好
- **前端**: 统一使用 toast 显示错误信息，详细错误记录到 console
- **后端**: HTTP 状态码 + detail 字段描述具体错误
- **调试**: 优先检查 Network 请求内容，再检查后端日志

### 代码质量要求
- **函数命名**: 使用明确的业务动词 (validate, upload, process)
- **错误信息**: 中文显示给用户，英文记录到日志  
- **API设计**: RESTful 风格，资源层次清晰
- **类型安全**: 前后端严格类型定义，避免 any

### 内存管理规则
- **技术修复**: 记录到 @self.md，包含问题原因和解决方案
- **用户偏好**: 记录到 @project.md，包含配置和规范
- **保持简洁**: 只记录可重用的知识，避免特定任务信息

## 业务逻辑模式

### Excel文件处理流程
1. **验证阶段**: validateExcel API 检查数据格式和业务规则
2. **上传阶段**: uploadExcel API 保存数据到数据库
3. **错误处理**: 详细的字段级错误信息，支持批量验证

### 订单管理模式
- **状态流转**: initial → submitted → processing → completed/failed
- **批量操作**: 支持批量预订、批量清空
- **权限控制**: 基于项目的数据隔离

### 实现位置
- **后端实现**：service-operation-server/src/api/auth/sso_service.py
- **前端实现**：service-operation-frontend/src/services/sso-service.ts

### 关键技术点
1. 必须使用POST方式调用/oauth/logoutapi接口
2. 正确解析success_code响应字段判断登出结果
3. 实现多重备用策略确保登出成功率
4. 前端简化为基础清理+后端调用+重定向的可靠模式

### 解决的业务问题
解决了用户在同程管家切换用户后，DTTrip系统登出再登录仍然显示原来用户的问题。通过正确实现SSO登出功能，确保服务器端会话彻底清除。

## 左侧导航菜单
- 应用中心 (/dashboard)
- 团房团票 (/projects) - 原"项目管理"已更名
- 护照识别 (/passport-recognition)  
- 系统设置 (/settings)

菜单配置位于service-operation-frontend/src/components/layout/SideNav.tsx文件中。

## 火车票订单验证规则
1. **基础必填字段（所有情况都必填）**：出行人姓名、证件类型、证件号码、手机号、出行日期、出发站名、到达站名、车次、座位类型、出发时间、到达时间、成本中心、联系人、联系人手机号、审批参考人
2. **动态必填字段（仅证件类型不为身份证时必填）**：出行人姓、出行人名、国籍、性别、出生日期、证件有效期至  
3. **非必填字段**：手机号国际区号（自动补齐为86）、联系人邮箱（如填写需验证格式）、金额等

## 枚举字段验证规则
1. **性别字段**：只能选择"男"或"女"
2. **证件类型字段**：支持12种证件类型
   - 身份证、公务护照、普通护照、港澳通行证、台胞证、回乡证
   - 军人证、海员证、台湾通行证、外国永久居留证、港澳台居民居住证、其他

## 证件号码验证规则（已优化）
- 验证规则：`/^[0-9Xx]{15,18}$/` - 支持15-18位数字和大小写X
- 错误信息：证件号码格式不正确（15-18位数字或字母X）
- 支持范围：15位老身份证、16-17位特殊证件、18位标准身份证
- 校验位：支持大写X和小写x

## 验证逻辑
- 身份证类型：只验证15个基础必填字段
- 其他证件类型（护照、港澳通行证等）：验证15个基础必填字段 + 6个动态必填字段
- 联系人邮箱：非必填，但如果填写需要验证邮箱格式
- 手机号国际区号：非必填，系统自动补齐为86
- 性别和证件类型：必须在预定义的枚举值范围内，否则验证失败

## 订单状态自动更新
- 编辑订单保存时，如果前端验证通过，自动将订单状态设置为'initial'（待提交）
- 确保验证通过的订单能够参与预订流程
- 保存成功提示明确显示状态变化

## 编辑界面
- 性别和证件类型字段在编辑模式下显示为下拉框选择器
- 其他字段保持输入框形式
- 下拉框包含"请选择"默认选项，引导用户选择
- 实时验证确保数据符合规范

# DTTrip Project Memory

## User Preferences and Requirements

### 护照识别功能要求
- 护照列表字段按照指定的15个字段顺序显示
- 日期格式统一使用 YYYY-MM-DD 格式
- 所有字段不做截断，完整显示内容
- 一致性检查：只有完全一致才显示绿色，其他情况都显示红色
- SSR DOCS码使用水平滚动条显示，控件高度要足够（min-h-[3rem]）
- 任务详情页面的统计卡片要显示准确的全任务统计数据，不能基于当前页面数据计算
- **S3云存储**: 护照图片必须上传到S3 OSS服务器，将S3 URL存储到数据库uploaded_image_url字段

### S3 OSS配置要求
- **服务端点**: http://oss.qa.17usoft.com
- **访问凭证**: FhhO34TTwubHsi5AlYAQ / AbwMPCvDDWE66sb4dcMEDrPDmoaui1QRv54IKYfh
- **存储桶**: soap-files
- **键前缀**: tmc.ai.soap.server
- **文件组织**: 按日期分层存储 (YYYY/MM/DD)
- **容错机制**: S3上传失败时回退到本地存储
- **大文件支持**: 超过50MB使用分段上传

### 技术实现偏好
- 前端使用 TypeScript + React
- 保持向后兼容性，新字段为可选类型
- 使用专门的统计接口获取准确数据，避免基于部分数据计算统计
- 长文本字段使用合适的显示方式（滚动条或换行）
- 用户体验优先：双击复制、悬停提示、颜色区分等交互细节
- **云存储优先**: 优先使用S3存储，本地存储作为备份方案
- **环境配置**: 所有S3配置通过环境变量管理，支持多环境部署

## 项目技术栈偏好
- 前端：React + TypeScript + Tailwind CSS
- 状态管理：useState + useEffect hooks
- 路由：React Router
- UI组件：shadcn/ui组件库
- API请求：axios封装的统一API服务
- 图标：Lucide React
- 代码风格：TypeScript严格模式，函数式组件

## 酒店预订功能实现详情

### 功能完整性
✅ **完全对等的酒店预订功能**：与火车票预订功能完全对等，包括：
- Excel上传和文本输入两种数据录入方式
- 完整的订单管理和展示功能
- 异常订单专门处理
- 预订任务管理
- 统计数据展示

### 前端实现要点

#### 1. 酒店预订页面设计风格
**重要：完全遵循火车票预订页面设计风格**
- 页面路径：`/hotel-booking/:projectId`（专用酒店预订页面）
- URL示例：`http://localhost:5173/hotel-booking/45`
- 设计风格：完全参照 `http://localhost:5173/project-task-detail/45?type=%E7%81%AB%E8%BD%A6%E7%A5%A8%E9%A2%84%E8%AE%A2&tab=booking`

#### 2. 核心组件结构
- **主页面**：`service-operation-frontend/src/pages/hotel_order/HotelBookingPage.tsx`
  - 完全遵循火车票预订页面的布局和风格
  - 页面头部：白色背景，阴影效果，返回按钮，项目信息展示
  - Tab导航：4个标签页（待预订、所有订单、异常订单、预订任务）
  - 内容区域：最小高度96，居中布局

- **预订内容组件**：`service-operation-frontend/src/components/booking/HotelBookingContent.tsx`
  - 实现与 `TrainBookingContent` 完全一致的功能结构
  - Tab导航：Excel文件上传、文本内容两个子标签
  - 订单列表：完整的表格展示，分页，搜索筛选功能
  - 操作功能：预订、预订且出票、清空订单等

#### 3. Tab功能详情
**待预订Tab（booking）**：
- Excel文件上传功能（支持.xlsx和.xls）
- 文本内容解析功能
- 订单列表展示（表格形式，支持分页）
- 搜索和筛选功能
- 预订设置（短信通知、代订人信息）
- 操作按钮：预订、预订且出票、清空订单

**所有订单Tab（all-orders）**：
- 完整的订单列表展示
- 搜索功能：入住人姓名、手机号
- 状态筛选：验证失败、待提交、已提交、处理中、已完成、失败
- 分页导航
- 订单操作：查看详情、编辑

**异常订单Tab（exception-orders）**：
- 异常订单专门展示（验证失败和预订失败）
- 卡片式布局，突出显示错误信息
- 失败原因详细展示

**预订任务Tab（details）**：
- 预订任务管理界面
- 任务列表展示和详情查看

#### 4. API集成
- **API服务文件**：`service-operation-frontend/src/api/hotel.ts`
- **接口数量**：13个完整的API方法
- **数据类型**：61个业务字段的完整酒店订单接口
- **类型安全**：所有API调用都通过 `response.data` 提取数据

#### 5. 颜色主题规范
- **酒店预订主色调**：蓝色系（`text-blue-600`, `bg-blue-50`, `border-blue-500`）
- **状态颜色**：
  - 验证失败：琥珀色（`text-amber-600`, `bg-amber-100`）
  - 待提交：灰色（`text-gray-600`, `bg-gray-100`）
  - 已提交：蓝色（`text-blue-600`, `bg-blue-100`）
  - 处理中：青色（`text-cyan-600`, `bg-cyan-100`）
  - 已完成：绿色（`text-green-600`, `bg-green-100`）
  - 失败：红色（`text-red-600`, `bg-red-100`）

### 页面路由配置
- **项目详情页面集成**：项目详情页面中的酒店预订按钮链接到 `/hotel-booking/${projectId}`
- **导航路径**：项目详情页 → 酒店预订页 → 订单管理
- **业务流程闭环**：完整的从项目管理到酒店预订的业务流程

### 技术特点
- **响应式设计**：支持不同屏幕尺寸，表格支持横向滚动
- **状态管理**：完整的加载状态、分页状态、搜索状态、错误状态管理
- **用户体验**：Toast通知、加载动画、空状态提示、错误处理
- **类型安全**：TypeScript严格类型检查，API响应类型安全处理

### 最新更新（2024）
- **设计风格完全对齐**：酒店预订页面现在完全遵循火车票预订页面的设计风格
- **页面结构统一**：头部布局、Tab导航、内容区域与火车票页面保持一致
- **组件复用**：`HotelBookingContent` 组件实现与 `TrainBookingContent` 相同的功能结构
- **用户体验一致**：确保用户在使用不同预订类型时有一致的操作体验

## 前端开发规范
- 组件命名：使用PascalCase，如 `HotelBookingPage`
- 文件组织：按功能模块分组，如 `pages/hotel_order/`、`components/booking/`
- 状态管理：优先使用React Hooks，避免复杂的状态管理库
- 错误处理：统一使用toast通知，提供用户友好的错误信息
- 加载状态：所有异步操作都要有加载状态指示
- 响应式设计：所有页面都要支持移动端访问

## API开发规范
- 接口路径：使用RESTful风格，如 `/api/hotel-order/project/{projectId}`
- 数据格式：统一使用JSON，响应格式包含 `ApiResponse<T>` 包装
- 错误处理：标准化错误码和错误信息
- 分页支持：列表接口必须支持分页参数
- 搜索筛选：列表接口支持常用字段的搜索和状态筛选

# DTTrip 项目记忆 - 用户偏好和自定义规则

## 核心技术栈和偏好设置

### 后端 API 开发规范
- **Framework**: FastAPI (Python)
- **数据库**: Tortoise ORM + MySQL
- **文件上传处理**: 使用 `Form(...)` 参数处理 multipart/form-data
- **错误处理**: 统一返回 HTTP 状态码 + detail 消息
- **认证**: JWT Token + SSO 集成

### 前端开发规范  
- **Framework**: React + TypeScript + Vite
- **UI组件**: shadcn/ui + Tailwind CSS
- **API请求**: 自定义 request.ts 封装
- **文件上传**: FormData + isFormData 标志，**禁止手动设置Content-Type**

### 重要修复记录

#### multipart/form-data 文件上传标准
**问题**: 手动设置 `'Content-Type': 'multipart/form-data'` 导致 "Missing boundary in multipart" 错误
**正确方式**: 使用 `isFormData: true` 标志，让浏览器自动设置包含 boundary 的正确 Content-Type
```typescript
// ❌ 错误方式
const response = await api.post('/upload', formData, {
  headers: {
    'Content-Type': 'multipart/form-data',  // 缺少boundary参数
  },
});

// ✅ 正确方式
const response = await api.post('/upload', formData, {
  isFormData: true,  // 让浏览器自动设置正确的Content-Type
});
```

**技术原理**: 浏览器发送FormData时会自动生成随机boundary并设置为 `Content-Type: multipart/form-data; boundary=----WebKitFormBoundary...`，手动设置会覆盖这个自动生成的完整头部。

#### FastAPI Form 参数要求
后端接收 multipart/form-data 必须使用 `Form(...)` 参数：
```python
# ✅ 正确方式
async def upload_file(
    project_id: int = Form(...),
    file: UploadFile = File(...)
):
    pass

# ❌ 错误方式  
async def upload_file(project_id: int, file: UploadFile = File(...)):
    pass  # project_id 无法从表单数据中获取
```

## 项目特定配置和偏好

### 开发环境设置
- **后端端口**: 8000 
- **前端端口**: 5173
- **代理配置**: 前端 /api/* 代理到后端
- **开发调试**: 优先使用浏览器 Network 面板分析请求

### 错误处理偏好
- **前端**: 统一使用 toast 显示错误信息，详细错误记录到 console
- **后端**: HTTP 状态码 + detail 字段描述具体错误
- **调试**: 优先检查 Network 请求内容，再检查后端日志

### 代码质量要求
- **函数命名**: 使用明确的业务动词 (validate, upload, process)
- **错误信息**: 中文显示给用户，英文记录到日志  
- **API设计**: RESTful 风格，资源层次清晰
- **类型安全**: 前后端严格类型定义，避免 any

### 内存管理规则
- **技术修复**: 记录到 @self.md，包含问题原因和解决方案
- **用户偏好**: 记录到 @project.md，包含配置和规范
- **保持简洁**: 只记录可重用的知识，避免特定任务信息

## 业务逻辑模式

### Excel文件处理流程
1. **验证阶段**: validateExcel API 检查数据格式和业务规则
2. **上传阶段**: uploadExcel API 保存数据到数据库
3. **错误处理**: 详细的字段级错误信息，支持批量验证

### 订单管理模式
- **状态流转**: initial → submitted → processing → completed/failed
- **批量操作**: 支持批量预订、批量清空
- **权限控制**: 基于项目的数据隔离

## 项目开发规范和偏好设置

### 技术栈偏好
- 前端：React + TypeScript + Tailwind CSS + shadcn/ui
- 后端：Python + FastAPI + Tortoise ORM + MySQL
- 包管理：前端使用npm，后端使用poetry

### 代码风格偏好
- TypeScript严格模式，明确类型定义
- 组件命名使用PascalCase
- API接口使用RESTful设计
- 错误处理统一使用toast提示
- 数据库操作优先使用ORM而非原生SQL

### 酒店订单系统功能模块

### 数据导入导出系统
**核心特性**：
1. Excel模板导入，支持61个字段
2. 智能数据验证和格式转换
3. 重复数据检测和处理
4. 完善的导出功能（前端+后端双重支持）
5. "导入什么是什么"的数据处理原则

**导出功能架构**：
- 前端导出：适合小数据量，在浏览器中处理
- 后端导出：推荐用于大数据量，服务器处理更快
- 支持字段自定义选择和分类管理
- 多格式支持（Excel、CSV）
- 实时进度显示和错误处理

### 字段类型处理策略
**布尔字段存储方案**：
- include_breakfast（含早餐）
- is_half_day_room（半日房）  
- is_group_booking（团体预订）

存储格式：VARCHAR字段存储"是"/"否"字符串，而非数字或布尔值
转换函数：parse_yes_no() 智能处理多种输入格式
显示函数：formatYesNoDisplay() 统一显示格式

### UI/UX设计原则
- 操作按钮使用图标+文字的组合
- 重要操作需要确认弹窗
- 加载状态要有明确的进度指示
- 错误信息要具体且可操作
- 大数据量操作提供批量处理选项

### 性能优化策略
- 分页查询避免一次性加载大量数据
- 导出功能分批获取数据避免超时
- 前端状态管理减少不必要的重新渲染
- API响应缓存减少重复请求