import React, { useState, useEffect } from 'react';
import { X, Save, Shield, Key } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { toast } from '@/hooks/use-toast';
import { Checkbox } from '@/components/ui/checkbox';
import { userManagementApi, Role, Permission } from '@/services/userManagementApi';

interface RolePermissionModalProps {
  role: Role | null;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

interface PermissionWithAssigned extends Permission {
  assigned: boolean;
}

const RolePermissionModal: React.FC<RolePermissionModalProps> = ({
  role,
  isOpen,
  onClose,
  onSuccess
}) => {
  const [permissions, setPermissions] = useState<PermissionWithAssigned[]>([]);
  const [selectedPermissionIds, setSelectedPermissionIds] = useState<number[]>([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);

  // 加载角色权限数据
  const loadRolePermissions = async () => {
    if (!role) return;
    
    setLoading(true);
    try {
      const response = await userManagementApi.getRolePermissions(role.id);
      setPermissions(response.permissions);
      setSelectedPermissionIds(response.assigned_permission_ids);
    } catch (error) {
      console.error('加载角色权限失败:', error);
      toast({
        title: "加载失败",
        description: "无法加载角色权限数据",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // 当角色变化时重新加载数据
  useEffect(() => {
    if (isOpen && role) {
      loadRolePermissions();
    }
  }, [isOpen, role]);

  // 处理权限选择
  const handlePermissionChange = (permissionId: number, checked: boolean) => {
    setSelectedPermissionIds(prev => {
      if (checked) {
        return [...prev, permissionId];
      } else {
        return prev.filter(id => id !== permissionId);
      }
    });
  };

  // 全选/取消全选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedPermissionIds(permissions.map(p => p.id));
    } else {
      setSelectedPermissionIds([]);
    }
  };

  // 保存权限分配
  const handleSave = async () => {
    if (!role) return;

    setSaving(true);
    try {
      const result = await userManagementApi.assignRolePermissions(role.id, selectedPermissionIds);
      
      toast({
        title: "保存成功",
        description: `已为角色 ${role.role_name} 分配 ${result.assigned_count} 个权限`,
      });
      
      onSuccess();
      onClose();
    } catch (error) {
      console.error('保存角色权限失败:', error);
      toast({
        title: "保存失败",
        description: error instanceof Error ? error.message : "保存角色权限时发生错误",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  if (!isOpen || !role) return null;

  const allSelected = permissions.length > 0 && selectedPermissionIds.length === permissions.length;
  const someSelected = selectedPermissionIds.length > 0 && selectedPermissionIds.length < permissions.length;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <Card className="w-full max-w-4xl max-h-[90vh] overflow-y-auto bg-white">
        <div className="p-6">
          {/* 标题栏 */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Key className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900">角色权限管理</h2>
                <p className="text-sm text-gray-600">为角色 "{role.role_name}" 分配权限</p>
              </div>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={onClose}
              className="flex items-center gap-2"
            >
              <X className="h-4 w-4" />
              关闭
            </Button>
          </div>

          {/* 权限列表 */}
          <div className="space-y-4">
            {/* 全选控制 */}
            <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
              <Checkbox
                checked={allSelected}
                onCheckedChange={handleSelectAll}
                className="data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
              />
              <span className="font-medium text-gray-900">
                全选权限 ({selectedPermissionIds.length}/{permissions.length})
              </span>
            </div>

            {/* 权限列表 */}
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <span className="ml-2 text-gray-600">加载权限数据中...</span>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {permissions.map((permission) => (
                  <div
                    key={permission.id}
                    className="flex items-center gap-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50"
                  >
                    <Checkbox
                      checked={selectedPermissionIds.includes(permission.id)}
                      onCheckedChange={(checked) => handlePermissionChange(permission.id, checked as boolean)}
                      className="data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
                    />
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <Shield className="h-4 w-4 text-blue-600" />
                        <span className="font-medium text-gray-900">{permission.permission_name}</span>
                      </div>
                      <div className="text-sm text-gray-500 mt-1">
                        <code className="bg-gray-100 px-1 py-0.5 rounded text-xs">
                          {permission.permission_code}
                        </code>
                      </div>
                      {permission.description && (
                        <div className="text-xs text-gray-400 mt-1">
                          {permission.description}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* 操作按钮 */}
          <div className="flex items-center justify-end gap-3 mt-6 pt-4 border-t border-gray-200">
            <Button
              variant="outline"
              onClick={onClose}
              disabled={saving}
            >
              取消
            </Button>
            <Button
              onClick={handleSave}
              disabled={saving || loading}
              className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2"
            >
              {saving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  保存中...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4" />
                  保存权限
                </>
              )}
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default RolePermissionModal;
