---
description: 
globs: 
alwaysApply: false
---
# 常见问题解决指南

## 后端问题

### 日期转换错误
**问题**: 出现 "Input should be a valid string" 错误
**原因**: Pydantic模型期望字符串类型，但收到了date对象
**解决方案**: 
1. 在 [schemas.py](mdc:service-operation-server/src/api/project/schemas.py) 中使用正确的字段类型：
   ```python
   project_date: date = Field(..., description="项目创建日期")
   ```
2. 添加字段序列化器：
   ```python
   @field_serializer('project_date')
   def serialize_date(self, date_value: date) -> str:
       return date_value.isoformat()
   ```

### 用户认证问题
**问题**: "创建用户不存在" 错误
**原因**: JWT token解析或用户查找失败
**解决方案**:
1. 检查 JWT 配置和密钥
2. 确保用户存在且token有效
3. 在 [endpoints.py](mdc:service-operation-server/src/api/project/endpoints.py) 中使用 `get_current_user` 依赖

### 数据库查询错误
**问题**: 项目列表查询返回400错误
**原因**: 数据序列化失败或查询参数错误
**解决方案**:
1. 检查日志中的具体错误信息
2. 验证数据模型的字段定义
3. 确保所有必需字段都有值

## 前端问题

### 输入框样式不一致
**问题**: 不同页面的输入框样式不统一
**解决方案**: 使用标准的Tailwind CSS类名：
```css
w-full h-10 px-4 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm
```

### API调用失败
**问题**: 前端API调用返回错误
**解决方案**:
1. 检查网络请求是否正确
2. 验证API端点路径
3. 确认认证token是否有效
4. 查看浏览器控制台和网络面板

### 组件状态问题
**问题**: 组件状态更新不及时
**解决方案**:
1. 确保使用正确的React Hooks
2. 检查依赖数组设置
3. 验证异步操作的处理

## 通用调试技巧

### 后端调试
1. 检查日志输出 - 查看详细的错误信息
2. 使用FastAPI自动文档 - `/docs` 端点测试API
3. 验证数据库连接和模型定义
4. 检查环境变量配置

### 前端调试
1. 使用浏览器开发者工具
2. 检查Console日志和Network请求
3. 使用React Developer Tools
4. 验证组件Props和State

### 开发流程
1. **后端优先**: 先确保API端点正常工作
2. **数据验证**: 检查请求和响应数据格式
3. **错误处理**: 实现完善的错误处理机制
4. **测试**: 进行单元测试和集成测试

## 性能优化

### 后端优化
- 使用数据库索引
- 实现分页查询
- 优化SQL查询
- 添加缓存机制

### 前端优化
- 使用React.memo避免不必要的重渲染
- 实现虚拟滚动（大列表）
- 使用懒加载
- 优化bundle大小
