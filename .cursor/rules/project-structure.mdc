---
description: 
globs: 
alwaysApply: false
---
# 服务运营自动化平台项目结构

## 项目概述
这是一个服务运营自动化平台，包含后端API服务和前端Web界面，主要用于管理团队住宿和交通预订项目。

## 目录结构

### 后端服务 (FastAPI + Tortoise ORM)
- `service-operation-server/` - 后端服务根目录
  - `src/api/` - API路由和端点
    - `project/` - 项目管理相关API
      - [endpoints.py](mdc:service-operation-server/src/api/project/endpoints.py) - 项目CRUD操作端点
      - [schemas.py](mdc:service-operation-server/src/api/project/schemas.py) - Pydantic数据模型
  - `src/db/models/` - 数据库模型定义
    - [project.py](mdc:service-operation-server/src/db/models/project.py) - 项目数据模型
    - [user.py](mdc:service-operation-server/src/db/models/user.py) - 用户数据模型
  - `src/api/dependencies.py` - 依赖注入和认证中间件

### 前端应用 (React + TypeScript + Tailwind)
- `service-operation-frontend/` - 前端应用根目录
  - `src/pages/` - 页面组件
    - [ProjectManagementPage.tsx](mdc:service-operation-frontend/src/pages/ProjectManagementPage.tsx) - 项目管理主页面
  - `src/components/` - 可复用组件
    - `project/` - 项目相关组件
    - `common/` - 通用组件
    - `layout/` - 布局组件
  - `src/services/` - API服务层
  - `src/types/` - TypeScript类型定义

### 原型设计
- `service-operation-prototype/` - 原型设计文件

## 技术栈
- **后端**: FastAPI, Tortoise ORM, PostgreSQL/SQLite, JWT认证
- **前端**: React, TypeScript, Tailwind CSS, Lucide图标
- **工具**: Vite, ESLint, Prettier
