---
description: 
globs: 
alwaysApply: false
---
# 前端开发指南

## 组件开发模式

### 页面组件
主要页面组件在 `src/pages/` 目录中，如 [ProjectManagementPage.tsx](mdc:service-operation-frontend/src/pages/ProjectManagementPage.tsx)：
- 使用React Hooks进行状态管理
- 实现数据获取和CRUD操作
- 包含分页、搜索、筛选功能
- 使用MainLayout包装页面结构

### 可复用组件
组件按功能模块组织在 `src/components/` 目录：
- `project/` - 项目相关组件（表格、卡片、模态框）
- `common/` - 通用组件（分页、按钮等）
- `layout/` - 布局组件

### 状态管理模式
使用React Hooks进行本地状态管理：
```typescript
const [loading, setLoading] = useState(false);
const [projects, setProjects] = useState<Project[]>([]);
const [currentPage, setCurrentPage] = useState(1);
```

## 样式和UI开发

### Tailwind CSS类名规范
遵循一致的样式模式：
- 输入框：`w-full h-10 px-4 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm`
- 按钮：`px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm font-medium`
- 卡片：`bg-white rounded-lg border border-gray-200 p-4`

### 图标使用
使用Lucide React图标库：
```typescript
import { Search, Grid, List, Plus } from 'lucide-react';
```

### 响应式设计
使用Tailwind的响应式前缀：
- `md:` - 中等屏幕
- `lg:` - 大屏幕
- `xl:` - 超大屏幕

## API服务调用

### 服务层模式
API调用通过服务层封装：
```typescript
const response = await ProjectService.getProjects(params);
if (response.success && response.data) {
    setProjects(response.data.items);
} else {
    console.error('API调用失败:', response.error);
}
```

### 错误处理
统一的错误处理模式：
```typescript
try {
    const response = await apiCall();
    if (response.success) {
        // 处理成功情况
    } else {
        alert('操作失败：' + response.error);
    }
} catch (error) {
    console.error('操作异常:', error);
    alert('操作失败，请稍后重试');
}
```

## 组件交互模式

### 模态框
使用受控组件模式：
```typescript
const [showModal, setShowModal] = useState(false);
const [selectedItem, setSelectedItem] = useState<Item | null>(null);
```

### 表单处理
实现统一的表单提交流程：
1. 验证输入数据
2. 显示加载状态
3. 调用API
4. 处理响应
5. 更新UI状态

### 列表操作
支持CRUD操作的标准模式：
- 创建：打开模态框 → 提交表单 → 刷新列表
- 查看：显示详情页面或模态框
- 编辑：预填充表单 → 提交更新 → 刷新列表
- 删除：确认对话框 → 删除操作 → 刷新列表
